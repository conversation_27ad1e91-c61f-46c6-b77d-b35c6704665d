/**
 * 
 */
package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.ActiveDirectoryConfiguration;
import com.bolt.dashboard.request.ActiveDirectoryReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.ActiveDirectoryService;

@RestController
public class ActiveDirectoryController {
    private static final Logger LOG = LogManager.getLogger(ActiveDirectoryController.class);
    @Autowired
    private ActiveDirectoryService activeDirectoryService;

    @Autowired
    public ActiveDirectoryController(ActiveDirectoryService activeDirectoryService) {
        this.activeDirectoryService = activeDirectoryService;
    }

    @RequestMapping(value = "/Savedata", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    public ResponseEntity<ActiveDirectoryConfiguration> saveADDetails(@RequestBody List<ActiveDirectoryReq> req) {
    	if(!req.isEmpty()){
        ActiveDirectoryReq activeDirectoryReq = req.get(0);

        LOG.info("Inside ADController and org  :  " + activeDirectoryReq.getOrganisationName());
        return ResponseEntity.status(HttpStatus.CREATED).body(
                activeDirectoryService.saveJobDetails(activeDirectoryReq.toDetailsAddSetting(activeDirectoryReq)));
    	}else
    	{
    		LOG.info("ADReq values from UI are null.....");
    		return null;
    	}
    }

    @RequestMapping(value = "/getADDetails", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<List<ActiveDirectoryConfiguration>> fetchADList() {

        return activeDirectoryService.fetchJobDetails();

    }

}
