package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.AzureLogMonitorResource;
import com.bolt.dashboard.core.model.ComponentStoryAgeing;
import com.bolt.dashboard.service.AzureLogMonitorService;

@RestController
public class AzureLogMonitorController {

	private AzureLogMonitorService azureLogMonitorService;
	@Autowired
	public AzureLogMonitorController(AzureLogMonitorService azureLogMonitorService) {
		this.azureLogMonitorService=azureLogMonitorService;
	}
	@RequestMapping(value = "/azureLogData", method = GET, produces = APPLICATION_JSON_VALUE)
	public  List<AzureLogMonitorResource>  storyAgeing(@RequestParam("pName") String projName){
		 
		return this.azureLogMonitorService.getAzureLogdata(projName);
	}
	
	
}
