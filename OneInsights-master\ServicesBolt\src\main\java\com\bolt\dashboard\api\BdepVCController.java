package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.json.simple.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.SCMToolService;
import com.bolt.dashboard.tfsversion.TFSVersionControllerClientImplementation;
import com.bolt.dashboard.tfsversion.TFSVersionControllerExceptions;
import com.sun.jersey.api.client.ClientResponse;
import com.sun.jersey.api.client.WebResource;

@RestController
public class BdepVCController {
    private SCMToolService scmToolService;
    private static final Logger LOG = LogManager.getLogger(TFSVersionControllerClientImplementation.class);
    String urlnew;

    @Autowired
    public BdepVCController(SCMToolService scmToolService) {
        this.scmToolService = scmToolService;
    }

    public BdepVCController() {
    }

    @RequestMapping(value = "/bdepVCNew", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<List<SCMTool>> getJSONData(@RequestParam("url") String url,
            @RequestParam("userName") String userName, @RequestParam("password") String password,
            @RequestParam("projectName") String projectName) throws TFSVersionControllerExceptions {

        long lastUpdate = 1;
        try {
            new BdepVCController().getCommits(url, userName, password, projectName);
        } catch (Exception e) {
            LOG.error(e.getMessage());
            throw new TFSVersionControllerExceptions(e);
        }
        List<SCMTool> scmtoolList;
        try {
            scmtoolList = new BdepVCController().getCommits(url, userName, password, projectName);
            return new DataResponse<List<SCMTool>>(scmtoolList, lastUpdate);
        } catch (Exception e) {
            LOG.error(e.getMessage());
            throw new TFSVersionControllerExceptions(e);
        }

    }

    @SuppressWarnings({ "unchecked", "rawtypes", "unused" })
    public List<SCMTool> getCommits(String url, String userName, String password, String projectName)
            throws TFSVersionControllerExceptions {
        List<SCMTool> toolList = new ArrayList<SCMTool>();
        JSONObject jsonObject;
        urlnew = url + "/tfs/DefaultCollection/" + projectName + "/_apis/tfvc/changesets/";
        jsonObject = makeRestCall(urlnew, userName, password);
        List<Object> objOutput = (ArrayList<Object>) jsonObject.get("value");

        for (Object objOutputJson : objOutput) {

            Iterator<Entry<Object, Object>> keySetIterator = ((Map<Object, Object>) objOutputJson).entrySet()
                    .iterator();

            SCMTool commit = new SCMTool();
            commit.setScType("TFS");
            commit.setTimestamp(new Date().getTime());
            while (keySetIterator.hasNext()) {
                Entry hm = (Entry) keySetIterator.next();
                Object key = hm.getKey();
                Object value = hm.getValue();
                if ("comment".equalsIgnoreCase(hm.getKey().toString()) && hm.getValue().toString() != null) {
                    commit.setCommitLog(hm.getValue().toString());
                }
                if ("createdDate".equalsIgnoreCase(hm.getKey().toString()) && hm.getValue() != null) {
                    long scmDate = ConstantVariable.timestamp(hm.getValue(),projectName);
                    commit.setCommitTS(scmDate);
                }
                if ("url".equalsIgnoreCase(hm.getKey().toString()) && hm.getValue().toString() != null) {
                    commit.setUrl(hm.getValue().toString());
                }
                if ("changesetId".equalsIgnoreCase(key.toString())) {
                    String revision = hm.getValue().toString();
                    commit.setRevisionNo(revision);
                    if (urlnew.contains("/" + projectName)) {
                        urlnew = urlnew.replace("/" + projectName, "");

                    }
                    String changeSetUrl = urlnew + revision + "/changes?api-version=1.0";
                    LOG.info("Changeset Id URL  " + changeSetUrl);
                    int[] changes = new int[4];
                    changes = makeRestCallForChangeType(changeSetUrl, userName, password);
                    int totalAdditions = changes[0];
                    int totalDeletions = changes[1];
                    int totalModification = changes[2];
                    commit.setAddition(totalAdditions);
                    commit.setDeletion(totalDeletions);
                    commit.setModification(totalModification);
                    int totalChanges = 0;
                    	totalChanges = totalChanges+totalAdditions;
                    	totalChanges = totalChanges+totalDeletions;
                    	totalChanges = totalChanges+totalModification;
                    commit.setNoOfChanges(totalChanges);
                }
                if ("checkedInBy".equalsIgnoreCase(key.toString())) {
                    Iterator<Entry<Object, Object>> keySetIteratorNew = ((Map<Object, Object>) value).entrySet()
                            .iterator();
                    while (keySetIteratorNew.hasNext()) {
                        Entry hmNew = (Entry) keySetIteratorNew.next();

                        if ("displayName".equalsIgnoreCase(hmNew.getKey().toString()) && hmNew.getValue() != null) {

                            commit.setCommiter(hmNew.getValue().toString());

                        }

                    }

                }

            }
            toolList.add(commit);

        }
        return toolList;

    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    public int[] makeRestCallForChangeType(String url, String userId, String password) {
        int totalAdditions = 0;
        int totalDeletions = 0;
        int totalModification = 0;
        int totalChanges = 0;
        JSONObject jsonObject = makeRestCall(url, userId, password);
        List<Object> objOutput = (ArrayList<Object>) jsonObject.get("value");
        for (Object objOutputJson : objOutput) {
            Iterator<Entry<Object, Object>> keySetIterator = ((Map<Object, Object>) objOutputJson).entrySet()
                    .iterator();
            while (keySetIterator.hasNext()) {
                Entry hm = (Entry) keySetIterator.next();

                if ("changeType".equalsIgnoreCase(hm.getKey().toString())) {
                    String editType = hm.getValue().toString();
                    if (editType.contains("edit"))
                        totalModification = totalModification + 1;

                    else if (editType.contains("delete"))
                        totalDeletions = totalDeletions + 1;
                    else {
                        totalAdditions = totalAdditions + 1;
                    }

                }

            }
        }
        totalChanges = (totalAdditions + totalDeletions + totalModification);
        LOG.info("total additions per revision " + totalAdditions);
        LOG.info("total deletions per revision " + totalDeletions);
        LOG.info("total modification per revision " + totalModification);
        LOG.info("Total changes per revision " + (totalAdditions + totalDeletions + totalModification));

        return new int[] { totalAdditions, totalDeletions, totalModification, totalChanges };
    }

    public JSONObject makeRestCall(String url, String userId, String password) {

        if (!"".equals(userId) && !"".equals(password)) {

            com.sun.jersey.api.client.Client restClient = com.sun.jersey.api.client.Client.create();
            String authString = userId + ":" + password;
            String authStringEnc = String.valueOf(new Base64().encode(authString.getBytes()));
            WebResource webResource = restClient.resource(url);
            ClientResponse resp = webResource.accept("application/json")
                    .header("Authorization", "Basic " + authStringEnc).get(ClientResponse.class);
            if (resp.getStatus() != 200) {
                LOG.error("Unable to connect to the server");
            }
            return resp.getEntity(JSONObject.class);

        }
        return null;

    }
}
