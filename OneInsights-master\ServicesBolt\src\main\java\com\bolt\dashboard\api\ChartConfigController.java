package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.ChartConfiguration;
import com.bolt.dashboard.request.ChartConfigurationReq;
import com.bolt.dashboard.request.ChartMetricReq;
import com.bolt.dashboard.service.ChartConfigService;

@RestController
public class ChartConfigController {

	@Autowired
	private ChartConfigService chartConfigService;

	@RequestMapping(value = "/getChartConfigForProject", method = GET, produces = APPLICATION_JSON_VALUE)
	public ChartConfiguration getChartConfigForProject(@RequestParam("pName") String pName,
			@RequestParam("almType") String almType) {
		return chartConfigService.getChartConfigForProject(pName, almType);
	}
	
	@RequestMapping(value = "/getChartConfigForProjectConfig", method = GET, produces = APPLICATION_JSON_VALUE)
	public ChartConfiguration getChartConfigForProjectCopy(@RequestParam("pName") String pName,
			@RequestParam("almType") String almType) {
		return chartConfigService.getChartConfigForProject(pName, almType);
	}

	/*@RequestMapping(value = "/saveChartDetails", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public String saveChartDetails(@RequestBody ChartConfiguration model) {
		return chartConfigService.saveChartDetailsForProject(model);

	}*/
	
	@RequestMapping(value = "/saveChartDetails", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public String saveChartDetails(@RequestBody ChartConfigurationReq  model) {
		//ChartConfigurationReq chartConfigurationReq = new ChartConfigurationReq();
		//chartConfigurationReq.setMetrics(model);
		//return ResponseEntity.status(HttpStatus.CREATED).body(chartConfigService.saveChartDetailsForProject(chartConfigurationReq.toChartConfig()));
		
		return chartConfigService.saveChartDetailsForProject((model.toChartConfig()));

	}

}
