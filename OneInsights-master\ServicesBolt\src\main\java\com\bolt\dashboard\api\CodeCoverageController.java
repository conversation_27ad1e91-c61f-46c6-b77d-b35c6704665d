package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.CodeCoverageBO;
import com.bolt.dashboard.core.model.ProjectCodeCoverage;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.CodeCoverageService;

@RestController
public class CodeCoverageController {

	private CodeCoverageService codeCoverageService = null;

	@Autowired
	public CodeCoverageController(CodeCoverageService codeCoverageService) {
		this.codeCoverageService = codeCoverageService;
	}

	@RequestMapping(value = "/coverage", method = GET, produces = APPLICATION_JSON_VALUE)
	public Map<String, CodeCoverageBO> getCodeCoverageData(@RequestParam("pName") String pName) {

		return codeCoverageService.searchLastRecord(pName);

	}

	@RequestMapping(value = "/fileCoverage", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<String> getFileCoverage(@RequestParam("pName") String pName) {
		return codeCoverageService.searchFileCoverage(pName);

	}

	@RequestMapping(value = "/latestCodeCoverage", method = GET, produces = APPLICATION_JSON_VALUE)
	public Map<String, ProjectCodeCoverage> getHeatChartCoverageData(@RequestParam("pName") String pName) {
		return codeCoverageService.searchLastRecordHeatCoverage(pName);

	}

}