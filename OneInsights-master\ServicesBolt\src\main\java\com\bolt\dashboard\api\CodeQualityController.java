package com.bolt.dashboard.api;

/**
 * 
 */
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpSession;
import javax.ws.rs.core.Response;

import org.json.JSONArray;
import org.json.JSONTokener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.request.CodeQualityReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.CodeQualityService;

@RestController
public class CodeQualityController {
	private final CodeQualityService codeQualityService;

	@Autowired
	public CodeQualityController(CodeQualityService codeQualityService) {
		this.codeQualityService = codeQualityService;
	}

	@RequestMapping(value = "/quality", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<CodeQuality>> codeQualityData(@RequestParam("proName") String[] proName,
			@RequestParam("sDate") long sDate, @RequestParam("eDate") long eDate, @RequestParam("flag") boolean flag) {
		CodeQualityReq qualityRequest = new CodeQualityReq();
		return codeQualityService.search(qualityRequest, proName[0], sDate, eDate, flag);

	}
	
	@RequestMapping(value = "/qualityConfig", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<CodeQuality>> codeQualityDataCopy(@RequestParam("proName") String[] proName,
			@RequestParam("sDate") long sDate, @RequestParam("eDate") long eDate, @RequestParam("flag") boolean flag) {
		CodeQualityReq qualityRequest = new CodeQualityReq();
		return codeQualityService.search(qualityRequest, proName[0], sDate, eDate, flag);

	}
	
	@RequestMapping(value = "/codeQualityHome", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<Map<String,String>> getCodeQualityHome(@RequestParam("pName") String projName,@RequestParam("almType") String almType){
		 
	
		
		return this.codeQualityService.getCodeQualityHome(projName,almType);
	}
	
	
	@RequestMapping(value = "/lastRecord", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<CodeQuality> lastCodeQualityData(@RequestParam("proName") String[] proName) {
		CodeQualityReq qualityRequest = new CodeQualityReq();
		return codeQualityService.lastRecord(qualityRequest, proName[0]);

	}

	@RequestMapping(value = "/bdepCodeQuality", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<String> getAJSONCodeQualityData(@RequestParam("url") String url,
			@RequestParam("userName") String userName, @RequestParam("password") String password) {

		return codeQualityService.searchJson(url, userName, password);

	}

	@RequestMapping(value = "/bdepCodeQualityNew", method = GET, produces = APPLICATION_JSON_VALUE)
	public Response getJSONCodeQualityData(@RequestParam("url") String url, @RequestParam("userName") String userName,
			@RequestParam("password") String password) {

		DataResponse<String> result = codeQualityService.searchJson(url, userName, password);
		JSONArray jsonresponse = (JSONArray) new JSONTokener(result.getResult()).nextValue();
		StringBuilder responseAsString = new StringBuilder(jsonresponse.toString());

		return Response.status(200).entity(responseAsString).build();

	}

}
