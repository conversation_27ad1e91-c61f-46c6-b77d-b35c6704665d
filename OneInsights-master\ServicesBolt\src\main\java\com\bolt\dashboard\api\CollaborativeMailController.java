package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.Date;
import java.util.Iterator;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.CollaborativeMailModel;
import com.bolt.dashboard.request.CollaborativeMailReq;
import com.bolt.dashboard.request.CollaborativeMailSetupReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.CollaborativeMailService;
import com.bolt.dashboard.serviceModel.MailStatusModel;

@RestController
public class CollaborativeMailController {

	
	
    private CollaborativeMailService service;

    @Autowired
    public CollaborativeMailController(CollaborativeMailService mailService) {
	this.service = mailService;

    }

    @RequestMapping(value = "/collaborativemail", method = POST, produces = APPLICATION_JSON_VALUE)
    public DataResponse<MailStatusModel> getCollaborativeMail(@RequestBody List<CollaborativeMailReq> reqList) {
	String toAddress = null;
	//CollaborativeMailReq req =null;
	CollaborativeMailReq req = new CollaborativeMailReq();
	Iterator<CollaborativeMailReq> modelIterator = reqList.iterator();
	if (modelIterator.hasNext()) {
	    req = modelIterator.next();
	    toAddress = req.getToAddress();
	}
	CollaborativeMailSetupReq request = new CollaborativeMailSetupReq();
	request.setMetric(reqList);
	if (toAddress == null) {
	    CollaborativeMailModel model = new CollaborativeMailModel();
	    model.setActionId(req.getActionId());
	    model.setChartName(req.getChartName());
	    model.setComment(req.getComment());
	    model.setProjectName(req.getProjectName());
	    model.setTagName(req.getTagName());
	    model.setTimeStamp(new Date().getTime());
	    model.setToAddress(req.getToAddress());
	    model.setFileName(req.getFileName());
	    model.setReporter(req.getReporter());
	    model.setStatus(req.getStatus());
	    model.setAssignedTo(req.getAssignedTo());
	    model.setSprintName(req.getSprintName());
	    return service.saveMailDetails(model);
	} else {
	    return service.sendMail((request.toMailSetupSetting()).get(0));
	}

    }
    
    @RequestMapping(value = "/saveAllActionTracker", method = POST, produces = APPLICATION_JSON_VALUE)
    public DataResponse<MailStatusModel> saveAllActionTracker(@RequestBody List<CollaborativeMailReq> reqList) {
	for(CollaborativeMailReq req : reqList) {
	    
	
	CollaborativeMailSetupReq request = new CollaborativeMailSetupReq();
	request.setMetric(reqList);
	    CollaborativeMailModel model = new CollaborativeMailModel();
	    model.setActionId(req.getActionId());
	    model.setChartName(req.getChartName());
	    model.setComment(req.getComment());
	    model.setProjectName(req.getProjectName());
	    model.setTagName(req.getTagName());
	    model.setTimeStamp(new Date().getTime());
	    model.setToAddress(req.getToAddress());
	    model.setFileName(req.getFileName());
	    model.setReporter(req.getReporter());
	    model.setStatus(req.getStatus());
	    model.setAssignedTo(req.getAssignedTo());
	    model.setSprintName(req.getSprintName());
	    service.sendMail(model);
//	     service.saveMailDetails(model);
	}
	 return null;
    }

    @RequestMapping(value = "/collaborativemailsearch", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<Iterable<CollaborativeMailModel>> getCollaborativeMailSearch(
	    @RequestParam("projectName") String projectName) {
	return service.search(projectName);

    }
     @RequestMapping(value = "/collaborativemaildelete", method = RequestMethod.DELETE, produces = APPLICATION_JSON_VALUE)
     public String deleteActionList(@RequestParam("projectName")String projectName,@RequestParam("actionId")String actionId){
    	 
    	 return service.deleteActionList(projectName,actionId);
     }

}
