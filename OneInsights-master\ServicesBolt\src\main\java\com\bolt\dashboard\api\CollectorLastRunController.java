/**
 * 
 */
package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.service.CollectorLastRunService;

@RestController
public class CollectorLastRunController {
	@Autowired
	private CollectorLastRunService collectorLastRunService;

	@Autowired
	public CollectorLastRunController(CollectorLastRunService collectorLastRunService) {
		this.collectorLastRunService = collectorLastRunService;
	}

	@RequestMapping(value = "/getLastRun", method = GET, produces = APPLICATION_JSON_VALUE)
	public Map<String, String[]> fetchData(@RequestParam("proName") String proName) {

		return collectorLastRunService.fetchData(proName);

	}

}
