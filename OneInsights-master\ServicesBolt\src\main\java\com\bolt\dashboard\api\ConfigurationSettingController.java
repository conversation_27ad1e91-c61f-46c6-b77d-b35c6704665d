package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.ProjectCollector;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.request.ConfigurationSettingMetricsReq;
import com.bolt.dashboard.request.ConfigurationSettingReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.ConfigurationSettingService;

@RestController
public class ConfigurationSettingController {

	@Autowired
	private ConfigurationSettingService configurationSettingServices;

	private static final Logger LOG = LogManager.getLogger(ConfigurationSettingController.class);

	@RequestMapping(value = "/configuration", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<ConfigurationSetting>> configData() {
		return configurationSettingServices.getConfig();
	}
@RequestMapping(value = "/configurationProject", method=GET, produces = APPLICATION_JSON_VALUE)
public ResponseEntity<ConfigurationSetting> configDataProject(@RequestParam("pName") String pName){
	ConfigurationSetting connfig=configurationSettingServices.getConfigProject(pName);
	return ResponseEntity.ok().body(connfig);
	
}
@RequestMapping(value = "/configurationProjectConfig", method=GET, produces = APPLICATION_JSON_VALUE)
public ResponseEntity<ConfigurationSetting> configDataProjectCopy(@RequestParam("pName") String pName){
	ConfigurationSetting connfig=configurationSettingServices.getConfigProject(pName);
	return ResponseEntity.ok().body(connfig);
	
}
	
	@RequestMapping(value = "/configuration", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<ConfigurationSetting> createDashboard(@RequestBody List<ConfigurationSettingMetricsReq> req) {
		ConfigurationSettingReq config = new ConfigurationSettingReq();

		config.setMetrics(req);

		ConfigurationSetting configurationSetting = configurationSettingServices.addConfig(config.toConfigSetting());
		LOG.info("Value of Flag : " + configurationSetting.isAddFlag());
		if (configurationSetting.isAddFlag()) {
			LOG.info("Inside flag loop.....................");
			new ProjectCollector().multiTaskThread(configurationSetting.getProjectName());
		}
		LOG.info("create dashboard completed.......................");
		return ResponseEntity.status(HttpStatus.CREATED)
				.body(configurationSettingServices.addConfig(config.toConfigSetting()));
	}

	@RequestMapping(value = "/configurationDelete", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<Integer> deleteDashboard(@RequestBody List<ConfigurationSettingMetricsReq> req) {

		ConfigurationSettingReq config = new ConfigurationSettingReq();

		config.setMetrics(req);
		return ResponseEntity.status(HttpStatus.CREATED)
				.body(configurationSettingServices.deleteConfig(config.toConfigSetting()));
	}
	@RequestMapping(value="/delAllCollections", method=RequestMethod.GET)
   public boolean deleteAllCollections(@RequestParam("projectName") String projectName) {
	   return configurationSettingServices.deleteAllCollections(projectName);
		
		
	}
	
	
	@RequestMapping(value="/delProject", method=RequestMethod.GET)
	   public boolean deleteProject(@RequestParam("projectName") String projectName) {
		   return configurationSettingServices.deleteProject(projectName);
			
			
		}
	
}
