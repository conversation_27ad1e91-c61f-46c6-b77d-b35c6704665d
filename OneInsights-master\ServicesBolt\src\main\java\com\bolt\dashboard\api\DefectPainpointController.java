package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.DefectPainpoint;
import com.bolt.dashboard.request.DefectPainpointReq;
import com.bolt.dashboard.service.DefectPainpointService;

/**
 * <AUTHOR>
 *
 */
@RestController
public class DefectPainpointController {

	/**
	 * 
	 */
	private DefectPainpointService defectPainpointService;

	/**
	 * 
	 */
	@Autowired
	public DefectPainpointController(DefectPainpointService service) {
		this.defectPainpointService = service;
	}

	
	@RequestMapping(value = "/savedefectPainpoint", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<Boolean> save(@RequestBody DefectPainpointReq req) {
                 DefectPainpoint health= req.toDefectPainPoint(req);
		return ResponseEntity.status(HttpStatus.CREATED).body(defectPainpointService.saveData(health));
	}

	@RequestMapping(value = "/getdefectPainpoint", method = GET, produces = APPLICATION_JSON_VALUE)
	public DefectPainpoint fetchData(@RequestParam("projectName") String projectName) {

		return defectPainpointService.fetchData(projectName);
	}
	@RequestMapping(value = "/getPpData", method = GET, produces = APPLICATION_JSON_VALUE)
	public Iterable<DefectPainpoint> getPpData() {

		return defectPainpointService.getPpData();
	}
}
