package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.EngagementConfig;
import com.bolt.dashboard.core.model.EngagementConfigMetrics;
import com.bolt.dashboard.core.model.Engagement;
import com.bolt.dashboard.request.EngagementConfigReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.EngagementConfigService;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

@RestController
public class EngagementConfigController {

	private EngagementConfigService engConfigService;

	@Autowired
	public EngagementConfigController(EngagementConfigService engConfigService) {
		this.engConfigService = engConfigService;
	}

	@RequestMapping(value = "/engConfigSave", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<Boolean> saveEngageConfig(@RequestBody EngagementConfigReq req) {
		EngagementConfig engConfig=req.toEngagementConfig(req);

		return ResponseEntity.status(HttpStatus.CREATED).body(engConfigService.saveEngagementConfig(engConfig));
	}

	@RequestMapping(value = "/engConfig", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<EngagementConfig>> getEngageConfig() {

		return engConfigService.getEngagementConfig();
	}

	@RequestMapping(value = "/engConfigByTower", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<EngagementConfig>> getEngConfigByTower(@RequestParam("towerName") String towerName) {
		return engConfigService.getEngConfigByTower(towerName);
	}
    
	@RequestMapping(value = "/engDefaultData", method = GET ,produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<EngagementConfigMetrics>> getEngDefaultData() {

		return engConfigService.getEngDefaultData();
	}
	
	@RequestMapping(value = "/engData", method = GET ,produces = APPLICATION_JSON_VALUE)
	public DataResponse<Engagement> getEngData() {

		return engConfigService.getEngagementData();
	}
}
