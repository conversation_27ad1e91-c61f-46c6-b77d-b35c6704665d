package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.SortedMap;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import com.bolt.dashboard.core.model.EngagementRule;
import com.bolt.dashboard.request.EngagementRuleReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.EngagementScoreService;

@RestController
public class EngagementRuleController {
	private static final Logger LOG = LogManager.getLogger(EngagementRuleController.class);
	@Autowired
	private EngagementScoreService engagementScoreService;

	@RequestMapping(value = "/engScoreSave", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<Boolean> saveEngageScore(@RequestBody EngagementRuleReq req) {
   
		EngagementRule engScore=req.toEngagementRule(req);
		return ResponseEntity.status(HttpStatus.CREATED).body(engagementScoreService.saveEngagementScoreService(engScore));
	}
	@RequestMapping(path = "/downloadEngIndexTemp", method = GET)
	  public void getSteamingFile(HttpServletRequest req,HttpServletResponse resp) throws IOException, URISyntaxException {
			 resp.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		        resp.setHeader("Content-disposition", "attachment; filename=EngIndexTemplate.xlsx");
		        InputStream in =getClass().getClassLoader().getResourceAsStream("EngIndexTemplate.xlsx");
		    	//URL resource1 = getClass().getClassLoader().getResource("EngIndexTemplate.xlsx");
		//File excelFile= new File(resource1.toURI());
		//FileInputStream file = 
		        try {
		          OutputStream out = resp.getOutputStream();

		            byte[] buffer = new byte[1048];
		        
		            int numBytesRead;
		            while ((numBytesRead = in.read(buffer)) > 0) {
		                out.write(buffer, 0, numBytesRead);
		            }
		        }catch (Exception e) {
					// TODO: handle exception
		        	LOG.info(e);
		        	
		        	
				}
		}

	@RequestMapping(value = "/engScoreByProjectName", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<EngagementRule>> getEngScoreByProjectName(@RequestParam String projectName) {

		return engagementScoreService.getEngScoreByProjectName(projectName);
	}
	
	@RequestMapping(value = "/engScoreByTowerName", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<EngagementRule>> getEngScoreByTowerName(@RequestParam String towerName) {
		
		return engagementScoreService.getEngScoreByTowerName(towerName);
	}
	@RequestMapping(value = "/engScoreAll", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<EngagementRule>> getEngScore() {
		return engagementScoreService.getEngScore();
	}
	@RequestMapping(value = "/engScoreHome", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<SortedMap<String,Double>> getEngScoreHome(@RequestParam String pName) {
		
		return engagementScoreService.getEngScoreHome(pName);
	}
	

	@RequestMapping(value = "/engIndexExcel", method = POST, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<Boolean> engScorecardExcelUpload(@RequestParam("file0") MultipartFile file,
	@RequestParam("projectName") String projectName,@RequestParam("monthName") String monthName,
	@RequestParam("displayMonth") String displayMonth) {
	return ResponseEntity.status(HttpStatus.OK).body(engagementScoreService.engScorecardExcelUpload(file,projectName,monthName,displayMonth));
	}


}




