package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URISyntaxException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.bolt.dashboard.core.model.EngScorecard;
import com.bolt.dashboard.core.model.EngagementScorecardSubjectiveData;
import com.bolt.dashboard.engagementScorecard.EngScoreCardQuarterData;
import com.bolt.dashboard.request.EngagementScorecardSubjectiveDataReq;
import com.bolt.dashboard.service.EngagementScorecardService;



@RestController
public class EngagementScorecardController {
	private static final Logger LOGGER = LogManager.getLogger(EngagementScorecardController.class);
	@Autowired
	EngagementScorecardService engScorecardService;

	@RequestMapping(value = "/engScoreCardByProject", method = GET, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<EngScorecard> engScoreCardByProject(@RequestParam("pName") String pName) {

		return ResponseEntity.status(HttpStatus.OK).body(engScorecardService.getEngScoreCardByProject(pName));
	}

	@RequestMapping(value = "/engScoreCardSubjetiveHistory", method = POST)
	public ResponseEntity<EngagementScorecardSubjectiveData> engScoreCardSubjectiveDataHistory(
			@RequestBody EngagementScorecardSubjectiveDataReq engScoreCardSubjectiveReq) {
		EngagementScorecardSubjectiveData engScoreSubjObj = engScoreCardSubjectiveReq.toEngScorecardSubjective();

		return ResponseEntity.status(HttpStatus.OK)
				.body(engScorecardService.saveEngScoreCardSubjectiveDataHistory(engScoreSubjObj));
	}

	@RequestMapping(value = "/engScoreCardSubjetive", method = POST)
	public ResponseEntity<EngagementScorecardSubjectiveData> engScoreCardSubjectiveDataSave(
			@RequestBody EngagementScorecardSubjectiveDataReq engScoreCardSubjectiveReq) {
		EngagementScorecardSubjectiveData engScoreSubjObj = engScoreCardSubjectiveReq.toEngScorecardSubjective();
		return ResponseEntity.status(HttpStatus.OK)
				.body(engScorecardService.saveEngScoreCardSubjectiveData(engScoreSubjObj));
	}

	@RequestMapping(value = "/engScoreCardSubjetiveByProject", method = GET, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<EngagementScorecardSubjectiveData> engScoreCardSubjectiveData(
			@RequestParam("pName") String pName) {

		return ResponseEntity.status(HttpStatus.OK)
				.body(engScorecardService.getEngScoreCardSubjetiveDataByProject(pName));
	}

	@RequestMapping(value = "/engScorecardQuarters", method = GET, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<EngScoreCardQuarterData> engScoreCardQuarter(@RequestParam("pName") String pName) {

		return ResponseEntity.status(HttpStatus.OK).body(engScorecardService.getEngScoreCardQuaters(pName));

	}

	@RequestMapping(value = "/engScorecardExcel", method = POST, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<Boolean> engScorecardExcelUpload(@RequestParam("file0") MultipartFile file,
			@RequestParam("projectName") String projectName,
			@RequestParam("sprintName") String sprintName,
			@RequestParam("startDate") long startDate,
			@RequestParam("endDate") long endDate,
			@RequestParam("releaseIterationNo") String releaseIterationNo,
			@RequestParam("unitOfSizing") String unitOfSizing,
			@RequestParam("teamSize") String teamSize) {
		
		return ResponseEntity.status(HttpStatus.OK).body(engScorecardService.engScorecardExcelUpload(file,projectName,sprintName,startDate,endDate,releaseIterationNo,unitOfSizing,teamSize));
	
	}

	@RequestMapping(value = "/engScorecardFormFill", method = POST, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<Boolean> engScorecardFormFill(
			@RequestBody EngagementScorecardSubjectiveDataReq engScoreCardSubjectiveReqe) {
		return ResponseEntity.status(HttpStatus.OK)
				.body(engScorecardService.engScorecardFormUpload(engScoreCardSubjectiveReqe));

	}

	@RequestMapping(value = "/engScorecardFormFillEdit", method = POST, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<Boolean> engScorecardFormFillEdit(
			@RequestBody EngagementScorecardSubjectiveDataReq engScoreCardSubjectiveReqe) {
		return ResponseEntity.status(HttpStatus.OK)
				.body(engScorecardService.engScorecardFormUploadEdit(engScoreCardSubjectiveReqe));

	}

	@RequestMapping(value = "downloadScoreCardSprintTemplate", method = GET)
	public void getSteamingFileSprint(@RequestParam("projectType") String type ,HttpServletRequest req, HttpServletResponse resp)
			throws IOException, URISyntaxException {
		resp.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		resp.setHeader("Content-disposition", "attachment; filename=EngIndexTemplate.xlsx");
		//URL resource1 = getClass().getClassLoader().getResource("EngIndexTemplate.xlsx");
		File excelFile; 
		if(type.equalsIgnoreCase("Kanban"))
		 excelFile = new File("KanbanProject.xlsx");
		else
			excelFile = new File("SprintProject.xlsx");
		// FileInputStream file =
		try (InputStream in = new FileInputStream(excelFile); OutputStream out = resp.getOutputStream()) {

			byte[] buffer = new byte[1048];

			int numBytesRead;
			while ((numBytesRead = in.read(buffer)) > 0) {
				out.write(buffer, 0, numBytesRead);
			}
		}
	}
	
//	@RequestMapping(path = "/download", method = GET)
//	public ResponseEntity<InputStreamResource> download(String param) throws IOException {
//
//		File file= new File("C:/Migration/BrillioBOLT_A8_Migration/SprintProject.xlsx");
//	    InputStreamResource resource = new InputStreamResource(new FileInputStream(file));
//	    return ResponseEntity.ok()
//	            .contentLength(file.length())
//	            .body(resource);
//	}
	

	@RequestMapping(value="/downloadExcel",method=GET)
	public ResponseEntity<Object> downloadFile() 
	{
		BufferedReader br=null;
		try {
			String fileName = "C:\\Migration\\BrillioBOLT_A8_Migration\\txtFile.txt";
			File file = new File(fileName);
			InputStreamResource resource = new InputStreamResource(new FileInputStream(file));
			 br
	            = new BufferedReader(new FileReader(file));
			 
			 String st;
		        // Condition holds true till
		        // there is character in a string
		        while ((st = br.readLine()) != null)
		 
		            // Print the string
		            System.out.println(st);
			HttpHeaders header = new HttpHeaders();
			header.add("Content-Disposition", String.format("attachment; filename=\"%s\"", file.getName()));
			header.add("Cache-Control", "no-cache,no-store,must-revalidate");
			header.add("Pragme","no-cache");
			header.add("Expire","0");
			
			
			
			return ResponseEntity.ok().headers(header)
					.contentLength(file.length())
					.contentType(MediaType.parseMediaType("application/txt")).body(resource);
			 
		}
		catch (Exception ex) {
			LOGGER.error(ex.getMessage());
	
		}finally {
			try {
				  if( br!= null)
					   br.close();
				
			
			} catch (IOException e) {
				LOGGER.error(e.getMessage());
			
			}
		}

		return null;
		

		
		
	}
	


}
