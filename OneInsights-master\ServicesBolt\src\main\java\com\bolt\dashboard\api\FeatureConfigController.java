package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.POST;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.FeatureConfig;
import com.bolt.dashboard.request.FeatureConfigReq;
import com.bolt.dashboard.service.FeatureConfigService;

@RestController
public class FeatureConfigController {
	
	@Autowired
	private FeatureConfigService featureConfigService;
	
	@RequestMapping(value = "/featureConfig", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    public ResponseEntity<FeatureConfig> saveFeatureConfig(@RequestBody FeatureConfigReq featureConfigReq){
		
		FeatureConfig featureConfig= featureConfigReq.toFeatureConfig();
		
		featureConfig = this.featureConfigService.saveFeatureConfig(featureConfig);
		
		return ResponseEntity.ok().body(featureConfig);
		
		
	}
	@RequestMapping(value = "/featureConfig", method = GET, produces = APPLICATION_JSON_VALUE)
    public ResponseEntity<FeatureConfig> getFeatureConfig(@RequestParam("pName") String pName){
		
		FeatureConfig featureConfig= this.featureConfigService.getFeatureConfig(pName);
		
		
		return ResponseEntity.ok().body(featureConfig);
		
		
	}
	@RequestMapping(value = "/featureConfigForConfig", method = GET, produces = APPLICATION_JSON_VALUE)
    public ResponseEntity<FeatureConfig> getFeatureConfigCopy(@RequestParam("pName") String pName){
		
		FeatureConfig featureConfig= this.featureConfigService.getFeatureConfig(pName);
		
		
		return ResponseEntity.ok().body(featureConfig);
		
		
	}

}
