/**
 * 
 */
package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.GeneralDirectoryConfiguration;
import com.bolt.dashboard.request.GeneralDirectoryReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.GeneralDirectoryService;

@RestController
public class GeneralDirectoryController {
    private static final Logger LOG = LogManager.getLogger(GeneralDirectoryController.class);
    @Autowired
    private GeneralDirectoryService generalDirectoryService;

    @Autowired
    public GeneralDirectoryController(GeneralDirectoryService generalDirectoryService) {
        this.generalDirectoryService = generalDirectoryService;
    }

    @RequestMapping(value = "/generalData", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    public ResponseEntity<GeneralDirectoryConfiguration> saveGDDetails(@RequestBody List<GeneralDirectoryReq> req) {

        for (int j = 0; j < req.size(); j++) {

            GeneralDirectoryReq generalDirectoryReq = req.get(j);

            LOG.info("Inside GDController and org  :  " + generalDirectoryReq.getName());
            generalDirectoryService.saveGeneralDetails(generalDirectoryReq.toDetailsAddSetting(generalDirectoryReq));
        }

        return null;

    }

    @RequestMapping(value = "/gdDetails", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<List<GeneralDirectoryConfiguration>> retrieveList() {

        return generalDirectoryService.retrieveGeneralDetails();

    }

}
