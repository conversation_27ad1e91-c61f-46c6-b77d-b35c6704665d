/**
 * 
 */
package com.bolt.dashboard.api;

/**
 * 
 */
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.GoalSetting;
import com.bolt.dashboard.core.model.ProjectHealthConfig;
import com.bolt.dashboard.request.GoalCoveraeSettingMetricsReq;
import com.bolt.dashboard.request.GoalSettingMetricsReq;
import com.bolt.dashboard.request.GoalSettingReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.GoalSettingService;

@RestController
public class GoalSettingController {
	private final GoalSettingService goalSettingService;

	@Autowired
	public GoalSettingController(GoalSettingService goalSettingService) {
		this.goalSettingService = goalSettingService;
	}

	@RequestMapping(value = "/goals", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<GoalSetting>> goalData(@RequestParam("proName") String proName,
			@RequestParam("sDate") long sDate, @RequestParam("eDate") long eDate, @RequestParam("flag") boolean flag) {

		return goalSettingService.getGoals(proName, sDate, eDate, flag);

	}
	@RequestMapping(value = "/goalsConfig", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<GoalSetting>> goalDataCopy(@RequestParam("proName") String proName,
			@RequestParam("sDate") long sDate, @RequestParam("eDate") long eDate, @RequestParam("flag") boolean flag) {

		return goalSettingService.getGoals(proName, sDate, eDate, flag);

	}

	@RequestMapping(value = "/goals", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<GoalSetting> createDashboard(@RequestBody List<GoalSettingMetricsReq> req) {
		GoalSettingReq goal = new GoalSettingReq();
		goal.setMetrics(req);
		return ResponseEntity.status(HttpStatus.CREATED).body(goalSettingService.addGoals(goal.toGoalSetting()));
	}
	@RequestMapping(value = "/goalsCoverage", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<GoalSetting> createDashboard(@RequestBody GoalCoveraeSettingMetricsReq req) {
		GoalSettingReq goal = new GoalSettingReq();
		goal.setMetricFromTo(req);
		return ResponseEntity.status(HttpStatus.CREATED).body(goalSettingService.addGoalsCoverage(goal.toGoalSetting()));
	}


}
