package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class HealthCheckController {
	
	private static final Logger LOG = LogManager.getLogger(ALMConfigController.class);
	@RequestMapping(value = "/checkhealth", method = GET)
	public ResponseEntity<String> checkHelth(){
		LOG.info("HelthCheck--ok");
		return ResponseEntity.status(HttpStatus.OK).body("ok");
	}
}
