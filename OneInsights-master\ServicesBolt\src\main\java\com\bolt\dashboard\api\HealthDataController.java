package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.Iterator;
import java.util.List;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.HealthData;
import com.bolt.dashboard.core.model.sprintPredictedBugMetrics;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.HealthDataService;

@RestController
public class HealthDataController {
	private static final Logger LOG = LogManager.getLogger(HealthDataController.class);
	private HealthDataService healthDataService;
	ConfigurationSettingRep configurationRepo;
	AnnotationConfigApplicationContext ctx = null;

	/**
	 * 
	 */
	@Autowired
	public HealthDataController(HealthDataService healthDataService) {
		this.healthDataService = healthDataService;
		ctx = DataConfig.getContext();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
	}

	@RequestMapping(value = "/HealthGetData", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<HealthData> fetchProjectHealthData(@RequestParam("projectName") String projectName) {
		long lastUpdated = 0;
		List<HealthData> hlmServiceModel = healthDataService.fetchHealthData(projectName);
		if (!hlmServiceModel.isEmpty()) {
			HealthData lastRecord = hlmServiceModel.get(hlmServiceModel.size() - 1);
			return new DataResponse<HealthData>(lastRecord, lastUpdated);
		} else {
			LOG.info("no data found( fetchProjectHealthData() HealthDataController )for project " + projectName);
			return null;
		}

	}

	@RequestMapping(value = "/HealthGetDataAll", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<List<HealthData>> getHealthDataForUser() {
		long lastUpdated = 0;
		List<HealthData> hlmServiceModel = healthDataService.getHealthData();
		return new DataResponse<List<HealthData>>((List<HealthData>) hlmServiceModel, lastUpdated);
	}

	@RequestMapping(value = "/HealthPredictedBug", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<List<sprintPredictedBugMetrics>> getHealthDataForPB(
			@RequestParam("projectName") String projectName) {
		long lastUpdated = 0;
		List<sprintPredictedBugMetrics> predictedBugMetrics = healthDataService.getHealthLastRecord(projectName);
		return new DataResponse<List<sprintPredictedBugMetrics>>(predictedBugMetrics, lastUpdated);
	}

	@RequestMapping(value = "/HealthGetState", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Set<String>> fetchProjectHealthState(
			@RequestParam("projectName") String projectName/*
															 * ,@RequestParam(
															 * "almType") String
															 * almType
															 */) {
		long lastUpdated = 0;
		String almType = getAlmType(projectName);
		Set<String> hlmServiceModel = healthDataService.getState(projectName, almType);
		return new DataResponse<Set<String>>((Set<String>) hlmServiceModel, lastUpdated);
	}

	public String getAlmType(String projectName) {
		String almType = "";

		ConfigurationSetting configurationColection = configurationRepo.findByProjectName(projectName).get(0);
		Set<ConfigurationToolInfoMetric> metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
			if ("Jira".equals(metric1.getToolName()) || "Jira Defects".equals(metric1.getToolName())) {
				almType = "JIRA";
				break;
			} else {
				almType = "TFS";
			}

		}
		return almType;

	}
}