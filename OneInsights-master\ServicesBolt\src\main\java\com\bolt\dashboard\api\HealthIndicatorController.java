package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.HealthIndicator;
import com.bolt.dashboard.request.HealthIndicatorReq;
import com.bolt.dashboard.request.HealthIndicatorSettingReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.HealthIndicatorService;
import com.bolt.dashboard.service.HealthIndicatorServiceImplementation;

@RestController
public class HealthIndicatorController {

    @Autowired
    private HealthIndicatorService healthService;

    @RequestMapping(value = "/Health", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<Iterable<HealthIndicator>> healthData() {
    	return healthService.getHealth();
    }

    @RequestMapping(value = "/Health1", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    public ResponseEntity<HealthIndicator> createDashboard(
            @RequestBody List<HealthIndicatorReq> req/*
                                                      * ,HttpServletRequest
                                                      * request, HttpSession
                                                      * httpSession
                                                      */) {
        HealthIndicatorSettingReq healthIndi = new HealthIndicatorSettingReq();
        healthIndi.setMetric(req);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(new HealthIndicatorServiceImplementation().addHealth(healthIndi.toHealthSetting()));

    }
}
