/**
 * 
 */
package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.request.HightlightSettingReq;
import com.bolt.dashboard.core.model.HighLightModel;
import com.bolt.dashboard.core.model.HighLightReelModel;
import com.bolt.dashboard.request.HighlightReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.HighLightService;

/**
 * <AUTHOR>
 *
 */
@RestController
public class HighLightController {

    @Autowired
    private HighLightService highLightservice;

    /**
     * 
     */

    @RequestMapping(value = "/highLightSaveData", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    public ResponseEntity<Boolean> createDashboard(@RequestBody List<HighlightReq> req) {
        HightlightSettingReq highlightReq = new HightlightSettingReq();
        highlightReq.setMetrics(req);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(highLightservice.saveHighLightData(highlightReq.toHighlightAddSetting()));

    }

    @RequestMapping(value = "/highLightFetchData", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<Iterable<HighLightModel>> fetchHighLightData() {
        
        return highLightservice.fetchHighLightData();
    }
    
    @RequestMapping(value = "/highLightFetchDataProj", method = GET, produces = APPLICATION_JSON_VALUE)
    public HighLightModel fetchHighLightDataProj(@RequestParam("pName") String pName) {
    	HighLightModel highLightReel=highLightservice.fetchHighLightDataProj(pName);
        return highLightReel;
    }

    @RequestMapping(value = "/highLightReelFetchData", method = GET, produces = APPLICATION_JSON_VALUE)
    public HighLightReelModel fetchHighReelLightData(@RequestParam("proName") String[] proName) {
    	return highLightservice.fetchHighLightReelData(proName[0]);
    }

}
