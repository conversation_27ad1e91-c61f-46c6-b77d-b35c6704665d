/**
 * 
 */
package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.JobDetails;
import com.bolt.dashboard.request.JobDetailsMetricsReq;
import com.bolt.dashboard.request.JobDetailsReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.JobDetailsService;

/**
 * <AUTHOR>
 *
 */
@RestController
public class JobDetailsController {
	@Autowired
	private JobDetailsService jobDetailsService;

	@Autowired
	public JobDetailsController(JobDetailsService service) {
		this.jobDetailsService = service;
	}

/*	@RequestMapping(value = "/SaveJobDetails", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<JobDetails> saveJobDetails(@RequestBody JobDetails req) {

		LOG.info("added to metrics.....................");

		if (req.getMetrics().iterator().next() != null) {
			return ResponseEntity.status(HttpStatus.CREATED).body(jobDetailsService.saveJobDetails(req));
		}
		return null;

	}*/
	
	@RequestMapping(value = "/SaveJobDetails", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<JobDetails> saveJobDetails(@RequestBody JobDetailsReq req) {

		JobDetails jobDetails = req.toJobDetails();
		
		return ResponseEntity.status(HttpStatus.CREATED)
                .body(jobDetailsService.saveJobDetails(jobDetails));

	}


	@RequestMapping(value = "/fetchJobDetails", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<JobDetails> fetchJobList(@RequestParam("proName") String[] proName) {

		return jobDetailsService.fetchJobDetails(proName[0]);

	}

}
