package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.engagementScorecard.ScoreCardSprintData;
import com.bolt.dashboard.service.KanbanMetricsService;
import com.bolt.dashboard.util.DefectDensity;
import com.bolt.dashboard.util.DefectProductionSlippage;

@RestController
public class KanbanMetricsContoller {
	
	private KanbanMetricsService kanbanMetricsService;
	
	@Autowired
	public KanbanMetricsContoller(KanbanMetricsService kanbanMetricsService) {
		this.kanbanMetricsService = kanbanMetricsService;
	}
	
	@RequestMapping(value = "/kanbanCycleTimeMetrics", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<Map<String, String>> getCycleTimeMetrics(@RequestParam("pName") String pName){
		return kanbanMetricsService.getCycleTimeMetrics(pName);
	}
	
	@RequestMapping(value = "/kanbanCumulativeChart", method = GET, produces = APPLICATION_JSON_VALUE)
	public Map getkanbanCumulativeChart(@RequestParam("pName") String pName){
		//return kanbanMetricsService.getkanbanCumulativeChart1(pName);
		return kanbanMetricsService.getkanbanCumulativeChart1(pName);
	}
	
	@RequestMapping(value = "/kanbanThroughputMetrics", method = GET, produces = APPLICATION_JSON_VALUE)
	public Map getKanbanThroughputMetrics(@RequestParam("pName") String pName){
		return kanbanMetricsService.getKanbanThroughputMetrics(pName);
	}
	
	@RequestMapping(value = "/kanbanDefectTrend", method = GET, produces = APPLICATION_JSON_VALUE)
	public Map<String,List<Long>>  getKanbanDefectTrend(@RequestParam("pName") String pName,@RequestParam("pAlmType") String pAlmType){
		return kanbanMetricsService.getKanbanDefectTrend(pName,pAlmType);
	}
	@RequestMapping(value = "/kanbanProductionSlippage", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<DefectProductionSlippage>  kanbanProductionSlippage(@RequestParam("pName") String pName,@RequestParam("pAlmType") String pAlmType){
		return kanbanMetricsService.getKanbanProductionSlippage(pName,pAlmType);
	}
	@RequestMapping(value = "/kanbanDefectDenisty", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<DefectDensity>  kanbanDefectDenisty(@RequestParam("pName") String pName,@RequestParam("pAlmType") String pAlmType){
		return kanbanMetricsService.getKanbanDefectDenisty(pName,pAlmType);
	}
	
	@RequestMapping(value = "/kanbanWeeks", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<ScoreCardSprintData> getKanbanWeeks(@RequestParam("pName") String pName,@RequestParam("almType") String almType){
		return kanbanMetricsService.geKanbantWeeks(pName,almType);
	}

}
