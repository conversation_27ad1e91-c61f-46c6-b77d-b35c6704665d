package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.HashMap;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.service.LPMService;
@RestController
public class LPMController {
	
	private static final Logger LOG = LogManager.getLogger(MailerController.class);
	private LPMService lpmService;

	@Autowired
	public LPMController(LPMService lpmService) {
		this.lpmService = lpmService;
	}

	

	@RequestMapping(value = "/getPortfolioHorizonEpic", method = GET, produces = APPLICATION_JSON_VALUE)
	public HashMap<String, List<MetricsModel>> getPortfolioHorizonEpic(@RequestParam("projName") String projName){
		HashMap<String, List<MetricsModel>> repsonse=	lpmService.getPortfolioHorizonAndEpicInvestment(projName);
		
		return repsonse;
	}
	
	@RequestMapping(value = "/getStrategicThemeData", method = GET, produces = APPLICATION_JSON_VALUE)
	public  HashMap<String, List<MetricsModel>> getStrategicThemeData(@RequestParam("projName") String projName){
		 HashMap<String, List<MetricsModel>> repsonse=	lpmService.getStrategicThemeData(projName);
		
		return repsonse;
	}
	
	
	@RequestMapping(value = "/getPortfolioNames", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<String> getPortfoliosNames(@RequestParam("projName") String projName){
		 List<String> repsonse=	lpmService.getPortfolioNames(projName);
		
		return repsonse;
	}
	
	@RequestMapping(value = "/getPortfolioData", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<MetricsModel> getPortfolios(@RequestParam("projName") String projName){
		 List<MetricsModel> repsonse=	lpmService.getPortfolioData(projName);
		
		return repsonse;
	}
	

}
