package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ManageUser;
import com.bolt.dashboard.core.repository.UserRepo;
import com.bolt.dashboard.request.ManageUserReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.LoginServices;

@RestController
public class LoginController {
	private static final Logger LOG = LogManager.getLogger(LoginController.class);
	@Autowired
	private LoginServices loginServices;
	AnnotationConfigApplicationContext ctx = null;
	UserRepo repo = null;

	@Autowired
	public LoginController(LoginServices loginServices) {
		this.loginServices = loginServices;
		ctx = DataConfig.getContext();
		repo = ctx.getBean(UserRepo.class);
	}

	@RequestMapping(value = "/Login", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<ManageUser>> loginData(@RequestParam("sessionId") String sessionId,
			HttpSession httpSession) {

		if (httpSession.getAttribute("uname").equals(sessionId)) {
			return loginServices.getSignIn();
		}
		return null;
	}

	@RequestMapping(value = "/LoginUser", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<Object> createDashboard(@RequestBody ManageUserReq req, HttpServletRequest request,
			HttpSession httpSession) {
		Object loginStatus = loginServices.checkLogin(req, request);
		if (loginStatus.equals(false)) {
			Object status = loginServices.checkLoginForActiveDirectory(req, request, null);
			return ResponseEntity.status(HttpStatus.CREATED).body(status);
		} else {
			LOG.info("User is successfully Authenticated, User is navigated to portfolio page");
			return ResponseEntity.status(HttpStatus.CREATED).body(loginStatus);
		}
	}

	
	@RequestMapping(value = "/CheckEmail", method = GET, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<Object> emailChecker(@RequestParam("email") String email, HttpServletRequest request) {
		Object loginStatus = loginServices.checkEmail(email, request);
		if (loginStatus.equals(false)) {
			return ResponseEntity.status(HttpStatus.NOT_FOUND).body(loginStatus);
		} else {
			LOG.info("User with email: "+email+" is present");
			return ResponseEntity.status(HttpStatus.CREATED).body(loginStatus);
		}
	}
	
	@RequestMapping(value = "/CheckSSO", method = GET, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<Object> emailSSOChecker(@RequestParam("email") String email,@RequestParam("token") String token, HttpServletRequest request,HttpServletResponse response) throws IOException {
		Object loginStatus = loginServices.checkUserSSO(email, request, response);
		if (loginStatus.equals(false)) {
			return ResponseEntity.status(HttpStatus.NOT_FOUND).body(loginStatus);
		} else {
			LOG.info("User is successfully Authenticated, User is navigated to portfolio page");
			return ResponseEntity.status(HttpStatus.CREATED).body(loginStatus);
		}
	}

	@RequestMapping(value = "/logout", method = GET)
	public String createDashboard5(HttpServletRequest request, HttpServletResponse response) {
		LOG.info("--------->inside LOGINOUT controller-------> ");
		Authentication auth = SecurityContextHolder.getContext().getAuthentication();  
        if (auth != null){      
           new SecurityContextLogoutHandler().logout(request, response, auth);  
        }  
        return "redirect:/";  
	}

	@RequestMapping(value = "/tokenRefresh", method = GET)
    public void getRefreshToken(HttpServletRequest request, HttpServletResponse response) throws IOException {
		loginServices.refreshToken(request, response);
    }
	/*
	 * public boolean getStatus(String userName, String password) { boolean
	 * overalStatus = false; boolean status = false;
	 * 
	 * boolean getStatusResult = new
	 * LoginServicesImplementation(repo).getActiveDirectoryStatus(userName,
	 * password); if (Boolean.FALSE.equals(getStatusResult)) { return false; }
	 * else { status = new LoginController(loginServices).iterateReasult(repo,
	 * userName); if (getStatusResult == status) { overalStatus = true; } return
	 * overalStatus; }
	 * 
	 * }
	 * 
	 * public boolean iterateReasult(UserRepo repo, String userName) { boolean
	 * status = false; String userEmailId = null;
	 * 
	 * Iterable<ManageUser> managerUserList = repo.findAll();
	 * 
	 * @SuppressWarnings("rawtypes") Iterator it = managerUserList.iterator();
	 * while (it.hasNext()) { Object obj = it.next(); ManageUser manageUser =
	 * (ManageUser) obj;
	 * 
	 * Set<ManageUserMetric> metrics = manageUser.getMetric();
	 * 
	 * @SuppressWarnings("rawtypes") Iterator iterator = metrics.iterator();
	 * while (iterator.hasNext()) { ManageUserMetric manageUserMetrics =
	 * (ManageUserMetric) iterator.next(); String[] emailArray = new String[5];
	 * if (!(manageUserMetrics.getEmail() == null)) { userEmailId =
	 * manageUserMetrics.getEmail(); }
	 * 
	 * if (userEmailId.contains("@")) { emailArray =
	 * userEmailId.split(Pattern.quote("@")); userEmailId = emailArray[0]; if
	 * (userEmailId.equals(userName)) { status = true; } } }
	 * 
	 * } return status; }
	 */
}
