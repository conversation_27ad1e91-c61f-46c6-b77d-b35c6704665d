package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.MailSetup;
import com.bolt.dashboard.request.MailSetupReq;
import com.bolt.dashboard.request.MailSetupSettingReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.MailSetupService;

@RestController
public class MailSetupController {

	@Autowired
	private MailSetupService mailSetupService;
	private static final Logger LOG = LogManager.getLogger(MailSetupController.class);

	@RequestMapping(value = "/retrieveMailConfiguration", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<MailSetup>> setupData() {
		return mailSetupService.getSetup();
	}

	@RequestMapping(value = "/saveMailConfiguration", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<MailSetup> createDashboard(@RequestBody List<MailSetupReq> req) {
		MailSetupSettingReq mailSettingReq = new MailSetupSettingReq();
		if (!req.isEmpty()) {
			mailSettingReq.setMetric(req);
			return ResponseEntity.status(HttpStatus.CREATED)
					.body(mailSetupService.save(mailSettingReq.toMailSetupSetting()));
		} else {
			LOG.info("No data getting from UI to save createDashboard() MailSetupController() ");
			return null;
		}

	}
}
