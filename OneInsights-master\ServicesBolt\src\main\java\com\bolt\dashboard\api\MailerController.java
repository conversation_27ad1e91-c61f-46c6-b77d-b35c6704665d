package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.request.MailerRequest;
import com.bolt.dashboard.request.MailerSettingReq;
import com.bolt.dashboard.service.MailerService;

@RestController
public class MailerController {
	private static final Logger LOG = LogManager.getLogger(MailerController.class);
	private MailerService mailService;

	@Autowired
	public MailerController(MailerService mailService) {
		this.mailService = mailService;

	}

	@RequestMapping(value = "/sentMail", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<Boolean> sendTextMail(@RequestBody List<MailerRequest> req) {
		LOG.info("Inside Mail controller....");

		MailerSettingReq request = new MailerSettingReq();
		if (!req.isEmpty()) {
			request.setMetric(req);
			return ResponseEntity.status(HttpStatus.CREATED).body(mailService.sentMail(request.toSendMailSetting()));
		} else {
			LOG.info("not getting appropriate data from UI sendTextMail()  MailerController() ....");
			return null;
		}
	}
}
