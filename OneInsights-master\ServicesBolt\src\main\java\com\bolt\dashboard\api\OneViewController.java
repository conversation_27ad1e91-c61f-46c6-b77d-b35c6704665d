package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.OneView;
import com.bolt.dashboard.core.repository.OneViewRepositary;
import com.bolt.dashboard.request.OneViewChartOptionsReq;
import com.bolt.dashboard.request.OneViewReq;
import com.bolt.dashboard.service.OneViewService;

@RestController
public class OneViewController {

	@Autowired
	OneViewService oneViewService;

	@Autowired
	OneViewRepositary oneViewRepositary;

	@RequestMapping(value = "/saveOneView", method = POST, produces = APPLICATION_JSON_VALUE, consumes = APPLICATION_JSON_VALUE)
	public OneView save(@RequestBody List<OneViewChartOptionsReq> oneViewChartOptionsReq) {
		
		OneViewReq oneViewReq = new OneViewReq();
		oneViewReq.setMetrics(oneViewChartOptionsReq);
		//return ResponseEntity.status(HttpStatus.CREATED).body(oneViewService.save(oneViewReq.toOneView()));
		return oneViewService.save(oneViewReq.toOneView());
	}

	@RequestMapping(value = "/getOneView", method = GET, produces = APPLICATION_JSON_VALUE)
	public OneView findByProjectNameAndUser(@RequestParam("projectName") String projectName,
			@RequestParam("user") String user) {
		return oneViewRepositary.findByProjectNameAndUser(projectName, user);
	}

}
