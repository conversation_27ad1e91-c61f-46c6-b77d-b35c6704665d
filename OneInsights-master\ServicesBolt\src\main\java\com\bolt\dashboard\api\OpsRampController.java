package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.OpsResources;
import com.bolt.dashboard.service.OpsRampService;

@RestController
public class OpsRampController {
	private OpsRampService opsRampService;

	@Autowired
	public OpsRampController(OpsRampService service) {
		this.opsRampService = service;

	}

	@RequestMapping(value = "/getServerResources", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<OpsResources> getServerResources() {
		return opsRampService.getServerResources();
	}
	@RequestMapping(value = "/getAllResources", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<OpsResources> getAllResources() {
		return opsRampService.getAllResources();
	}
//	@RequestMapping(value = "/getOpenClosedInsights", method = GET, produces = APPLICATION_JSON_VALUE)
//	public List<IncidentOpenClosed> getOpenClosedInsights() {
//		return opsRampService.getOpenClosedInsights();
//	}

}
