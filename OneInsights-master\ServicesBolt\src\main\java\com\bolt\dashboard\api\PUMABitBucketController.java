package com.bolt.dashboard.api;

/**
 * 
 */
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.PUMABitBucketService;

@RestController
public class PUMABitBucketController {
    private PUMABitBucketService scmToolService;

    @Autowired
    public PUMABitBucketController(PUMABitBucketService scmToolService) {
        this.scmToolService = scmToolService;
    }

    @RequestMapping(value = "/pumaBit", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<Iterable<SCMTool>> bitbucketData(@RequestParam("proName") String[] proName,
            @RequestParam("sDate") long sDate, @RequestParam("eDate") long eDate, @RequestParam("flag") boolean flag) {
        return scmToolService.search("BITBUCKET", proName[0], sDate, eDate, flag);

    }

}
