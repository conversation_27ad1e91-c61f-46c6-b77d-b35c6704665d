package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.PortfolioConfig;
import com.bolt.dashboard.request.PortfolioConfigReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.PortfolioConfigService;
import com.bolt.dashboard.service.PortfolioConfigServiceImplementation;

@RestController
public class PortfolioConfigController {

	@Autowired
	PortfolioConfigService portfolioConfigService;

	@Autowired
	PortfolioConfigController(PortfolioConfigService service) {
		this.portfolioConfigService = service;
	}

	@RequestMapping(value = "/portfolioConfigSaveData", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<PortfolioConfig> createDashboard(
			@RequestBody PortfolioConfigReq req) {

		return ResponseEntity.status(HttpStatus.CREATED)
				.body(portfolioConfigService.savePortfolioConfigData(req.toPortfolioConfigAddSetting(req)));

	}

	@RequestMapping(value = "/portfolioConfigFetchData", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<PortfolioConfig>> fetchPortfolioConfigData() {
		return portfolioConfigService.fetchPortfolioConfigData();

	}
	
	@RequestMapping(value = "/getTowers", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<String> fetchTowers() {
		return portfolioConfigService.fetchPortfolioTowers();

	}
	
	
	@RequestMapping(value = "/getTowerProj", method = GET, produces = APPLICATION_JSON_VALUE)
	public String fetchTowerName(@RequestParam("pName") String pName) {
		return portfolioConfigService.fetchTowerByProj(pName);

	}

	@RequestMapping(value = "/portfolioDetails", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<PortfolioConfig>> getPorfolioDetails(@RequestParam("pName") String pName) {

		return portfolioConfigService.getPortfolioDetails(pName);

	}
	@RequestMapping(value = "/saveEngScorePort", method = GET, produces = APPLICATION_JSON_VALUE)
	public String saveEngScore(@RequestParam("pName")String projectName,@RequestParam("month")String month,@RequestParam("engScore")double engScore){
	
		return portfolioConfigService.saveEngScore(projectName,month,engScore);
		
	}
}