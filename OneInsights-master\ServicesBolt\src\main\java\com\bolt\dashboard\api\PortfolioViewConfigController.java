package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.PortfolioViewConfig;
import com.bolt.dashboard.request.PortfolioViewConfigReq;
import com.bolt.dashboard.service.PortfolioViewConfigService;

@RestController
public class PortfolioViewConfigController {
	private static final Logger LOG = LogManager.getLogger(ALMConfigController.class);
	
	@Autowired
	PortfolioViewConfigService portfolioViewConfigService;

	@RequestMapping(value = "/portfolioViewConfig", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<PortfolioViewConfig> savePortfolioViewConfig(@RequestBody PortfolioViewConfigReq req) {

		LOG.info("Inside portfolioViewConfig and org  :  " + req.getProjectName());
		return ResponseEntity.status(200).body(portfolioViewConfigService.savePortfolioViewConfig(req));
	}

	@RequestMapping(value = "/portfolioViewConfig", method = GET, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<PortfolioViewConfig> getPortfolioViewConfig(String projectName) {
		return ResponseEntity.status(200).body(portfolioViewConfigService.getPortfolioViewConfig(projectName));

	}
}
