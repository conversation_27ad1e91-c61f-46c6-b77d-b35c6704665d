/**
 * 
 */
package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.ProductLicenceConfig;
import com.bolt.dashboard.request.ProductLicenceRequest;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.ProductLicenceService;

@RestController
public class ProductLicenceController {
    @Autowired
    private ProductLicenceService productLicenceService;

    @Autowired
    public ProductLicenceController(ProductLicenceService productLicenceService) {
        this.productLicenceService = productLicenceService;
    }

    @RequestMapping(value = "/getCount", method = GET, produces = APPLICATION_JSON_VALUE)
    public int getUserCount(@RequestParam String licenceKey) {

        return productLicenceService.fetchUserCount(licenceKey);

    }

    @RequestMapping(value = "/licData", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<List<ProductLicenceConfig>> getData() {
        return productLicenceService.getData();

    }

    @RequestMapping(value = "/updateLicData", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> updateData(@RequestBody ProductLicenceRequest key) {
        return ResponseEntity.status(HttpStatus.CREATED).body(productLicenceService.updateData(key.getLicenceKey()));

    }

    @RequestMapping(value = "/sendMail", method = GET, produces = APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> sendMail() {
        return ResponseEntity.status(HttpStatus.CREATED).body(productLicenceService.sendMail());

    }

}
