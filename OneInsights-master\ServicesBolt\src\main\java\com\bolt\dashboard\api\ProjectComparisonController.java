package com.bolt.dashboard.api;

/**
 * 
 */
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.ProjectComparison;
import com.bolt.dashboard.request.ProjectComparisonReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.ProjectComparisonService;

@RestController
public class ProjectComparisonController {
    private final ProjectComparisonService projectComparisonService;

    @Autowired
    public ProjectComparisonController(ProjectComparisonService service) {
        this.projectComparisonService = service;
    }

    @RequestMapping(value = "/projectComparisonLastRecord", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<ProjectComparison> getProjectComparison() {
        ProjectComparisonReq req = new ProjectComparisonReq();
        return projectComparisonService.search(req);

    }

}
