package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.ProjectCoverageDetails;
import com.bolt.dashboard.core.model.RepoCodeCoverageStatus;
import com.bolt.dashboard.service.ProjectCoverageService;
import com.bolt.dashboard.util.ProjectHomeCalculation;

@RestController
public class ProjectCoverageController {

	@Autowired
	ProjectCoverageService projectCoverageService;
	
	@RequestMapping(value = "/projectCoverage", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<ProjectCoverageDetails> projectCoverage(@RequestParam("pName") String pName) {
		return projectCoverageService.getProjectCoverage(pName);
	}
	
	@RequestMapping(value = "/repoCoverageStatus", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<RepoCodeCoverageStatus> repoCoverageStatus(@RequestParam("pName") String pName) {
		return projectCoverageService.getRepoCoverageStatus(pName);
	}
	
	@RequestMapping(value ="/coverageSummary",method = GET, produces = APPLICATION_JSON_VALUE)
//	@Cacheable(value="ProjectCoveragecoverageSummary", key ="'ProjectCoveragecoverageSummary'+#pName+#almType", cacheManager="timeoutCacheManager")
	public List<Map<String, String>> coverageSummary(@RequestParam("pName") String pName,@RequestParam("almType") String almType) {
		ProjectHomeCalculation projHomeCalc = new ProjectHomeCalculation();
		return projHomeCalc.coverageSummary(pName,almType);
	}
	
	
}
