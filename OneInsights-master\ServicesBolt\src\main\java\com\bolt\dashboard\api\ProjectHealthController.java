package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.ProjectHealth;
import com.bolt.dashboard.request.ProjectHealthReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.ProjectHealthService;

@RestController
public class ProjectHealthController {

	private ProjectHealthService projectHealthService;

	/**
	 * 
	 */
	@Autowired
	public ProjectHealthController(ProjectHealthService projectHealthService) {
		this.projectHealthService = projectHealthService;
	}

	@RequestMapping(value = "/projectHealthSaveData", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<Boolean> saveProjectHealthData(@RequestBody ProjectHealthReq health) {

		/*ProjectHealthSettingReq projectHealthReq = new ProjectHealthSettingReq();
		projectHealthReq.setMetrics(health);*/
		ProjectHealth healthObj=health.toProjectHealth(health);
		return ResponseEntity.status(HttpStatus.CREATED)
				.body(projectHealthService.saveProjectHealthData(healthObj));
	}

	@RequestMapping(value = "/projectHealthGetData", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<ProjectHealth>> fetchProjectHealthData(@RequestParam("proName") String proName) {

		return projectHealthService.fetchprojectHealthData(proName);
	}
	
	@RequestMapping(value = "/projectHealthGetDataConfig", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<ProjectHealth>> fetchProjectHealthDataCopy(@RequestParam("proName") String proName) {

		return projectHealthService.fetchprojectHealthData(proName);
	}

}
