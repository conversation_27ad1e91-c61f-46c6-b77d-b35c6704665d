/**
 * 
 */
package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.Retrospective;
import com.bolt.dashboard.request.RetrospectiveReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.RetrospectiveService;

@RestController
public class RetrospectiveController {
    private static final Logger LOG = LogManager.getLogger(RetrospectiveController.class);
    @Autowired
    private RetrospectiveService retrospectiveService;

    @Autowired
    public RetrospectiveController(RetrospectiveService retrospectiveService) {
        this.retrospectiveService = retrospectiveService;
    }

    @RequestMapping(value = "/retrospectiveData", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    public ResponseEntity<Retrospective> saveRetrospectiveDetails(@RequestBody List<RetrospectiveReq> req) {

        for (int j = 0; j < req.size(); j++) {

            RetrospectiveReq retrospectiveReq = req.get(j);

            LOG.info("Inside RetrospectiveController and org  :  " + retrospectiveReq.getUserName());
            retrospectiveService.saveRetrospectiveDetails(retrospectiveReq.toDetailsAddSetting(retrospectiveReq));
        }

        return null;

    }

    @RequestMapping(value = "/retrospectiveDelete", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    public ResponseEntity<Retrospective> deleteRetrospective(@RequestBody RetrospectiveReq req) {

      
            retrospectiveService.deleteRetrospective(req.toDetailsAddSetting(req));
       

        return null;

    }

    @RequestMapping(value = "/retrospectiveDetails", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<List<Retrospective>> retrieveList() {

        return retrospectiveService.retrieveRetrospectiveDetails();

    }
    
    @RequestMapping(value = "/retrospectiveDetailsByProject", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<List<Retrospective>> retrieveListByProject(@RequestParam String projectName) {

        return retrospectiveService.retrieveRetrospectiveDetailsByProject(projectName);

    }

}
