package com.bolt.dashboard.api;

/**
 * 
 */
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.SCMMR;
import com.bolt.dashboard.core.model.SCMToolBO;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.SCMToolService;
import com.bolt.dashboard.service.SCMToolServiceImplementation;

@RestController
public class SCMToolController {
	private static final Logger LOGGER=LogManager.getLogger(SCMToolController.class);
	
	private SCMToolService scmToolService;
	// ConfigurationSettingRep configurationRepo = null;
	// AnnotationConfigApplicationContext ctx = null;

	@Autowired
	public SCMToolController(SCMToolService scmToolService) {
		this.scmToolService = scmToolService;
		// ctx = DataConfig.getContext();
		// configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
	}
/*public static void main(String[] args) {
	new SCMToolController().bitbucketCommitData("Brillio BOLT");
}*/
	
	
	public SCMToolController() {
	}

	@RequestMapping(value = "/bdepVC", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<JSONObject> getJSONData(@RequestParam("url") String url,
			@RequestParam("userName") String userName, @RequestParam("password") String password,
			@RequestParam("projectName") String projectName) {
		String returnUrl = url + "/tfs/DefaultCollection/" + projectName + "/_apis/tfvc/changesets?api-version=1.0";
		return new SCMToolServiceImplementation().searchJson(returnUrl, userName, password);
	}

	@RequestMapping(value = "/scmDetails", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<SCMToolBO>> bitbucketCommitData(@RequestParam("pName") String pName) {
		return scmToolService.getCommitDetails(getSCMType(pName), pName);
	}
	@RequestMapping(value = "/scmMergeRequestData", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<SCMMR>> scmMergeRequestData(@RequestParam("pName") String pName) {
		return scmToolService.getMergeRequestDetails(getSCMType(pName), pName);
	}

	public String getSCMType(String pName) {
		String scmType = "";
		Query query = new Query();		
		query.addCriteria(new Criteria("projectName").is(pName).and("metric.toolType").is("SCM"));
		query.fields().include("metric.$1");
		//List<ConfigurationSetting> list=null;
		List<ConfigurationSetting> list=new ArrayList<>();
		try {
			list = DataConfig.getInstance().mongoTemplate().find(query,
					ConfigurationSetting.class);
		} catch (Exception e) {
			LOGGER.error(e.getCause());
		}
		if(!list.isEmpty()){
			Set<ConfigurationToolInfoMetric> metrics= list.get(0).getMetrics();
			if(!metrics.isEmpty()){
				scmType =metrics.iterator().next().getToolName();
				if("BIT Server".equals(scmType)){
					scmType="BITBUCKET";
				}
				
			}
		}
		return scmType;
		
		/*
		AnnotationConfigApplicationContext context = new AnnotationConfigApplicationContext(DataConfig.class);
		ConfigurationSettingRep configurationRepo = context.getBean(ConfigurationSettingRep.class);
		ConfigurationSetting configurationColection = configurationRepo.findByProjectName(projectName);
		Set<ConfigurationToolInfoMetric> metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();

		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
			if ("SCM".equals(metric1.getToolType())) {
				scmType = metric1.getToolName();
				if ("BIT Server".equals(metric1.getToolName())) {
					scmType = "BITBUCKET";
				}
				break;
			}
		}
		context.close();
		return scmType;*/

	}

}