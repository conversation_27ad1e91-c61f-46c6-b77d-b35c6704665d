
package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.SLAConfiguration;
import com.bolt.dashboard.request.SLAConfigReq;
import com.bolt.dashboard.service.SLAConfigService;

@RestController
public class SLAConfigController {
	private static final Logger LOG = LogManager.getLogger(SLAConfigController.class);
	@Autowired
	private SLAConfigService slaConfigService;

	@Autowired
	public SLAConfigController(SLAConfigService slaConfigService) {
		this.slaConfigService = slaConfigService;
	}

	@RequestMapping(value = "/slaconfig", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<SLAConfiguration> saveConfig(@RequestBody SLAConfigReq req) {
		SLAConfigReq slaConfigReq = req;
		LOG.info("Inside SLAConfigController and org  :  " + slaConfigReq.getProjectName());
		slaConfigService.saveConfig(slaConfigReq.addSetting(slaConfigReq));
		return null;

	}

	@RequestMapping(value = "/slaconfigdetails", method = GET, produces = APPLICATION_JSON_VALUE)
	public Iterable<SLAConfiguration> getSLA() {
		return slaConfigService.getSLA();
	}

}
