
package com.bolt.dashboard.api;

/**
 * 
 */
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.SRCommits;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.SRCommitsService;

/**
 * 
 * <AUTHOR>
 *
 */
@RestController
public class SRCommitsController {
    private SRCommitsService srCommitsService;

    List<?> days = new ArrayList<Object>();

    @Autowired
    public SRCommitsController(SRCommitsService srCommitsService) {
        this.srCommitsService = srCommitsService;
    }

    @RequestMapping(value = "/SRCommitt", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<Iterable<SRCommits>> srData() {
    	return srCommitsService.searchSR();

    }
}
