package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.IncidentCategory;
import com.bolt.dashboard.core.model.IncidentOpenClosed;
import com.bolt.dashboard.core.model.InflowTrend;
import com.bolt.dashboard.core.model.ServiceNowInc;
import com.bolt.dashboard.core.model.TeamMember;
import com.bolt.dashboard.request.TeamMemberReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.ServiceNowService;
import com.bolt.dashboard.service.TeamMemberService;

@RestController
public class ServiceNowController {
	private ServiceNowService serviceNowService;

	@Autowired
	public ServiceNowController(ServiceNowService service) {
		this.serviceNowService = service;

	}

	@RequestMapping(value = "/getCategoryInsights", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<IncidentCategory> getCategoryInsights() {
		return serviceNowService.getCategoryInsights();
	}
	@RequestMapping(value = "/getOpenClosedInsights", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<IncidentOpenClosed> getOpenClosedInsights() {
		return serviceNowService.getOpenClosedInsights();
	}
	
	@RequestMapping(value = "/getSLA", method = GET, produces = APPLICATION_JSON_VALUE)
	public Map<String, Map<String, Integer>> getSLA() {
		return serviceNowService.getResoultionSLA();
	}
	
	@RequestMapping(value = "/getInflowTrend", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<InflowTrend> getInflowTrend() {
		return serviceNowService.getInflowTrend();
	}
	@RequestMapping(value = "/getAllIncidents", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<ServiceNowInc> getAllIncidents() {
		return serviceNowService.getAllIncidents();
	}

}
