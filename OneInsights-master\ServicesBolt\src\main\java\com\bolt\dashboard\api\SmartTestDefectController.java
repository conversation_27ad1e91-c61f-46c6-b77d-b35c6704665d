package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.SmartTestDefectTool;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.DefectToolService;

@RestController
public class SmartTestDefectController {
    private DefectToolService service;

    @Autowired
    public SmartTestDefectController(DefectToolService service) {
        this.service = service;
    }

    @RequestMapping(value = "/defectManagement", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<SmartTestDefectTool> getTestManagement(@RequestParam("proName") String[] proName) {
        return service.search(proName[0], "Smart Test Defect");

    }

}
