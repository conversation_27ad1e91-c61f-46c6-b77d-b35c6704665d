package com.bolt.dashboard.api;

/**
 * 
 */
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.SprintComparison;
import com.bolt.dashboard.request.SprintComparisonReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.SprintComparisonService;

@RestController
public class SprintComparisonController {
    private final SprintComparisonService sprintComparisonService;

    @Autowired
    public SprintComparisonController(SprintComparisonService service) {
        this.sprintComparisonService = service;
    }

    @RequestMapping(value = "/sprintComparisonLastRecord", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<SprintComparison> getSprintComparison(@RequestParam("proName") String[] proName) {
        SprintComparisonReq req = new SprintComparisonReq();
        return sprintComparisonService.search(req, proName[0]);

    }

}
