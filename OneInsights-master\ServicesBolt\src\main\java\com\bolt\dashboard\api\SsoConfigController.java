package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.SsoSetup;
import com.bolt.dashboard.request.SsoSetupReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.SsoConfigService;

@RestController
public class SsoConfigController {
	@Autowired
	private SsoConfigService service;
	private static final Logger LOG = LogManager.getLogger(SsoConfigController.class);
	
	@RequestMapping(value = "/getSSOConfig", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<SsoSetup> getSsoConfig(@RequestParam String type) {
		return service.getSsoConfig(type);
	}
	
	@RequestMapping(value = "/getSSOConfigs", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<SsoSetup>> getSsoConfigs() {
		return service.getSsoConfigs();
	}
	
	@RequestMapping(value = "/saveSSOConfiguration", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<DataResponse<Iterable<SsoSetup>>> createSSOConfig(@RequestBody List<SsoSetupReq> req) {
		if (req != null) {
			List<SsoSetup> data = new ArrayList<SsoSetup>();
			for(SsoSetupReq setup:req) {
				data.add(setup.toSssoSetup());
			}
			DataResponse<Iterable<SsoSetup>> tempSSO = service.save(data);
			return ResponseEntity.status(HttpStatus.CREATED)
					.body(tempSSO);
		} else {
			LOG.info("No data getting from UI to save createSSOConfig() SsoConfigController() ");
			return null;
		}

	}
}
