package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.StackGrade;
import com.bolt.dashboard.core.model.StackGradeMetrics;
import com.bolt.dashboard.core.model.StackRanking;
import com.bolt.dashboard.request.StackGradingReq;
import com.bolt.dashboard.request.StackRankingReq;
import com.bolt.dashboard.request.StackRankingSettingReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.StackRankingService;

@RestController
public class StackRankingController {
    @Autowired
    private StackRankingService stackService;

    @Autowired
    public StackRankingController(StackRankingService stackservice) {
        this.stackService = stackservice;
    }

    
    @RequestMapping(value = "/stackByProject", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<StackRanking> stackDataByProject(@RequestParam("projectName") String projectName) {

    	return stackService.getStackByProject(projectName);
    }

    @RequestMapping(value = "/Stack", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    public ResponseEntity<StackRanking> createDashboard(@RequestBody List<StackRankingReq> req) {
        StackRankingSettingReq stackRanking = new StackRankingSettingReq();
        stackRanking.setMetric(req);
        return ResponseEntity.status(HttpStatus.CREATED).body(stackService.addStack(stackRanking.toStackSetting()));

    }

    @RequestMapping(value = "/StackGradingSystem", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
    public DataResponse<Iterable<StackGrade>> saveStackGradingData(@RequestBody List<StackGradingReq> req) {

    	return stackService.getStackGradingData(req);
    }

    @RequestMapping(value = "/gettingStackGradingData", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<List<StackGradeMetrics>> fetchStackGradingData(@RequestParam("pName") String pName, @RequestParam("role") String role, @RequestParam("ruleName") String ruleName) {

    	return stackService.fetchStackGradingData(pName,role,ruleName);
    }

    @RequestMapping(value = "/GettingAllStackGradingData", method = POST, produces = APPLICATION_JSON_VALUE)
    public DataResponse<List<StackGrade>> fetchAllStackGradingData(@RequestBody StackGradingReq req) {

    	return stackService.fetchAllStackGradingData(req);
    }

    @RequestMapping(value = "/DeleteStackGradingData", method = POST, produces = APPLICATION_JSON_VALUE)
    public boolean deleteStackGradingData(@RequestBody StackGradingReq req) {
    	stackService.deleteStackGradingData(req);
        return true;
    }

}
