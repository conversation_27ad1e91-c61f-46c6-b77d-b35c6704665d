package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.map.HashedMap;
import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.exception.StatusReportException;
import com.bolt.dashboard.request.StatusReportReq;
import com.bolt.dashboard.request.StatusReportSetUpReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.StatusReportService;
import com.bolt.dashboard.sonar.SonarApplication;
import com.bolt.dashboard.util.ImagesUtils;

@RestController
public class StatusReportController {
    private StatusReportService service;
    private static final Logger LOG = LogManager.getLogger(SonarApplication.class);

    @Autowired
    ImagesUtils imageUtils;

    @Autowired
    public StatusReportController(StatusReportService mailService) {
	this.service = mailService;

    }

    @RequestMapping(value = "/statusReportSendMail", method = POST, produces = APPLICATION_JSON_VALUE)
    public DataResponse<String> getStatusReport(@RequestBody List<StatusReportReq> req) throws StatusReportException {
	LOG.info("In service");
	try {
	    StatusReportSetUpReq statusreq = new StatusReportSetUpReq();
	    statusreq.setMetric(req);
	    LOG.info("before return...............");
	    return service.sendMail((statusreq.toMailSetupSetting()).get(0));
	} catch (Exception e) {
	    throw new StatusReportException(e);
	}

    }

    @RequestMapping(value = "/uploadImage", method = POST, produces = APPLICATION_JSON_VALUE, consumes = APPLICATION_JSON_VALUE)
    public Map<String, String> generateImage(@RequestBody Map<String, String> imageData, HttpServletRequest request)
	    throws StatusReportException {
	LOG.info("Upload Image controller part");
	try {
	    @SuppressWarnings("unchecked")
		Map<String, String> map = new HashedMap();
	    imageUtils.base64ToImage(imageData.get("base64Data").toString(), imageData.get("imageName").toString());
	    return map;
	} catch (Exception e) {
	    throw new StatusReportException(e);
	}
    }

}
