package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;

import static org.springframework.web.bind.annotation.RequestMethod.GET;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.TeamIndexConfiguration;
import com.bolt.dashboard.request.TeamIndexConfigurationReq;
import com.bolt.dashboard.service.TeamIndexConfigService;

@RestController
public class TeamIndexConfigController {
	@Autowired
	 TeamIndexConfigService teamIndexConfService;
	
	  @RequestMapping(value = "/teamIndexConfig", method = POST, produces = APPLICATION_JSON_VALUE)
	  public ResponseEntity<Boolean> saveTeamIndexConfig(@RequestBody TeamIndexConfigurationReq teamIndexConfigReq){
		  return ResponseEntity.status(HttpStatus.CREATED).body(this.teamIndexConfService.saveTeamIndexConfig(teamIndexConfigReq.toTeamIndexConfig(teamIndexConfigReq)));
		  
	  }
	  @RequestMapping(value = "/teamIndexConfigLastRecord", method = GET, produces = APPLICATION_JSON_VALUE)
	  public ResponseEntity<TeamIndexConfiguration> getTeamIndexConfigLastRecord(@RequestParam("pName") String pName){
		  return ResponseEntity.status(HttpStatus.OK).body(this.teamIndexConfService.getTeamIndexLastRecord(pName));
		  
	  }
	  @RequestMapping(value = "/teamIndexConfig", method = GET, produces = APPLICATION_JSON_VALUE)
	  public ResponseEntity<List<TeamIndexConfiguration>> getTeamIndexConfigData(@RequestParam("pName") String pName){
		  return ResponseEntity.status(HttpStatus.OK).body(this.teamIndexConfService.getTeamIndexConfigData(pName));
		  
	  }

}
