package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.Iterator;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.TeamMember;
import com.bolt.dashboard.request.TeamMemberReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.TeamMemberService;

@RestController
public class TeamMemberController {
	TeamMemberReq teamMemberReq;
	private TeamMemberService teamMemberService;

	@Autowired
	public TeamMemberController(TeamMemberService service) {
		this.teamMemberService = service;

	}

	@RequestMapping(value = "/getMemberDetails", method = GET, produces = APPLICATION_JSON_VALUE)
	public TeamMember getMemberDetails(@RequestParam("email") String email, @RequestParam("pName") String pName) {
		return teamMemberService.getMemberDetails(email, pName);
	}

	@RequestMapping(value = "/projectMembers", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<TeamMember>> memberDetails(@RequestBody TeamMemberReq key) {
		return teamMemberService.getProjectMember(key);
	}
	@RequestMapping(value = "/getTeamByProject", method = GET,produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<TeamMember>> memberDetails(@RequestParam("pName") String pName) {
		return teamMemberService.getTeamByProject(pName);
	}

	@RequestMapping(value = "/deleteTeamMember", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<Boolean> deleteTeamMember(@RequestBody List<TeamMemberReq> req) {
		Iterator<TeamMemberReq> reqIterator = req.iterator();
		boolean flag = false;
		while (reqIterator.hasNext()) {
			TeamMemberReq tmpteamMemberReq = (TeamMemberReq) reqIterator.next();
			flag = teamMemberService.DeleteTeamMemer(tmpteamMemberReq);
		}
		return ResponseEntity.status(HttpStatus.CREATED).body(flag);

	}

	@RequestMapping(value = "/addNewMember", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<String> addNewMember(@RequestBody TeamMemberReq key) {
		return ResponseEntity.status(HttpStatus.CREATED).body(teamMemberService.addNewMember(key));
	}

	@RequestMapping(value = "/updateMemberDetails", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<String> updateMember(@RequestBody TeamMemberReq key) {
		return ResponseEntity.status(HttpStatus.CREATED).body(teamMemberService.updateMemberDetails(key));
	}
}
