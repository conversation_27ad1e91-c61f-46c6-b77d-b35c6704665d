
package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.TeamQuality;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.TeamQualityService;

@RestController
public class TeamQualityController {
	private TeamQualityService qualityService;

	@Autowired
	public TeamQualityController(TeamQualityService qualityService ) {
		this.qualityService = qualityService;
	}

	@RequestMapping(value = "/getTeamQualityOfDelivery", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<List<TeamQuality>> getMetrics(@RequestParam("pName") String projName) {
		return qualityService.getTeamQualityOfDelivery(projName);
	}

}
