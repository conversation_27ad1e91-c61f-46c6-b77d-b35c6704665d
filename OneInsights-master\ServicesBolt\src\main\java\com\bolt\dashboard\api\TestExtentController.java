package com.bolt.dashboard.api;

/**
 * 
 */
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.TestReportSummary;
import com.bolt.dashboard.request.TestExtentReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.TestExtentSevice;

@RestController
public class TestExtentController {
    private final TestExtentSevice testExtentSevice;

    @Autowired
    public TestExtentController(TestExtentSevice testExtentSevice) {
        this.testExtentSevice = testExtentSevice;
    }

    @RequestMapping(value = "/testExtent", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<TestReportSummary> testExtentData(@RequestParam("proName") String proName) {
        TestExtentReq request = new TestExtentReq();
        return testExtentSevice.search(request, proName);
    }
}
