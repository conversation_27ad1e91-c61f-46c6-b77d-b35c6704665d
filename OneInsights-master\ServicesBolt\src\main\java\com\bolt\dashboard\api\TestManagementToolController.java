package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.bolt.dashboard.core.model.TestManagementTool;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.TestManagementToolService;

@RestController
public class TestManagementToolController {
    private TestManagementToolService service;

    @Autowired
    public TestManagementToolController(TestManagementToolService service) {
        this.service = service;
    }

    @RequestMapping(value = "/testManagement", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<TestManagementTool> getTestManagement(@RequestParam("proName") String[] proName) {
    	return service.search(proName[0], "hpAlm");

    }

    @RequestMapping(value = "/smarttest", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<TestManagementTool> getSmartTestData(@RequestParam("proName") String[] proName) {
    	return service.search(proName[0], "smartTest");

    }

}
