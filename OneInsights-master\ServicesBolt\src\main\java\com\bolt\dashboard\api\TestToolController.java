package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.TestTool;
import com.bolt.dashboard.request.TestToolReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.TestToolService;

@RestController
public class TestToolController {
    private final TestToolService testToolService;

    @Autowired
    public TestToolController(TestToolService testToolService) {
        this.testToolService = testToolService;
    }

    @RequestMapping(value = "/testTool", method = GET, produces = APPLICATION_JSON_VALUE)
    public DataResponse<Iterable<TestTool>> testData(@RequestParam("proName") String[] proName) {
        TestToolReq testRequest = new TestToolReq();
        return testToolService.search(testRequest, proName[0]);

    }
}
