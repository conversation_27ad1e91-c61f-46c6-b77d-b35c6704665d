package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

import javax.annotation.PreDestroy;
import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.TriggerCollector;
import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.ProjectCollector;
import com.bolt.dashboard.engagementScorecard.EngScorecardApplication;
import com.bolt.dashboard.projectHealth.ProjectHealthApplication;
import com.bolt.dashboard.sprintcomparison.SprintComparisonApplication;
import com.bolt.dashboard.teamquality.TeamQualityApplication;

@RestController
public class UISchedulerController implements Runnable {
    private static final Logger LOG = LogManager.getLogger(UISchedulerController.class);
    @Autowired
    static TriggerCollector triggerCollector;
    ThreadPoolExecutor tg;
    List<String> triggeredTools = new ArrayList<>();
    ThreadPoolExecutor childThreadGroup;
    static String name = null;
    @Autowired
    static ProjectCollector sonarTaskCollector;
    static String[] proNameArray;
    static String[] tools;
    Map<String, String> map;

    static {
	triggerCollector = new TriggerCollector();
	sonarTaskCollector = new ProjectCollector();
    }

    @RequestMapping(value = "/immediateSchedule", method = GET, produces = APPLICATION_JSON_VALUE)
    public Map<String, String> immediateSchedule(@RequestParam("proName") String[] proName, String[] toolsArray) {
	LOG.info("*********************Run Scheduler************************* " + proName[0]);
	setToolValues(proName,toolsArray);
	
	LOG.info("************************************************");
	LOG.info("values initialized.........");
	new Thread(new UISchedulerController()).start();

	return map;

    }
    private  void setToolValues(String[] proName, String[] toolsArray) {
    	proNameArray = proName;
    	tools = toolsArray;
    	name = proName[0];
    	
    }

    public  Map<String, String> executeCollectors() {

	Map<String, String> map = new HashMap<>();
	tg = (ThreadPoolExecutor)Executors.newCachedThreadPool();
	ProjectCollector collector = new ProjectCollector(name);
	childThreadGroup = (ThreadPoolExecutor)Executors.newCachedThreadPool();

    for (String toolName : tools) {
	LOG.info(" Trigger collector running for  " + toolName + "  for  project  " + name);
	triggerCollect(toolName,collector);
    }
    while (tg.getActiveCount() > 0) {
	/*
	 * This while loop is required to restrict the JVM control till
	 * all threads in threadGroup completed their task
	 */
    }
   // while (tg.activeCount() == 0) {
    if (tg.getActiveCount() == 0) {
    	checkActiveAndInvokeChildThread(triggeredTools);
	//break;
    }

    map.put("Status", "SUCCESS");
	return map;
    }

    private  void checkActiveAndInvokeChildThread(List<String> triggeredTools2) {
		
    	invokeChildThread(triggeredTools);
		while (childThreadGroup.getActiveCount() > 0) {
		    if (childThreadGroup.getActiveCount() == 0) {
			childThreadGroup.shutdown();
		    }

		}
		tg.shutdown();
		
	}
	private  void triggerCollect(String toolName, ProjectCollector collector) {
		
    	if (toolName.equals("Sprint Comparsion") ||
    			toolName.equals("Project Health") ||toolName.equals("Team Quality") || toolName.equals("Engagement Scorecard")) {
		    triggeredTools.add(toolName);
		} else {
		    tg = collector.makeSwitchCaseCall(toolName);
		}
		
	}
	@PreDestroy
    public void cleanUp() {
		cleanThreads();
	

    }
	public static void cleanThreads() {
		if (!(triggerCollector == null))
		    triggerCollector = null;
		if (!(sonarTaskCollector == null))
		    sonarTaskCollector = null;
	}

	public void invokeChildThread(List<String> triggeredTools) {
		Iterator triggerIterator = triggeredTools.iterator();
		while (triggerIterator.hasNext()) {
			String toolName = triggerIterator.next().toString();
			switch (toolName) {
			case "Project Health":
				projectHealth(name);
				break;
			case "Engagement Scorecard":
				engagementScorecard(name);
				break;
			case "Sprint Comparsion":
				sprintComparison(name);
				break;
			case "Team Quality":
				teamQualityReport(name);
				break;
				default:
				LOG.info("No Tools match ");
			}
		}
	}

    public  void projectHealth(String projectName) {
    	childThreadGroup.execute(()->new ProjectHealthApplication().projectHealthMain(projectName));
	//new Thread(childThreadGroup, () -> new ProjectHealthApplication().projectHealthMain(projectName)).start();
	LOG.info("Project Health completed.....");
    }
    private  void engagementScorecard(String projectName) {
		
    	childThreadGroup.execute(()-> new EngScorecardApplication().engScorecardMain(projectName, true));
    //	new Thread(childThreadGroup, () -> new EngScorecardApplication().engScorecardMain(projectName, true)).start();
    	LOG.info("Engagement Scorecard Collector Scheduled.....");
	}

    public  void sprintComparison(String projectName) {
    	childThreadGroup.execute(()->new SprintComparisonApplication().sprintComparisonMain(projectName));
	//new Thread(childThreadGroup, () -> new SprintComparisonApplication().sprintComparisonMain(projectName)).start();
	LOG.info("Sprint comparison completed.....");
    }
    
    public  void teamQualityReport(String projectName) {
 // commented while velocity code movement 
    	childThreadGroup.execute(()->new TeamQualityApplication().teamQualityMain(projectName));
     	//new Thread(childThreadGroup, () -> new TeamQualityApplication().teamQualityMain(projectName)).start();
     	LOG.info("Team quality completed.....");
    }

    @Override
    public void run() {
	map = executeCollectors();

    }
}