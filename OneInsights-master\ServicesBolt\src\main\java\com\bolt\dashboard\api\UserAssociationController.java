package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.List;
import java.util.Set;

import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.AssociatedUsers;
import com.bolt.dashboard.core.model.PortfolioOutModel;
import com.bolt.dashboard.core.model.UserAssociation;
import com.bolt.dashboard.request.UserAssociationReq;
import com.bolt.dashboard.request.UserAssosiationMetricsReq;
import com.bolt.dashboard.service.UserAssociationService;
import com.bolt.dashboard.service.UserAssociationServiceImplementation;

@RestController
public class UserAssociationController {

	@Autowired
	private UserAssociationService userAssociationService;
	private static final Logger LOG = LogManager.getLogger(UserAssociationController.class);

	@Autowired
	public UserAssociationController(UserAssociationService userAssociationService) {
		this.userAssociationService = userAssociationService;
	}

	/*
	 * public static void main(String[] args) { ApplicationContext ctx =
	 * DataConfig.getContext(); new UserAssociationController(new
	 * UserAssociationServiceImplementation(ctx.getBean(UserAssociationRep.class ),
	 * ctx.getBean(UserRepo.class), ctx.getBean(PortfolioConfigRepo.class),
	 * ctx.getBean(HealthDataRepo.class))) .getUsersProjectDetails("Brillio BOLT"
	 * ,"avinash"); }
	 */

	// Code Added by Avinash ........start
	@RequestMapping(value = "/getProjects", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<PortfolioOutModel> associatedProjects(@RequestParam("userName") String userName) {
		return userAssociationService.getProjectAssosiatedWithUser(userName);
	}

	@RequestMapping(value = "/getProjectUsers", method = GET, produces = APPLICATION_JSON_VALUE)
	public Set<AssociatedUsers> getProjectUsers(@RequestParam("pName") String pName) {
		return userAssociationService.getProjectUsers(pName);
	}

	@RequestMapping(value = "/getUsersProjectDetails", method = GET, produces = APPLICATION_JSON_VALUE)
	public PortfolioOutModel getUsersProjectDetails(@RequestParam("pName") String pName,
			@RequestParam("userName") String userName) {
		return userAssociationService.getProjectdetails(pName, userName);
	}

	/*
	 * @RequestMapping(value = "/deleteAssociatedUser", method = POST, produces =
	 * APPLICATION_JSON_VALUE) public String deleteAssociatedUser(@RequestBody
	 * UserAssociation req) { return
	 * userAssociationService.deleteAssociatedUser(req); }
	 */
	
	 @RequestMapping(value = "/updateAssociatedUser", method = POST, produces =
	  APPLICATION_JSON_VALUE) public String updateAssociatedUser(@RequestBody
			  UserAssociationReq req) { return
	  userAssociationService.updateAssociatedUser(req); 
	  }
	 

	// End of Avinash Codes
	/*
	 * @RequestMapping(value = "/userAssociation", method = GET, produces =
	 * APPLICATION_JSON_VALUE) public DataResponse<Iterable<UserAssociation>>
	 * userData(@RequestParam("sessionId") String sessionId, HttpSession
	 * httpSession) { LOG.info("In " +
	 * httpSession.getAttribute(ConstantVariable.KYWRD_UNAME) +
	 * "Association with SessionId " + sessionId + " and HttpSession " +
	 * httpSession); if (httpSession.getAttribute(ConstantVariable.KYWRD_UNAME) !=
	 * null &&
	 * httpSession.getAttribute(ConstantVariable.KYWRD_UNAME).equals(sessionId)) {
	 * return userAssociationService.getUser(); } return null; }
	 * 
	 * @RequestMapping(value = "/userAssociationByProject", method = GET, produces =
	 * APPLICATION_JSON_VALUE) public DataResponse<Iterable<UserAssociation>>
	 * userAssociationData(@RequestParam("proName") String[] proName, HttpSession
	 * httpSession) { String sessionUI = proName[1]; if
	 * (httpSession.getAttribute(ConstantVariable.KYWRD_UNAME) != null &&
	 * httpSession.getAttribute(ConstantVariable.KYWRD_UNAME).equals(sessionUI)) {
	 * return userAssociationService.getUser(proName[0]); } return null;
	 * 
	 * }
	 */
	@RequestMapping(value = "/deleteUserAssosiation", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<Boolean> createDashboardDetete(@RequestBody List<UserAssosiationMetricsReq> req) {
		boolean flag = false;
		for (int counter = 0; counter < req.size(); counter++) {
			flag = userAssociationService.deleteUserData(req.get(counter)) != null;
		}
		return ResponseEntity.status(HttpStatus.CREATED).body(flag);

	}

//	@RequestMapping(value = "/userAssociation", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
//	public ResponseEntity<UserAssociation> createDashboard(@RequestBody List<UserAssosiationMetricsReq> req,
//			HttpSession httpSession) {
//		LOG.info("INSide Health-------->");
//		UserAssociationReq userAssociatReq = new UserAssociationReq();
//		userAssociatReq.setMetric(req);
//		if (httpSession.getAttribute(ConstantVariable.KYWRD_UNAME)
//				.equals(userAssociatReq.getMetric().iterator().next().getSessionId())) {
//			return ResponseEntity.status(HttpStatus.CREATED)
//					.body(userAssociationService.addUser(userAssociatReq.toUserAssociation()));
//		}
//		return null;
//
//	}

}
