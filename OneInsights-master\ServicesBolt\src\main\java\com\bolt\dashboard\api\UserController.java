package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.ManageUser;
import com.bolt.dashboard.request.ManageUserReq;
import com.bolt.dashboard.service.UserService;

@RestController
public class UserController {
	private UserService userService;

	@Autowired
	public UserController(UserService service) {
		this.userService = service;
	}
/*public static void main(String[] args) {
	new UserController(new UserServiceImplementation(DataConfig.getContext().getBean(UserRepo.class))).userData();
}*/
	@RequestMapping(value = "/getUsers", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<ManageUser> userData() {
		return userService.getAllUserData();
	}
	@RequestMapping(value = "/getUsersData", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<ManageUser> userDataAssc() {
		return userService.getAllUserDataAssc();
	}
	
	
	@RequestMapping(value = "/getUser", method = GET, produces = APPLICATION_JSON_VALUE)
	public List<ManageUser> userDataByUser(@RequestParam("userName") String userName) {
		return userService.getUserDataByUserName(userName);
	}

	@RequestMapping(value = "/saveUser", method = POST, produces = APPLICATION_JSON_VALUE)
	public String saveUser(@RequestBody ManageUserReq req) {
		ManageUser user=req.toManageUser(req);
		return userService.saveNewUser(user, true);
	}

	@RequestMapping(value = "/updateUser", method = POST, produces = APPLICATION_JSON_VALUE)
	public String updateUser(@RequestBody ManageUserReq req) {
		ManageUser user=req.toManageUser(req);
		return userService.saveNewUser(user, false);
	}

	@RequestMapping(value = "/deleteUsers", method = POST, produces = APPLICATION_JSON_VALUE)
	public String deleteUsers(@RequestBody List<ManageUserReq> req) {
		List<String> str = new ArrayList<>();
		for (ManageUserReq user : req) {
			ManageUser userReq=user.toManageUser(user);
			if (!((userService.deleteUser(userReq)).equals("deleted"))) {
				str.add(user.getUserName());
			}
		}
		if (str.size() > 0) {
			return "Some users" + str + " were inavlid so can't delete them";
		}
		return "All selected users are deleted";
	}
  
	@RequestMapping(value = "/updatePassword", method = POST, produces = APPLICATION_JSON_VALUE)
	public String updatePassword(@RequestBody ManageUserReq req) {
		ManageUser user=req.toManageUser(req);
		return userService.updatePassword(user);
	}
	/*
	 * ManageUserMetricReq manageUserSettingReq; private ManageUserService
	 * manageUserService; private static final Log LOG =
	 * LogFactory.getLog(ManageUserController.class);
	 * 
	 * @Autowired public ManageUserController(ManageUserService service) {
	 * this.manageUserService = service;
	 * 
	 * }
	 * 
	 * @RequestMapping(value = "/ManageUser", method = GET, produces =
	 * APPLICATION_JSON_VALUE) public DataResponse<Iterable<ManageUser>>
	 * userData() { return manageUserService.getUserData(); }
	 * 
	 * @RequestMapping(value = "/ProjectUser", method = POST, produces =
	 * APPLICATION_JSON_VALUE) public
	 * ResponseEntity<DataResponse<Iterable<ManageUser>>>
	 * projectusers(@RequestBody List<ManageUserMetricReq> req) { ManageUserReq
	 * user = new ManageUserReq(); if (!req.isEmpty()) { user.setMetrics(req);
	 * return ResponseEntity.status(HttpStatus.CREATED)
	 * .body(manageUserService.getProjectUsers(user.toUserRetrival())); } else {
	 * LOG.info(
	 * "no data getting from UI projectusers() ManageUserController()  ");
	 * return null; } }
	 * 
	 * @RequestMapping(value = "/deleteUser", method = POST, consumes =
	 * APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE) public
	 * ResponseEntity<Boolean> createDashboard(@RequestBody
	 * List<ManageUserMetricReq> req, HttpSession httpSession) { ManageUserReq
	 * user = new ManageUserReq(); user.setMetrics(req); boolean flag = false;
	 * if (httpSession.getAttribute(ConstantVariable.KYWRD_UNAME)
	 * .equals(user.getMetrics().iterator().next().getSessionId())) { Iterator
	 * reqIterator = req.iterator(); while (reqIterator.hasNext()) {
	 * ManageUserMetricReq manageUserMetricReq = (ManageUserMetricReq)
	 * reqIterator.next(); flag =
	 * manageUserService.delete(manageUserMetricReq.getEmail());
	 * 
	 * } return ResponseEntity.status(HttpStatus.CREATED).body(flag); } return
	 * null; }
	 * 
	 * @RequestMapping(value = "/SaveUser", method = POST, consumes =
	 * APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE) public
	 * ResponseEntity<ManageUser> createDashboard1(@RequestBody
	 * List<ManageUserMetricReq> req, HttpSession httpSession) { ManageUserReq
	 * user = new ManageUserReq(); user.setMetrics(req); if
	 * (httpSession.getAttribute(ConstantVariable.KYWRD_UNAME)
	 * .equals(user.getMetrics().iterator().next().getSessionId())) { return
	 * ResponseEntity.status(HttpStatus.CREATED)
	 * .body(manageUserService.saveUser(user.toManageUserSetting())); } return
	 * null; }
	 * 
	 * @RequestMapping(value = "/UpdateUser", method = POST, consumes =
	 * APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE) public
	 * ResponseEntity<WriteResult> createDashboard2(@RequestBody
	 * List<ManageUserMetricReq> req, HttpSession httpSession) throws
	 * IOException {
	 * 
	 * ManageUserReq user = new ManageUserReq(); user.setMetrics(req); if
	 * (httpSession.getAttribute(ConstantVariable.KYWRD_UNAME)
	 * .equals(user.getMetrics().iterator().next().getSessionId())) { return
	 * ResponseEntity.status(HttpStatus.CREATED)
	 * .body(manageUserService.updateUser(user.toUpdateUserSetting())); } return
	 * null; }
	 */
}
