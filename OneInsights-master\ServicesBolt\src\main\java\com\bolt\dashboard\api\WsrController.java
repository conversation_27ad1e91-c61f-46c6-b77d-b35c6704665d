package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.WSRModel;
import com.bolt.dashboard.request.WSRReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.WSRService;

@RestController
public class WsrController {
	WSRReq wsrReq;
	private WSRService wsrService;

	@Autowired
	public WsrController(WSRService service) {
		this.wsrService = service;
	}

	@RequestMapping(value = "/fetchDetails", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<Iterable<WSRModel>> fetchDetails(@RequestParam("project") String pName) {
		return wsrService.fetchDetails(pName);	
	}
/*public static void main(String[] args) {
	WSRReq req= new WSRReq();
	
	WSRProjectStatusModel data1 = new WSRProjectStatusModel();
	data1.setCmt("Everythings are going good.");
	data1.setCr("GREEN");
	data1.setFt("YELLOW");
	data1.setPre("RED");
	data1.setSpr("Iteration 1");
	List<WSRProjectStatusModel> a= new ArrayList<WSRProjectStatusModel>();
	a.add(data1);
	
	WSRActionItemsModel data2 = new WSRActionItemsModel();
	data2.setCmt("Everythings are going good.");
	data2.setFrm("1500967585163");
	data2.setItr("Iteration 1");
	data2.setOwner("Avinash");
	data2.setSts("HOLD");
	data2.setTo("1500967585163");
	data2.setItms("UI WORKS are still pendings.Ok");
	List<WSRActionItemsModel> b= new ArrayList<WSRActionItemsModel>();
	b.add(data2);
	
	WSRRisksModel data3 = new WSRRisksModel();
	data3.setCat("UI");
	data3.setDesc("UI Work should be completed by today EOD");
	data3.setItr("Iteration 1");
	data3.setOwner("Avinash");
	data3.setPlan("PLAN A");
	data3.setSts("IN Progress");
	data3.setTo("1500967585163");
	List<WSRRisksModel> c= new ArrayList<WSRRisksModel>();
	c.add(data3);
	
	WSRLnHModel data4 = new WSRLnHModel();
	data4.setFrm("1500967585163");
	data4.setName("Durga Puja");
	data4.setTo("1500967585163");
	List<WSRLnHModel> d= new ArrayList<WSRLnHModel>();
	d.add(data4);
	
	WSRKeyUpdatesModel data5 = new WSRKeyUpdatesModel();
	data5.setAccmp("Task Accmplish should be set up");
	data5.setOpp("Learning and developing");
	data5.setTa("Every things are completed");
	data5.setTp("Task planned is pending");
	List<WSRKeyUpdatesModel> e= new ArrayList<WSRKeyUpdatesModel>();
	e.add(data5); 
	
	req.setAct(b);
	req.setLnh(d);
	req.setpName("Avinash");
	req.setRisks(c);
	req.setSts(a);
	req.setUpd(e);
	req.setEndDate("1500967585163");
	req.setStDate("1500967585163");
	req.setItr("Iteration 1");
	
	
	new WsrController(new WSRServiceImplementation(DataConfig.getContext().getBean(WSRRepo.class))).addWsr(req);
	//new WsrController(new WSRServiceImplementation(DataConfig.getContext().getBean(WSRRepo.class))).fetchDetails("Avinash");
	//new WsrController(new WSRServiceImplementation(DataConfig.getContext().getBean(WSRRepo.class))).updateWsr(req);
}*/
	@RequestMapping(value = "/addWsr", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<String> addWsr(@RequestBody WSRReq req) {	
				return ResponseEntity.status(HttpStatus.CREATED).body(wsrService.addWsr(req));
	}

	@RequestMapping(value = "/updateWsr", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<String> updateWsr(@RequestBody WSRReq req) {	
				return ResponseEntity.status(HttpStatus.CREATED).body(wsrService.updateWsr(req));
	}
	
	@RequestMapping(value = "/delWsr", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
	public ResponseEntity<String> deleteWsr(@RequestBody WSRReq req) {	
				return ResponseEntity.status(HttpStatus.CREATED).body(wsrService.deleteWsr(req));
	}

}
