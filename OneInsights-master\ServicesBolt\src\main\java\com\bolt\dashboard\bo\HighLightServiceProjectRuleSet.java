/**
 * 
 */
package com.bolt.dashboard.bo;

/**
 * <AUTHOR>
 *
 */
public class HighLightServiceProjectRuleSet {

    private String tabName;
    private int percentage;
    private String messageSuccess;
    private String messageFailure;
    private String ruleName;
    private String description;
    private boolean status;
    private int cycleTimeDays;


    /**
     * @return the tabName
     */
    public String getTabName() {
        return tabName;
    }

    /**
     * @param tabName
     *            the tabName to set
     */
    public void setTabName(String tabName) {
        this.tabName = tabName;
    }

    /**
     * @return the percentage
     */
    public int getPercentage() {
        return percentage;
    }

    /**
     * @param percentage
     *            the percentage to set
     */
    public void setPercentage(int percentage) {
        this.percentage = percentage;
    }

    /**
     * @return the messageSuccess
     */
    public String getMessageSuccess() {
        return messageSuccess;
    }

    /**
     * @param messageSuccess
     *            the messageSuccess to set
     */
    public void setMessageSuccess(String messageSuccess) {
        this.messageSuccess = messageSuccess;
    }

    /**
     * @return the messageFailure
     */
    public String getMessageFailure() {
        return messageFailure;
    }

    /**
     * @param messageFailure
     *            the messageFailure to set
     */
    public void setMessageFailure(String messageFailure) {
        this.messageFailure = messageFailure;
    }

    /**
     * @return the ruleName
     */
    public String getRuleName() {
        return ruleName;
    }

    /**
     * @param ruleName
     *            the ruleName to set
     */
    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    /**
     * @return the description
     */
    public String getDescription() {
        return description;
    }

    /**
     * @param description
     *            the description to set
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * @return the status
     */
    public boolean isStatus() {
        return status;
    }

    /**
     * @param status
     *            the status to set
     */
    public void setStatus(boolean status) {
        this.status = status;
    }

    /**
     * @return the cycleTimeDays
     */
    public int getCycleTimeDays() {
        return cycleTimeDays;
    }

    /**
     * @param cycleTimeDays
     *            the cycleTimeDays to set
     */
    public void setCycleTimeDays(int cycleTimeDays) {
        this.cycleTimeDays = cycleTimeDays;
    }

}
