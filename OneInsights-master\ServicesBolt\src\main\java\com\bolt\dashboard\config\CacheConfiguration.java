package com.bolt.dashboard.config;

import java.util.concurrent.TimeUnit;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.guava.GuavaCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.google.common.cache.CacheBuilder;

@Configuration
@EnableCaching
public class CacheConfiguration extends CachingConfigurerSupport {
	GuavaCacheManager cacheManager;

	@Override
	@Bean
	public CacheManager cacheManager() {
		cacheManager = new GuavaCacheManager();
		return cacheManager;
	}

	@Bean
	public CacheManager timeoutCacheManager() {
		 cacheManager = (GuavaCacheManager) cacheManager();
		CacheBuilder<Object, Object> cacheBuilder = CacheBuilder.newBuilder().maximumSize(100).expireAfterWrite(30,
				TimeUnit.MINUTES);
		cacheManager.setCacheBuilder(cacheBuilder);
		return cacheManager;
	}

	

}
