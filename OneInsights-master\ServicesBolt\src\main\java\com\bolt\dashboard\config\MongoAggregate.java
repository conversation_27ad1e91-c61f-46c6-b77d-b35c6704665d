/*package com.bolt.dashboard.config;

import java.util.List;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperationContext;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.aggregation.SortOperation;
import org.springframework.data.mongodb.core.query.Criteria;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;

public class MongoAggregate {
	public MongoTemplate template = null;

	public MongoAggregate() {
		try {
			template = DataConfig.getInstance().mongoTemplate();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void aggregate(String pName) {
		MatchOperation filterSName = Aggregation.match(new Criteria("pName").is(pName));
		AggregationOperation conEffort = getLookUp("EffortHistory", "wId", "wId", "efforts");
		AggregationOperation conTrans = getLookUp("Transition", "wId", "wId", "transitions");
		AggregationOperation out = getOutOperation("Author");
		Aggregation mtrAggr = Aggregation.newAggregation(filterSName, conEffort, conTrans, out);
		template.aggregate(mtrAggr, "Metrics", MonogOutMetrics.class);

		AggregationOperation conMtr = getLookUp("Author", "sName", "sName", "metrics");
		Aggregation ItrAggr = Aggregation.newAggregation(filterSName, conMtr, out);
		template.aggregate(ItrAggr, "Iterations", IterationOutModel.class);

	}

	public AggregationOperation getLookUp(String to, String lId, String fId, String fld) {
		DBObject opr = (DBObject) new BasicDBObject("$lookup",
				new BasicDBObject("from", to).append("localField", lId).append("foreignField", fId).append("as", fld));
		return new AggregationOperation() {
			@Override
			public DBObject toDBObject(AggregationOperationContext arg0) {
				return opr;
			}
		};
	}

	public AggregationOperation getOutOperation(String collectionName) {
		DBObject opr = (DBObject) new BasicDBObject("$out", "Author");
		return new AggregationOperation() {
			@Override
			public DBObject toDBObject(AggregationOperationContext arg0) {
				return opr;
			}
		};
	}

	public List<MonogOutMetrics> getMetric(String pName, String wId) {
		MongoTemplate template = null;
		try {
			template = DataConfig.getInstance().mongoTemplate();
		} catch (Exception e) {
			e.printStackTrace();
		}
		MatchOperation filterSName = null;
		if (wId == null) {
			filterSName = Aggregation.match(new Criteria("pName").is(pName));
		} else {
			filterSName = Aggregation.match(new Criteria("wId").is(wId));
		}
		AggregationOperation conEffort = getLookUp("EffortHistory", "wId", "wId", "efforts");
		AggregationOperation conTrans = getLookUp("Transition", "wId", "wId", "transitions");
		Aggregation mtrAggr = Aggregation.newAggregation(filterSName, conEffort, conTrans);
		return template.aggregate(mtrAggr, "Metrics", MonogOutMetrics.class).getMappedResults();
	}

	public List<IterationOutModel> getCurrentItr(String pName, String almType, String state) {
		MatchOperation filterSName = Aggregation
				.match(new Criteria("pName").is(pName).and("state").is(state).and("pAlmType").is(almType));
		SortOperation sort = Aggregation.sort(Sort.Direction.DESC, "endDate");
		Aggregation mtrAggr = Aggregation.newAggregation(filterSName, sort);
		return template.aggregate(mtrAggr, "Author", IterationOutModel.class).getMappedResults();
	}
}
*/