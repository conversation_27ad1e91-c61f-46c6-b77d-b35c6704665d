package com.bolt.dashboard.filter;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.stream.Collectors;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.json.simple.parser.JSONParser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.filter.OncePerRequestFilter;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.AssociatedUsers;
import com.bolt.dashboard.core.model.UserAssociation;
import com.bolt.dashboard.core.repository.UserAssociationRep;
import com.fasterxml.jackson.core.JsonGenerationException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.HttpStatus.FORBIDDEN;
import static org.springframework.http.HttpStatus.UNAUTHORIZED;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

public class CustomAuthorizationFilter extends OncePerRequestFilter {

	private UserDetailsService userDetailsService;
	private UserAssociationRep userAssociationRep;
    
	@Value("${config.secret}")
	private String secret;
	InputStream in = null;
	private static final Logger LOG = LogManager.getLogger(CustomAuthorizationFilter.class);
	
	
	public CustomAuthorizationFilter(UserDetailsService userDetailsService, UserAssociationRep userAssociationRep) {
		this.userDetailsService = userDetailsService;
		this.userAssociationRep = userAssociationRep;
	}

	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
			throws ServletException, IOException {
		
		if(ConstantVariable.apiAccessToAllList.contains(request.getServletPath())) {
			filterChain.doFilter(request, response);
		} else {
			String authorizationHeader = request.getHeader(AUTHORIZATION);
			if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
				try {
					
					extractSecret();
					  
					String username = extractUserNameJWT(authorizationHeader);

					UserDetails userDetails = userDetailsService.loadUserByUsername(username);
					List<String> roles = new ArrayList<>();
					for (GrantedAuthority role : userDetails.getAuthorities()) {
						roles.add(role.getAuthority());
					}
					
					if(roles.contains("Executive") && ConstantVariable.executiveConfigAccessList.contains(request.getServletPath())) {
						
					}else {
					
					if ((roles.contains("General User") ||roles.contains("Executive") ) &&
							!ConstantVariable.postAPIbyUserName.contains(request.getServletPath())
							&& !ConstantVariable.adminAcessAPI.contains(request.getServletPath())) {
						handleGeneralUser(request, response, username);
					}
					
					if(ConstantVariable.postAPIbyUserName.contains(request.getServletPath()) && !roles.contains("Admin") ) {
						handleOtherUserUserName(request, response, username);
					}
					
					if (ConstantVariable.adminAcessAPI.contains(request.getServletPath()) && !roles.contains("Admin")) {
						accessDeniedResp(response);
					}
				}

					UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(
							username, null, userDetails.getAuthorities());
					SecurityContextHolder.getContext().setAuthentication(authenticationToken);
					filterChain.doFilter(request, response);
				} catch (Exception e) {
					LOG.error("Error in CustomAuthorizationFilter: " + e.getMessage());
					e.printStackTrace();
					response.setHeader("error", e.getMessage());
					response.setStatus(FORBIDDEN.value());
					Map<String, String> tokens = new HashMap<>();
					tokens.put("error_message", e.getMessage());

					response.setContentType(APPLICATION_JSON_VALUE);

					new ObjectMapper().writeValue(response.getOutputStream(), tokens);
				}
			} else {
				if("/CheckSSO".equals(request.getServletPath())) {
					
					String queryString = request.getQueryString();
					String[] queryStrings = queryString.split("&");
				 String[] data=extractTokenAndEmailFromQuery(queryStrings);
				String mailId= verifyUser(data[0]);
				String queryMailId=data[1];
				if(queryMailId.contains("%40")) {
				queryMailId=queryMailId.replace("%40", "@");
				}
				if(mailId.equals("") || !mailId.equalsIgnoreCase(queryMailId)) {
					accessDeniedResp(response);
				}else {
					filterChain.doFilter(request, response);
				
				}
					
				}else {
				
				filterChain.doFilter(request, response);
				}
			}
		}
	}

	private String verifyUser(String token) {
		String inputLine;
		String email="";
		String responseString = "";
		try {
			String url1 = "https://graph.microsoft.com/v1.0/me";

			URL url = new URL(url1);
			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			conn.setRequestMethod("GET");
			conn.setRequestProperty("Authorization", "Bearer " + token);
			conn.setRequestProperty("Accept", "application/json");
			int httpResponseCode = conn.getResponseCode();
			if (httpResponseCode == 200) {
				try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
					while ((inputLine = in.readLine()) != null) {
						responseString = inputLine;
					}
				}
			} 
			
			JSONObject json =parseAsObject(responseString);
			email=json.getString("mail");
		} catch (Exception e) {
			LOG.error("err", e.getLocalizedMessage());
			
			

		}
		return email;
		
	}

	private String[] extractTokenAndEmailFromQuery(String[] queryStrings) {
	String[] token=new String[2];
		for (String query : queryStrings) {
			String[] queryArr = query.split("=");

			if (queryArr[0].equalsIgnoreCase("token")) {
				token[0]=queryArr[1];

			}else if(queryArr[0].equalsIgnoreCase("email")) {
				token[1]=queryArr[1];
			}
		}
		
		return token;
		
		
	}

	private void handleOtherUserUserName(HttpServletRequest request, HttpServletResponse response, String username) throws IOException {
		
		 String uName="";
		  if(request.getMethod().equalsIgnoreCase("POST")) {
			  JSONObject bodyData = readyRequestBody(request);
			 uName = extractUserNameFromBody(bodyData);
		  }	else {
			  String queryString = request.getQueryString();
				String[] queryStrings = queryString.split("&");
			  uName=extractUserNameFromQuery(queryStrings);
		  }
		  
			if(!uName.equalsIgnoreCase(username)) {
				accessDeniedResp(response);
			}
		
	}

	private String extractUserNameFromQuery(String[] queryStrings) {
		String uName="";
		for (String query : queryStrings) {
			String[] userArr = query.split("=");

			if (userArr[0].equalsIgnoreCase("userName")) {
				uName=userArr[1];

			}
		}
		
		return uName;
	}

	private String extractUserNameFromBody(JSONObject bodyData) {
		String userName="";
		if(bodyData.has("userName")) {
			userName=bodyData.getString("userName");
		}
		return userName;
	}

	private String extractUserNameJWT(String authorizationHeader) {
		String token = authorizationHeader.substring("Bearer ".length());
		Algorithm algorithm = Algorithm.HMAC256(secret.getBytes());
		JWTVerifier verifier = JWT.require(algorithm).build();
		DecodedJWT decodedJWT = verifier.verify(token);
		return decodedJWT.getSubject();
	}

	private void accessDeniedResp(HttpServletResponse response)
			throws IOException {
		response.setHeader("error", "Access Denied");
		response.setStatus(FORBIDDEN.value());
		Map<String, String> tokens = new HashMap<>();
		tokens.put("error_message", "Access Denied");
		new ObjectMapper().writeValue(response.getOutputStream(), tokens);
	}

	private void extractSecret() {
		try {
			
			Properties properties = new Properties();
			in = getClass().getClassLoader().getResourceAsStream("application.properties");
			properties.load(in);
			secret = properties.getProperty("config.secret");
		} catch (Exception e) {
			LOG.error("Error in secret Key in CustomAuthorization");
		}
		
	}

	private void handleGeneralUser(HttpServletRequest request, HttpServletResponse response, String username) throws IOException {

		
		String pName="";
		
		if(ConstantVariable.apiList.contains(request.getServletPath()) || ConstantVariable.genUserGetApi.contains(request.getServletPath()) || 
				ConstantVariable.genUserGetApiUser.contains(request.getServletPath()) || ConstantVariable.postApiListUser.contains(request.getServletPath()) ) {
			
		if ( request.getMethod().equalsIgnoreCase("POST") && ConstantVariable.apiList.contains(request.getServletPath())) {
			 JSONObject bodyData = readyRequestBody(request);
			    
			pName = extractPNameFromBody(pName, bodyData);
			    
		}
		
		if ( ConstantVariable.genUserGetApi.contains(request.getServletPath()) 
				|| ConstantVariable.genUserGetApiUser.contains(request.getServletPath())) {
			String queryString = request.getQueryString();
			String[] queryStrings = queryString.split("&");
			pName = extractPNameFromQueryString(pName, queryStrings);
			if(pName.contains("%20")) {
				pName=pName.replace("%20", " ");
			}

		}
		
		
		if(ConstantVariable.genUserGetApiUser.contains(request.getServletPath()) || ConstantVariable.postApiListUser.contains(request.getServletPath())) {
			handleUserAssocUser(pName,username,response);
		}else {
		handleUserAssocAdmin(pName,username,response);
		}
	}
		
	}

	private String extractPNameFromQueryString(String pName, String[] queryStrings) {
		for (String query : queryStrings) {
			String[] projArr = query.split("=");

			if (projArr[0].equalsIgnoreCase(ConstantVariable.projectNameArr[0]) || 
					projArr[0].equalsIgnoreCase(ConstantVariable.projectNameArr[1])
					|| projArr[0].equalsIgnoreCase(ConstantVariable.projectNameArr[2])) {
				pName=projArr[1];
				

			}
		}
		return pName;
	}

	private String extractPNameFromBody(String pName, JSONObject bodyData) {
		if(bodyData.has(ConstantVariable.projectNameArr[0])) {
			pName=bodyData.getString(ConstantVariable.projectNameArr[0]);
		}else if(bodyData.has(ConstantVariable.projectNameArr[1])) {
			pName=bodyData.getString(ConstantVariable.projectNameArr[1]);
		}else if(bodyData.has(ConstantVariable.projectNameArr[2])) {
			pName=bodyData.getString(ConstantVariable.projectNameArr[2]);
			
		}
		return pName;
	}

	private JSONObject readyRequestBody(HttpServletRequest request) throws IOException {
		StringBuilder buffer = new StringBuilder();
		    BufferedReader reader = request.getReader();
		    String line;
		    while ((line = reader.readLine()) != null) {
		        buffer.append(line);
		        buffer.append(System.lineSeparator());
		    }
		    String data = buffer.toString();
		    return parseAsObject(data);
	}

	private void handleUserAssocUser(String pName,String userName, HttpServletResponse response) throws JsonGenerationException, JsonMappingException, IOException {
		List<UserAssociation> userAsscList = this.userAssociationRep.findByPName(pName);

		if (userAsscList.isEmpty() || checkUserExist(userAsscList.get(0).getUsers(),userName)) {
			accessDeniedResp(response);
		}

		
	}
	
	private void handleUserAssocAdmin(String pName, String username, HttpServletResponse response) throws JsonGenerationException, JsonMappingException, IOException {
		List<UserAssociation> userAsscList = this.userAssociationRep.findByPName(pName);

		if (userAsscList.isEmpty() || checkUserIsAdmin(userAsscList.get(0).getUsers(), username)) {
			accessDeniedResp(response);
		}

		
	}
	
	private boolean checkUserExist(Set<AssociatedUsers> users, String username) {

		List<AssociatedUsers> filterUser = users.stream().filter(p -> {
			return p.getUserName().equals(username);
		}).collect(Collectors.toList());

		return filterUser.isEmpty();
	}

	private boolean checkUserIsAdmin(Set<AssociatedUsers> users, String username) {

		List<AssociatedUsers> filterUser = users.stream().filter(p -> {
			return p.getUserName().equals(username);
		}).collect(Collectors.toList());

		return filterUser.isEmpty() || !filterUser.get(0).getAccess().equals("Admin");
	}
	
	
	 public JSONObject parseAsObject(String staticsticResponse) {
			
			return (JSONObject) new JSONTokener(staticsticResponse).nextValue();
		}

}
