/**
 * 
 */
package com.bolt.dashboard.request;

import org.springframework.beans.BeanUtils;

import com.bolt.dashboard.core.model.ALMConfiguration;

public class ALMConfigReq {
	private String storyName;
	private String priorityName;
	private String projectName;
	private String defectName;
	private String releaseName;
	private String[] taskName;
	private String[] closeState;
	private String[] newState;
	private String[] progressState;
	private String[] criticalPriority;
	private String[] highPriority;
	private String[] medPriority;
	private String[] lowPriority;
	private String[] tracksSet;
	private String[] rejectionPhase;
	private String[] reopenPhase;
	private String[] testingPhase;
	private String[] productionPhase;
	private double personHours;
	private String timeZone;
	private String[] velocityFields; 
	private String environment;
	private boolean safeEnabled;
	private String ccrLabel;
	private String[] cycleTimeStates;
	private String[] throughputStates;
	private String firstSprint;
	public String getCcrLabel() {
		return ccrLabel;
	}

	public void setCcrLabel(String ccrLabel) {
		this.ccrLabel = ccrLabel;
	}

	public String[] getCycleTimeStates() {
		return cycleTimeStates;
	}

	public void setCycleTimeStates(String[] cycleTimeStates) {
		this.cycleTimeStates = cycleTimeStates;
	}

	public String[] getThroughputStates() {
		return throughputStates;
	}

	public void setThroughputStates(String[] throughputStates) {
		this.throughputStates = throughputStates;
	}

	public String[] getRejectionPhase() {
		return rejectionPhase;
	}

	public void setRejectionPhase(String[] rejectionPhase) {
		this.rejectionPhase = rejectionPhase;
	}

	public String[] getReopenPhase() {
		return reopenPhase;
	}

	public void setReopenPhase(String[] reopenPhase) {
		this.reopenPhase = reopenPhase;
	}

	public String[] getTestingPhase() {
		return testingPhase;
	}

	public void setTestingPhase(String[] testingPhase) {
		this.testingPhase = testingPhase;
	}

	public String[] getProductionPhase() {
		return productionPhase;
	}

	public void setProductionPhase(String[] productionPhase) {
		this.productionPhase = productionPhase;
	}

	private String trendType;

	public String getStoryName() {
		return storyName;
	}

	public String getPriorityName() {
		return priorityName;
	}

	public void setPriorityName(String priorityName) {
		this.priorityName = priorityName;
	}

	public String[] getTaskName() {
		return taskName;
	}

	public void setTaskName(String[] taskName) {
		this.taskName = taskName;
	}

	public String getReleaseName() {
		return releaseName;
	}

	public void setReleaseName(String releaseName) {
		this.releaseName = releaseName;
	}

	public String[] getCloseState() {
		return closeState;
	}

	public void setCloseState(String[] closeState) {
		this.closeState = closeState;
	}

	public String[] getCriticalPriority() {
		return criticalPriority;
	}

	public void setCriticalPriority(String[] criticalPriority) {
		this.criticalPriority = criticalPriority;
	}

	public String[] getHighPriority() {
		return highPriority;
	}

	public void setHighPriority(String[] highPriority) {
		this.highPriority = highPriority;
	}

	public String[] getMedPriority() {
		return medPriority;
	}

	public void setMedPriority(String[] medPriority) {
		this.medPriority = medPriority;
	}

	public String[] getLowPriority() {
		return lowPriority;
	}

	public void setLowPriority(String[] lowPriority) {
		this.lowPriority = lowPriority;
	}

	public void setStoryName(String storyName) {
		this.storyName = storyName;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getDefectName() {
		return defectName;
	}

	public void setDefectName(String defectName) {
		this.defectName = defectName;
	}

	public String getTrendType() {
		return trendType;
	}

	public void setTrendType(String trendType) {
		this.trendType = trendType;
	}

	public String[] getTracksSet() {
		return tracksSet;
	}

	public void setTracksSet(String[] tracksSet) {
		this.tracksSet = tracksSet;
	}
	
	

	public String[] getNewState() {
		return newState;
	}

	public void setNewState(String[] newState) {
		this.newState = newState;
	}

	public String[] getProgressState() {
		return progressState;
	}

	public void setProgressState(String[] progressState) {
		this.progressState = progressState;
	}

	public ALMConfiguration toDetailsAddSetting(ALMConfigReq req) {
		ALMConfiguration details = new ALMConfiguration();

		BeanUtils.copyProperties(req, details);
		return details;
	}

	public double getPersonHours() {
		return personHours;
	}

	public void setPersonHours(double personHours) {
		this.personHours = personHours;
	}

	public String getTimeZone() {
		return timeZone;
	}

	public void setTimeZone(String timeZone) {
		this.timeZone = timeZone;
	}

	public String[] getVelocityFields() {
		return velocityFields;
	}

	public void setVelocityFields(String[] velocityFields) {
		this.velocityFields = velocityFields;
	}

	public String getEnvironment() {
		return environment;
	}

	public void setEnvironment(String environment) {
		this.environment = environment;
	}

	public boolean isSafeEnabled() {
		return safeEnabled;
	}

	public void setSafeEnabled(boolean safeEnabled) {
		this.safeEnabled = safeEnabled;
	}

	public String getFirstSprint() {
		return firstSprint;
	}

	public void setFirstSprint(String firstSprint) {
		this.firstSprint = firstSprint;
	}
	
	

}
