/**
 * 
 */
package com.bolt.dashboard.request;

import com.bolt.dashboard.core.model.ActiveDirectoryConfiguration;

/**
 * <AUTHOR> organisationName :
 * 
 */
public class ActiveDirectoryReq {
    private String $$hashKey;
    private String organisationName;
    private String host;
    private String port;
    private String basedn;

    public String getOrganisationName() {
        return organisationName;
    }

    public void setOrganisationName(String organisationName) {
        this.organisationName = organisationName;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getBasedn() {
        return basedn;
    }

    public void setBasedn(String basedn) {
        this.basedn = basedn;
    }

    public String get$$hashKey() {
        return $$hashKey;
    }

    public void set$$hashKey(String $$hashKey) {
        this.$$hashKey = $$hashKey;
    }

    public ActiveDirectoryConfiguration toDetailsAddSetting(ActiveDirectoryReq req) {
        ActiveDirectoryConfiguration configuration = new ActiveDirectoryConfiguration();

        configuration.setBasedn(req.getBasedn());
        configuration.setHost(req.getHost());
        configuration.setPort(req.getPort());
        configuration.setOrganisationName(req.getOrganisationName());
        return configuration;
    }

}
