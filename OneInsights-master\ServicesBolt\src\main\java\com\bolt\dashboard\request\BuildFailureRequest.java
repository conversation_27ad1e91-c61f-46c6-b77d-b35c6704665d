/**
 * 
 */
package com.bolt.dashboard.request;

/**
 * <AUTHOR>
 *
 */
public class BuildFailureRequest {
    private String ruleName;
    private String patternFromUser;
    private String patternDisplay;
    private String projectName;
    private String tabName;
    private String userName;



    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getTabName() {
        return tabName;
    }

    public void setTabName(String tabName) {
        this.tabName = tabName;
    }

    /**
     * 
     */

    /**
     * @return the patternFromUser
     */
    public String getPatternFromUser() {
        return patternFromUser;
    }

    /**
     * @param patternFromUser
     *            the patternFromUser to set
     */
    public void setPatternFromUser(String patternFromUser) {
        this.patternFromUser = patternFromUser;
    }

    /**
     * @return the patternDisplay
     */
    public String getPatternDisplay() {
        return patternDisplay;
    }

    /**
     * @param patternDisplay
     *            the patternDisplay to set
     */
    public void setPatternDisplay(String patternDisplay) {
        this.patternDisplay = patternDisplay;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

}
