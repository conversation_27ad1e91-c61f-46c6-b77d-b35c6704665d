package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;

public class BuildToolFailureSettingReq {
    private String projectName;
    private boolean addFlag;
    private List<BuildFailureRequest> metrics = new ArrayList<BuildFailureRequest>();

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public boolean isAddFlag() {
        return addFlag;
    }

    public void setAddFlag(boolean addFlag) {
        this.addFlag = addFlag;
    }

    public List<BuildFailureRequest> getMetrics() {
        return metrics;
    }

    public void setMetrics(List<BuildFailureRequest> metrics) {
        this.metrics = metrics;
    }

}
