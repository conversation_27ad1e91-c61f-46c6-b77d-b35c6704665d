package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.bolt.dashboard.core.model.ChartConfiguration;
import com.bolt.dashboard.core.model.ChartMetric;

public class ChartConfigurationReq {
	private String pName;
	private Set<ChartMetric> metrics = new HashSet<>();
	private String almType;

	public String getpName() {
		return pName;
	}

	public void setpName(String pName) {
		this.pName = pName;
	}

	public String getAlmType() {
		return almType;
	}

	public void setAlmType(String almType) {
		this.almType = almType;
	}

	
	public ChartConfiguration toChartConfig() {

		ChartConfiguration configuration = new ChartConfiguration();
		configuration.setpName(this.getpName());
		configuration.setAlmType(this.getAlmType());
//		for (ChartMetricReq configurationMetrics : this.getMetrics()) {
//			
//			ChartMetric configMetrics = new ChartMetric(configurationMetrics.getChartName());
//			configMetrics.setChartName(configurationMetrics.getChartName());
//			configMetrics.setProject(configurationMetrics.getProject());
//			configMetrics.setAdmin(configurationMetrics.getAdmin());
//			configMetrics.setUser(configurationMetrics.getUser());
//			configuration.getMetrics().add(configMetrics);
//
//		}
		configuration.setMetrics(this.getMetrics());

		return configuration;
	}

	public Set<ChartMetric> getMetrics() {
		return metrics;
	}

	public void setMetrics(Set<ChartMetric> metrics) {
		this.metrics = metrics;
	}



}
