package com.bolt.dashboard.request;

public class ChartMetricReq {
	private String chartName;
	private Boolean project;
	private Boolean user;
	private Boolean admin;
	private String projectName;

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getChartName() {
		return chartName;
	}

	public void setChartName(String chartName) {
		this.chartName = chartName;
	}

	public Boolean getProject() {
		return project;
	}

	public void setProject(Boolean project) {
		this.project = project;
	}

	public Boolean getUser() {
		return user;
	}

	public void setUser(Boolean user) {
		this.user = user;
	}

	public Boolean getAdmin() {
		return admin;
	}

	public void setAdmin(Boolean admin) {
		this.admin = admin;
	}

}
