package com.bolt.dashboard.request;

import javax.validation.constraints.NotNull;

import org.bson.types.ObjectId;

public class CodeCoverageReq {
	@NotNull
	private ObjectId componentId;

	/**
	 * @return the componentId
	 */
	public ObjectId getComponentId() {
		return componentId;
	}

	/**
	 * @param componentId
	 *            the componentId to set
	 */
	public void setComponentId(ObjectId componentId) {
		this.componentId = componentId;
	}
}