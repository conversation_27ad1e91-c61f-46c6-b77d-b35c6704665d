package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.bolt.dashboard.core.model.CollaborativeMailModel;

public class CollaborativeMailSetupReq {

	private List<CollaborativeMailReq> metric = new ArrayList<CollaborativeMailReq>();

	public List<CollaborativeMailReq> getMetric() {
		return metric;
	}

	public void setMetric(List<CollaborativeMailReq> metric) {
		this.metric = metric;
	}

	@Autowired

	public List<CollaborativeMailModel> toMailSetupSetting() {
		List<CollaborativeMailModel> mailSetuplist = new ArrayList<>();
		for (CollaborativeMailReq mailerReq : this.getMetric()) {
			CollaborativeMailModel mailSetup = new CollaborativeMailModel();
			mailSetup.setActionId(mailerReq.getActionId());
			mailSetup.setChartName(mailerReq.getChartName());
			mailSetup.setComment(mailerReq.getComment());
			mailSetup.setTagName(mailerReq.getTagName());
			mailSetup.setSprintName(mailerReq.getSprintName());
			mailSetup.setProjectName(mailerReq.getProjectName());
			mailSetup.setToAddress(mailerReq.getToAddress());
			mailSetup.setFileName(mailerReq.getFileName());
			mailSetup.setExtension(mailerReq.getExtension());
			mailSetup.setReporter(mailerReq.getReporter());
			mailSetup.setStatus(mailerReq.getStatus());
			mailSetup.setAssignedTo(mailerReq.getAssignedTo());
			mailSetuplist.add(mailSetup);

		}
		return mailSetuplist;

	}

}
