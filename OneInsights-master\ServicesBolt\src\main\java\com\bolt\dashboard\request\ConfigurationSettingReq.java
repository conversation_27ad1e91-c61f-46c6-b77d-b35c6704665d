package com.bolt.dashboard.request;

import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class ConfigurationSettingReq {
    private String projectName;
    private String projectType;
    private boolean manualData;
    private boolean addFlag;
    private List<ConfigurationSettingMetricsReq> metrics = new ArrayList<ConfigurationSettingMetricsReq>();

    public List<ConfigurationSettingMetricsReq> getMetrics() {
        return metrics;
    }

    public boolean isManualData() {
		return manualData;
	}

	public void setManualData(boolean manualData) {
		this.manualData = manualData;
	}

	public void setMetrics(List<ConfigurationSettingMetricsReq> newmetrics) {
        this.metrics = newmetrics;
    }

    public ConfigurationSetting toConfigSetting() {

        ConfigurationSetting configuration = new ConfigurationSetting();
        for (ConfigurationSettingMetricsReq configurationMetrics : this.getMetrics()) {
            configuration.setProjectName(configurationMetrics.getProjectName());
            configuration.setAddFlag(configurationMetrics.isAddFlag());
            configuration.setBaseline(configurationMetrics.isBaseline());
            configuration.setProjectType(configurationMetrics.getProjectType());
            ConfigurationToolInfoMetric configMetrics = new ConfigurationToolInfoMetric(
                    configurationMetrics.getToolName());
            configMetrics.setToolName(configurationMetrics.getToolName());
            configMetrics.setUrl(configurationMetrics.getUrl());
            configMetrics.setUserName(configurationMetrics.getUserName());
               // String secret=EncryptionDecryptionAES.getAlphaNumericString(15);
				configMetrics.setPassword( EncryptionDecryptionAES.encrypt(configurationMetrics.getPassword(),ConstantVariable.SECRET_KEY));
			     //configMetrics.setPassword(configurationMetrics.getPassword());
			
            //configMetrics.setPassword(configurationMetrics.getPassword());
            configMetrics.setJobName(configurationMetrics.getJobName());
            configMetrics.setProjectCode(configurationMetrics.getProjectCode());
            configMetrics.setWidgetName(configurationMetrics.getWidgetName());
            configMetrics.setDomain(configurationMetrics.getDomain());
            configMetrics.setSelected(configurationMetrics.getSelected());
            configMetrics.setToolType(configurationMetrics.getToolType());
            configMetrics.setRepoName(configurationMetrics.getRepoName());
            configuration.getMetrics().add(configMetrics);
            configuration.setManualData(configurationMetrics.isManualData());
        }

        return configuration;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public boolean isAddFlag() {
        return addFlag;
    }

    public void setAddFlag(boolean addFlag) {
        this.addFlag = addFlag;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }
}
