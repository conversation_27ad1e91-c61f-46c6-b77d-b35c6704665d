package com.bolt.dashboard.request;

import java.util.LinkedHashSet;
import java.util.Set;
import java.util.TreeMap;

import com.bolt.dashboard.core.model.DefectPainpoint;
import com.bolt.dashboard.core.model.DefectPainpointRules;

public class DefectPainpointReq {
	private long timestamp;
	private String projectName;
	private String[] excludeStates;
	private TreeMap<Long,Double> ppThreshold;
	private boolean absolute;
	private Set<DefectPainpointRules> rules = new LinkedHashSet<>();


public DefectPainpoint toDefectPainPoint(DefectPainpointReq req) {
	DefectPainpoint painPoint= new DefectPainpoint();
	painPoint.setAbsolute(req.isAbsolute());
	painPoint.setExcludeStates(req.getExcludeStates());
	painPoint.setPpThreshold(req.getPpThreshold());
	painPoint.setProjectName(req.getProjectName());
	painPoint.setRules(req.getRules());
	painPoint.setTimestamp(req.getTimestamp());
	return painPoint;
	
}
	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public Set<DefectPainpointRules> getRules() {
		return rules;
	}

	public void setRules(Set<DefectPainpointRules> rules) {
		this.rules = rules;
	}

	public TreeMap<Long,Double> getPpThreshold() {
		return ppThreshold;
	}

	public void setPpThreshold(TreeMap<Long,Double> ppThreshold) {
		this.ppThreshold = ppThreshold;
	}

	public boolean isAbsolute() {
		return absolute;
	}

	public void setAbsolute(boolean absolute) {
		this.absolute = absolute;
	}

	public String[] getExcludeStates() {
		return excludeStates;
	}

	public void setExcludeStates(String[] excludeStates) {
		this.excludeStates = excludeStates;
	}

	
}
