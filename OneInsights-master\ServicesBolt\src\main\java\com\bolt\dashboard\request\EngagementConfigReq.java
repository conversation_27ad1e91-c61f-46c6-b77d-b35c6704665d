package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;

import com.bolt.dashboard.core.model.EngagementConfig;
import com.bolt.dashboard.core.model.EngagementConfigMetrics;

public class EngagementConfigReq {
	 private String towerName;
	    private String towerType;
	    private List<EngagementConfigMetrics> engMetrics = new ArrayList<>();
	    
	    public EngagementConfig toEngagementConfig(EngagementConfigReq req) {
	    	EngagementConfig engConfig=new EngagementConfig();
	    	engConfig.setEngMetrics(req.getEngMetrics());
	    	engConfig.setTowerName(req.getTowerName());
	    	engConfig.setTowerType(req.getTowerType());
	    	
	    	return engConfig;
	    }
		public String getTowerName() {
			return towerName;
		}
		public void setTowerName(String towerName) {
			this.towerName = towerName;
		}
		public String getTowerType() {
			return towerType;
		}
		public void setTowerType(String towerType) {
			this.towerType = towerType;
		}
		public List<EngagementConfigMetrics> getEngMetrics() {
			return engMetrics;
		}
		public void setEngMetrics(List<EngagementConfigMetrics> engMetrics) {
			this.engMetrics = engMetrics;
		}
	   
}