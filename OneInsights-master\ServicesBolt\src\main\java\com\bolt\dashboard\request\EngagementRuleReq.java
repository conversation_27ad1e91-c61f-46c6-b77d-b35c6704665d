package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;


import com.bolt.dashboard.core.model.EngRulesBasedOnMonth;
import com.bolt.dashboard.core.model.EngagementRule;

public class EngagementRuleReq {

	private String towerName;
	private String projectName;
	//private String paramType;
	 private List<EngRulesBasedOnMonth> listOfRulesBasedOnMonths =new ArrayList<EngRulesBasedOnMonth>();

	 public EngagementRule toEngagementRule(EngagementRuleReq req) {
		 EngagementRule rule= new EngagementRule();
		 rule.setListOfRulesBasedOnMonths(req.getListOfRulesBasedOnMonths());
		 rule.setProjectName(req.getProjectName());
		 rule.setTowerName(req.getProjectName());
		 return rule;
		 
	 }
	 
	public String getTowerName() {
		return towerName;
	}

	public List<EngRulesBasedOnMonth> getListOfRulesBasedOnMonths() {
		return listOfRulesBasedOnMonths;
	}

	public void setListOfRulesBasedOnMonths(List<EngRulesBasedOnMonth> listOfRulesBasedOnMonths) {
		this.listOfRulesBasedOnMonths = listOfRulesBasedOnMonths;
	}

	public void setTowerName(String towerName) {
		this.towerName = towerName;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	
}
