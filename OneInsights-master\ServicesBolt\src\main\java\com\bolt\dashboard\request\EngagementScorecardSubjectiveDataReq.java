package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;


import com.bolt.dashboard.core.model.EngScorecardSubjectiveSprintData;
import com.bolt.dashboard.core.model.EngagementScorecardSubjectiveData;
import com.bolt.dashboard.core.model.FeatureConfig;

public class EngagementScorecardSubjectiveDataReq {

	private String pName; 
	private List<EngScorecardSubjectiveSprintData> engScorecardSprintData = new ArrayList<>();
	
	public EngagementScorecardSubjectiveData toEngScorecardSubjective() {
		EngagementScorecardSubjectiveData engScoreSubjective = new EngagementScorecardSubjectiveData();
		engScoreSubjective.setpName(this.pName);
		engScoreSubjective.setEngScorecardSprintData(this.getEngScorecardSprintData());
				
		return engScoreSubjective;
		
	}
	public String getpName() {
		return pName;
	}
	public void setpName(String pName) {
		this.pName = pName;
	}
	public List<EngScorecardSubjectiveSprintData> getEngScorecardSprintData() {
		return engScorecardSprintData;
	}
	public void setEngScorecardSprintData(List<EngScorecardSubjectiveSprintData> engScorecardSprintData) {
		this.engScorecardSprintData = engScorecardSprintData;
	}
	
	
	
	
	
}
