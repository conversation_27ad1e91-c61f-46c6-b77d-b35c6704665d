package com.bolt.dashboard.request;

import com.bolt.dashboard.core.model.FeatureConfig;

public class FeatureConfigReq {
	private String pName;
	private boolean engScorecardEnable;
	private boolean safeEnable;
	private boolean portfolioEnable;
	private boolean lpmEnable;
	private boolean phiEnable;
	 public boolean isPhiEnable() {
		return phiEnable;
	}

	public void setPhiEnable(boolean phiEnable) {
		this.phiEnable = phiEnable;
	}

	private boolean engIndexEnable;
	
	public boolean isEngIndexEnable() {
		return engIndexEnable;
	}

	public boolean isLpmEnable() {
		return lpmEnable;
	}

	public void setLpmEnable(boolean lpmEnable) {
		this.lpmEnable = lpmEnable;
	}

	public void setEngIndexEnable(boolean engIndexEnable) {
		this.engIndexEnable = engIndexEnable;
	}

	public FeatureConfig toFeatureConfig() {
		FeatureConfig featureConfig = new FeatureConfig();
		 
		featureConfig.setEngScorecardEnable(this.isEngScorecardEnable());
		featureConfig.setSafeEnable(this.isSafeEnable());
		featureConfig.setPortfolioEnable(this.isPortfolioEnable());
		featureConfig.setpName(this.getpName());
		featureConfig.setEngIndexEnable(this.isEngIndexEnable());
		featureConfig.setLpmEnable(this.isLpmEnable());
		featureConfig.setPhiEnable(this.isPhiEnable());
		return featureConfig;
		
	}

	public boolean isEngScorecardEnable() {
		return engScorecardEnable;
	}

	public void setEngScorecardEnable(boolean engScorecardEnable) {
		this.engScorecardEnable = engScorecardEnable;
	}

	public boolean isSafeEnable() {
		return safeEnable;
	}

	public void setSafeEnable(boolean safeEnable) {
		this.safeEnable = safeEnable;
	}

	public boolean isPortfolioEnable() {
		return portfolioEnable;
	}

	public void setPortfolioEnable(boolean portfolioEnable) {
		this.portfolioEnable = portfolioEnable;
	}

	public String getpName() {
		return pName;
	}

	public void setpName(String pName) {
		this.pName = pName;
	}

}
