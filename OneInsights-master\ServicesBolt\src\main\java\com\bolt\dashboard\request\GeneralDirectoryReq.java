/**
 * 
 */
package com.bolt.dashboard.request;

import com.bolt.dashboard.core.model.GeneralDirectoryConfiguration;

/**
 * <AUTHOR> organisationName :
 * 
 */
public class GeneralDirectoryReq {
    private String $$hashKey;
    private String name;
    private String value;

    public String get$$hashKey() {
        return $$hashKey;
    }

    public void set$$hashKey(String $$hashKey) {
        this.$$hashKey = $$hashKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public GeneralDirectoryConfiguration toDetailsAddSetting(GeneralDirectoryReq req) {
        GeneralDirectoryConfiguration configuration = new GeneralDirectoryConfiguration();

        configuration.setName(req.getName());
        configuration.setValue(req.getValue());
        return configuration;
    }
}
