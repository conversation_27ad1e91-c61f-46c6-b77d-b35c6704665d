package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;

import com.bolt.dashboard.core.model.GoalMetric;
import com.bolt.dashboard.core.model.GoalSetting;
import com.bolt.dashboard.core.model.ProjectHealthConfig;

/**
 * 
 * <AUTHOR>
 *
 */
public class GoalSettingReq {
	private List<GoalSettingMetricsReq> metrics = new ArrayList<GoalSettingMetricsReq>();
	private GoalCoveraeSettingMetricsReq metricFromTo;

	public List<GoalSettingMetricsReq> getMetrics() {
		return metrics;
	}

	public void setMetrics(List<GoalSettingMetricsReq> newmetrics) {
		this.metrics = newmetrics;
	}

	public void setMetricFromTo(GoalCoveraeSettingMetricsReq metricFromTo) {
		this.metricFromTo = metricFromTo;
	}

	public GoalSetting toGoalSetting() {
		GoalSetting goal = new GoalSetting();
		if (this.metrics.size()>0) {

			for (GoalSettingMetricsReq goalMetrics : this.getMetrics()) {
				goal.setName(goalMetrics.gettype());
				goal.setProjectName(goalMetrics.getprojectName());
				GoalMetric metrics = new GoalMetric(goalMetrics.getname());
				metrics.setDisplayName(goalMetrics.getdisplayname());
				metrics.setGoal(goalMetrics.getgoal());
				metrics.setSelected(goalMetrics.getselected());
				metrics.setOperator(goalMetrics.getoperator());
				goal.getMetrics().add(metrics);
			}
		} else {
			goal.setConfig(this.metricFromTo.getConfig());
			goal.setName(this.metricFromTo.getName());
			goal.setProjectName(this.metricFromTo.getProjectName());

		}
		return goal;
	}
}
