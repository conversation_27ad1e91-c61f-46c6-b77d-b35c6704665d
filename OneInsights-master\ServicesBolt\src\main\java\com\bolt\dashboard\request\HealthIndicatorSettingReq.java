package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.bolt.dashboard.core.model.HealthIndicator;

public class HealthIndicatorSettingReq {
    private static final Logger LOG = LogManager.getLogger(HealthIndicatorSettingReq.class);
    private List<HealthIndicatorReq> metric = new ArrayList<HealthIndicatorReq>();

    public List<HealthIndicatorReq> getMetric() {
        return metric;
    }

    public void setMetric(List<HealthIndicatorReq> metric) {
        this.metric = metric;
    }

    public HealthIndicator toHealthSetting() {
        LOG.info("inside toHealthSetting---------->");
        HealthIndicator healthIndicator = new HealthIndicator();
        for (HealthIndicatorReq healthIndicatorReq : this.getMetric()) {
            healthIndicator.setOperator(healthIndicatorReq.getOperator());
            healthIndicator.setValue(healthIndicatorReq.getValue());
            healthIndicator.setPhase(healthIndicatorReq.getPhase());
            healthIndicator.setRuleName(healthIndicatorReq.getRuleName());
            LOG.info("healthIndicatorReq.getRuleName()--------->" + healthIndicatorReq.getRuleName()
                    + "------>healthIndicator.setRuleName()" + healthIndicator.getRuleName());
            healthIndicator.setSelect(healthIndicatorReq.isSelect());
            healthIndicator.setWeightage(healthIndicatorReq.getWeightage());
            healthIndicator.setProjectName(healthIndicatorReq.getProjectName());
            healthIndicator.setId(healthIndicatorReq.getId());
            LOG.info("Project Name" + healthIndicatorReq.getProjectName() + "Value" + healthIndicatorReq.getValue()
                    + "Phase" + healthIndicatorReq.getPhase() + "Operator" + healthIndicatorReq.getOperator()
                    + "Weightage" + healthIndicatorReq.getWeightage() + "RuleName" + healthIndicatorReq.getRuleName()
                    + "id" + healthIndicatorReq.getId());
        }
        LOG.info("healthIndicator-----" + healthIndicator);
        return healthIndicator;

    }
}
