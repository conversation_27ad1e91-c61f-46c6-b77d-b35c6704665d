package com.bolt.dashboard.request;

public class HighlightReq {

    private String projectName;
    private String tabName;
    private int value;
    private String messageSuccess;
    private String messageFailure;
    private String ruleName;
    private String description;
    private String operator;
    private boolean status;
    private String userName;
    private int cycleTimeDays;

    /**
     * @return the tabName
     */
    public String getTabName() {
        return tabName;
    }

    /**
     * @param tabName
     *            the tabName to set
     */
    public void setTabName(String tabName) {
        this.tabName = tabName;
    }

    /**
     * @return the percentage
     */
    public int getValue() {
        return value;
    }

    /**
     * @param percentage
     *            the percentage to set
     */
    public void setValue(int value) {
        this.value = value;
    }

    /**
     * @return the messageSuccess
     */
    public String getMessageSuccess() {
        return messageSuccess;
    }

    /**
     * @param messageSuccess
     *            the messageSuccess to set
     */
    public void setMessageSuccess(String messageSuccess) {
        this.messageSuccess = messageSuccess;
    }

    /**
     * @return the messageFailure
     */
    public String getMessageFailure() {
        return messageFailure;
    }

    /**
     * @param messageFailure
     *            the messageFailure to set
     */
    public void setMessageFailure(String messageFailure) {
        this.messageFailure = messageFailure;
    }

    /**
     * @return the ruleName
     */
    public String getRuleName() {
        return ruleName;
    }

    /**
     * @param ruleName
     *            the ruleName to set
     */
    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    /**
     * @return the description
     */
    public String getDescription() {
        return description;
    }

    /**
     * @param description
     *            the description to set
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * @return the status
     */
    public boolean isStatus() {
        return status;
    }

    /**
     * @param status
     *            the status to set
     */
    public void setStatus(boolean status) {
        this.status = status;
    }

    /**
     * @return the cycleTimeDays
     */
    public int getCycleTimeDays() {
        return cycleTimeDays;
    }

    /**
     * @param cycleTimeDays
     *            the cycleTimeDays to set
     */
    public void setCycleTimeDays(int cycleTimeDays) {
        this.cycleTimeDays = cycleTimeDays;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

}
