package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;

import com.bolt.dashboard.core.model.HighLightModel;
import com.bolt.dashboard.core.model.HighLightProjectRuleSet;

public class HightlightSettingReq {
    private String projectName;
    private boolean addFlag;
    private List<HighlightReq> metrics = new ArrayList<HighlightReq>();

    public List<HighlightReq> getMetrics() {
        return metrics;
    }

    public void setMetrics(List<HighlightReq> newmetrics) {
        this.metrics = newmetrics;
    }

    public HighLightModel toHighlightAddSetting() {

        HighLightModel highLightModel = new HighLightModel();

        for (HighlightReq highlightReq : this.getMetrics()) {
            highLightModel.setProjectName(highlightReq.getProjectName());
            highLightModel.setUserName(highlightReq.getUserName());
            HighLightProjectRuleSet highlightMetrics = new HighLightProjectRuleSet();
            highlightMetrics.setCycleTimeDays(highlightReq.getCycleTimeDays());
            highlightMetrics.setMessageFailure(highlightReq.getMessageFailure());
            highlightMetrics.setMessageSuccess(highlightReq.getMessageSuccess());
            highlightMetrics.setOperator(highlightReq.getOperator());
            highlightMetrics.setValue(highlightReq.getValue());
            highlightMetrics.setRuleName(highlightReq.getRuleName());
            highlightMetrics.setTabName(highlightReq.getTabName());

            highLightModel.getRulesListOfProject().add(highlightMetrics);
        }

        return highLightModel;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public boolean isAddFlag() {
        return addFlag;
    }

    public void setAddFlag(boolean addFlag) {
        this.addFlag = addFlag;
    }
}
