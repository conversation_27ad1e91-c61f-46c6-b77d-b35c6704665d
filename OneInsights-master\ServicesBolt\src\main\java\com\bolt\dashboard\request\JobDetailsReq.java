package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;

import com.bolt.dashboard.core.model.JobDetails;
import com.bolt.dashboard.core.model.JobDetailsMetrics;

public class JobDetailsReq {

	
	private String projectName;
	 private List<JobDetailsMetrics> metrics = new ArrayList<>();


		public JobDetails toJobDetails() {

			JobDetails jobDetails = new JobDetails();

//		for (JobDetailsMetricsReq jobDetailsMetricsReq : this.) {
//			jobDetails.setProjectName(this.getProjectName());
//			JobDetailsMetrics jobDetailsMetrics = new JobDetailsMetrics();
//			jobDetailsMetrics.setJobName(jobDetailsMetricsReq.getJobName());
//			jobDetailsMetrics.setSelected(jobDetailsMetricsReq.getSelected());
//			jobDetailsMetrics.setSequenceNumber(jobDetailsMetricsReq.getSequenceNumber());
//			jobDetailsMetrics.setValueStreamName(jobDetailsMetricsReq.getValueStreamName());
//			jobDetails.getMetrics().add(jobDetailsMetrics);
//
//		}
			
			jobDetails.setProjectName(this.getProjectName());
			jobDetails.setMetrics(this.getMetrics());

		return jobDetails;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public List<JobDetailsMetrics> getMetrics() {
		return metrics;
	}

	public void setMetrics(List<JobDetailsMetrics> metrics) {
		this.metrics = metrics;
	}

	
	

}
