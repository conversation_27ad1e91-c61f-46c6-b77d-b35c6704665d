package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;

import com.bolt.dashboard.core.model.MailSetup;

public class MailSetupSettingReq {
    private List<MailSetupReq> metric = new ArrayList<MailSetupReq>();

    public List<MailSetupReq> getMetric() {
        return metric;
    }

    public void setMetric(List<MailSetupReq> metric) {
        this.metric = metric;
    }

    public MailSetup toMailSetupSetting() {
        MailSetup mailSetup = new MailSetup();
        for (MailSetupReq mailerReq : this.getMetric()) {
            mailSetup.setHost(mailerReq.getHost());
            mailSetup.setPort(mailerReq.getPort());
            mailSetup.setStarttls(mailerReq.isStarttls());
            mailSetup.setUserName(mailerReq.getUserName());
            mailSetup.setPassword(mailerReq.getPassword());
            mailSetup.setNotification(mailerReq.getNotification());

        }
        return mailSetup;

    }

}
