package com.bolt.dashboard.request;

public class MailerRequest {

    private String userName;
    private String password;

    private String toMailRecipients;
    private String toCCRecipients;
    private String toBCCRecipients;
    private String subject;
    private String mailBody;
    private String salutation;
    private String projectName;
    private String access;
    private String email;
    private String toAdd;
    private String ccAdd;
    private String bccAdd;
    private String msgBody;

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getAccess() {
        return access;
    }

    public void setAccess(String access) {
        this.access = access;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getToMailRecipients() {
        return toMailRecipients;
    }

    public void setToMailRecipients(String toMailRecipients) {
        this.toMailRecipients = toMailRecipients;
    }

    public String getToCCRecipients() {
        return toCCRecipients;
    }

    public void setToCCRecipients(String toCCRecipients) {
        this.toCCRecipients = toCCRecipients;
    }

    public String getToBCCRecipients() {
        return toBCCRecipients;
    }

    public void setToBCCRecipients(String toBCCRecipients) {
        this.toBCCRecipients = toBCCRecipients;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getToAdd() {
        return toAdd;
    }

    public void setToAdd(String toAdd) {
        this.toAdd = toAdd;
    }

    public String getCcAdd() {
        return ccAdd;
    }

    public void setCcAdd(String ccAdd) {
        this.ccAdd = ccAdd;
    }

    public String getBccAdd() {
        return bccAdd;
    }

    public void setBccAdd(String bccAdd) {
        this.bccAdd = bccAdd;
    }

    public String getMsgBody() {
        return msgBody;
    }

    public void setMsgBody(String msgBody) {
        this.msgBody = msgBody;

    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getMailBody() {
        return mailBody;
    }

    public void setMailBody(String mailBody) {
        this.mailBody = mailBody;
    }

    public String getSalutation() {
        return salutation;
    }

    public void setSalutation(String salutation) {
        this.salutation = salutation;
    }

}
