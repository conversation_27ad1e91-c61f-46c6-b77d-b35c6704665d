package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;

import com.bolt.dashboard.core.model.MailerAssociation;

public class MailerSettingReq {
    private List<MailerRequest> metric = new ArrayList<MailerRequest>();

    public List<MailerRequest> getMetric() {
        return metric;
    }

    public void setMetric(List<MailerRequest> metric) {
        this.metric = metric;
    }

    public MailerAssociation toSendMailSetting() {
        MailerAssociation mailerAssociation = new MailerAssociation();
        for (MailerRequest mailerReq : this.getMetric()) {

            mailerAssociation.setToAdd(mailerReq.getToAdd());
            mailerAssociation.setCcAdd(mailerReq.getCcAdd());
            mailerAssociation.setBccAdd(mailerReq.getBccAdd());
            mailerAssociation.setMsgBody(mailerReq.getMsgBody());
            mailerAssociation.setSubject(mailerReq.getSubject());

        }
        return mailerAssociation;

    }
}
