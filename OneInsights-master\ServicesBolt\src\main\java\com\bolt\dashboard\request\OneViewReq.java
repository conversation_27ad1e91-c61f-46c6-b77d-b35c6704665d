package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;

import com.bolt.dashboard.core.model.OneView;
import com.bolt.dashboard.core.model.OneViewChartOptions;

public class OneViewReq {
    private String projectName;
    private String user;

    
    private List<OneViewChartOptionsReq> metrics = new ArrayList<OneViewChartOptionsReq>();
    
       
    public OneView toOneView() {

    	OneView oneView = new OneView();

        for (OneViewChartOptionsReq oneViewChartOptionsReq : this.getMetrics()) {
        	oneView.setProjectName(this.getProjectName());
        	oneView.setUser(this.getUser());
        	OneViewChartOptions oneViewChartOptions=new OneViewChartOptions();
        	oneViewChartOptions.setChartId(oneViewChartOptionsReq.getChartId());
        	oneViewChartOptions.setCol(oneViewChartOptionsReq.getCol());
        	oneViewChartOptions.setDirective(oneViewChartOptionsReq.isDirective());
        	oneViewChartOptions.setMethod(oneViewChartOptionsReq.getMethod());
        	oneViewChartOptions.setName(oneViewChartOptionsReq.getName());
        	oneViewChartOptions.setTab(oneViewChartOptionsReq.getTab());
        	oneViewChartOptions.setRow(oneViewChartOptionsReq.getRow());
        	oneViewChartOptions.setSizeX(oneViewChartOptionsReq.getSizeX());
        	oneViewChartOptions.setSizeY(oneViewChartOptionsReq.getSizeY());
        	oneView.getGraphs().add(oneViewChartOptions);
        	
        }

        return oneView;
    }


	public String getProjectName() {
		return projectName;
	}


	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}


	public String getUser() {
		return user;
	}


	public void setUser(String user) {
		this.user = user;
	}

	public List<OneViewChartOptionsReq> getMetrics() {
		return metrics;
	}


	public void setMetrics(List<OneViewChartOptionsReq> metrics) {
		this.metrics = metrics;
	}

   
}
