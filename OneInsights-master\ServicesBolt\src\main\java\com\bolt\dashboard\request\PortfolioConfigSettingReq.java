package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;

import com.bolt.dashboard.core.model.PortfolioConfig;

public class PortfolioConfigSettingReq {
    private List<PortfolioConfigReq> metrics = new ArrayList<PortfolioConfigReq>();

    public List<PortfolioConfigReq> getMetrics() {
        return metrics;
    }

    public void setMetrics(List<PortfolioConfigReq> newmetrics) {
        this.metrics = newmetrics;
    }

    public PortfolioConfig toPortfolioConfigAddSetting() {

        PortfolioConfig portfolioConfig = new PortfolioConfig();

        for (PortfolioConfigReq portfolioConfigReq : this.getMetrics()) {
            portfolioConfig.setProjectName(portfolioConfigReq.getProjectName());
            portfolioConfig.setUserName(portfolioConfigReq.getUserName());
            portfolioConfig.setBusinessUnit(portfolioConfigReq.getBusinessUnit());
            portfolioConfig.setBusinessUnitId(portfolioConfigReq.getBusinessUnitId());
            portfolioConfig.setEndDate(portfolioConfigReq.getEndDate());
            portfolioConfig.setOrgId(portfolioConfigReq.getOrgId());
            portfolioConfig.setOrgName(portfolioConfigReq.getOrgName());
            portfolioConfig.setPortfolioID(portfolioConfigReq.getPortfolioID());
            portfolioConfig.setPortfolioName(portfolioConfigReq.getPortfolioName());
            portfolioConfig.setProductOwner(portfolioConfigReq.getProductOwner());
            portfolioConfig.setScrumMaster(portfolioConfigReq.getScrumMaster());
            portfolioConfig.setStartDate(portfolioConfigReq.getStartDate());
            portfolioConfig.setCronExpression(portfolioConfigReq.getCronExpression());
            portfolioConfig.setSchedulerEnabled(portfolioConfigReq.getSchedulerEnabled());
            portfolioConfig.setBdepId(portfolioConfigReq.getBdepId());

        }

        return portfolioConfig;
    }

}
