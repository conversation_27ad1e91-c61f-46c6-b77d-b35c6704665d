/**
 * 
 */
package com.bolt.dashboard.request;

import com.bolt.dashboard.core.model.ManageUser;
import com.bolt.dashboard.core.model.ProductLicenceConfig;

/**
 * <AUTHOR> organisationName :
 * 
 */
public class ProductLicenceRequest {
    private String projectName;
    private String expiryDate;
    private String organizationName;
    private String senNumber;
    private String licenceType;
    private int useerCount;
    private String licenceKey;
    
    public ProductLicenceConfig toProductLicenceRequest(ProductLicenceRequest req) {
    	ProductLicenceConfig productLicenceConfig=new ProductLicenceConfig();
		
    	productLicenceConfig.setExpiryDate(req.getExpiryDate());
    	productLicenceConfig.setLicenceKey(req.getLicenceKey());
    	productLicenceConfig.setLicenceType(req.getLicenceType());
    	productLicenceConfig.setOrganizationName(req.getOrganizationName());
    	productLicenceConfig.setProjectName(req.getProjectName());
    	productLicenceConfig.setSenNumber(req.getSenNumber());
    	productLicenceConfig.setUseerCount(req.getUseerCount());
    	
		return productLicenceConfig;
		
	}

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getSenNumber() {
        return senNumber;
    }

    public void setSenNumber(String senNumber) {
        this.senNumber = senNumber;
    }

    public String getLicenceType() {
        return licenceType;
    }

    public void setLicenceType(String licenceType) {
        this.licenceType = licenceType;
    }

    public int getUseerCount() {
        return useerCount;
    }

    public void setUseerCount(int useerCount) {
        this.useerCount = useerCount;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getLicenceKey() {
        return licenceKey;
    }

    public void setLicenceKey(String licenceKey) {
        this.licenceKey = licenceKey;
    }
}
