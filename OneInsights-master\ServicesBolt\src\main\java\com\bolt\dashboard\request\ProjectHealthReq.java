package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.annotation.Id;

import com.bolt.dashboard.core.model.ProjectHealth;
import com.bolt.dashboard.core.model.ProjectHealthApplicationPhase;
import com.bolt.dashboard.core.model.ProjectHealthConfig;

public class ProjectHealthReq {
	@Id
	private String id;
	private String projectName;
	private long timestamp;
	private String sprintName;
	private List<ProjectHealthApplicationPhase> applicationPhaseList = new ArrayList<>();
	private List<ProjectHealthConfig> config = new ArrayList<>();

	public ProjectHealth toProjectHealth(ProjectHealthReq req) {
		ProjectHealth projectHealth = new ProjectHealth();

		projectHealth.setApplicationPhaseList(req.getApplicationPhaseList());
		projectHealth.setConfig(req.getConfig());
		projectHealth.setId(req.getId());
		projectHealth.setProjectName(req.getProjectName());
		projectHealth.setSprintName(req.getSprintName());
		projectHealth.setTimestamp(req.getTimestamp());
		return projectHealth;

	}

	public String getSprintName() {
		return sprintName;
	}

	public void setSprintName(String sprintName) {
		this.sprintName = sprintName;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public List<ProjectHealthApplicationPhase> getApplicationPhaseList() {
		return applicationPhaseList;
	}

	public void setApplicationPhaseList(List<ProjectHealthApplicationPhase> applicationPhaseList) {
		this.applicationPhaseList = applicationPhaseList;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public List<ProjectHealthConfig> getConfig() {
		return config;
	}

	public void setConfig(List<ProjectHealthConfig> config) {
		this.config = config;
	}

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

}
