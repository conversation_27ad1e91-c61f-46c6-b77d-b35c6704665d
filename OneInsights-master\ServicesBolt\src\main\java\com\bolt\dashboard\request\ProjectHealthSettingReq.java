package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;

import com.bolt.dashboard.core.model.ProjectHealth;
import com.bolt.dashboard.core.model.ProjectHealthApplicationPhase;
import com.bolt.dashboard.core.model.ProjectHealthRuleSet;

public class ProjectHealthSettingReq {
	private String projectName;
	private boolean addFlag;
	private List<ProjectHealthReq> projecthealth = new ArrayList<ProjectHealthReq>();
	private ProjectHealthReq metrics;/* = new ArrayList<ProjectHealthReq>(); */

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public boolean isAddFlag() {
		return addFlag;
	}

	public void setAddFlag(boolean addFlag) {
		this.addFlag = addFlag;
	}

	public ProjectHealthReq getMetrics() {
		return metrics;
	}

	public void setMetrics(ProjectHealthReq metrics) {
		this.metrics = metrics;
	}

	/*public ProjectHealth toProjectAddSetting() {
		ProjectHealth projectModel = new ProjectHealth();
		ProjectHealthReq req = this.getMetrics();
		projectModel.setProjectName(req.getProjectName());
		projectModel.setSprintName(req.getSprintName());
		projectModel.setApplicationPhaseList(req.getApplicationPhaseList());
		projectModel.setConfig(req.getConfig());
		List<ProjectHealthApplicationPhase> phaseList = new ArrayList<>();
		for (ProjectHealthApplicationPhase projectHealthApplicationPhase : req.getApplicationPhaseList()) {
			List<ProjectHealthRuleSet> ruleSetList = new ArrayList<>();
			projectHealthApplicationPhase.getApplicationPhaseName();
			projectHealthApplicationPhase.getApplicationPhaseWeightage();
			for (ProjectHealthRuleSet ruleSet : projectHealthApplicationPhase.getRuleSet()) {
				ProjectHealthRuleSet ruleSetObj = new ProjectHealthRuleSet();
				ruleSetObj.setOperator(ruleSet.getOperator());
				ruleSetObj.setRuleName(ruleSet.getRuleName());
				ruleSetObj.setSelect(ruleSet.getSelect());
				ruleSetObj.setSprintName(ruleSet.getSprintName());
				ruleSetObj.setValue(ruleSet.getValue());
				ruleSetObj.setWeightage(ruleSet.getWeightage());

				ruleSetList.add(ruleSetObj);

			}

			phaseList.add(projectHealthApplicationPhase);
		}

		projectModel.setApplicationPhaseList(phaseList);
		return projectModel;
	}*/

	public List<ProjectHealthReq> getProjecthealth() {
		return projecthealth;
	}

	public void setProjecthealth(List<ProjectHealthReq> projecthealth) {
		this.projecthealth = projecthealth;
	}
}