/**
 * 
 */
package com.bolt.dashboard.request;

import com.bolt.dashboard.core.model.Retrospective;;

/**
 * <AUTHOR> organisationName :
 * 
 */
public class RetrospectiveReq {
	private int uniqueID;
	private String userName;
	private String projectName;
	private String sprintName;
	private String wentWell;
	private String wentBad;
	private String toContinue;
	private String happyMood;
	private String $$hashKey;

	public int getUniqueID() {
		return uniqueID;
	}

	public void setUniqueID(int uniqueID) {
		this.uniqueID = uniqueID;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getSprintName() {
		return sprintName;
	}

	public void setSprintName(String sprintName) {
		this.sprintName = sprintName;
	}

	public String getWentWell() {
		return wentWell;
	}

	public void setWentWell(String wentWell) {
		this.wentWell = wentWell;
	}

	public String getWentBad() {
		return wentBad;
	}

	public void setWentBad(String wentBad) {
		this.wentBad = wentBad;
	}

	public String getToContinue() {
		return toContinue;
	}

	public void setToContinue(String toContinue) {
		this.toContinue = toContinue;
	}

	public String getHappyMood() {
		return happyMood;
	}

	public void setHappyMood(String happyMood) {
		this.happyMood = happyMood;
	}

	public String get$$hashKey() {
		return $$hashKey;
	}

	public void set$$hashKey(String $$hashKey) {
		this.$$hashKey = $$hashKey;
	}

	public Retrospective toDetailsAddSetting(RetrospectiveReq req) {
		Retrospective details = new Retrospective();
		details.setUniqueID(req.getUniqueID());
		details.setUserName(req.getUserName());
		details.setProjectName(req.getProjectName());
		details.setSprintName(req.getSprintName());
		details.setWentWell(req.getWentWell());
		details.setWentBad(req.getWentBad());
		details.setToContinue(req.getToContinue());
		details.setHappyMood(req.getHappyMood());
		return details;
	}

}
