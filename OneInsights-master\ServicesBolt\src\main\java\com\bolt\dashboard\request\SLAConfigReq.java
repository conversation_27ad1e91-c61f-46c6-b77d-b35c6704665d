package com.bolt.dashboard.request;

import com.bolt.dashboard.core.model.SLAConfiguration;

public class SLAConfigReq {
	private String tier;
	private String projectName;
	private double storyPoint;
	private String[] responseState;
	private String[] resolveState;
	private String[] sevArray;
	private String respTime;
	private String resoTime;

	public String getRespTime() {
		return respTime;
	}

	public void setRespTime(String respTime) {
		this.respTime = respTime;
	}

	public String getResoTime() {
		return resoTime;
	}

	public void setResoTime(String resoTime) {
		this.resoTime = resoTime;
	}

	public String getTier() {
		return tier;
	}

	public void setTier(String tier) {
		this.tier = tier;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String[] getResponseState() {
		return responseState;
	}

	public void setResponseState(String[] responseState) {
		this.responseState = responseState;
	}

	public String[] getResolveState() {
		return resolveState;
	}

	public void setResolveState(String[] resolveState) {
		this.resolveState = resolveState;
	}

	public String[] getSevArray() {
		return sevArray;
	}

	public void setSevArray(String[] sevArray) {
		this.sevArray = sevArray;
	}

	public SLAConfiguration addSetting(SLAConfigReq req) {
		SLAConfiguration details = new SLAConfiguration();
		details.setProjectName(req.getProjectName());
		details.setTier(req.getTier());
		details.setSevArray(req.getSevArray());
		details.setStoryPoint(req.getStoryPoint());
		details.setResolveState(req.getResolveState());
		details.setResoTime(req.getResoTime());
		details.setRespTime(req.getRespTime());
		details.setResponseState(req.getResponseState());
		return details;
		
	}

	public double getStoryPoint() {
		return storyPoint;
	}

	public void setStoryPoint(double storyPoint) {
		this.storyPoint = storyPoint;
	}

}
