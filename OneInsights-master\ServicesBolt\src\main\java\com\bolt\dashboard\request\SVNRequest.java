package com.bolt.dashboard.request;

import javax.validation.constraints.NotNull;
import org.bson.types.ObjectId;

/**
 * 
 * <AUTHOR>
 *
 */
public class SVNRequest {
    @NotNull
    private ObjectId componentId;
    private String scType;
    private Integer max;
    private Integer numberOfDays;
    private Long dateBegins;
    private Long dateEnds;

    public String getScType() {
        return scType;
    }

    public void setScType(String scType) {
        this.scType = scType;
    }

    /**
     * @return the componentId
     */
    public ObjectId getComponentId() {
        return componentId;
    }

    /**
     * @param componentId
     *            the componentId to set
     */
    public void setComponentId(ObjectId componentId) {
        this.componentId = componentId;
    }

    /**
     * @return the max
     */
    public Integer getMax() {
        return max;
    }

    /**
     * @param max
     *            the max to set
     */
    public void setMax(Integer max) {
        this.max = max;
    }

    /**
     * @return the numberOfDays
     */
    public Integer getNumberOfDays() {
        return numberOfDays;
    }

    /**
     * @param numberOfDays
     *            the numberOfDays to set
     */
    public void setNumberOfDays(Integer numberOfDays) {
        this.numberOfDays = numberOfDays;
    }

    /**
     * @return the dateBegins
     */
    public Long getDateBegins() {
        return dateBegins;
    }

    /**
     * @param dateBegins
     *            the dateBegins to set
     */
    public void setDateBegins(Long dateBegins) {
        this.dateBegins = dateBegins;
    }

    /**
     * @return the dateEnds
     */
    public Long getDateEnds() {
        return dateEnds;
    }

    /**
     * @param dateEnds
     *            the dateEnds to set
     */
    public void setDateEnds(Long dateEnds) {
        this.dateEnds = dateEnds;
    }

    /**
     * 
     * @return
     */
    public boolean validDateRange() {
        return dateBegins != null || dateEnds != null;
    }
}
