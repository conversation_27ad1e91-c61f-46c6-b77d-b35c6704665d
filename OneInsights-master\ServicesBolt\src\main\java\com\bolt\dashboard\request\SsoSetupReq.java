package com.bolt.dashboard.request;

import com.bolt.dashboard.core.model.SsoSetup;

public class SsoSetupReq {
	private String clientId;
	private String authority;
	private String redirectUri;
	private String ssoType;
	
	public String getSsoType() {
		return ssoType;
	}
	public void setSsoType(String ssoType) {
		this.ssoType = ssoType;
	}
	
	public SsoSetup toSssoSetup() {
		SsoSetup ssoSetup= new SsoSetup();
		ssoSetup.setClientId(this.clientId);
		ssoSetup.setAuthority(this.authority);
		ssoSetup.setRedirectUri(this.redirectUri);
		ssoSetup.setSsoType(this.ssoType);
		return ssoSetup;
	}
	
	public String getClientId() {
		return clientId;
	}
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}
	public String getAuthority() {
		return authority;
	}
	public void setAuthority(String authority) {
		this.authority = authority;
	}
	public String getRedirectUri() {
		return redirectUri;
	}
	public void setRedirectUri(String redirectUri) {
		this.redirectUri = redirectUri;
	}
	
}
