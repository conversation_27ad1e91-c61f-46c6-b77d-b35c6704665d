package com.bolt.dashboard.request;

public class StackGradingReq {
    private String projectName;

    private String role;
    private StackRuleNameReq rulemetric = new StackRuleNameReq();
    private boolean select;

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public StackRuleNameReq getRulemetric() {
        return rulemetric;
    }

    public void setRulemetric(StackRuleNameReq rulemetric) {
        this.rulemetric = rulemetric;
    }

}
