package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import com.bolt.dashboard.core.model.StackRanking;
import com.bolt.dashboard.core.model.StackRankingMetrics;

public class StackRankingSettingReq {
    private static final Logger LOG = LogManager.getLogger(StackRankingSettingReq.class);
    private List<StackRankingReq> metric = new ArrayList<StackRankingReq>();

    public List<StackRankingReq> getMetric() {
        return metric;
    }

    public void setMetric(List<StackRankingReq> metric) {
        this.metric = metric;
    }

    public StackRanking toStackSetting() {
        LOG.info("inside toStackSetting---------->");
        StackRanking stackRanking = new StackRanking();
        for (StackRankingReq stackRankingReq : this.getMetric()) {
            stackRanking.setProjectName(stackRankingReq.getProjectName());
            stackRanking.setId(stackRankingReq.getId());

            StackRankingMetrics metrics = new StackRankingMetrics();
            metrics.setRuleName(stackRankingReq.getRuleName());
            metrics.setGoal(stackRankingReq.getGoal());
            metrics.setOperator(stackRankingReq.getOperator());
            metrics.setRole(stackRankingReq.getRole());
            metrics.setWeightage(stackRankingReq.getWeightage());
            stackRanking.getStackRankingMetrics().add(metrics);
        }
        return stackRanking;

    }
}
