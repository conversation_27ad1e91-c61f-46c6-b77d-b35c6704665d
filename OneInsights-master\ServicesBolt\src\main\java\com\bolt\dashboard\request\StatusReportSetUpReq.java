package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.bolt.dashboard.core.model.StatusReportModel;

public class StatusReportSetUpReq {
    private List<StatusReportReq> metric = new ArrayList<StatusReportReq>();

    public List<StatusReportReq> getMetric() {
	return metric;
    }

    public void setMetric(List<StatusReportReq> metric) {
	this.metric = metric;
    }

    @Autowired

    public List<StatusReportModel> toMailSetupSetting() {
	List<StatusReportModel> mailSetuplist = new ArrayList<>();

	for (StatusReportReq mailerReq : this.getMetric()) {
	    StatusReportModel mailSetup = new StatusReportModel();
	    mailSetup.setProjectName(mailerReq.getProjectName());
	    mailSetuplist.add(mailSetup);

	}
	return mailSetuplist;

    }
}
