package com.bolt.dashboard.request;

import java.util.Map;

import com.bolt.dashboard.core.model.TeamIndexConfiguration;
import com.bolt.dashboard.core.model.TeamIndexQuestion;

public class TeamIndexConfigurationReq {
	private String pName;
	private long timestamp;
	Map<String,TeamIndexQuestion> teamIndexQuestions;
	public String getpName() {
		return pName;
	}
	public void setpName(String pName) {
		this.pName = pName;
	}
	public Map<String, TeamIndexQuestion> getTeamIndexQuestions() {
		return teamIndexQuestions;
	}
	public void setTeamIndexQuestions(Map<String, TeamIndexQuestion> teamIndexQuestions) {
		this.teamIndexQuestions = teamIndexQuestions;
	}
	
	public TeamIndexConfiguration toTeamIndexConfig(TeamIndexConfigurationReq req) {
		TeamIndexConfiguration teamIndexConfig= new TeamIndexConfiguration();
		teamIndexConfig.setTeamIndexQuestions(req.getTeamIndexQuestions());
		teamIndexConfig.setpName(req.getpName());
		return teamIndexConfig;
	}
	public long getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}
	
}
