package com.bolt.dashboard.request;

public class TestToolReq {
    private long failCount;
    private long skipcount;
    private long totalcount;

    /**
     * @return the failCount
     */
    public long getFailCount() {
        return failCount;
    }

    /**
     * @param failCount
     *            the failCount to set
     */
    public void setFailCount(long failCount) {
        this.failCount = failCount;
    }

    /**
     * @return the skipcount
     */
    public long getSkipcount() {
        return skipcount;
    }

    /**
     * @param skipcount
     *            the skipcount to set
     */
    public void setSkipcount(long skipcount) {
        this.skipcount = skipcount;
    }

    /**
     * @return the totalcount
     */
    public long getTotalcount() {
        return totalcount;
    }

    /**
     * @param totalcount
     *            the totalcount to set
     */
    public void setTotalcount(long totalcount) {
        this.totalcount = totalcount;
    }
}
