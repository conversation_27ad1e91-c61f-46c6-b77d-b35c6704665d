package com.bolt.dashboard.request;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.bolt.dashboard.core.model.AssociatedUsers;
import com.bolt.dashboard.core.model.UserAssociation;

public class UserAssociationReq {
    private String pName;
    private Set<AssociatedUsers> users = new HashSet<>();
    
//    public UserAssociation toUserAssociation() {
//    	UserAssociation configuration = new UserAssociation();
//		for (UserAssosiationMetricsReq userAssosiationMetricsReq : this.getMetric()) {
//			configuration.setpName(this.getpName());
//			//configuration.setAlmType(this.almType);
//			AssociatedUsers associatedUsers = new AssociatedUsers();
//			associatedUsers.setAccess(userAssosiationMetricsReq.getAccess());
//			associatedUsers.setEmail(userAssosiationMetricsReq.getEmail());
//			associatedUsers.setIsTeamMember(userAssosiationMetricsReq.isFlag());
//			associatedUsers.setUserName(userAssosiationMetricsReq.getUserName());
//			configuration.getUsers().add(associatedUsers);
//
//		}
//
//		return configuration;
//	}
    


	public String getpName() {
		return pName;
	}



	public void setpName(String pName) {
		this.pName = pName;
	}



	public Set<AssociatedUsers> getUsers() {
		return users;
	}



	public void setUsers(Set<AssociatedUsers> users) {
		this.users = users;
	}
	
	


}
