package com.bolt.dashboard.request;

public class UserAssosiationMetricsReq {
    private String project;
    private String almType;
    private String userName;
    private String email;
    private String access;
    private boolean flag;
    private boolean fromService;

    public boolean isFlag() {
        return flag;
    }

    
    public String getAlmType() {
		return almType;
	}


	public void setAlmType(String almType) {
		this.almType = almType;
	}


	public void setFromService(boolean fromService) {
		this.fromService = fromService;
	}


	public void setFlag(boolean flag) {
        this.flag = flag;
    }

    public boolean isFromService() {
        return fromService;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAccess() {
        return access;
    }

    public void setAccess(String access) {
        this.access = access;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

}
