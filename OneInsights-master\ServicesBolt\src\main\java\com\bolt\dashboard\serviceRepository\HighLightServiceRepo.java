package com.bolt.dashboard.serviceRepository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.bo.HighLightServiceModel;

public interface HighLightServiceRepo extends CrudRepository<HighLightServiceModel, ObjectId> {
    List<HighLightServiceModel>findAll();
    HighLightServiceModel findByProjectName(String projectName);

}
