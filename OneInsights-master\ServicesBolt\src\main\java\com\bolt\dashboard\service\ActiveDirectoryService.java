/**
 * 
 */
package com.bolt.dashboard.service;

import java.util.List;

import com.bolt.dashboard.core.model.ActiveDirectoryConfiguration;
import com.bolt.dashboard.response.DataResponse;

/**
 * <AUTHOR>
 *
 */
public interface ActiveDirectoryService {

    ActiveDirectoryConfiguration saveJobDetails(ActiveDirectoryConfiguration activeDirectoryConfiguration);

    DataResponse<List<ActiveDirectoryConfiguration>> fetchJobDetails();

}
