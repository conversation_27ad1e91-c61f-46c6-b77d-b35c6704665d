/**
 * 
 */
package com.bolt.dashboard.service;

import java.util.List;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.ActiveDirectoryConfiguration;
import com.bolt.dashboard.core.repository.ADConfigurationRepo;
import com.bolt.dashboard.response.DataResponse;

/**
 * <AUTHOR>
 *
 */
@Service
public class ActiveDirectoryServiceImplementation implements ActiveDirectoryService {
    private ADConfigurationRepo adConfigurationRepo;
    private static final Logger LOG = LogManager.getLogger(ActiveDirectoryServiceImplementation.class);

    @Autowired
    public ActiveDirectoryServiceImplementation(ADConfigurationRepo repo) {
        this.adConfigurationRepo = repo;
    }

    @Override
//    @CacheEvict(value="fetchJobDetails", key ="'fetchJobDetails'", cacheManager="timeoutCacheManager")
    public ActiveDirectoryConfiguration saveJobDetails(ActiveDirectoryConfiguration req) {

        if (adConfigurationRepo.findByOrganisationName(req.getOrganisationName()) != null) {
            adConfigurationRepo.deleteByOrganisationName(req.getOrganisationName());
        }
        LOG.info("Active Directory Data saved successfully...");
        return adConfigurationRepo.save(req);

    }

    @Override
//    @Cacheable(value="fetchJobDetails", key ="'fetchJobDetails'", cacheManager="timeoutCacheManager")
    public DataResponse<List<ActiveDirectoryConfiguration>> fetchJobDetails() {
        long lastUpdate = 1;
        List<ActiveDirectoryConfiguration> result = adConfigurationRepo.findAll();
        if(!result.isEmpty())
        {
        return new DataResponse<List<ActiveDirectoryConfiguration>>(result, lastUpdate);
        }else{
        	LOG.info("Active Directory data not found in DB.....");
        	return null;
        }
    }

}
