package com.bolt.dashboard.service;

import java.util.List;
import java.util.Map;

import com.bolt.dashboard.core.model.ChangeHistoryModel;
import com.bolt.dashboard.core.model.ComponentBurnDown;
import com.bolt.dashboard.core.model.ComponentGroomingTable;
import com.bolt.dashboard.core.model.ComponentIssueBreakup;
import com.bolt.dashboard.core.model.ComponentSprintWiseStories;
import com.bolt.dashboard.core.model.ComponentStoryAgeing;
import com.bolt.dashboard.core.model.ComponentStoryProgress;
import com.bolt.dashboard.core.model.ComponentTaskRisk;
import com.bolt.dashboard.core.model.ComponentVelocityList;
import com.bolt.dashboard.core.model.DefectInsightData;
import com.bolt.dashboard.core.model.EffortHistoryModel;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MetricAgeData;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.ProjectModel;
import com.bolt.dashboard.core.model.ReleaseDetails;
import com.bolt.dashboard.core.model.TransitionModel;
import com.bolt.dashboard.util.BurnDownDataSprint;
import com.bolt.dashboard.util.DefectBacklog;
import com.bolt.dashboard.util.DefectDensity;
import com.bolt.dashboard.util.DefectParetoModel;
import com.bolt.dashboard.util.DefectProductionSlippage;
import com.bolt.dashboard.util.StoryProgressSprintwise;
import com.bolt.dashboard.util.TaskRiskSprint;

/**
 * <AUTHOR>
 *
 */
public interface AlmService {

	MonogOutMetrics getMetricDetails(String wId);

	List<MonogOutMetrics> getAllMetrics(String pName);

	List<ChangeHistoryModel> getChangesItems(String wId);

	List<TransitionModel> getTransitionsData(String wId);

	IterationOutModel getIterationData(String pName, String itrName, String almType);

	List<EffortHistoryModel> getEffortData(String wId);

	List<IterationOutModel> getProjectDetails(String pName, String almType);

	ProjectModel getDefectCounts(String pName, String almType);

	List<IterationOutModel> getCrtItr(String pName, String almType);

	ReleaseDetails getRelease(String projectName);

	List<MetricsModel> getUnReleaseData(String projectName, String almType);

	List<MetricsModel> getDefects(String projectName, String almType, String defect);

	List<IterationOutModel> getSlaData(String projectName, String almType, long createDate);

	List<IterationOutModel> getAssigneeIssues(String projectName, String almType, String[] members);

	List<IterationOutModel> getDateIterations(String projectName, String almType, long startDate, long endDate);

	List<MetricsModel> getProdDefects(String projectName, String almType, String defect, String whereFound,
			String whenFound);

	String delDuplicate(String projName);

	Map<Integer, List<IterationOutModel>> getCurrentProjectDetails(String pName, String almType);

	String delAllIssues(String projName);

	List<TransitionModel> getAllTransitions(String pName);

	List<ComponentVelocityList> getComponentVelocity(String projectName, boolean b);

	List<ComponentSprintWiseStories> getComponentsSprint(String projectName, boolean flag);

	Map<String, List> getIssueHierarchy(String projectName);

	Map<String, Map> getComponentWiseIssueHierarchy(String projectName);

	List<String> getComponents(String projectName, boolean b);

	void updateComponentsOfTaskandSubtask(String projectName);

	List<MonogOutMetrics> getFeatureMetrics(String pName, String almType);

	List<Map<String, List<String>>> getSprintProgressHome(String projName, String almType);

	List<Map<String, String>> getDefectsSummaryHome(String projName, String almType);

	List<ComponentTaskRisk> getTaskRisk(String projName, String almType, boolean storyPointBased);

	List<String> getActiveSprints(String projName, String almType);

	List<ComponentIssueBreakup> getIssueBrakeUp(String projName, String almType);

	List<ComponentStoryProgress> getStoryProgress(String projName, String almType);

	List<ComponentBurnDown> burndownCalculation(String projName, String almType, boolean storyPointBased);

	Map<String, List<DefectInsightData>> getDefectInsightData(String projName, String almType, boolean componentFlag);

	Map<String, List<DefectParetoModel>> defectParetoCalculation(String projName, String almType,
			boolean componentBased);

	Map<String, List<DefectProductionSlippage>> getProductionSlippage(String projName, String almType,
			boolean componentBased);

	Map<String, List<DefectDensity>> getDefectDensity(String projName, String almType, boolean componentBased);

	Map<String, DefectBacklog> getDefectBacklog(String projName, String almType, boolean componentBased);

	Map getDefectTrendAndClassification(String projName, String almType, boolean componentBased);

	 List<ComponentStoryAgeing> getStoryAgeingData(String projName, String almType);

	 List<ComponentGroomingTable> getGroomingTable(String projName, String almType);

	List<IterationModel> getAllIterations(String pName);

	Map getDefectClassification(String projName, String almType);

	String saveEngScore(String projectName, String month, double engScore);
	
	List<ComponentVelocityList> getComponentVelocityChart(String projectName, boolean b);

	List<ComponentSprintWiseStories> getComponentsSprintStories(String projectName);

	Map<String, List> getIssueHierarchyChart(String projectName);

	Map<String, Map> getComponentWiseIssueHierarchyChart(String projectName);

	List<String> getComponentsChart(String projectName);
	


}
