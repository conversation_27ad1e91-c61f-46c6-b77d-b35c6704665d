package com.bolt.dashboard.service;

import java.util.List;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.azureLogMonitor.AzureLogMonitorApplication;
import com.bolt.dashboard.core.model.AzureLogMonitorResource;

@Service
public class AzureLogMonitorServiceImplementation implements AzureLogMonitorService {

	@Override
	@Cacheable(value="getAzureLogdata", key ="'getAzureLogdata'+#projName", cacheManager="timeoutCacheManager")
	public List<AzureLogMonitorResource> getAzureLogdata(String projName) {
		System.out.println("No cache");
		return new AzureLogMonitorApplication().azureLogMonitorMain(projName);
	}
	
	
	

}
