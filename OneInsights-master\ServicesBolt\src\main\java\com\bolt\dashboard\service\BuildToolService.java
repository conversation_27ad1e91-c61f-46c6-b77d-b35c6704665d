package com.bolt.dashboard.service;

import java.util.List;
import java.util.Map;

import com.bolt.dashboard.core.model.BuildFailurePatternForProjectInJenkinsModel;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.GitlabValueStream;
import com.bolt.dashboard.core.model.ValueStreamStep;
import com.bolt.dashboard.request.BuildFailureRequest;
import com.bolt.dashboard.response.DataResponse;

/**
 * 
 * <AUTHOR>
 *
 */
public interface BuildToolService {
    Iterable<BuildTool> searchForTest(String buildType, String projectName);

    DataResponse<Iterable<BuildTool>> search(String projectName);

    DataResponse<Iterable<BuildTool>> searchJobList(String buildType, String projectName);

    DataResponse<Iterable<BuildTool>> search(String projectName, long sDate, long eDate,
            boolean flag);

    DataResponse<List<BuildFailureRequest>> buildFailurePattern(String toolName,
            List<BuildFailureRequest> failureRequestList);

    DataResponse<List<BuildFailurePatternForProjectInJenkinsModel>> fetchFailurePatternData(String projectName);

    DataResponse<List<BuildFailurePatternForProjectInJenkinsModel>> fetchBuildData();

	BuildTool getOneByProjectName(String projName);

	List<Map<String, String>> getBuildDetailsHome(String projName, String almType);

	List<ValueStreamStep> getValueStream(String proName);

	List<GitlabValueStream> getGitlabValueStream(String proName);
}
