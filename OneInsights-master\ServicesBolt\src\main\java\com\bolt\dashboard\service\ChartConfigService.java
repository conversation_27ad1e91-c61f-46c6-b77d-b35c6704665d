package com.bolt.dashboard.service;

import com.bolt.dashboard.core.model.ChartConfiguration;

public interface ChartConfigService {


	String saveChartDetailsForProject(ChartConfiguration data);
	//ChartConfiguration saveChartDetailsForProject(ChartConfiguration data);

	String updateChartDetails(ChartConfiguration data);

	ChartConfiguration getChartConfigForProject(String pName, String almType);

}
