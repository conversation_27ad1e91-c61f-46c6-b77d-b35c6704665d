package com.bolt.dashboard.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.ChartConfiguration;
import com.bolt.dashboard.core.repository.ChartConfigRepo;

@Service
public class ChartConfigServiceImplementation implements ChartConfigService {
	private ChartConfigRepo chartConfigRep;

	@Autowired
	public ChartConfigServiceImplementation(ChartConfigRepo chartConfigRep) {
		this.chartConfigRep = chartConfigRep;
	}

	@Override
//	@Cacheable(value="getChartConfigForProject", key ="'getChartConfigForProject'+#pName+#almType", cacheManager="timeoutCacheManager")
	public ChartConfiguration getChartConfigForProject(String pName, String almType) {
		ChartConfiguration result = chartConfigRep.findByPNameAndAlmType(pName, almType);
		return result;
	}


	@Override
//	@Caching(evict = {
//			@CacheEvict(value="getChartConfigForProject", key ="'getChartConfigForProject'+#data.getpName()+#data.getAlmType()", cacheManager="timeoutCacheManager"),
//			@CacheEvict(value="getChartDetails", key ="'getChartDetails'", cacheManager="timeoutCacheManager")
//		})
	public String saveChartDetailsForProject(ChartConfiguration data) {
		ChartConfiguration tempCall = chartConfigRep.findByPNameAndAlmType(data.getpName(), data.getAlmType());
		String message = null;
		if (tempCall != null) {
			chartConfigRep.delete(tempCall);
			message = "Data updated successfully.";
		}
		chartConfigRep.save(data);
		message = message == null ? "Data saved successfully." : message;
		return message;
	}
	
	/*@Override
	public ChartConfiguration saveChartDetailsForProject(ChartConfiguration data) {
		ChartConfiguration tempCall = chartConfigRep.findByPNameAndAlmType(data.getpName(), data.getAlmType());
		if (tempCall != null) {
			chartConfigRep.delete(tempCall);
		}
		return chartConfigRep.save(data);
		
	}*/

	@Override
	public String updateChartDetails(ChartConfiguration data) {
		return null;
	}

}
