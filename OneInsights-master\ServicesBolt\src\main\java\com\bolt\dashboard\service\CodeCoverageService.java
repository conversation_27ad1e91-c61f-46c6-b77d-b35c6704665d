package com.bolt.dashboard.service;

import java.util.Map;

import com.bolt.dashboard.core.model.CodeCoverageBO;
import com.bolt.dashboard.core.model.ProjectCodeCoverage;
import com.bolt.dashboard.request.CodeCoverageReq;
import com.bolt.dashboard.response.DataResponse;

public interface CodeCoverageService {
	DataResponse<Iterable<ProjectCodeCoverage>> search(CodeCoverageReq request);

	Map<String, CodeCoverageBO> searchLastRecord(String pName);

	DataResponse<String> searchFileCoverage(String pName);

	DataResponse<ProjectCodeCoverage> search(CodeCoverageReq request, String projectName);

	Map<String, ProjectCodeCoverage> searchLastRecordHeatCoverage(String pName);
}