package com.bolt.dashboard.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.CodeCoverageBO;
import com.bolt.dashboard.core.model.ProjectCodeCoverage;
import com.bolt.dashboard.core.repository.CodeCoverageRepository;
import com.bolt.dashboard.request.CodeCoverageReq;
import com.bolt.dashboard.response.DataResponse;
import com.itextpdf.text.pdf.codec.Base64;

@Service
public class CodeCoverageServiceImplementation implements CodeCoverageService {
	String fileName = "";
	FileInputStream fileInputStreamReader = null;
	FileOutputStream fileOut =null;
	private CodeCoverageRepository codeCoverageRepository;
	private static final Logger LOGGER = LogManager.getLogger(CodeCoverageServiceImplementation.class);

	@Autowired
	public CodeCoverageServiceImplementation(CodeCoverageRepository codeCoverageRepository) {
		this.codeCoverageRepository = codeCoverageRepository;
		try {
			File tempFile = File.createTempFile("codeCoverage", ".xls",new File("/coverage-report"));
			fileName = tempFile.getCanonicalFile().toString();
		} catch (Exception e) {
			LOGGER.info(e);
		}

	}

	@Override
	public DataResponse<Iterable<ProjectCodeCoverage>> search(CodeCoverageReq request) {
		long lastUpdated = 1;
		Iterable<ProjectCodeCoverage> result = codeCoverageRepository.findAll();
		return new DataResponse<Iterable<ProjectCodeCoverage>>(result, lastUpdated);
	}

	@Override
	public DataResponse<ProjectCodeCoverage> search(CodeCoverageReq request, String projectName) {
		long lastUpdated = 1;
		List<ProjectCodeCoverage> result = codeCoverageRepository.findByProjectName(projectName);
		if (!result.isEmpty()) {
			// Collections.sort(result);
			ProjectCodeCoverage lastRecord = result.get(result.size() - 1);
			return new DataResponse<ProjectCodeCoverage>(lastRecord, lastUpdated);
		} else {
			LOGGER.info("no data found in DB for project  " + projectName);
			return null;
		}
	}

	public ProjectCodeCoverage getLastData(String pName, String type, String flag) {
		ProjectCodeCoverage tempData = null;
		Query query = new Query();
		query.limit(1);
		query.addCriteria(new Criteria("projectName").is(pName).and("coverageType").is(type));
		query.with(new Sort(Sort.Direction.DESC, "timestamp"));
		if (flag.equals("some")) {
			query.fields().include("projectLinesValid").include("projectLevelTotalLines")
					.include("projectLevelLinesCovered").include("projectLevelTotalClasses")
					.include("projectLevelClassesCovered").include("greaterThanEightyCount")
					.include("greaterThanFiftyCount").include("greaterThanThirtyFiveCount")
					.include("betweenThirtyFiveCount").include("hundraedPercentaCount");
		} else if (flag.equals("file")) {
			query.fields().include("fileCoverage");
		}
		try {
			List<ProjectCodeCoverage> list = DataConfig.getInstance().mongoTemplate().find(query,
					ProjectCodeCoverage.class);
			if (!list.isEmpty()) {
				tempData = list.get(0);
			}
		} catch (Exception e) {
		LOGGER.error(e.getMessage());	
		}
		return tempData;

	}

	@Override
//	@Cacheable(value="searchLastRecord", key ="'searchLastRecord'+#pName", cacheManager="timeoutCacheManager")
	public Map<String, CodeCoverageBO> searchLastRecord(String pName) {
		Map<String, CodeCoverageBO> overAllCodeCoverage = new HashMap<String, CodeCoverageBO>();
		ProjectCodeCoverage backendData = getLastData(pName, "Backend", "some");
		ProjectCodeCoverage uiData = getLastData(pName, "UI", "some");
		CodeCoverageBO backEndCodeCoverage = prepareData(new CodeCoverageBO(), backendData);
		overAllCodeCoverage.put("backEnd", backEndCodeCoverage);
		CodeCoverageBO uiCodeCoverage = prepareData(new CodeCoverageBO(), uiData);
		overAllCodeCoverage.put("ui", uiCodeCoverage);
		overAllCodeCoverage.put("overall", getProjectCodeCoverageDetails(backEndCodeCoverage, uiCodeCoverage));
		return overAllCodeCoverage;
	}

	public CodeCoverageBO getProjectCodeCoverageDetails(CodeCoverageBO backEnd, CodeCoverageBO ui) {
		CodeCoverageBO coverageData = new CodeCoverageBO();
		if (!(backEnd == null) && !(ui == null)) {
			coverageData.setProjectLevelTotalClasses(
					backEnd.getProjectLevelTotalClasses() + ui.getProjectLevelTotalClasses());
			coverageData.setProjectLevelClassesCovered(
					backEnd.getProjectLevelClassesCovered() + ui.getProjectLevelTotalClasses());
			coverageData
					.setProjectLevelTotalLines(backEnd.getProjectLevelTotalLines() + ui.getProjectLevelTotalLines());
			coverageData.setProjectLevelLinesCovered(
					backEnd.getProjectLevelLinesCovered() + ui.getProjectLevelLinesCovered());
			coverageData
					.setGreaterThanEightyCount(backEnd.getGreaterThanEightyCount() + ui.getGreaterThanEightyCount());
			coverageData.setHundraedPercentaCount(backEnd.getHundraedPercentaCount() + ui.getHundraedPercentaCount());
			coverageData.setGreaterThanFiftyCount(backEnd.getGreaterThanFiftyCount() + ui.getGreaterThanFiftyCount());
			coverageData.setGreaterThanThirtyFiveCount(
					backEnd.getGreaterThanThirtyFiveCount() + ui.getGreaterThanThirtyFiveCount());
			coverageData
					.setBetweenThirtyFiveCount(backEnd.getBetweenThirtyFiveCount() + ui.getBetweenThirtyFiveCount());
			return coverageData;
		} else {
			LOGGER.info("Exception in getProjectCodeCoverageDetails() codecoverageController ....");
			return null;
		}

	}

	public CodeCoverageBO prepareData(CodeCoverageBO newCodeCoverage, ProjectCodeCoverage dataFromDb) {
		if (!(newCodeCoverage == null) && !(dataFromDb == null)) {
			newCodeCoverage.setProjectLevelTotalClasses(dataFromDb.getProjectLevelTotalClasses());
			newCodeCoverage.setProjectLevelClassesCovered(dataFromDb.getProjectLevelClassesCovered());
			newCodeCoverage.setProjectLevelTotalLines(dataFromDb.getProjectLevelTotalLines());
			newCodeCoverage.setProjectLevelLinesCovered(dataFromDb.getProjectLevelLinesCovered());
			newCodeCoverage.setGreaterThanEightyCount(dataFromDb.getGreaterThanEightyCount());
			newCodeCoverage.setHundraedPercentaCount(dataFromDb.getHundraedPercentaCount());
			newCodeCoverage.setGreaterThanFiftyCount(dataFromDb.getGreaterThanFiftyCount());
			newCodeCoverage.setGreaterThanThirtyFiveCount(dataFromDb.getGreaterThanThirtyFiveCount());
			newCodeCoverage.setBetweenThirtyFiveCount(dataFromDb.getBetweenThirtyFiveCount());
			newCodeCoverage.setProjectLinesValid(dataFromDb.getProjectLinesValid());
			return newCodeCoverage;
		} else {
			LOGGER.info("Exception in prepareData() codecoverageController ....");
			return null;
		}
	}

	@Override
//	@Cacheable(value="searchFileCoverage", key ="'searchFileCoverage'+#pName", cacheManager="timeoutCacheManager")
	public DataResponse<String> searchFileCoverage(String pName) {
		ProjectCodeCoverage backendData = getLastData(pName, "Backend", "file");
		ProjectCodeCoverage uiData = getLastData(pName, "UI", "file");
		Map<String, Double> mergedMaps = new LinkedHashMap<>();
		if (backendData != null)
			mergedMaps.putAll(backendData.getFileCoverage());

		if (uiData != null)
			mergedMaps.putAll(uiData.getFileCoverage());
		try {
			return new DataResponse<String>(csvWriter(mergedMaps), 1);
		} catch (IOException e) {
			
			LOGGER.error(e.getMessage());
		}
		return null;
	}

	@Override
//	@Cacheable(value="searchLastRecordHeatCoverage", key ="'searchLastRecordHeatCoverage'+#pName", cacheManager="timeoutCacheManager")
	public Map<String, ProjectCodeCoverage> searchLastRecordHeatCoverage(String pName) {
		ProjectCodeCoverage backendData = getLastData(pName, "Backend", "All");
		ProjectCodeCoverage uiData = getLastData(pName, "UI", "All");
		Map<String, ProjectCodeCoverage> map = new HashMap<String, ProjectCodeCoverage>();
		map.put("backEnd", backendData);
		map.put("ui", uiData);
		return map;
	}

	@SuppressWarnings({ "rawtypes", "deprecation" })
	public String csvWriter(Map<String, Double> listOfMap) throws IOException {
		try {
			fileOut = new FileOutputStream(fileName);
			HSSFWorkbook workbook = new HSSFWorkbook();
			HSSFSheet worksheet = workbook.createSheet("File Coverage");

			Iterator it = listOfMap.entrySet().iterator();
			int rowNum = 0;
			HSSFRow row1 = worksheet.createRow((short) rowNum);

			HSSFCell cellA1 = row1.createCell((short) 0);
			cellA1.setCellValue("File Name");

			HSSFCell cellB1 = row1.createCell((short) 1);
			cellB1.setCellValue("Lines Covered(%)");

			while (it.hasNext()) {
				rowNum++;
				Map.Entry pair = (Map.Entry) it.next();
				String key = pair.getKey().toString();
				double value = Double.parseDouble(pair.getValue().toString());
				HSSFRow row = worksheet.createRow((short) rowNum);

				HSSFCell cellA2 = row.createCell((short) 0);
				cellA2.setCellValue(key);

				HSSFCell cellB2 = row.createCell((short) 1);
				cellB2.setCellValue(value);

			}
			workbook.write(fileOut);
			fileOut.flush();
			
			
			return convertCsvToBase64();

		} catch (Exception e) {
			LOGGER.info(e);
			return null;

		}finally {
			fileOut.close();
		}
		
	}

	@SuppressWarnings("resource")
	public String convertCsvToBase64() {
		File originalFile = new File(fileName);
		String encodedBase64 = null;
		try {
			fileInputStreamReader = new FileInputStream(originalFile);
			byte[] bytes = new byte[(int) originalFile.length()];
			//fileInputStreamReader.read(bytes);
			int count=0;
			while((count=fileInputStreamReader.read(bytes))>0)
			encodedBase64 = new String(Base64.encodeBytes(bytes));

			return encodedBase64;
		} catch (FileNotFoundException e) {
			LOGGER.info(e);
			return null;
		} catch (IOException e) {
			LOGGER.info(e);
			return null;
		}
	}
}