package com.bolt.dashboard.service;

import java.util.List;
import java.util.Map;

import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.request.CodeQualityReq;
import com.bolt.dashboard.response.DataResponse;

public interface CodeQualityService {
    DataResponse<Iterable<CodeQuality>> search(CodeQualityReq request, String projectName, long sDate, long eDate,
            boolean flag);

    DataResponse<Iterable<CodeQuality>> search(CodeQualityReq request, String projectName);
    DataResponse<CodeQuality> lastRecord(CodeQualityReq request, String projectName);
    DataResponse<String> searchJson(String url, String userName, String password);

	List<Map<String, String>> getCodeQualityHome(String projName, String almType);
}
