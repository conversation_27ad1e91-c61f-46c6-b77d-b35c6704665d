package com.bolt.dashboard.service;

/**
 *
 */
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.core.repository.CodeQualityRep;
import com.bolt.dashboard.request.CodeQualityReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.sonar.SonarClientImplementation;
import com.bolt.dashboard.util.ProjectHomeCalculation;
import com.google.gson.Gson;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import com.mongodb.util.JSON;

@Service
public class CodeQualityServiceImplementation implements CodeQualityService {
    private CodeQualityRep codeQualityRepository;
    private static final Logger LOG = LogManager.getLogger(CodeQualityServiceImplementation.class);
    DBCursor cursor = null;

    @Autowired
    public CodeQualityServiceImplementation(CodeQualityRep codeQualityRepository) {
	this.codeQualityRepository = codeQualityRepository;
    }

    public CodeQualityServiceImplementation() {
    }

    @Override
    public DataResponse<Iterable<CodeQuality>> search(CodeQualityReq request, String projectName) {
	long lastUpdated = 1;

	Iterable<CodeQuality> result = codeQualityRepository.findByName(projectName);
	return new DataResponse<Iterable<CodeQuality>>(result, lastUpdated);
    }

    @Override
//    @Cacheable(value="CodeQualitylastRecord", key ="'CodeQualitylastRecord'+#request+#projectName", cacheManager="timeoutCacheManager")
    public DataResponse<CodeQuality> lastRecord(CodeQualityReq request, String projectName) {
	long lastUpdated = 1;
	CodeQuality quality = null;
	try {
	    DBCollection collectionName = DataConfig.getInstance().mongoTemplate().getCollection("Code_Quality");
	    DBObject dbObject = new BasicDBObject("name", projectName);
	    cursor = collectionName.find(dbObject);
	    cursor.sort(new BasicDBObject("$natural", -1)).limit(1);
	    while (cursor.hasNext()) {
		DBObject dbObject1 = cursor.next();
		JSONObject jsonObject = new JSONObject(JSON.serialize(dbObject1));
		Gson gson = new Gson();
		quality = gson.fromJson(jsonObject.toString(), CodeQuality.class);

	    }
	} catch (Exception e) {
	    LOG.info(e);
	}

	return new DataResponse<CodeQuality>(quality, lastUpdated);
    }

    @Override
//    @Cacheable(value="CodeQualitysearch", key ="'CodeQualitysearch'+#request+#projectName+#sDate+#eDate+#flag", cacheManager="timeoutCacheManager")
    public DataResponse<Iterable<CodeQuality>> search(CodeQualityReq request, String projectName, long sDate,
	    long eDate, boolean flag) {
	boolean flagnew = flag;
	if (!flagnew) {
	    return search(request, projectName);
	} else {

	    Collection<CodeQuality> list = codeQualityUpdatedData(projectName, sDate, eDate);

	    long lastUpdate = 1;

	    return new DataResponse<Iterable<CodeQuality>>(list, lastUpdate);

	}

    }

    public List<CodeQuality> codeQualityUpdatedData(String projectName, long sDate, long eDate) {

	List<CodeQuality> list = new ArrayList<>();
	try {

	    JSONObject jsonObject = null;

	    DBCollection collectionName = DataConfig.getInstance().mongoTemplate().getCollection("Code_Quality");
	    DBObject dbObject = new BasicDBObject("name", projectName);
	    cursor = collectionName.find(dbObject);
	    cursor.sort(new BasicDBObject("$natural", -1)).limit(1000);
	    while (cursor.hasNext()) {
		DBObject dbObject1 = cursor.next();
		jsonObject = new JSONObject(JSON.serialize(dbObject1));
		Gson gson = new Gson();
		CodeQuality quality = gson.fromJson(jsonObject.toString(), CodeQuality.class);

		if (quality.getTimestamp() >= sDate && quality.getTimestamp() <= eDate) {
		    list.add(quality);
		}

	    }
	    return list;
	} catch (Exception e) {
	    LOG.info(e);
	    return list;
	}

    }

    @Override
    public DataResponse<String> searchJson(String instanceURL, String userName, String password) {
	String url = "";
	String projectCode = "";
	JSONArray jsonArray = null;
	long lastUpdate = 1;
	String[] projectSeparationString = null;
	String[] projectCodeSeparationString = null;
	if (instanceURL.contains("/")) {
	    projectSeparationString = instanceURL.split(Pattern.quote("/"));
	    projectCode = projectSeparationString[projectSeparationString.length - 1];
	    if (projectCode.contains("id")) {
		projectCodeSeparationString = projectCode.split(Pattern.quote("="));
		projectCode = projectCodeSeparationString[projectCodeSeparationString.length - 1];

		url = instanceURL.replace("dashboard?id=", "api/resources?format=json&resource=");
	    } else {
		url = instanceURL.replace("dashboard/index/", "api/resources?format=json&resource=");
	    }
	}

	try {

	    url = url + ConstantVariable.SONAR_METRICS;
	    ResponseEntity<String> response = new SonarClientImplementation().makeRestCall(url, userName, password);
	    jsonArray = (JSONArray) new JSONTokener(response.getBody()).nextValue();

	    LOG.info("End of searchJson");
	    return new DataResponse<String>(jsonArray.toString(), lastUpdate);
	} catch (Exception e) {
	    LOG.error(e);
	    return null;
	}

    }

	@Override
	@Cacheable(value="getCodeQualityHome", key ="'getCodeQualityHome'+#projName+#almType")
	public List<Map<String, String>> getCodeQualityHome(String projName, String almType) {
		
		ProjectHomeCalculation projHomeCalc = new ProjectHomeCalculation();
		return projHomeCalc.getCodeQualityData(projName, almType);
	}

}
