package com.bolt.dashboard.service;

import com.bolt.dashboard.core.model.CollaborativeMailModel;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.serviceModel.MailStatusModel;

public interface CollaborativeMailService {
    public DataResponse<MailStatusModel> sendMail(CollaborativeMailModel model);

    DataResponse<Iterable<CollaborativeMailModel>> search(String projectName);

    public DataResponse<MailStatusModel> saveMailDetails(CollaborativeMailModel req);

	public String deleteActionList(String projectName, String actionId);
}
