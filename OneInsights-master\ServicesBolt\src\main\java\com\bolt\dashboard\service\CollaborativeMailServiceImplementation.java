package com.bolt.dashboard.service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Properties;
import java.util.regex.Pattern;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.CollaborativeMailModel;
import com.bolt.dashboard.core.model.MailSetup;
import com.bolt.dashboard.core.repository.CollaborativeMailRepo;
import com.bolt.dashboard.core.repository.MailSetupRepo;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.serviceModel.MailStatusModel;

@Service
public class CollaborativeMailServiceImplementation implements CollaborativeMailService {
    CollaborativeMailRepo collaborativeMailRepo;
    private static final Logger LOG = LogManager.getLogger(CollaborativeMailServiceImplementation.class);
    AnnotationConfigApplicationContext ctx = null;
    MailSetup mailSetup;
    public CollaborativeMailServiceImplementation() {
	ctx = DataConfig.getContext();
	this.collaborativeMailRepo = ctx.getBean(CollaborativeMailRepo.class);
    }

    @Autowired
    public CollaborativeMailServiceImplementation(CollaborativeMailRepo repo) {

	this.collaborativeMailRepo = repo;
    }

    @Override
    public DataResponse<Iterable<CollaborativeMailModel>> search(String projectName) {
	long lastUpdate = 1;

	LOG.info("In Se arch");
	Iterable<CollaborativeMailModel> result = collaborativeMailRepo.findByProjectName(projectName);

	return new DataResponse<Iterable<CollaborativeMailModel>>(result, lastUpdate);
    }

    @SuppressWarnings("null")
    @Override
    public DataResponse<MailStatusModel> sendMail(CollaborativeMailModel model) {
	String dwnldFolder = System.getProperty("user.dir");
	FileOutputStream fo = null;
	long lastUpdate = 1;
	String[] smtpFieldsArray = new String[6];
	AnnotationConfigApplicationContext context = new AnnotationConfigApplicationContext(DataConfig.class);
	MailSetupRepo repo = context.getBean(MailSetupRepo.class);
	List<MailSetup> mailSetupModelList = repo.findAll();
	Iterator<MailSetup> it = mailSetupModelList.iterator();
	while (it.hasNext()) {

	    mailSetup = it.next();
	    smtpFieldsArray[0] = mailSetup.getHost();
	    smtpFieldsArray[1] = mailSetup.getPort();
	    smtpFieldsArray[2] = mailSetup.getUserName();
	    smtpFieldsArray[3] = mailSetup.getPassword();
	    smtpFieldsArray[4] = String.valueOf(mailSetup.isStarttls());
	}
	LOG.info("Saving to db");
	  model.setTimeStamp(new Date().getTime());
	    saveMailDetails(model);
	LOG.info("In send mail");
	MailStatusModel messageSend = new MailStatusModel();
	Properties props = new Properties();
	props.put(ConstantVariable.MAIL_SMTP_AUTH, smtpFieldsArray[4]);
	props.put(ConstantVariable.MAIL_SMTP_ENABLE, smtpFieldsArray[4]);
	props.put(ConstantVariable.MAIL_SMTP_HOST, smtpFieldsArray[0]);
	props.put(ConstantVariable.MAIL_SMTP_PORT, smtpFieldsArray[1]);
	props.put(ConstantVariable.MAIL_SMTP_SSL_TRUST,smtpFieldsArray[0]);
	props.setProperty(ConstantVariable.MAIL_SMTP_SSL_PROTOCOLS, "TLSv1.2");
	LOG.info("property file updated.....");
	Session session = Session.getInstance(props, new javax.mail.Authenticator() {
	    protected PasswordAuthentication getPasswordAuthentication() {
		return new PasswordAuthentication(smtpFieldsArray[2], smtpFieldsArray[3]);
	    }
	});
	LOG.info("Successfully Authenticated.........");

	try {
	    MimeMessage message = new MimeMessage(session);
	    message.setFrom(new InternetAddress(mailSetup.getUserName()));

	    byte[] decodes = Base64.getMimeDecoder().decode(model.getFileName());

	    fo = new FileOutputStream(dwnldFolder + "\\test215.png");
	    fo.write(decodes);

	    message.setSubject("Action Report from BOLT");

	    MimeMultipart multipart = new MimeMultipart("related");

	    BodyPart messageBodyPart = new MimeBodyPart();
	    if (model.getToAddress().contains(";")) {
		String[] toAddressCollection = model.getToAddress().split(";");
		StringBuilder toAddress = new StringBuilder();
		for (String to : toAddressCollection) {
		    toAddress.append(to + ",");
		    message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(toAddress.toString()));
		}
	    } else {
		message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(model.getToAddress()));
	    }

	    String[] toAddress = model.getToAddress().split(Pattern.quote("."));
	    String htmlText="";
	    if(model.getFileName().equals("")){
	    	 htmlText = /*"Hi " + toAddress[0] + ",\n" +*/ model.getComment();
	    }
	    else{
	    	  htmlText = /*"Hi " + toAddress[0] + ",\n" +*/ model.getComment() + "\n" + "<img src=\"cid:image\">";
	    }
	   
	    messageBodyPart.setContent(htmlText, "text/html");
	    multipart.addBodyPart(messageBodyPart);

	    messageBodyPart = new MimeBodyPart();
	    DataSource fds = new FileDataSource(dwnldFolder + "\\test215.png");

	    messageBodyPart.setDataHandler(new DataHandler(fds));
	    messageBodyPart.setHeader("Content-ID", "<image>");

	    multipart.addBodyPart(messageBodyPart);
	    LOG.info("All datad addd to Mutltipart....");
	    message.setContent(multipart);

	    new Thread(new Runnable() {
		public void run() {
		    try {
			Transport.send(message);
			LOG.info("mail sent successfully");
		    } catch (MessagingException e) {
			LOG.info(e);
		    }
		}
	    }).start();

	    LOG.info("Mail put in queue");
	  

	} catch (Exception e) {
	    LOG.info(e);
	} finally {

	    try {
		if (fo != null) {
		    fo.close();
		}

	    } catch (IOException e) {
		LOG.info(e);
	    }
	}
	return new DataResponse<MailStatusModel>(messageSend, lastUpdate);

    }

    @Override
    public DataResponse<MailStatusModel> saveMailDetails(CollaborativeMailModel model) {
	AnnotationConfigApplicationContext context = new AnnotationConfigApplicationContext(DataConfig.class);

	CollaborativeMailRepo repo = context.getBean(CollaborativeMailRepo.class);

	if (repo.findByProjectNameAndActionIdAndChartName(model.getProjectName(), model.getActionId(),
		model.getChartName()) != null) {
	    repo.deleteByProjectNameAndActionIdAndChartName(model.getProjectName(), model.getActionId(),
		    model.getChartName());
	}
	repo.save(model);
	return null;

    }


	@Override
	public String deleteActionList(String projectName, String actionId) {
		
		int val=0;
		AnnotationConfigApplicationContext context = new AnnotationConfigApplicationContext(DataConfig.class);

		CollaborativeMailRepo repo = context.getBean(CollaborativeMailRepo.class);
		if (repo.findByProjectNameAndActionId(projectName,actionId) != null) {
			    val=repo.deleteByProjectNameAndActionId(projectName,actionId);
			}
		
		if(val==1){
			return "Action Item Deleted Successfully";
		}
		else{
		return "Action Item is not deleted";
		}
		}

}