/**
 * 
 */
package com.bolt.dashboard.service;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.CollectorLastRun;
import com.bolt.dashboard.core.repository.CollectorLastRunRepo;

/**
 * <AUTHOR>
 *
 */
@Service
public class CollectorLastRunServiceImplementation implements CollectorLastRunService {
	private CollectorLastRunRepo collectorRepo;

	@Autowired
	public CollectorLastRunServiceImplementation(CollectorLastRunRepo repo) {
		this.collectorRepo = repo;
	}

	@Override
	public Map<String, String[]> fetchData(String projectName) {
		CollectorLastRun result = collectorRepo.findByProjectName(projectName);

		return result.getLastRun();

	}

}
