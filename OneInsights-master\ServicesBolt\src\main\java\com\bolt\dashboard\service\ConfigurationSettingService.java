package com.bolt.dashboard.service;

import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.response.DataResponse;

public interface ConfigurationSettingService {
    DataResponse<Iterable<ConfigurationSetting>> getConfig();

    ConfigurationSetting addConfig(ConfigurationSetting req);

    public int deleteConfig(ConfigurationSetting configurationSetting);

	boolean deleteAllCollections(String projectName);

	boolean deleteProject(String projectName);

	 ConfigurationSetting getConfigProject(String pName);

}
