package com.bolt.dashboard.service;

import java.io.IOException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.bo.ActiveDirectoryBO;
import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.ManageUser;
import com.bolt.dashboard.core.repository.ADConfigurationRepo;
import com.bolt.dashboard.core.repository.UserRepo;

@Service
public class CustomUserDetailService implements UserDetailsService {
    @Autowired
    private UserRepo loginRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private ProductLicenceService productLicenceService;
    @Autowired
    private ADConfigurationRepo repo;
    static String fullName = null;
    String userEmailId = null;
    String activeState = "Active";
    private static final Logger LOG = LogManager.getLogger(CustomUserDetailService.class);
    
    public boolean checkExpiry(String key) {
	try {

	    String info = new ProductLicenceServiceImplementation().decrypt(key);
	    Date expiryDate = null;
	    if (info.contains("@#")) {
		String expDate = info.split(Pattern.quote("@#"))[0];
		DateFormat fromDF = new SimpleDateFormat("dd/MMM/yyyy");
		fromDF.setLenient(false);
		expiryDate = fromDF.parse(expDate);

		if (expiryDate.before(new Date())) {
		    return false;
		}
	    } else {
		return false;
	    }
	} catch (InvalidKeyException | NoSuchAlgorithmException | InvalidKeySpecException | NoSuchPaddingException
		| InvalidAlgorithmParameterException | IllegalBlockSizeException | BadPaddingException | IOException
		| ParseException e) {
	    LOG.info(e);
	    return false;
	}
	return true;
    }
    
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        List<ManageUser> result = loginRepository.findByUserName(username);
    	boolean deleteMsgFlag =false;
    	boolean checkExpiryFlag =false;
    	for (ManageUser login : result) {
    	    String dbUserName = login.getUserName();
    	    String dbUserpassword = login.getPassword();
    	    String dbUserStatus = login.getStatus();
    	    String userRole = login.getUserRole();
    	    if (dbUserName.equalsIgnoreCase(username)) {
    	    	if (!activeState.equalsIgnoreCase(dbUserStatus) || login.getDeleteFlag()) {
    	    		deleteMsgFlag=true;
    	    	}
    		if (activeState.equalsIgnoreCase(dbUserStatus) && !login.getDeleteFlag()) {
    			
    		    checkExpiryFlag = checkExpiry(login.getLicenceKey());
    		    if (checkExpiryFlag) {
    		    	Collection<SimpleGrantedAuthority> auth=new ArrayList<>();
    		    	auth.add(new SimpleGrantedAuthority(userRole));
    		        return new org.springframework.security.core.userdetails.User(dbUserName,dbUserpassword,auth);
    		    } 
              } 
    	    }else {
    	    	throw new UsernameNotFoundException("Check your Username/Password");
    	    }
    	}
    	if(deleteMsgFlag) {
    		throw new UsernameNotFoundException("Access Denied. Kindly contact BrillioOne.ai insights team to activate or create user id.");
    	}
    	if(!checkExpiryFlag) {
    		throw new UsernameNotFoundException("Your product lincese has expired. Contact BrillioOne.ai insights team");
    	}
    	throw new UsernameNotFoundException("Check your Username/Password");
    }
}
