/**
 * 
 */
package com.bolt.dashboard.service;

import java.util.TreeMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.DefectPainpoint;
import com.bolt.dashboard.core.repository.DefectPainpointRepo;

/**
 * <AUTHOR>
 *
 */
@Service
public class DefectPainpointServiceImplementation implements DefectPainpointService {
	private DefectPainpointRepo painPointRepo;
	DefectPainpoint model = null;

	@Autowired
	public DefectPainpointServiceImplementation(DefectPainpointRepo repo) {
		this.painPointRepo = repo;
	}

	@Override
//	@Cacheable(value="DefectPainpointfetchData", key ="'DefectPainpointfetchData'+#projectName", cacheManager="timeoutCacheManager")
	public DefectPainpoint fetchData(String projectName) {
		DefectPainpoint result = painPointRepo.findByProjectName(projectName);

		return result;

	}

	@Override
//	@CacheEvict(value="DefectPainpointfetchData", key ="'DefectPainpointfetchData'+#defectPainpoint.getProjectName()", cacheManager="timeoutCacheManager")
	public boolean saveData(DefectPainpoint defectPainpoint) {
		boolean result = false;
		TreeMap<Long,Double> oldThreshold=null;
		TreeMap<Long, Double> newThreshold=null;
		try {
		model = painPointRepo.findByProjectName(defectPainpoint.getProjectName());
		
		if (model == null) {
			painPointRepo.save(defectPainpoint);
			return true;
		} else {
			
			oldThreshold=model.getPpThreshold();
			newThreshold=new TreeMap<>();
			if(oldThreshold!=null)
			newThreshold.putAll(oldThreshold);
			if(defectPainpoint.getPpThreshold()!=null)
			newThreshold.putAll(defectPainpoint.getPpThreshold());
			defectPainpoint.setPpThreshold(newThreshold);
			if(model!=null)
			painPointRepo.delete(model);
			painPointRepo.save(defectPainpoint);
			return true;
		}
		}catch (Exception e) {
			// TODO: handle exception
			return result;
		}

	}

	@Override
//	@Cacheable(value="DefectPainpointgetPpData", key ="'DefectPainpointgetPpData'", cacheManager="timeoutCacheManager")
	public Iterable<DefectPainpoint> getPpData() {
		return painPointRepo.findAll();
	}

}
