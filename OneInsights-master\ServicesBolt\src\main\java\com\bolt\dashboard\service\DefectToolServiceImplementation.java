package com.bolt.dashboard.service;

import java.util.List;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.SmartTestDefectTool;
import com.bolt.dashboard.core.repository.DefectRepo;
import com.bolt.dashboard.response.DataResponse;

@Service
public class DefectToolServiceImplementation implements DefectToolService {
	private DefectRepo repository;
	 private static final Logger LOG = LogManager.getLogger(DefectToolServiceImplementation.class);

	public DefectToolServiceImplementation() {

	}

	@Autowired
	public DefectToolServiceImplementation(DefectRepo repo) {

		this.repository = repo;
	}

	@Override
	public DataResponse<SmartTestDefectTool> search(String projectName, String defectType) {
		long lastUpdate = 1;

		List<SmartTestDefectTool> data = (List<SmartTestDefectTool>) repository
				.findByProjectNameAndDefectType(projectName, defectType);
		if (!data.isEmpty()) {
			SmartTestDefectTool lastRecord = data.get(data.size() - 1);

			return new DataResponse<SmartTestDefectTool>(lastRecord, lastUpdate);
		} else {
			LOG.info("defect data not found for project "+projectName+"  and defectType   "+defectType);
			return null;
		}
	}
}
