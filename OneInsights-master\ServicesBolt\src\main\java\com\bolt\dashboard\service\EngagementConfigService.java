package com.bolt.dashboard.service;

import java.util.List;

import com.bolt.dashboard.core.model.Engagement;
import com.bolt.dashboard.core.model.EngagementConfig;
import com.bolt.dashboard.core.model.EngagementConfigMetrics;
import com.bolt.dashboard.response.DataResponse;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

public interface EngagementConfigService {

    Boolean saveEngagementConfig(EngagementConfig engConfig);

    DataResponse<Iterable<EngagementConfig>> getEngagementConfig();
    DataResponse<Iterable<EngagementConfig>> getEngConfigByTower(String towerName);

	DataResponse<Iterable<EngagementConfigMetrics>> getEngDefaultData();

	DataResponse<Engagement> getEngagementData();
    

}
