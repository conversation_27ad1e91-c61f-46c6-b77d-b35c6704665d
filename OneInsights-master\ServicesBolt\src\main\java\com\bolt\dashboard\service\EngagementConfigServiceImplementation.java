package com.bolt.dashboard.service;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.Engagement;
import com.bolt.dashboard.core.model.EngagementConfig;
import com.bolt.dashboard.core.model.EngagementConfigMetrics;
import com.bolt.dashboard.core.repository.EngagementConfigRepo;
import com.bolt.dashboard.response.DataResponse;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

@Service
public class EngagementConfigServiceImplementation implements EngagementConfigService {
	private EngagementConfigRepo engageRepo;
	private static final Logger LOG = LogManager.getLogger(EngagementConfigServiceImplementation.class);

	/**
	 * 
	 */
	@Autowired
	public EngagementConfigServiceImplementation(EngagementConfigRepo engageRepo) {
		this.engageRepo = engageRepo;
	}

	@Override
//	@Cacheable(value="getEngagementConfig", key ="'getEngagementConfig'", cacheManager="timeoutCacheManager")
	public DataResponse<Iterable<EngagementConfig>> getEngagementConfig() {

		long lastUpdated = 1;
		Iterable<EngagementConfig> result = engageRepo.findAll();
		return new DataResponse<Iterable<EngagementConfig>>(result, lastUpdated);
	}

	@Override
//	@Cacheable(value="getEngConfigByTower", key ="'getEngConfigByTower'+#towerName", cacheManager="timeoutCacheManager")
	public DataResponse<Iterable<EngagementConfig>> getEngConfigByTower(String towerName) {
		long lastUpdated = 1;
		List<EngagementConfig> engObject = engageRepo.findByTowerName(towerName);
		return new DataResponse<Iterable<EngagementConfig>>(engObject, lastUpdated);
	}

	@Override
//	@Caching(evict = {
//			@CacheEvict(value="getEngConfigByTower", key ="'getEngConfigByTower'+#engageConfig.getTowerName()", cacheManager="timeoutCacheManager"),
//			@CacheEvict(value="getEngagementConfig", key ="'getEngagementConfig'", cacheManager="timeoutCacheManager")
//	})
	public Boolean saveEngagementConfig(EngagementConfig engageConfig) {
		Boolean status;

		List<EngagementConfig> engObj = engageRepo.findByTowerName(engageConfig.getTowerName());

		if (engObj.isEmpty()) {
			engageRepo.save(engageConfig);
			status = true;
		} else {
			EngagementConfig engCopy = engObj.get(engObj.size() - 1);
			engCopy.setEngMetrics(engageConfig.getEngMetrics());
			engCopy.setTowerName(engageConfig.getTowerName());
			engageRepo.save(engCopy);
			LOG.info("Record for " + engCopy.getTowerName() + " updated ...");
			status = true;

		}

		return status;
	}

	@Override
	public DataResponse<Iterable<EngagementConfigMetrics>> getEngDefaultData() {
		FileInputStream inputStream = null;
		List<EngagementConfigMetrics> engConfigMetric = null;
		long lastUpdated=1;
		try {
			inputStream = new FileInputStream("defaultEngConfig.json");
		ObjectMapper objectMapper = new ObjectMapper();
		engConfigMetric = objectMapper.readValue(inputStream, new TypeReference<List<EngagementConfigMetrics>>(){});
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	
	return new DataResponse<Iterable<EngagementConfigMetrics>>(engConfigMetric, lastUpdated);	
	}

	@Override
	public DataResponse<Engagement> getEngagementData() {
		FileInputStream inputStream = null;
		Engagement engdata = null;
		long lastUpdated=1;
		try {
			inputStream = new FileInputStream("engagement.json");
		ObjectMapper objectMapper = new ObjectMapper();
		engdata = objectMapper.readValue(inputStream, new TypeReference<Engagement>(){});
		} catch (Exception e) {
			e.printStackTrace();
		}
	
	return new DataResponse<>(engdata, lastUpdated);
	}

}
