package com.bolt.dashboard.service;

import java.util.SortedMap;

import org.springframework.web.multipart.MultipartFile;

import com.bolt.dashboard.core.model.EngagementRule;
import com.bolt.dashboard.response.DataResponse;

public interface EngagementScoreService {

	boolean saveEngagementScoreService(EngagementRule engScore);
	DataResponse<Iterable<EngagementRule>> getEngScore();

	DataResponse<Iterable<EngagementRule>> getEngScoreByProjectName(String projectName);

	DataResponse<Iterable<EngagementRule>> getEngScoreByTowerName(String towerName);
	DataResponse<SortedMap<String, Double>> getEngScoreHome(String pName);
	boolean engScorecardExcelUpload(MultipartFile file, String projectName, String monthName, String displayMonth);
	

}
