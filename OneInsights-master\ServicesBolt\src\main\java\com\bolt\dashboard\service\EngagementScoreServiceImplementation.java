package com.bolt.dashboard.service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.SortedMap;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.bolt.dashboard.core.model.EngAreaData;
import com.bolt.dashboard.core.model.EngParamData;
import com.bolt.dashboard.core.model.EngRuleData;
import com.bolt.dashboard.core.model.EngRulesBasedOnMonth;
import com.bolt.dashboard.core.model.EngSubParamData;
import com.bolt.dashboard.core.model.EngagementRule;
import com.bolt.dashboard.core.model.ProjectModel;
import com.bolt.dashboard.core.repository.EngagementScoreRepo;
import com.bolt.dashboard.core.repository.ProjectRepo;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.util.EIScoresCalculation;
import com.bolt.dashboard.util.EngagementExcelUtil;

@Service
public class EngagementScoreServiceImplementation implements EngagementScoreService {

	private static final Logger LOGGER = LogManager.getLogger(EngagementScoreServiceImplementation.class);
	
	@Autowired
	private EngagementScoreRepo engagementScoreRepo;
	@Autowired
	private ProjectRepo projectRepo;

	@Override
//	@Caching(evict= {
//			@CacheEvict(value="getEngScoreByProjectName", key ="'getEngScoreByProjectName'+#engScore.getProjectName()", cacheManager="timeoutCacheManager"),
//			@CacheEvict(value="getEngScoreByTowerName", key ="'getEngScoreByTowerName'+#engScore.getTowerName()", cacheManager="timeoutCacheManager"),
//			@CacheEvict(value="getEngScore", key ="'getEngScore'", cacheManager="timeoutCacheManager"),
//			@CacheEvict(value="getEngScoreHome", key ="'getEngScoreHome'+#engScore.getProjectName()", cacheManager="timeoutCacheManager")
//	})
	public boolean saveEngagementScoreService(EngagementRule engScore) {
		Boolean status=false;
		EIScoresCalculation eiScoreCalc= new EIScoresCalculation();
		List<EngagementRule> engScoreList = engagementScoreRepo.findByProjectName(engScore.getProjectName());
try {
		if (engScoreList.isEmpty()) {
			engagementScoreRepo.save(engScore);
			status = true;
		} else {
			EngagementRule engCopy = engScoreList.get(engScoreList.size() - 1);

			List<EngRulesBasedOnMonth> inputListOfRulesBasedOnMonths = engScore.getListOfRulesBasedOnMonths();
			EngRulesBasedOnMonth inputEngRulesBasedOnMonth = inputListOfRulesBasedOnMonths.get(0);
			String inputMonthName = inputEngRulesBasedOnMonth.getMonthName();
			String inputDisplayMonth=inputEngRulesBasedOnMonth.getDisplayMonth();

			List<EngRulesBasedOnMonth> listOfRulesBasedOnMonths = engCopy.getListOfRulesBasedOnMonths();
			String existingMonthName = null;
			String existingdisplayMonth=null;

			for (int i = 0; i < listOfRulesBasedOnMonths.size(); i++) {

				EngRulesBasedOnMonth engRulesBasedOnMonth = listOfRulesBasedOnMonths.get(i);
				existingMonthName = engRulesBasedOnMonth.getMonthName();
				existingdisplayMonth=engRulesBasedOnMonth.getDisplayMonth();
				if (existingdisplayMonth.equals(inputDisplayMonth)) {
					existingMonthName = inputMonthName;
					EngRulesBasedOnMonth en2 = engScore.getListOfRulesBasedOnMonths().get(0);
					List<EngRuleData> allData2 = en2.getRuleData();
					List<EngParamData> paramData2 = en2.getParamData();
                    List<EngSubParamData> subParamData2=en2.getSubParamData();
                    List<EngAreaData> areaData2=en2.getAreaData();
                    double engScr=en2.getEngScore();
                    engRulesBasedOnMonth.setInference(en2.getInference());
					engRulesBasedOnMonth.setRuleData(allData2);
					engRulesBasedOnMonth.setParamData(paramData2);
					engRulesBasedOnMonth.setAreaData(areaData2);
					engRulesBasedOnMonth.setSubParamData(subParamData2);
					engRulesBasedOnMonth.setEngScore(engScr);
					Collections.sort(engCopy.getListOfRulesBasedOnMonths());
					engagementScoreRepo.save(engCopy);
					
					eiScoreCalc.processEIScores(engScore.getProjectName());
					return true;
				}
			}

			List<EngRulesBasedOnMonth> listOfRulesBasedOnMonths2 = engCopy.getListOfRulesBasedOnMonths();
			Collections.sort(engCopy.getListOfRulesBasedOnMonths());
			listOfRulesBasedOnMonths2.add(inputEngRulesBasedOnMonth);

			engagementScoreRepo.save(engCopy);
			eiScoreCalc.processEIScores(engScore.getProjectName());
			status = true;
		}
}catch(Exception ex) {
	status=false;
}
		return status;
	}

	@Override
//	@Cacheable(value="getEngScoreByProjectName", key ="'getEngScoreByProjectName'+#projectName", cacheManager="timeoutCacheManager")
	public DataResponse<Iterable<EngagementRule>> getEngScoreByProjectName(String projectName) {
		long lastUpdated = 1;
		Iterable<EngagementRule> result = engagementScoreRepo.findByProjectName(projectName);
		return new DataResponse<Iterable<EngagementRule>>(result, lastUpdated);

	}

	@Override
//	@Cacheable(value="getEngScoreByTowerName", key ="'getEngScoreByTowerName'+#towerName", cacheManager="timeoutCacheManager")
	public DataResponse<Iterable<EngagementRule>> getEngScoreByTowerName(String towerName) {
		long lastUpdated = 1;
		Iterable<EngagementRule> result = engagementScoreRepo.findByTowerName(towerName);
		return new DataResponse<Iterable<EngagementRule>>(result, lastUpdated);
	}
	@Override
//	@Cacheable(value="getEngScore", key ="'getEngScore'", cacheManager="timeoutCacheManager")
	public DataResponse<Iterable<EngagementRule>> getEngScore() {
		long lastUpdated = 1;
		Iterable<EngagementRule> result = engagementScoreRepo.findAll();
		return new DataResponse<Iterable<EngagementRule>>(result, lastUpdated);
	}

	@Override
//	@Cacheable(value="getEngScoreHome", key ="'getEngScoreHome'+#pName", cacheManager="timeoutCacheManager")
	public DataResponse<SortedMap<String, Double>> getEngScoreHome(String pName) {
		
		List<ProjectModel> temp=this.projectRepo.findByProjectName(pName);
		SortedMap<String, Double> result = null;
		if(temp.size()>0)
		{
		result= temp.get(0).getEngScores();
		}
		
			return new DataResponse<SortedMap<String, Double>>(result,1);
	
	}

	@Override
	public boolean engScorecardExcelUpload(MultipartFile file, String projectName, String monthName,
			String displayMonth) {
		
		
//		try {
//			File excelFile = new File(projectName+"_t_"+monthName+".xlsx");
//			excelFile.createNewFile();
//			FileOutputStream fos = new FileOutputStream(excelFile);
//			fos.write(file.getBytes());
//			fos.close();
//		} catch (IOException e) {
//			
//			
//			LOGGER.error(e);
//			return false;
//		}
		
		return new EngagementExcelUtil().readExcel(file,projectName, monthName, displayMonth);
	}

}
