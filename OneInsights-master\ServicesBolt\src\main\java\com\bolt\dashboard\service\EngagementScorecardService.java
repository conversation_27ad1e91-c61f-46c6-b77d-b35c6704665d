package com.bolt.dashboard.service;

import org.springframework.web.multipart.MultipartFile;
import com.bolt.dashboard.core.model.EngScorecard;
import com.bolt.dashboard.core.model.EngagementScorecardSubjectiveData;
import com.bolt.dashboard.engagementScorecard.EngScoreCardQuarterData;
import com.bolt.dashboard.request.EngagementScorecardSubjectiveDataReq;

public interface EngagementScorecardService {
	public EngScorecard getEngScoreCardByProject(String pName);

	public EngagementScorecardSubjectiveData saveEngScoreCardSubjectiveData(
			EngagementScorecardSubjectiveData engScoreCardSubjectiveData);

	public EngagementScorecardSubjectiveData saveEngScoreCardSubjectiveDataHistory(
			EngagementScorecardSubjectiveData engScoreCardSubjectiveData);

	public EngagementScorecardSubjectiveData getEngScoreCardSubjetiveDataByProject(String pName);

	public EngScoreCardQuarterData getEngScoreCardQuaters(String pName);

	public boolean engScorecardExcelUpload(MultipartFile tempfile, String projectName, String sprintName,
			long startDate, long endDate, String releaseIterationNo, String unitOfSizing,String teamSize);

	public boolean engScorecardFormUpload(EngagementScorecardSubjectiveDataReq subjective);

	public boolean engScorecardFormUploadEdit(EngagementScorecardSubjectiveDataReq subjective);

}
