package com.bolt.dashboard.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.EngScorecard;
import com.bolt.dashboard.core.model.EngScorecardParamData;
import com.bolt.dashboard.core.model.EngScorecardSprint;
import com.bolt.dashboard.core.model.EngScorecardSubjectiveSprintData;
import com.bolt.dashboard.core.model.EngagementScorecardSubjectiveData;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.EngScorecardRepo;
import com.bolt.dashboard.core.repository.EngScorecardSubjectiveDataRepo;
import com.bolt.dashboard.engagementScorecard.EngScoreCardQuarterData;
import com.bolt.dashboard.engagementScorecard.EngScorecardApplication;
import com.bolt.dashboard.engagementScorecard.EngagementScorecardQuarterCalculations;
import com.bolt.dashboard.request.EngagementScorecardSubjectiveDataReq;
import com.bolt.dashboard.util.EIScoresCalculation;

@Service
public class EngagementScorecardServiceImplementation implements EngagementScorecardService {

	EngScorecardRepo engRepo;
	EngScorecardSubjectiveDataRepo engScoreSubjectiveRepo;
	ALMConfigRepo almConfigRepo;
	ConfigurationSettingRep configurationRepo;
	private static final Logger LOGGER = LogManager.getLogger(EngagementScorecardServiceImplementation.class);
	
	@Autowired
	public EngagementScorecardServiceImplementation(EngScorecardRepo engScorecardRepo,
			EngScorecardSubjectiveDataRepo engScoreSubjectiveRepo, ALMConfigRepo almConfigRepo,ConfigurationSettingRep configurationRepo) {

		this.engRepo = engScorecardRepo;
		this.engScoreSubjectiveRepo = engScoreSubjectiveRepo;
		this.almConfigRepo = almConfigRepo;
		this.configurationRepo = configurationRepo;
	}
	@Override
//	@Cacheable(value="getEngScoreCardByProject", key ="'getEngScoreCardByProject'+#pName", cacheManager="timeoutCacheManager")

	public EngScorecard getEngScoreCardByProject(String pName) {
		ALMConfiguration almConfig = null;
		EngScorecard temp = this.engRepo.findByPName(pName);
		List<ALMConfiguration> configList = this.almConfigRepo.findByProjectName(pName);
		if(configList!=null && !configList.isEmpty()) {
			almConfig = configList.get(0);
		}
		

		List<String> filteredSprints = new ArrayList<String>();
		String[] temfilter = null;
		if(almConfig != null) {
			temfilter = almConfig.getFilteredSprints();
		}
		if (temfilter != null) {
			filteredSprints.addAll(Arrays.asList(temfilter));
		}
		List<EngScorecardSprint> engScoreSprintList = temp.getEngScoreCardSprint().stream()
				.filter(score -> !(filteredSprints.contains(score.getSprintName()))).collect(Collectors.toList());
		temp.setEngScoreCardSprint(engScoreSprintList);
		return temp;
	}
	@Override
//	@Caching(evict= {
//			@CacheEvict(value="getEngScoreCardSubjetiveDataByProject", key ="'getEngScoreCardSubjetiveDataByProject'+#engScoreCardSubjectiveData.getpName()", cacheManager="timeoutCacheManager"),
//			@CacheEvict(value="getEngScoreCardQuaters", key ="'getEngScoreCardQuaters'+#engScoreCardSubjectiveData.getpName()", cacheManager="timeoutCacheManager")
//	})

	public EngagementScorecardSubjectiveData saveEngScoreCardSubjectiveData(
			EngagementScorecardSubjectiveData engScoreCardSubjectiveData) {
		
		EngagementScorecardSubjectiveData temp = this.engScoreSubjectiveRepo
				.findByPName(engScoreCardSubjectiveData.getpName());
		if (temp == null) {
			temp = engScoreCardSubjectiveData;
		} else {
			temp.setEngScorecardSprintData(engScoreCardSubjectiveData.getEngScorecardSprintData());
		}
		EngScorecard engscoreCard = this.engRepo.findByPName(temp.getpName());
		this.engScoreSubjectiveRepo.save(temp);
		callCalculateSubjetiveRules(engscoreCard.getpName());

		return temp;
	}

	private void callCalculateSubjetiveRules(String projectName) {
		new EngScorecardApplication().engScorecardMain(projectName, true);
	}
	@Override
//	@Cacheable(value="getEngScoreCardSubjetiveDataByProject", key ="'getEngScoreCardSubjetiveDataByProject'+#pName", cacheManager="timeoutCacheManager")
	public EngagementScorecardSubjectiveData getEngScoreCardSubjetiveDataByProject(String pName) {
		return this.engScoreSubjectiveRepo.findByPName(pName);
	}
	@Override
//	@Cacheable(value="getEngScoreCardQuaters", key ="'getEngScoreCardQuaters'+#pName", cacheManager="timeoutCacheManager")
	public EngScoreCardQuarterData getEngScoreCardQuaters(String pName) {
		
		EngagementScorecardQuarterCalculations engScoreQuarterCalc = new EngagementScorecardQuarterCalculations();
		return engScoreQuarterCalc.calculateQuarters(pName);

	}
	@Override
//	@Caching(evict= {
//			@CacheEvict(value="getEngScoreCardSubjetiveDataByProject", key ="'getEngScoreCardSubjetiveDataByProject'+#engScoreCardSubjectiveData.getpName()", cacheManager="timeoutCacheManager"),
//			@CacheEvict(value="getEngScoreCardQuaters", key ="'getEngScoreCardQuaters'+#engScoreCardSubjectiveData.getpName()", cacheManager="timeoutCacheManager")
//	})

	public EngagementScorecardSubjectiveData saveEngScoreCardSubjectiveDataHistory(
			EngagementScorecardSubjectiveData engScoreCardSubjectiveData) {
		
		EngagementScorecardSubjectiveData temp = this.engScoreSubjectiveRepo
				.findByPName(engScoreCardSubjectiveData.getpName());
		if (temp == null) {
			temp = engScoreCardSubjectiveData;
		} else {
			temp.setEngScorecardSprintData(engScoreCardSubjectiveData.getEngScorecardSprintData());
		}
		Map<String, String> defaultValue = new HashMap<>();
		defaultValue.put("score", "Green");
		defaultValue.put("comment", "");

		for (EngScorecardSubjectiveSprintData sprintSubjective : temp.getEngScorecardSprintData()) {
			sprintSubjective.setDailyCheckPoint(defaultValue);
			sprintSubjective.setBacklogGromming(defaultValue);

		}

		this.engScoreSubjectiveRepo.save(temp);

		return temp;
	}
	
	EngScorecardSprint engManualValuesForm(EngScorecardSubjectiveSprintData subjectiveSprint,String projectName) {
		
		
		EngScorecardSprint engSprint= new EngScorecardSprint();
		engSprint.setSprintName(subjectiveSprint.getSprintName());
		engSprint.setStartDate(subjectiveSprint.getStDate());
		engSprint.setEndDate(subjectiveSprint.getEndDate());
		engSprint.setState("closed");
		engSprint.setReleaseIterationNo(subjectiveSprint.getReleaseIterationNo());
		engSprint.setUnitOfSizing(subjectiveSprint.getUnitOfSizing());
		engSprint.setTeamSize(subjectiveSprint.getTeamSize()+"");
		String commitedStories="0";
		String projectAlmType="";
		String velocity="0";
		String codeReviewD="0";
		String customerReportedD="0";
		String sitD="0";
		String uatD="0";
		String capacity = "0";
		String availableCapacity="0";
		String retroOpen="0";
		String retroClose="0";
		List<ConfigurationSetting> configurationColection = configurationRepo.findByProjectName(projectName);
		if(configurationColection!=null && !configurationColection.isEmpty()) {
			projectAlmType = configurationColection.get(0).getProjectType();
		}
		
		List<EngScorecardParamData> engParams = new LinkedList<EngScorecardParamData>();
		if(subjectiveSprint.getBacklogGromming() !=null) {
			addScoreCardValueFromSubjective(engParams,"Communication","Backlog Grooming",subjectiveSprint.getBacklogGromming().get("score"));
		}
		if(subjectiveSprint.getDailyCheckPoint() !=null) {
			addScoreCardValueFromSubjective(engParams,"Communication","Daily Check Point",subjectiveSprint.getDailyCheckPoint().get("score"));
		}
		if(subjectiveSprint.getExternalDependency() !=null) {
			addScoreCardValueFromSubjective(engParams,"Communication","External Dependencies",subjectiveSprint.getExternalDependency().get("score"));
		}
		if(subjectiveSprint.getReadinessIndex0() !=null) {
			addScoreCardValueFromSubjective(engParams,"PM Expectations","n+0",subjectiveSprint.getReadinessIndex0().get("score"));
		}
		if(subjectiveSprint.getReadinessIndex1() !=null) {
			addScoreCardValueFromSubjective(engParams,"PM Expectations","n+1",subjectiveSprint.getReadinessIndex1().get("score"));
		}
		if(subjectiveSprint.getReadinessIndex2() !=null) {
			addScoreCardValueFromSubjective(engParams,"PM Expectations","n+2",subjectiveSprint.getReadinessIndex2().get("score"));
		}
		if(subjectiveSprint.getReadinessIndex3() !=null) {
			addScoreCardValueFromSubjective(engParams,"PM Expectations","n+6",subjectiveSprint.getReadinessIndex3().get("score"));
		}
		if(subjectiveSprint.getSprintGoalsMet() !=null) {
			addScoreCardValueFromSubjective(engParams,"Communication","Sprint Goals Met",subjectiveSprint.getSprintGoalsMet().get("score"));
		}
		if(subjectiveSprint.getZeroMidSprintChanges() !=null) {
			addScoreCardValueFromSubjective(engParams,"Communication","Zero mid sprint changes",subjectiveSprint.getZeroMidSprintChanges().get("score"));
		}
		if(subjectiveSprint.getCommitedStoryPoints() !=null) {
			addScoreCardValueFromSubjective(engParams,"Committed vs Completed","Committed Storypoints",subjectiveSprint.getCommitedStoryPoints().get("score"));
			if(projectAlmType.equalsIgnoreCase("Sprint Wise"))
				commitedStories = subjectiveSprint.getCommitedStoryPoints().get("score");
		}
		if(subjectiveSprint.getLeadTime() !=null) {
			addleadTimeCycleTimeValueSubjective(engParams,"Committed vs Completed","Lead Time",subjectiveSprint.getLeadTime().get("score"));
		}
		if(subjectiveSprint.getMttr() !=null) {
			addleadTimeCycleTimeValueSubjective(engParams,"Build","MTTR",subjectiveSprint.getMttr().get("score"));
		}
		if(subjectiveSprint.getCycleTime() !=null) {
			addleadTimeCycleTimeValueSubjective(engParams,"Committed vs Completed","Cycle Time",subjectiveSprint.getCycleTime().get("score"));
		}
			
		if(subjectiveSprint.getVelocity() !=null) {
			addScoreCardValueFromSubjective(engParams,"Committed vs Completed","Velocity",subjectiveSprint.getVelocity().get("score"));
			if(projectAlmType.equalsIgnoreCase("Sprint Wise"))
				velocity = subjectiveSprint.getVelocity().get("score");
		}	
		if(subjectiveSprint.getCoverage() !=null)
			addScoreCardValueFromSubjective(engParams,"Quality","Unit Test Coverage",subjectiveSprint.getCoverage().get("score"));
		
		if(subjectiveSprint.getCriticalIssues() !=null)
			addScoreCardValueFromSubjective(engParams,"Quality","Open Critical Issues",subjectiveSprint.getCriticalIssues().get("score"));
		
		if(subjectiveSprint.getComplexity() !=null)
			addScoreCardValueFromSubjective(engParams,"Quality","Complexity",subjectiveSprint.getComplexity().get("score"));
		
		if(subjectiveSprint.getBlockerViolations() !=null)
			addScoreCardValueFromSubjective(engParams,"Quality","Blocker Violations",subjectiveSprint.getBlockerViolations().get("score"));
		
		if(subjectiveSprint.getCriticalViolations() !=null)
			addScoreCardValueFromSubjective(engParams,"Quality","Critical Violations",subjectiveSprint.getCriticalViolations().get("score"));
		
		if(subjectiveSprint.getTechnicalDebt() !=null)
			addScoreCardValueFromSubjective(engParams,"Quality","Technical Debt",subjectiveSprint.getTechnicalDebt().get("score"));
		
		if(subjectiveSprint.getTestAutomation() !=null)
			addScoreCardValueFromSubjective(engParams,"Quality","Test Automation Coverage",subjectiveSprint.getTestAutomation().get("score"));
		
		if(subjectiveSprint.getAddedIssues() !=null) {
			addScoreCardValueFromSubjective(engParams,"Committed vs Completed","Added Issues",subjectiveSprint.getAddedIssues().get("score"));
			if(projectAlmType.equalsIgnoreCase("kanban"))
				commitedStories = subjectiveSprint.getAddedIssues().get("score");
		}
		if(subjectiveSprint.getClosedIssues() !=null) {
			addScoreCardValueFromSubjective(engParams,"Committed vs Completed","Closed Issues",subjectiveSprint.getClosedIssues().get("score"));
			if(projectAlmType.equalsIgnoreCase("kanban"))
				velocity =subjectiveSprint.getClosedIssues().get("score");
		}
		if(subjectiveSprint.getCcrTickets() !=null)
			addScoreCardValueFromSubjective(engParams,"Committed vs Completed","CCRTickets",subjectiveSprint.getCcrTickets().get("score"));
		if(subjectiveSprint.getCapacity()!=null && projectAlmType.equalsIgnoreCase("Sprint Wise")) {
			addSprintPlanning(engParams, commitedStories, subjectiveSprint.getCapacity().get("score"));
		}
		if(subjectiveSprint.getCapacity() !=null) {
			capacity = subjectiveSprint.getCapacity().get("score");
			addScoreCardValueFromSubjective(engParams,"Committed vs Completed","Available Capacity for the sprint ( In Hrs)",subjectiveSprint.getCapacity().get("score"));
		}
			
		
		if(subjectiveSprint.getCapacityAtSprintEnd() !=null) {
			availableCapacity = subjectiveSprint.getCapacityAtSprintEnd().get("score");
			addScoreCardValueFromSubjective(engParams,"Committed vs Completed","Available Capacity at the end of the sprint ( In Hrs)",subjectiveSprint.getCapacityAtSprintEnd().get("score"));
		}
			
		if(subjectiveSprint.getHoursSpent() !=null)
			addScoreCardValueFromSubjective(engParams,"Committed vs Completed","Actual Hours Spent",subjectiveSprint.getHoursSpent().get("score"));	
		
		if(subjectiveSprint.getCodeReviewDefects() !=null) {
			addScoreCardValueFromSubjective(engParams,"Quality","Code Review Defects",subjectiveSprint.getCodeReviewDefects().get("score"));
			codeReviewD = subjectiveSprint.getCodeReviewDefects().get("score");
		}
			
		if(subjectiveSprint.getSitDefects() !=null) {
			addScoreCardValueFromSubjective(engParams,"Quality","SIT Defects for the sprint",subjectiveSprint.getSitDefects().get("score"));
			sitD = subjectiveSprint.getSitDefects().get("score");
		}
			
		if(subjectiveSprint.getUatDefects() !=null) {
			addScoreCardValueFromSubjective(engParams,"Quality","UAT Defects",subjectiveSprint.getUatDefects().get("score"));
			uatD = subjectiveSprint.getUatDefects().get("score");
		}
		
		if(subjectiveSprint.getCustomerReportedDefects() !=null) {
			addScoreCardValueFromSubjective(engParams,"Quality","Customer reported Defects",subjectiveSprint.getCustomerReportedDefects().get("score"));
			customerReportedD = subjectiveSprint.getCustomerReportedDefects().get("score");
		}
		
		if(subjectiveSprint.getSprintDemoPulse() !=null) {
			addScoreCardValueFromSubjective(engParams,"Value","Sprint Demo Pulses",subjectiveSprint.getSprintDemoPulse().get("score"));
		}
		if(subjectiveSprint.getRetrospectionActionOpened() !=null) {
			retroOpen = subjectiveSprint.getRetrospectionActionOpened().get("score");
			addScoreCardValueFromSubjective(engParams,"Execution Excellence","Retrospection Action Opened",subjectiveSprint.getRetrospectionActionOpened().get("score"));
		}
		if(subjectiveSprint.getRetrospectionActionClosed() !=null) {
			retroClose = subjectiveSprint.getRetrospectionActionClosed().get("score");
			addScoreCardValueFromSubjective(engParams,"Execution Excellence","Retrospection Action Closed",subjectiveSprint.getRetrospectionActionClosed().get("score"));
		}
		if(subjectiveSprint.getRetrospectionIndex() !=null) {
			addScoreCardValueFromSubjective(engParams,"Execution Excellence","Retrospection Index",subjectiveSprint.getRetrospectionIndex().get("score"));
		}
		if(subjectiveSprint.getSecurityRating() !=null) {
			addScoreCardValueFromSubjective(engParams,"Quality","Code Quality - Security Rating",subjectiveSprint.getSecurityRating().get("score"));
		}
		if(subjectiveSprint.getDeploymentFrequency() !=null) {
			addScoreCardValueFromSubjective(engParams,"Quality","Deployment Frequency",subjectiveSprint.getDeploymentFrequency().get("score"));
		}
		if(subjectiveSprint.getLeadTimeForChanges() !=null) {
			addleadTimeCycleTimeValueSubjective(engParams,"Flow","Lead Time for Changes",subjectiveSprint.getLeadTimeForChanges().get("score"));
		}
		if(subjectiveSprint.getChangeFailureRate() !=null) {
			addScoreCardValueFromSubjective(engParams,"Quality","Change Failure Rate",subjectiveSprint.getChangeFailureRate().get("score"));
		}
		
		addEstimationAccuracyAndCompletion(engParams, velocity, commitedStories);
		addTestAndReviewEffectiveness(engParams, codeReviewD, sitD, customerReportedD, "Review Effectiveness");
		addTestAndReviewEffectiveness(engParams, sitD, uatD, customerReportedD, "Test Effectiveness");
		addActualHoursSpent(engParams, capacity, availableCapacity);
		addPredictableSP(engParams,velocity,projectName);
		addRetroSpectionIndex(engParams,retroOpen,retroClose,projectName);
		engSprint.setEngScoreParamData(engParams);
		return engSprint;
	}
	
	void addTestAndReviewEffectiveness(List<EngScorecardParamData> engParams,String param1,String param12,String param3,String name) {
		double codeRD=0;
		double sitD =0;
		double customerRD = 0;
		
		try {
			codeRD = Double.parseDouble(param1);
		}catch(Exception e) {
			codeRD=0;
		}
		
		try {
			sitD = Double.parseDouble(param12);
		}catch(Exception e) {
			sitD=0;
		}
		
		try {
			customerRD = Double.parseDouble(param3);
		}catch(Exception e) {
			customerRD=0;
		}
		if(codeRD > 0 && customerRD>0) {
			codeRD = codeRD/(sitD+customerRD);
		}else {
			codeRD = 0;
		}
		
		EngScorecardParamData param = new EngScorecardParamData();
		param.setParamaterName("Defects");
		param.setSubParamaterName(name);
		param.setValue(codeRD+"");
		engParams.add(param);
	}
	
	void addleadTimeCycleTimeValueSubjective(List<EngScorecardParamData> engParams,String paramName,String subParamName,String value){
		long tempLong=0;
		try {
			 tempLong = Integer.parseInt(value);
		}catch(Exception e) {
			tempLong =0 ;
		}
		long millis = tempLong*60*60*1000;
		String formatted = convertTime(millis);
		EngScorecardParamData param = new EngScorecardParamData();
		param.setParamaterName(paramName);
		param.setSubParamaterName(subParamName);
		param.setValue(formatted);
		param.setTimestamp(millis);
		engParams.add(param);
	}
	
	void addScoreCardValueFromSubjective(List<EngScorecardParamData> engParams,String paramName,String subParamName,String value){
		EngScorecardParamData param = new EngScorecardParamData();
		param.setParamaterName(paramName);
		param.setSubParamaterName(subParamName);
		if(value !=null && (value.equalsIgnoreCase("Red") ||value.equalsIgnoreCase("Amber") ||value.equalsIgnoreCase("Green"))) {
			param.setValue(getScoreInnumber(value));
		}
		else {
			param.setValue(value);
		}
		
		engParams.add(param);
	}
	
	EngScorecardSprint engManualValuesExcel(String projectName,String sprintName,long startDate,long endDate,String releaseIterationNo,String unitOfSizing,String teamSize) {
		EngagementScorecardSubjectiveData subjective = this.engScoreSubjectiveRepo
				.findByPName(projectName);
		if (subjective == null) {
			subjective = new EngagementScorecardSubjectiveData();
			subjective.setpName(projectName);
		}
		EngScorecardSubjectiveSprintData subjectiveSprint = new EngScorecardSubjectiveSprintData();
		subjectiveSprint.setSprintName(sprintName);
		//subjectiveSprint.setTeamSize(Double.parseDouble(teamSize));
		subjectiveSprint.setStDate(startDate);
		subjectiveSprint.setEndDate(endDate);
		subjectiveSprint.setReleaseIterationNo(releaseIterationNo);
		subjectiveSprint.setUnitOfSizing(unitOfSizing);
		
		
		try {
			subjectiveSprint.setTeamSize(Double.parseDouble(teamSize));
		}catch(Exception e) {
			subjectiveSprint.setTeamSize(0);
		}
		
		
		String commitedStories="0";
		String projectAlmType="";
		String velocity="0";
		String capacity="0";
		String codeReviewD="0";
		String customerReportedD="0";
		String sitD="0";
		String uatD="0";
		String availableCapacity="0";
		String retroOpen="0";
		String retroClose="0";
		List<ConfigurationSetting> configurationColection = configurationRepo.findByProjectName(projectName);
		if(configurationColection!=null && !configurationColection.isEmpty()) {
			projectAlmType = configurationColection.get(0).getProjectType();
		}
		EngScorecardSprint engSprint= new EngScorecardSprint();
		engSprint.setSprintName(sprintName);
		engSprint.setStartDate(startDate);
		engSprint.setEndDate(endDate);
		engSprint.setState("closed");
		engSprint.setReleaseIterationNo(releaseIterationNo);
		engSprint.setUnitOfSizing(unitOfSizing);
		
		try {
			engSprint.setTeamSize(teamSize);
		}catch(Exception e) {
			engSprint.setTeamSize(teamSize);
		}
		try {
			File excelFile = new File("temp.xlsx");
			excelFile.createNewFile();
			FileInputStream file = new FileInputStream(excelFile);

			Workbook workbook = new XSSFWorkbook(file);

			Sheet sheet = workbook.getSheetAt(0);

			int rowLength = sheet.getLastRowNum();
			List<EngScorecardParamData> engParams = new LinkedList<EngScorecardParamData>();
			for (int index = 1; index <= rowLength; index++) {
				EngScorecardParamData param = new EngScorecardParamData();
				param.setValue("0");
				Row row = sheet.getRow(index);
				String value = String.valueOf(row.getCell(3));
				String rawValue = value.trim();
				if(value !=null & value.equalsIgnoreCase("Red") ||value.equalsIgnoreCase("Amber") ||value.equalsIgnoreCase("Green")) {
					
					param.setValue(getScoreInnumber(value));
				}else if(value != null) {
					value = value.split("\\.")[0];
					param.setValue(value);
				}
				
				String subParamname = String.valueOf(row.getCell(1));
				subParamname = subParamname.trim();
				if(subParamname.equalsIgnoreCase("Sprint Backlog Health")){
					subParamname = "Backlog Grooming";
					subjectiveSprint.setBacklogGromming(prepareSubjectiveMap(rawValue));
				}
				if(subParamname.equalsIgnoreCase("Unit Test Automation Coverage")){
					subParamname = "Unit Test Coverage";
					subjectiveSprint.setCoverage(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Velocity")) {
					if(projectAlmType.equalsIgnoreCase("Sprint Wise"))
						velocity = param.getValue();
					subParamname = "Velocity";
					subjectiveSprint.setVelocity(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Committed Story Points")) {
					if(projectAlmType.equalsIgnoreCase("Sprint Wise"))
						commitedStories = param.getValue();
					subParamname = "Committed Storypoints";
					subjectiveSprint.setCommitedStoryPoints(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("No Of Tickets Assigned")) {
					if(projectAlmType.equalsIgnoreCase("kanban"))
						commitedStories = param.getValue();
					subParamname = "Added Issues";
					subjectiveSprint.setAddedIssues(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("No Of Tickets Closed")) {
					if(projectAlmType.equalsIgnoreCase("kanban"))
						velocity = param.getValue();
					subParamname = "Closed Issues";
					subjectiveSprint.setClosedIssues(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("No. of tickets delivered on time")) {
					subjectiveSprint.settDeleveredOnTime(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("No of tickets move to production")) {
					subjectiveSprint.setTmoveToProduction(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("No. of tickets with UAT Defects")) {
					subjectiveSprint.setTwithUATDefects(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Requirement Readiness for the sprint")) {
					subParamname = "n+0";
					subjectiveSprint.setReadinessIndex0(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("N+1 readiness")) {
					subParamname = "n+1";
					subjectiveSprint.setReadinessIndex1(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("N+2 readiness")) {
					subParamname = "n+2";
					subjectiveSprint.setReadinessIndex2(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("N+6 readiness")) {
					subParamname = "n+6";
					subjectiveSprint.setReadinessIndex3(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("CCR Tickets")) {
					subParamname = "CCRTickets";
					subjectiveSprint.setCcrTickets(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Process Adherence")) {
					subParamname = "Daily Check Point";
					subjectiveSprint.setDailyCheckPoint(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Scope Creep")) {
					subParamname = "Zero Mid Sprint Changes";
					subjectiveSprint.setZeroMidSprintChanges(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("External Dependencies")) {
					subjectiveSprint.setExternalDependency(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Average Lead Time")) {
					subParamname = "Lead Time";
					subjectiveSprint.setLeadTime(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Average Cycle Time")) {
					subParamname = "Cycle Time";
					subjectiveSprint.setCycleTime(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Mean Time to Recovery (MTTR)")) {
					subParamname = "MTTR";
					subjectiveSprint.setMttr(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Business Value Met")) {
					subParamname = "Sprint Goals Met";
					subjectiveSprint.setSprintGoalsMet(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Open Critical Issues")) {
					subjectiveSprint.setCriticalIssues(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Blocker Violations")) {
					subjectiveSprint.setBlockerViolations(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Critical Violations")) {
					subjectiveSprint.setCriticalViolations(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Code Quality - Technical Debt")) {
					subParamname = "Technical Debt";
					subjectiveSprint.setTechnicalDebt(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Available Capacity for Sprint (Hrs)")) {
					subParamname = "Available Capacity for the sprint ( In Hrs)";
					capacity = value;
					subjectiveSprint.setCapacity(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Available Capacity at the end of the sprint ( In Hrs)")) {
					availableCapacity = value;
					subjectiveSprint.setCapacityAtSprintEnd(prepareSubjectiveMap(rawValue));
				}

				if(subParamname !=null && subParamname.equalsIgnoreCase("Code Review Defects")) {
					codeReviewD = value;
					subjectiveSprint.setCodeReviewDefects(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("SIT Defects for the sprint")) {
					sitD = value;
					subjectiveSprint.setSitDefects(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("UAT Defects")) {
					uatD = value;
					subjectiveSprint.setUatDefects(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Production Defects")) {
					subParamname = "Customer reported Defects";
					customerReportedD = value;
					subjectiveSprint.setCustomerReportedDefects(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Regression Coverage")) {
					subParamname = "Test Automation Coverage";
					subjectiveSprint.setTestAutomation(prepareSubjectiveMap(rawValue));
				}
				
				
				if(subParamname !=null && subParamname.equalsIgnoreCase("Sprint Demo Pulse")) {
					//subParamname = "Sprint Demo Pulse";
					subjectiveSprint.setSprintDemoPulse(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Retrospection Action Opened")) {
					//subParamname = "Sprint Demo Pulse";
					retroOpen = value;
					subjectiveSprint.setRetrospectionActionOpened(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Retrospection Action Closed")) {
					//subParamname = "Sprint Demo Pulse";
					retroClose = value;
					subjectiveSprint.setRetrospectionActionClosed(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Retrospection Index")) {
					//subParamname = "Sprint Demo Pulse";
					subjectiveSprint.setRetrospectionIndex(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Code Quality - Security Rating")) {
					//subParamname = "Sprint Demo Pulse";
					subjectiveSprint.setSecurityRating(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Code Quality - Complexity")) {
					subParamname = "Complexity";
					subjectiveSprint.setComplexity(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Deployment Frequency")) {
					//subParamname = "Sprint Demo Pulse";
					subjectiveSprint.setDeploymentFrequency(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Lead Time for Changes")) {
					//subParamname = "Sprint Demo Pulse";
					subjectiveSprint.setLeadTimeForChanges(prepareSubjectiveMap(rawValue));
				}
				if(subParamname !=null && subParamname.equalsIgnoreCase("Change Failure Rate")) {
					//subParamname = "Sprint Demo Pulse";
					subjectiveSprint.setChangeFailureRate(prepareSubjectiveMap(rawValue));
				}
				
				
				
				if(subParamname.equalsIgnoreCase("Lead Time for Changes") || subParamname.equalsIgnoreCase("Lead Time")|| subParamname.equalsIgnoreCase("Cycle Time")|| subParamname.equalsIgnoreCase("MTTR")) {
					param.setParamaterName(String.valueOf(row.getCell(0)));
					param.setSubParamaterName(subParamname);
					long tempint=0;
					try {
						tempint = Integer.parseInt(value);
					}catch(Exception e) {
						tempint=0;
					}
					
					long millis = tempint*60*60*1000;
					String formatted = convertTime(millis);
					param.setTimestamp(millis);
					param.setValue(formatted);
					if(param.getSubParamaterName()!=null && !param.getSubParamaterName().equals(""))
						engParams.add(param);
				} else{
					param.setParamaterName(String.valueOf(row.getCell(0)));
					param.setSubParamaterName(subParamname);
					if(param.getSubParamaterName()!=null && !param.getSubParamaterName().equals(""))
						engParams.add(param);
				}
				
				
			}
			
			
			if(projectAlmType.equalsIgnoreCase("Sprint Wise")) {
				addSprintPlanning(engParams, commitedStories, capacity);
			}
				
			addEstimationAccuracyAndCompletion(engParams, velocity, commitedStories);
			addTestAndReviewEffectiveness(engParams, codeReviewD, sitD, customerReportedD, "Review Effectiveness");
			addTestAndReviewEffectiveness(engParams, sitD, uatD, customerReportedD, "Test Effectiveness");
			addActualHoursSpent(engParams, capacity, availableCapacity);
			addPredictableSP(engParams,velocity,projectName);
			addRetroSpectionIndex(engParams,retroOpen,retroClose,projectName);
			engSprint.setEngScoreParamData(engParams);
			subjective.getEngScorecardSprintData().add(subjectiveSprint);
			this.engScoreSubjectiveRepo.save(subjective);
			return engSprint;
		} catch (IOException e) {
			LOGGER.error(e.getMessage());
		}
		return engSprint;
	}
	

	
	String convertTime(long milli) {
		String result="";
		long seconds = milli / 1000;
		long minutes = seconds / 60;
		long hours = minutes / 60;
		// double leftMinutes=minutes%60;
		long days = hours / 24;
		long leftHours = hours % 24;
		long week = days / 7;
		long leftDays = days % 7;

		if (week != 0) {
			result = String.valueOf(week) + "w ";

		}
		if (leftDays != 0) {
			result += String.valueOf(leftDays) + "d ";
		}

		if (leftHours != 0) {
			result += String.valueOf(leftHours) + "h";
		}

		if (result.equals("")) {
			result = "0h";
		}
		
		return result;
	}
	
	
	Map<String,String> prepareSubjectiveMap(String score){
		Map<String,String> dataMap = new HashMap<String, String>();
		dataMap.put("score", score.split("\\.")[0]);
		return dataMap;
	}
	
	void addSprintPlanning(List<EngScorecardParamData> engParams,String comm,String size) {	
		double commited=0;
		double capacity=0;
		String sprintPlanning= "0";
		try {
			commited = Double.parseDouble(comm);
		}catch(Exception e) {
			commited =0;
		}
		
		try {
			capacity = Double.parseDouble(size);
		}catch(Exception e) {
			capacity=0;
		}
		 
		double val = capacity - commited;
		val = Math.abs(val);
		if(capacity>0) {
			sprintPlanning = String.valueOf(100 - (val * 100 / capacity));
			sprintPlanning = sprintPlanning.split("\\.")[0];
		}
		EngScorecardParamData param = new EngScorecardParamData();
		param.setParamaterName("Communication");
		param.setSubParamaterName("Sprint Planning");
		param.setValue(sprintPlanning);
		engParams.add(param);
	}
	
	void addPredictableSP(List<EngScorecardParamData>  engParams,String velocity,String projectName) {
		EngagementScorecardSubjectiveData dataList =  this.engScoreSubjectiveRepo.findByPName(projectName);
		double total = 0;
		double currentVelocity = 0;
		double result = 0;
		int count = 0;
		if(dataList !=null && dataList.getEngScorecardSprintData() !=null && !dataList.getEngScorecardSprintData().isEmpty()) {
			for(EngScorecardSubjectiveSprintData sprint:dataList.getEngScorecardSprintData()) {
				count++;
				try {
					total = total+ Double.parseDouble(sprint.getCommitedStoryPoints().get("score"));
				} catch(Exception e) {
					
				}
			}
		}
		
		try {
			currentVelocity = Double.parseDouble(velocity);
		} catch(Exception e) {
			currentVelocity =0 ;
		}
		
		total = total + currentVelocity;
		result = total / (count+1);
		
		EngScorecardParamData param = new EngScorecardParamData();
		param.setParamaterName("Execution Excellence");
		param.setSubParamaterName("Predictable SP");
		param.setValue(String.valueOf(result));
		engParams.add(param);
		
	}
	
	void addActualHoursSpent(List<EngScorecardParamData>  engParams,String capacity,String availableCapacity){
		double c1=0;
		double c2=0;
		try {
			c1 = Double.parseDouble(capacity);
		}catch(Exception e) {
			c1 =0;
		}
		
		try {
			c2 = Double.parseDouble(availableCapacity);
		}catch(Exception e) {
			c2=0;
		}
		double val = c1 - c2;
		String sprintPlanning = String.valueOf(val);
		sprintPlanning = sprintPlanning.split("\\.")[0];
		EngScorecardParamData param = new EngScorecardParamData();
		param.setParamaterName("Execution Excellence");
		param.setSubParamaterName("Actual Hours Spent");
		param.setValue(sprintPlanning);
		engParams.add(param);
	}
	
	void addRetroSpectionIndex(List<EngScorecardParamData> engParams,String open,String close,String projectName) {
		EngagementScorecardSubjectiveData dataList =  this.engScoreSubjectiveRepo.findByPName(projectName);
		double totalOpen = 0;
		double totalClose = 0;
		double currentOpen = 0;
		double currentClose = 0 ;
		double result=0;
		if(dataList !=null && dataList.getEngScorecardSprintData() !=null && !dataList.getEngScorecardSprintData().isEmpty()) {
			for(EngScorecardSubjectiveSprintData sprint:dataList.getEngScorecardSprintData()) {
				
				try {
					totalOpen = totalOpen+ Double.parseDouble(sprint.getRetrospectionActionOpened().get("score"));
				}catch(Exception e) {
					
				}
				
				try {
					totalClose = totalOpen+ Double.parseDouble(sprint.getRetrospectionActionClosed().get("score"));
				}catch(Exception e) {
					
				}
				
			}

		}
		
		try {
			currentOpen = Double.parseDouble(open);
		}catch(Exception e){
			currentOpen = 0;
		}
		
		try {
			currentClose = Double.parseDouble(close);
		}catch(Exception e){
			currentClose = 0;
		}
		totalOpen +=currentOpen;
		totalClose +=currentClose;
		
		if((totalOpen+totalClose) > 0) {
			result = totalOpen / (totalOpen+totalClose);
		}

		EngScorecardParamData param = new EngScorecardParamData();
		param.setParamaterName("Continuous Improvement");
		param.setSubParamaterName("Retrospection Index");
		param.setValue(String.valueOf(result));
		engParams.add(param);
		
	}
	
	void addEstimationAccuracyAndCompletion(List<EngScorecardParamData> engParams,String velocity,String comm) {
		double completed=0;
		double commited=0;
		
		try {
			commited = Double.parseDouble(comm);
		}catch(Exception e) {
			commited=0;
		}
		
		try {
			completed = Double.parseDouble(velocity);
		}catch(Exception e) {
			completed=0;
		}
		String estimationaAccuracy="0",  completion ="0";
		if(completed>0) {
		estimationaAccuracy = String.valueOf((commited/completed) *100);
		estimationaAccuracy = estimationaAccuracy.split("\\.")[0];
		}
		if(commited>0) {
		completion = String.valueOf((completed/commited)*100);
		completion = completion.split("\\.")[0];
		}
		EngScorecardParamData param = new EngScorecardParamData();
		
		param.setParamaterName("Committed vs Completed");
		param.setSubParamaterName("Estimation Accuracy");
		param.setValue(estimationaAccuracy);
		engParams.add(param);
		
		param = new EngScorecardParamData();
		param.setParamaterName("Committed vs Completed");
		param.setSubParamaterName("Completion");
		param.setValue(completion);
		engParams.add(param);
		
	}
	
	String getScoreInnumber(String value) {
		if(value.equalsIgnoreCase("Green")){
			return "100";
		} else if(value.equalsIgnoreCase("Amber")) {
			return "75";
		}else if(value.equalsIgnoreCase("Red")){
			return "0";
		}
		
		if(value.equalsIgnoreCase("Yes")) {
			return "100";
		}else if(value.equalsIgnoreCase("No")) {
			return "0";
		}
		
		return "0";
	}
	
	EngScorecard getInitialData(String pName) {
		EngScorecard scoreCard = engRepo.findByPName(pName);
		if (scoreCard == null) {
			scoreCard = new EngScorecard();
			scoreCard.setpName(pName);
			scoreCard.setEngScoreCardSprint(new LinkedList<EngScorecardSprint>());
		} 
		return scoreCard;
	}
	
	
	@Override
//	@Caching(evict= {
//			@CacheEvict(value="getEngScoreCardSubjetiveDataByProject", key ="'getEngScoreCardSubjetiveDataByProject'+#subjective.getpName()", cacheManager="timeoutCacheManager"),
//			@CacheEvict(value="getEngScoreCardQuaters", key ="'getEngScoreCardQuaters'+#subjective.getpName()", cacheManager="timeoutCacheManager")
//	})
	
	public boolean engScorecardFormUpload(EngagementScorecardSubjectiveDataReq subjective) {
		EngScorecard scorecard = getInitialData(subjective.getpName());
		List<EngScorecardSprint> sprints = scorecard.getEngScoreCardSprint();
		sprints.add(engManualValuesForm(subjective.getEngScorecardSprintData().get(0),subjective.getpName()));
		scorecard.setEngScoreCardSprint(sprints);
		engRepo.save(scorecard);
		
		EngagementScorecardSubjectiveData subjectiveData = this.engScoreSubjectiveRepo
				.findByPName(subjective.getpName());
		if (subjectiveData == null) {
			subjectiveData = new EngagementScorecardSubjectiveData();
			subjectiveData.setpName(subjective.getpName());
		}
		subjectiveData.getEngScorecardSprintData().add(subjective.getEngScorecardSprintData().get(0));
		engScoreSubjectiveRepo.save(subjectiveData);
		EIScoresCalculation eiScoreCalc= new EIScoresCalculation();
		eiScoreCalc.processEIScores(subjective.getpName());
		return true;
	}
	@Override
//	@Caching(evict= {
//			@CacheEvict(value="getEngScoreCardSubjetiveDataByProject", key ="'getEngScoreCardSubjetiveDataByProject'+#subjective.getpName()", cacheManager="timeoutCacheManager"),
//			@CacheEvict(value="getEngScoreCardQuaters", key ="'getEngScoreCardQuaters'+#subjective.getpName()", cacheManager="timeoutCacheManager")
//	})
	
	public boolean engScorecardFormUploadEdit(EngagementScorecardSubjectiveDataReq subjective) {
			
		EngScorecard scorecard = getInitialData(subjective.getpName());
		List<EngScorecardSprint> sprints = new LinkedList<EngScorecardSprint>();
		
		for(int i=0;i<subjective.getEngScorecardSprintData().size();i++) {
			sprints.add(engManualValuesForm(subjective.getEngScorecardSprintData().get(i),subjective.getpName()));
		}
		scorecard.setEngScoreCardSprint(sprints);
		if(engRepo.findByPName(subjective.getpName())!=null) {
			engRepo.delete(engRepo.findByPName(subjective.getpName()));
		}
		
		EngagementScorecardSubjectiveData subjectiveData = new EngagementScorecardSubjectiveData();
		BeanUtils.copyProperties(subjective, subjectiveData);
		if(engScoreSubjectiveRepo.findByPName(subjective.getpName())!=null) {
			engScoreSubjectiveRepo.delete(engScoreSubjectiveRepo.findByPName(subjective.getpName()));
		}
		engRepo.save(scorecard);
		engScoreSubjectiveRepo.save(subjectiveData);
		EIScoresCalculation eiScoreCalc= new EIScoresCalculation();
		eiScoreCalc.processEIScores(subjective.getpName());
		return true;
	}

	@Override
//	@Caching(evict= {
//			@CacheEvict(value="getEngScoreCardSubjetiveDataByProject", key ="'getEngScoreCardSubjetiveDataByProject'+#projectName", cacheManager="timeoutCacheManager"),
//			@CacheEvict(value="getEngScoreCardQuaters", key ="'getEngScoreCardQuaters'+#projectName", cacheManager="timeoutCacheManager")
//	})
	public boolean engScorecardExcelUpload(MultipartFile tempfile, String projectName, String sprintName,
			long startDate, long endDate, String releaseIterationNo, String unitOfSizing,String teamSize) {
		FileOutputStream fos= null;
		try {
			System.out.println("Saving file----------------------------------------------------------");
			File excelFile = new File("temp.xlsx");
			excelFile.createNewFile();
			fos = new FileOutputStream(excelFile);
			fos.write(tempfile.getBytes());
			
			
			EngScorecard scorecard = getInitialData(projectName);
			List<EngScorecardSprint> sprints = scorecard.getEngScoreCardSprint();
			sprints.add(engManualValuesExcel(projectName,sprintName,startDate,endDate,releaseIterationNo,unitOfSizing,teamSize));
			scorecard.setEngScoreCardSprint(sprints);
			engRepo.save(scorecard);
			excelFile.delete();
			EIScoresCalculation eiScoreCalc= new EIScoresCalculation();
			eiScoreCalc.processEIScores(projectName);
			return true;
		} catch (IOException e) {			
			LOGGER.error(e.getMessage());
			return false;
		} finally {
			if(fos!=null)
				try {
					fos.close();
				} catch (IOException e) {
					LOGGER.error(e.getMessage());
				}
		}

	}


	
}
