package com.bolt.dashboard.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.FeatureConfig;
import com.bolt.dashboard.core.repository.FeatureConfigRepo;

@Service
public class FeatureConfigServiceImplementation implements FeatureConfigService {
	@Autowired
	private FeatureConfigRepo featureConfigRepo;

	@Override
//	@CacheEvict(value="getFeatureConfig", key ="'getFeatureConfig'+#featureConfig.getpName()", cacheManager="timeoutCacheManager")
	public FeatureConfig saveFeatureConfig(FeatureConfig featureConfig) {
		

		FeatureConfig temp = this.featureConfigRepo.findByPName(featureConfig.getpName());

		if (temp != null) {
			this.featureConfigRepo.delete(temp);
		}
		return this.featureConfigRepo.save(featureConfig);
	}

	@Override
//	@Cacheable(value="getFeatureConfig", key ="'getFeatureConfig'+#pName", cacheManager="timeoutCacheManager")
	public FeatureConfig getFeatureConfig(String pName) {
		
		
		FeatureConfig result = this.featureConfigRepo.findByPName(pName);
		return result;
	}

}
