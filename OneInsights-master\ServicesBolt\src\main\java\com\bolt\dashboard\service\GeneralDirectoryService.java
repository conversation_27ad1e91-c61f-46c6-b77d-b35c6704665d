/**
 * 
 */
package com.bolt.dashboard.service;

import java.util.List;

import com.bolt.dashboard.core.model.GeneralDirectoryConfiguration;
import com.bolt.dashboard.response.DataResponse;

/**
 * <AUTHOR>
 *
 */
public interface GeneralDirectoryService {

    GeneralDirectoryConfiguration saveGeneralDetails(GeneralDirectoryConfiguration generalDirectoryConfiguration);

    DataResponse<List<GeneralDirectoryConfiguration>> retrieveGeneralDetails();

}
