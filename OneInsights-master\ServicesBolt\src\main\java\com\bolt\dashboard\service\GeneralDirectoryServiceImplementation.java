/**
 * 
 */
package com.bolt.dashboard.service;

import java.util.List;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.GeneralDirectoryConfiguration;
import com.bolt.dashboard.core.repository.GDConfigurationRepo;
import com.bolt.dashboard.response.DataResponse;

/**
 * <AUTHOR>
 *
 */
@Service
public class GeneralDirectoryServiceImplementation implements GeneralDirectoryService {
    private GDConfigurationRepo gdConfigurationRepo;
    private static final Logger LOG = LogManager.getLogger(GeneralDirectoryServiceImplementation.class);

    @Autowired
    public GeneralDirectoryServiceImplementation(GDConfigurationRepo repo) {
        this.gdConfigurationRepo = repo;
    }

    @Override
//    @CacheEvict(value="retrieveGeneralDetails", key ="'retrieveGeneralDetails'", cacheManager="timeoutCacheManager")
    public GeneralDirectoryConfiguration saveGeneralDetails(GeneralDirectoryConfiguration req) {

        if (gdConfigurationRepo.findByName(req.getName()) != null) {
            gdConfigurationRepo.deleteByName(req.getName());
        }
        LOG.info("General Directory Data saved successfully...");
        return gdConfigurationRepo.save(req);

    }

    @Override
//    @Cacheable(value="retrieveGeneralDetails", key ="'retrieveGeneralDetails'", cacheManager="timeoutCacheManager")
    public DataResponse<List<GeneralDirectoryConfiguration>> retrieveGeneralDetails() {
        long lastUpdate = 1;
        List<GeneralDirectoryConfiguration> result = gdConfigurationRepo.findAll();
        return new DataResponse<List<GeneralDirectoryConfiguration>>(result, lastUpdate);
    }

}
