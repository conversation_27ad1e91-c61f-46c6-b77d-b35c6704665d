package com.bolt.dashboard.service;

import com.bolt.dashboard.core.model.GoalSetting;
import com.bolt.dashboard.response.DataResponse;

public interface GoalSettingService {
    DataResponse<Iterable<GoalSetting>> getGoals(String name);

    DataResponse<Iterable<GoalSetting>> getGoals(String name, long sDate, long eDate, boolean flag);

    GoalSetting addGoals(GoalSetting req);
    
    GoalSetting addGoalsCoverage(GoalSetting req);
    
    
}