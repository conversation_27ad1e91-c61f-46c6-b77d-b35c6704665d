package com.bolt.dashboard.service;

/**
 * 
 */
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.GoalSetting;
import com.bolt.dashboard.core.repository.GoalSettingRep;
import com.bolt.dashboard.response.DataResponse;

@Service
public class GoalSettingServiceImplementation implements GoalSettingService {
	private GoalSettingRep goalSettingRepository;

	@Autowired
	public GoalSettingServiceImplementation(GoalSettingRep goalSettingRepository) {
		this.goalSettingRepository = goalSettingRepository;
	}

	@Override
	public DataResponse<Iterable<GoalSetting>> getGoals(String name) {

		long lastUpdated = 1;
		Iterable<GoalSetting> result = goalSettingRepository.findByProjectName(name);

		return new DataResponse<Iterable<GoalSetting>>(result, lastUpdated);
	}

	@Override
//	@CacheEvict(value="getGoals", key ="'retrieveGeneralDetails'+#req.getProjectName()", cacheManager="timeoutCacheManager")
	public GoalSetting addGoals(GoalSetting req) {
		Date date = new java.util.Date();
		long timeStamp = date.getTime();
		req.setTimestamp((long) timeStamp);
		return goalSettingRepository.save(req);
	}

	@Override
//	@CacheEvict(value="getGoals", key ="'retrieveGeneralDetails'+#req.getProjectName()", cacheManager="timeoutCacheManager")
	public GoalSetting addGoalsCoverage(GoalSetting req) {
		Date date = new java.util.Date();
		Iterable<GoalSetting> st = goalSettingRepository.findByProjectNameAndName(req.getProjectName(), req.getName());
		long timeStamp = date.getTime();
		if (st.iterator().hasNext()) {
			GoalSetting gs = st.iterator().next();
			gs.setConfig(req.getConfig());
			gs.setTimestamp((long) timeStamp);
			return goalSettingRepository.save(gs);
		} else {

			req.setTimestamp((long) timeStamp);
			return goalSettingRepository.save(req);
		}

	}

	@Override
//	@Cacheable(value="getGoals", key ="'retrieveGeneralDetails'+#name", cacheManager="timeoutCacheManager")
	public DataResponse<Iterable<GoalSetting>> getGoals(String name, long sDate, long eDate, boolean flag) {
		boolean flagnew = flag;
		if (!flagnew) {
			return getGoals(name);
		} else {

			long lastUpdate = 1;

			Iterable<GoalSetting> result = goalSettingRepository.findByProjectNameAndTimestampBetween(name, sDate,
					eDate);
			return new DataResponse<Iterable<GoalSetting>>(result, lastUpdate);

		}

	}
}