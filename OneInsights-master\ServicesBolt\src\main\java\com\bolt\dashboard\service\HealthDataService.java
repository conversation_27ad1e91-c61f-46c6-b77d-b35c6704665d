package com.bolt.dashboard.service;

import java.util.List;
import java.util.Set;

import com.bolt.dashboard.core.model.HealthData;
import com.bolt.dashboard.core.model.sprintPredictedBugMetrics;

public interface HealthDataService {

    List<HealthData> fetchHealthData(String projectName);

    List<HealthData> getHealthData();
    
    Set<String> getState(String projectName,String almtype);
    
    List<sprintPredictedBugMetrics> getHealthLastRecord(String projectName);

}