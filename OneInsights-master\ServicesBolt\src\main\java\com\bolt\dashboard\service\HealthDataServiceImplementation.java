package com.bolt.dashboard.service;

import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.HealthData;
import com.bolt.dashboard.core.model.sprintPredictedBugMetrics;

@Service
public class HealthDataServiceImplementation implements HealthDataService {

	@Override
	public List<HealthData> fetchHealthData(String projectName) {
		
		return null;
	}

	@Override
	public List<HealthData> getHealthData() {
		
		return null;
	}

	@Override
	public Set<String> getState(String projectName, String almtype) {
		
		return null;
	}

	@Override
	public List<sprintPredictedBugMetrics> getHealthLastRecord(String projectName) {
		
		return null;
	}}




/*package com.bolt.dashboard.service;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.ALMProject;
import com.bolt.dashboard.core.model.HealthData;
import com.bolt.dashboard.core.model.sprintPredictedBugMetrics;
import com.bolt.dashboard.core.repository.ALMRepository;
import com.bolt.dashboard.core.repository.HealthDataRepo;

@Service
public class HealthDataServiceImplementation implements HealthDataService {

	private static HealthDataRepo healthdataRepo;
	private static ALMRepository almRepository;
	private static final Log LOG = LogFactory.getLog(HealthDataServiceImplementation.class);

	*//**
	 * 
	 *//*

	@Autowired
	public HealthDataServiceImplementation(HealthDataRepo healthdataRepo, ALMRepository almRepository) {
		HealthDataServiceImplementation.healthdataRepo = healthdataRepo;
		HealthDataServiceImplementation.almRepository = almRepository;
	}

	@Override
	public List<HealthData> fetchHealthData(String projectName) {
		return (List<HealthData>) healthdataRepo.findByProjectName(projectName);

	}

	@Override
	public List<HealthData> getHealthData() {
		return (List<HealthData>) healthdataRepo.findAll();

	}

	@Override
	public Set<String> getState(String projectName, String almtype) {
		Set<String> statesInProject = null;
		statesInProject = new LinkedHashSet<>();
		List<ALMProject> data = (List<ALMProject>) almRepository.findByAlmTypeAndProjectName(almtype, projectName);
		if (!data.isEmpty()) {
			ALMProject lastRecord = data.get(data.size() - 1);
			Set<String> stateSet = lastRecord.getIteration().get(0).getMetrics().get(0).getStateSet();
			statesInProject.addAll(stateSet);
			return statesInProject;
		} else {
			LOG.info("no data found in getState() HealthDataServiceImplementation() ");
			return null;
		}
	}

	@Override
	public List<sprintPredictedBugMetrics> getHealthLastRecord(String projectName) {
		
		List<HealthData> projectHealthData = healthdataRepo.findByProjectName(projectName);
		HealthData projectHealthLastRecord = projectHealthData.get(projectHealthData.size()-1);
		List<sprintPredictedBugMetrics> predictedBugMetrics = projectHealthLastRecord.getSprintPredictedBugMetrics();
		
		return predictedBugMetrics;
	}

}*/