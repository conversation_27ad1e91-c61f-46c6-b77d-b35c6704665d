package com.bolt.dashboard.service;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.HealthIndicator;
import com.bolt.dashboard.core.repository.HealthIndicatorRep;
import com.bolt.dashboard.response.DataResponse;

@Service
public class HealthIndicatorServiceImplementation implements HealthIndicatorService {

    private HealthIndicatorRep healthRepository;
    
    public HealthIndicatorServiceImplementation() {
    }

    @Autowired
    public HealthIndicatorServiceImplementation(HealthIndicatorRep healthRepository) {
        this.healthRepository = healthRepository;
    }

    @Override
//    @Cacheable(value="getHealth", key ="'getHealth'", cacheManager="timeoutCacheManager")
    public DataResponse<Iterable<HealthIndicator>> getHealth() {
        long lastUpdated = 1;
        Iterable<HealthIndicator> result = healthRepository.findAll();
        return new DataResponse<Iterable<HealthIndicator>>(result, lastUpdated);
    }

    @Override
//    @CacheEvict(value="getHealth", key ="'getHealth'", cacheManager="timeoutCacheManager")
    public HealthIndicator addHealth(HealthIndicator req) {
        Date date = new Date();
        if (healthRepository.count() != 0) {

            deleteConfig(req);

        }
        long timeStamp = date.getTime();
        req.setTimestamp((long) timeStamp);
        return healthRepository.save(req);
    }

    @Override
    public int deleteConfig(HealthIndicator healthIndicator) {
        return healthRepository.deleteById(healthIndicator.getId());
    }

}
