/**
 * 
 */
package com.bolt.dashboard.service;

import com.bolt.dashboard.core.model.HighLightModel;
import com.bolt.dashboard.core.model.HighLightReelModel;
import com.bolt.dashboard.response.DataResponse;

/**
 * <AUTHOR>
 *
 */
public interface HighLightService {

    Boolean saveHighLightData(HighLightModel hlmServiceInstance);

    DataResponse<Iterable<HighLightModel>> fetchHighLightData();

    HighLightReelModel fetchHighLightReelData(String projectName);

    HighLightModel fetchHighLightDataProj(String pName);
}
