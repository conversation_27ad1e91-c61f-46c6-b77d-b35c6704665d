/**
 *
 */
package com.bolt.dashboard.service;

import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.HighLightModel;
import com.bolt.dashboard.core.model.HighLightReelModel;
import com.bolt.dashboard.core.repository.HighLightReelRepository;
import com.bolt.dashboard.core.repository.HighLightRepo;
//import com.bolt.dashboard.highlight.HighlightMain;
import com.bolt.dashboard.response.DataResponse;

/**
 * <AUTHOR>
 *
 */
@Service
public class HighLightServiceImplementation implements HighLightService {
    private HighLightRepo highLightServiceRepo;
    private HighLightReelRepository highLightReelRepository;
    private static final Logger LOG = LogManager.getLogger(HighLightServiceImplementation.class);

    /**
     *
     */
    @Autowired
    public HighLightServiceImplementation(HighLightRepo highLightRepo,
	    HighLightReelRepository highLightReelRepository) {
	this.highLightServiceRepo = highLightRepo;
	this.highLightReelRepository = highLightReelRepository;
    }

    @SuppressWarnings("null")
	@Override
//	@CacheEvict(value="fetchHighLightData", key ="'fetchHighLightData'", cacheManager="timeoutCacheManager")
    public Boolean saveHighLightData(HighLightModel hlmServiceInstance) {
	HighLightModel hlmServiceInstanceList = highLightServiceRepo
		.findByProjectName(hlmServiceInstance.getProjectName());
	Boolean status;
	if (hlmServiceInstanceList != null) {
		highLightServiceRepo.delete(hlmServiceInstanceList.getId());
	    highLightServiceRepo.save(hlmServiceInstance);
	    status = true;
	} else {
	    HighLightModel hlmServiceInstanceNew = hlmServiceInstance;
	    hlmServiceInstanceList = new HighLightModel();
	    hlmServiceInstanceList.setUserName(hlmServiceInstance.getUserName());
	    hlmServiceInstanceList.setProjectName(hlmServiceInstance.getProjectName());
	    hlmServiceInstanceList.setRulesListOfProject(hlmServiceInstance.getRulesListOfProject());
	    highLightServiceRepo.save(hlmServiceInstanceList);
	    LOG.info("Record for " + hlmServiceInstanceNew.getProjectName() + " updated ...");
	    status = true;
	}
	return status;
    }

    @Override
//    @Cacheable(value="fetchHighLightData", key ="'fetchHighLightData'", cacheManager="timeoutCacheManager")
    public DataResponse<Iterable<HighLightModel>> fetchHighLightData() {
	long lastUpdated = 1;
	Iterable<HighLightModel> result = highLightServiceRepo.findAll();
	return new DataResponse<Iterable<HighLightModel>>(result, lastUpdated);
    }

    @Override
    @Cacheable(value="fetchHighLightReelData", key ="'fetchHighLightReelData'+#projectName", cacheManager="timeoutCacheManager")
    public HighLightReelModel fetchHighLightReelData(String projectName) {
    	LOG.info("No cache");
	HighLightReelModel result = highLightReelRepository.findByProjectName(projectName);

	if (!(result == null)) {
	    return result;
	} else {
	    LOG.info("no data found fetchHighLightReelData() in HighLightController() for project  " + projectName);
	    return null;
	}

    }

	@Override
	public HighLightModel fetchHighLightDataProj(String pName) {
		return highLightServiceRepo.findByProjectName(pName);
	}
}
