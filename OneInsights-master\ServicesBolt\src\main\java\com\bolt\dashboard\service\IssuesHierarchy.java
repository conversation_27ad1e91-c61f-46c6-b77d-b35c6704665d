package com.bolt.dashboard.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.IssueHierarchyChild;
import com.bolt.dashboard.core.model.IssueHierarchyLink;
import com.bolt.dashboard.core.model.IssueHierrarchyNode;
import com.bolt.dashboard.core.model.IssueList;
import com.bolt.dashboard.core.model.MetricsModel;

@Service
public class IssuesHierarchy {

	List<MetricsModel> allMetrics;
	List<IssueList> mappedData;
	Map<String,List<IssueList>>  unmapped;
		
	public void  getHierarchy() {
		
		
		List<IssueList> tempCopy= new ArrayList<IssueList>();
		allMetrics.forEach(m ->{
			IssueList tempIssue = new IssueList();
			BeanUtils.copyProperties(m, tempIssue);
			tempCopy.add(tempIssue);
		});
		
				
		
		// groupby epic link
		Map<String, List<MetricsModel>> epicLinks = allMetrics.stream().collect(Collectors.groupingBy(MetricsModel::getEpicLink));

		List<String> epics = new ArrayList<String>();
		epicLinks.forEach((key, value) -> {
			if (!key.equals(" ")) {
				epics.add(key);
			}
		});

		
		//filter stories or epics with outwordLinks
		List<IssueList> story = tempCopy.stream()
		.filter((m) -> {
			if ((m.getType().equalsIgnoreCase("story") || m.getType().equalsIgnoreCase("epic")) && null!=m.getOutWardIssueLink()) {
				return true;
			}
			return false;
		})
		.collect(Collectors.toList());
		
		
		unmapped= getUnMappedData(tempCopy);
		
		//combine epic and stories into one array
		final List<IssueList> temp = new ArrayList<IssueList>();
		epics.forEach(e -> {
			IssueList child = getRelatedTaskInfo(e);
			if(child != null) {
				child.setEpicLinks(epicLinks.get(e));
				temp.add(child);
			}
		});
		story.addAll(temp);
		
		story = getDataInStructure(story); 
		List<IssueList> storyData=new ArrayList<IssueList>();
		
		Map<String,List<IssueList>> group = story.stream().collect(Collectors.groupingBy(IssueList::getType));
		
		//epic data should be first and stroy data should be at second
		if(group.get("Epic") != null) {
			storyData.addAll(group.get("Epic"));
		}
		if(group.get("Story") != null) {
			storyData.addAll(group.get("Story"));
		}
		
		
		mappedData = storyData.stream().collect(Collectors.toList());
		
	}
	
	private IssueList getRelatedTaskInfo( String e) {
		
		List<IssueList> tempCopy= new ArrayList<IssueList>();
		allMetrics.forEach(m ->{
			IssueList tempIssue = new IssueList();
			BeanUtils.copyProperties(m, tempIssue);
			tempCopy.add(tempIssue);
		});
		
		List<IssueList> data = new ArrayList<IssueList>();
		tempCopy.stream()
				.filter(m -> {
					if(m.getwId().equalsIgnoreCase(e)) {
						return true;
					}
					return false;
				})
				.forEach(m -> {
					data.add(m);
				});
		if(data.size() >0) {
			return data.get(0);
		}
		
		return null;
		
	}

	public Map<String,List<IssueList>> getUnMappedData(List<IssueList> allMetrics) {
		Map<String,List<IssueList>> unMapped = allMetrics.stream()
		.filter((m) -> {
			if (null==m.getOutWardIssueLink()) {
				return true;
			}
			return false;
		})
		.collect(Collectors.groupingBy(IssueList::getType));
		
		
		
		return unMapped;
	}
	
	public List<IssueList> getDataInStructure(List<IssueList> storyList) {
		
		
		if(storyList != null) {
			storyList.forEach(story -> {
				List<MetricsModel> relatedData = new ArrayList<MetricsModel>();
				List<MetricsModel> subTasks = new ArrayList<MetricsModel>();
				List<MetricsModel> epicLinks = new ArrayList<MetricsModel>();
				
				if(story.getOutWardIssueLink() !=null) {
					story.getOutWardIssueLink().forEach(child -> {
						IssueList childData = getRelatedTaskInfo(child);
						if(childData != null) {
							MetricsModel tempMetrics = new MetricsModel();
							BeanUtils.copyProperties(childData, tempMetrics);
							relatedData.add(tempMetrics);
						}
						
					});
					
					story.setLinks(relatedData);
				}
				
				
				if(story.getEpicLinks() != null) {
					story.getEpicLinks().forEach(l -> {
						if(l !=null) {
							epicLinks.add(l);
						}
					});
					story.setEpicArray(epicLinks);
				}
				
				
				if(story.getSubtaskList() != null) {
					story.getSubtaskList().forEach(subtask -> {
						IssueList childData = getRelatedTaskInfo(subtask);
						if(childData != null) {
							MetricsModel tempMetrics = new MetricsModel();
							BeanUtils.copyProperties(childData, tempMetrics);
							subTasks.add(tempMetrics);
						}
					});
					story.setSubTaskLists(subTasks);
				}
				
				
				
			});
		}
		
		
		return storyList;
	}
	
	
	public Map<String, List> getHierarchyData(List<MetricsModel> allmetrics) {
		this.allMetrics = allmetrics;
		getHierarchy();
		List<IssueHierrarchyNode> hierarchyView = getDataInTreeStructure();
		List<IssueHierarchyLink> unmappedHierarchyView =getUnMappedInTreeStructure();
		Map<String,List> hierarchyMap= new HashMap<String, List>();
		hierarchyMap.put("tracked", hierarchyView);
		hierarchyMap.put("untracked", unmappedHierarchyView);
		return hierarchyMap;
	}
	
	public List<IssueHierrarchyNode> getDataInTreeStructure(){
		List<IssueHierrarchyNode> hierarchyView = new ArrayList<IssueHierrarchyNode>();
		
		if(mappedData !=null) {
			mappedData.forEach(element -> {
				
				List<IssueHierarchyChild> subtakList= new ArrayList<IssueHierarchyChild>();
				List<IssueHierarchyChild> epicLinks=new ArrayList<IssueHierarchyChild>();
				IssueHierrarchyNode node = new IssueHierrarchyNode();
				node.setName(element.getwId());
				node.setSummary(element.getSumm());
				node.setState(element.getState());
				node.setNameALMProperty(element.getType());
	
				String storyId;
				final List<MetricsModel> linksData= new ArrayList<MetricsModel>();
				if(element.getLinks() !=null && element.getLinks().size()>0) {
					element.getLinks().forEach(l -> {
						if(l !=null) {
							node.setOriginalEstimation(node.getOriginalEstimation() + ( l.getOrgEst() != null ? l.getOrgEst() : 0));
							node.setActualEffort(node.getActualEffort() + (l.getEffort() !=null?l.getEffort():0));
							linksData.add(l);
						}
						
					});
				}
				
				//Adding links
				List<IssueHierarchyChild> children = getChildren(node,linksData);
				if(children !=null && children.size() >0) {
					IssueHierarchyLink c= new IssueHierarchyLink();
					c.setName("links");
					c.setChildren(children);
					node.getChildren().add(c);
					
				}
				
				
				//adding subTasks
				if(element.getSubTaskLists() !=null && element.getSubtaskList().size()>0) {		
					
					element.getSubTaskLists().forEach(subtask -> {
						if(subtask != null) {
							IssueHierarchyChild c=new IssueHierarchyChild();
							node.setOriginalEstimation(
									node.getOriginalEstimation() + (subtask.getOrgEst() != null ? subtask.getOrgEst() : 0));
							node.setActualEffort(node.getActualEffort() + (subtask.getEffort() !=null?subtask.getEffort():0));
							c.setName(subtask.getwId());
							c.setSummary(subtask.getSumm());
							c.setState(subtask.getState());
							subtakList.add(c);
						}
						
					});
					IssueHierarchyLink c=new IssueHierarchyLink();
					c.setName("SubTasks");
					c.setChildren(subtakList);
					node.getChildren().add(c);
				}
				
				//adding epics and stories
				if(element.getEpicArray() !=null && element.getEpicArray().size() >0) {
					
					element.getEpicArray().forEach(epic -> {
						if(epic !=null) {
							IssueHierarchyChild c=new IssueHierarchyChild();
							node.setOriginalEstimation(
									node.getOriginalEstimation() + (epic.getOrgEst() != null ? epic.getOrgEst() : 0));
							node.setActualEffort(node.getActualEffort() + (epic.getEffort() !=null?epic.getEffort():0));
							c.setName(epic.getwId());
							c.setSummary(epic.getSumm());
							c.setState(epic.getState());
							epicLinks.add(c);
						}
						
					});
					IssueHierarchyLink c=new IssueHierarchyLink();
					c.setName("Stories");
					c.setChildren(epicLinks);
					node.getChildren().add(c);
				}
				
				if(subtakList.size() >0) {
					node.getInfo().put("Sub tasks", subtakList.size());
				}
				if(epicLinks.size() >0) {
					node.getInfo().put("Stories", epicLinks.size());
				}
				hierarchyView.add(node);
				
			});
		}
		
		
		return hierarchyView;
	}
	
	private List<IssueHierarchyChild> getChildren(IssueHierrarchyNode node, List<MetricsModel> linksData) {
		
		List<IssueHierarchyChild> children=new ArrayList<IssueHierarchyChild>();
			
		if(linksData !=null) {
			Map<String,List<MetricsModel>> links = linksData.stream().collect(Collectors.groupingBy(MetricsModel::getType));
			
			
			links.forEach((key,value)->{
				IssueHierarchyChild child=new IssueHierarchyChild();
				child.setName(key);
				
				
				List<IssueList> tempIssueList = new ArrayList<IssueList>();
				
				value.forEach(m -> {
					IssueList tempIssue = new IssueList();
					BeanUtils.copyProperties(m, tempIssue);
					tempIssueList.add(tempIssue);
				});
				
				child.setChildren(tempIssueList);
				children.add(child);
				node.getInfo().put(key, value.size());
			});
		}
		
		return children;
	}


	public List<IssueHierarchyLink> getUnMappedInTreeStructure(){
		List<IssueHierarchyLink> nodeList =new ArrayList<IssueHierarchyLink>();
		if(unmapped != null) {

			unmapped.forEach((key,value) -> {
				IssueHierarchyLink node = new IssueHierarchyLink();
				node.setName(key);
				
				
				List<IssueHierarchyChild> children=new ArrayList<IssueHierarchyChild>();
				value.forEach(element -> {
					
					IssueHierarchyChild child = new IssueHierarchyChild();
					child.setName(element.getwId());
					child.setSummary(element.getSumm());
					child.setState(element.getState());
					children.add(child);
				});
				
				node.setChildren(children);
				nodeList.add(node);
			});
		}
		
		return nodeList;
	}
	
	
		
}
