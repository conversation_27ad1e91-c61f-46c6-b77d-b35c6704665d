/**
 * 
 */
package com.bolt.dashboard.service;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.JobDetails;
import com.bolt.dashboard.core.repository.JobDetailsRepo;
import com.bolt.dashboard.response.DataResponse;

/**
 * <AUTHOR>
 *
 */
@Service
public class JobDetailsServiceImplementation implements JobDetailsService {
    private static final Logger LOG = LogManager.getLogger(JobDetailsServiceImplementation.class);
    private JobDetailsRepo jobDetailsRepo;

    @Autowired
    public JobDetailsServiceImplementation(JobDetailsRepo repo) {
        this.jobDetailsRepo = repo;
    }

    @Override
//    @CacheEvict(value="fetchJobDetails", key ="'fetchJobDetails'+#req.getProjectName()", cacheManager="timeoutCacheManager")
    public JobDetails saveJobDetails(JobDetails req) {
        if (jobDetailsRepo.findByProjectName(req.getProjectName()) != null) {

            jobDetailsRepo.deleteByProjectName(req.getProjectName());
        }
        LOG.info("Job Details saved....");
        return jobDetailsRepo.save(req);
    }

    @Override
//    @Cacheable(value="fetchJobDetails", key ="'fetchJobDetails'+#projectName", cacheManager="timeoutCacheManager")
    public DataResponse<JobDetails> fetchJobDetails(String projectName) {
        long lastUpdate = 1;
        JobDetails result = jobDetailsRepo.findByProjectName(projectName);
        return new DataResponse<JobDetails>(result, lastUpdate);
    }

}
