package com.bolt.dashboard.service;

import java.util.List;
import java.util.Map;

import com.bolt.dashboard.engagementScorecard.ScoreCardSprintData;
import com.bolt.dashboard.util.DefectDensity;
import com.bolt.dashboard.util.DefectProductionSlippage;

public interface KanbanMetricsService {

	List<Map<String, String>> getCycleTimeMetrics(String pName);

	Map getkanbanCumulativeChart(String pName);
	Map getkanbanCumulativeChart1(String pName);

	Map getKanbanThroughputMetrics(String pName);

	Map<String,List<Long>> getKanbanDefectTrend(String pName, String pAlmType);

	List<ScoreCardSprintData> geKanbantWeeks(String pName, String almType);

	List<DefectProductionSlippage> getKanbanProductionSlippage(String pName, String pAlmType);

	List<DefectDensity> getKanbanDefectDenisty(String pName, String pAlmType);



}
