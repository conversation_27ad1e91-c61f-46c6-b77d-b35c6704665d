package com.bolt.dashboard.service;

import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.CycleTimeResponse;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.TransitionModel;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.ProjectIterationRepo;
import com.bolt.dashboard.core.repository.TransitionRepo;
import com.bolt.dashboard.engagementScorecard.EngScorecardCommonCalculations;
import com.bolt.dashboard.engagementScorecard.ScoreCardSprintData;
import com.bolt.dashboard.engagementScorecard.WeeklyAddedClosedCalculations;
import com.bolt.dashboard.util.DefectDensity;
import com.bolt.dashboard.util.DefectProductionSlippage;

@Service
public class KanbanMetricsServiceImplementation implements KanbanMetricsService {

	MetricRepo metricsRepo;
	TransitionRepo transitionRepo;
	ALMConfigRepo almConfigRepo;
	List<MetricsModel> metrics;
	List<TransitionModel> transitions;
	Map<String, List<TransitionModel>> transitionsGrouped;
	List<ALMConfiguration> almconfig;
	List<IterationOutModel> authorData;
	ProjectIterationRepo authorRepo;
	EngScorecardCommonCalculations eng = new EngScorecardCommonCalculations();
	private MongoTemplate mongo;
	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
	private long firstSprintStartDate;

	@Autowired
	KanbanMetricsServiceImplementation(MetricRepo metricsRepo, TransitionRepo transitionRepo,
			ALMConfigRepo almConfigRepo, ProjectIterationRepo authorRepo) {
		this.metricsRepo = metricsRepo;
		this.transitionRepo = transitionRepo;
		this.almConfigRepo = almConfigRepo;
		this.authorRepo = authorRepo;
		if (mongo == null) {
			try {
				mongo = DataConfig.getInstance().mongoTemplate();
			} catch (Exception e) {

			}
		}
	}

	KanbanMetricsServiceImplementation() {
		if (mongo == null) {
			try {
				mongo = DataConfig.getInstance().mongoTemplate();
			} catch (Exception e) {

			}
		}
		if (almConfigRepo == null)
			almConfigRepo = ctx.getBean(ALMConfigRepo.class);

		metricsRepo = ctx.getBean(MetricRepo.class);
	}

	@SuppressWarnings("unchecked")
	@Override
//	@Cacheable(value="KanbanMetricsgetCycleTimeMetrics", key ="'KanbanMetricsgetCycleTimeMetrics'+#pName", cacheManager="timeoutCacheManager")
	public List<Map<String, String>> getCycleTimeMetrics(String pName) {
		List<Map<String, String>> cycleTimes = new ArrayList<Map<String, String>>();
		getMetrices(pName);

		List<String> closedStates = Arrays.asList(almconfig.get(0).getCloseState());

		List<MetricsModel> filterdMetrices = metrics.stream().filter((m) -> {
			if (closedStates.contains(m.getState()) && (m.getResDate() != 0)) {
				return true;
			}
			return false;
		}).collect(Collectors.toList());

		for (MetricsModel metric : filterdMetrices) {

			// code to check if a particular WID does not have any transactions

			if (transitionsGrouped.get(metric.getwId()) != null && !transitionsGrouped.get(metric.getwId()).isEmpty())

			{
				List<String> cycleTimeStates = Arrays.asList(almconfig.get(0).getCycleTimeStates());
				long cycleTime = eng.stateTimeTransitionCalculation(transitionsGrouped.get(metric.getwId()),
						cycleTimeStates, almconfig.get(0));
				String days = eng.convertCycleTimeToDays(cycleTime);
				Map<String, String> ele = new HashedMap();
				ele.put("id", metric.getwId());
				ele.put("cycleTime", days);
				ele.put("completeDate", String.valueOf(metric.getResDate()));
				cycleTimes.add(ele);
			}

		}
		return cycleTimes;

	}

	@Override
//	@Cacheable(value="KanbanMetricsgetkanbanCumulativeChart1", key ="'KanbanMetricsgetkanbanCumulativeChart1'+#pName", cacheManager="timeoutCacheManager")
	public Map getkanbanCumulativeChart1(String pName) {
		Map<String, List<Long>> response = new HashMap<String, List<Long>>();
		Map<String, List<Long>> track = new HashMap<String, List<Long>>();
		Map addedIssues = new HashedMap();
		getMetrices(pName);
		for (MetricsModel m : metrics) {
			List<TransitionModel> trans = transitionsGrouped.get(m.getwId());
			if (trans != null && !trans.isEmpty()) {
				trans.sort(Comparator.comparing(TransitionModel::getMdfDate));
				for (TransitionModel tr : trans) {
					addToMap(track, addedIssues, tr);
				}
			}

		}

		transitions.sort(Comparator.comparing(TransitionModel::getMdfDate));
		long startTime = transitions.get(0).getMdfDate();
		long endTime = transitions.get(transitions.size() - 1).getMdfDate();
		int interval;
		final long ONE_DAY = 24 * 60 * 60 * 1000L;
		if (endTime - startTime > 90 * ONE_DAY) {
			interval = 20;
		} else if (endTime - startTime > 60 * ONE_DAY) {
			interval = 10;
		} else if (endTime - startTime > 30 * ONE_DAY) {
			interval = 5;
		} else {
			interval = 3;
		}
		ArrayList<Long> category = new ArrayList<Long>();
		do {
			startTime = startTime + (interval * ONE_DAY);
			final long start = startTime;

			for (Map.Entry state : track.entrySet()) {
				List<Long> arr;
				arr = response.get(state.getKey());
				if (arr == null) {
					arr = new ArrayList<Long>();
				}

				List<Long> currState = (List<Long>) state.getValue();
				currState = currState.stream().filter(ele -> ele <= start).collect(Collectors.toList());

				arr.add((long) currState.size());
				response.put((String) state.getKey(), arr);

			}
			category.add(startTime);

		} while (startTime <= endTime);
		response.put("categories", category);
//		System.out.println(track);
		return response;
	}

	private void addToMap(Map track, Map addedIssues, TransitionModel tr) {
		String currentState = tr.getCrState();
		if (track.containsKey(currentState)) {
			ArrayList<String> issues = (ArrayList) addedIssues.get(currentState);
			List<Long> timestamps = (List<Long>) track.get(currentState);
			if (!issues.contains(tr.getwId())) {
				issues.add(tr.getwId());
				timestamps.add(tr.getMdfDate());
			}

			track.put(currentState, timestamps);
			addedIssues.put(currentState, issues);

		} else {

			ArrayList<String> issues = new ArrayList<String>();

			List<Long> timestamps = new ArrayList<Long>();
			issues.add(tr.getwId());
			timestamps.add(tr.getMdfDate());
			track.put(currentState, timestamps);
			addedIssues.put(currentState, issues);
		}

	}

	@Override
	public Map getkanbanCumulativeChart(String pName) {
		final long ONE_DAY = 24 * 60 * 60 * 1000L;
		int interval;
		long startTime;
		long endTime;
		getMetrices(pName);
		Map<String, List<Long>> response = new HashMap<String, List<Long>>();
		Map track = new HashedMap();
		if (metrics != null && !metrics.isEmpty()) {

			transitions.sort(Comparator.comparing(TransitionModel::getMdfDate));
			startTime = transitions.get(0).getMdfDate();
			endTime = transitions.get(transitions.size() - 1).getMdfDate();
			if (endTime - startTime > 90 * ONE_DAY) {
				interval = 20;
			} else if (endTime - startTime > 60 * ONE_DAY) {
				interval = 10;
			} else if (endTime - startTime > 30 * ONE_DAY) {
				interval = 5;
			} else {
				interval = 3;
			}

			int index = 0;
			boolean added = false;
			ArrayList<Long> categories = new ArrayList<Long>();
			while (startTime < endTime) {
				final long start = startTime;

//				List<MetricsModel> filteredMetics = metrics.stream()
//						.filter((m) -> {
//							if(m.getCreateDate() > start) {
//								return false;
//							}
//							else {
//								return true;
//							}
//						})
//						.collect(Collectors.toList());

				for (MetricsModel m : metrics) {

					List<TransitionModel> transitions = transitionsGrouped.get(m.getwId());
					if (transitions != null) {
						transitions = transitions.stream().filter((t) -> {
							if (t.getMdfDate() <= start) {
								return true;
							} else {
								return false;
							}
						}).sorted((t1, t2) -> t1.getMdfDate().compareTo(t2.getMdfDate())).collect(Collectors.toList());
						if (transitions.size() > 0) {
							added = true;
							addToMap(transitions.get(transitions.size() - 1), response, index, startTime, track);
						}

					}

				}

				for (Map.Entry entry : response.entrySet()) {
					List<Integer> arr = (List<Integer>) entry.getValue();
					int i = arr.size();
					while (i <= index + 1) {
						arr.add(arr.get(i - 1));
						i++;
					}
				}

				startTime = startTime + (interval * ONE_DAY);
				if (added) {
					categories.add(start);
					index++;
				}

			}
			response.put("categories", categories);

		}
		return response;
	}

	private void addToMap(TransitionModel transition, Map response, int index, long startTime, Map track) {
		String currentState = transition.getCrState();
		if (response.containsKey(currentState)) {
			ArrayList<Integer> arr = (ArrayList) response.get(currentState);
			ArrayList<String> issues = (ArrayList) track.get(currentState);
			if (!issues.contains(transition.getwId())) {
				issues.add(transition.getwId());
			}
			if (arr.size() - 1 == index) {

				arr.set(index, issues.size());
			} else {

				arr.add(issues.size());
			}
		} else {
			ArrayList<Integer> arr = new ArrayList();
			ArrayList<String> issues = new ArrayList<String>();
			for (int i = 0; i < index; i++) {
				arr.add(0);

			}
			issues.add(transition.getwId());
			arr.add(1);
			response.put(currentState, arr);
			track.put(currentState, issues);
		}

	}

	@SuppressWarnings("unchecked")
	@Override
//	@Cacheable(value="KanbanMetricsgetKanbanThroughputMetrics", key ="'KanbanMetricsgetKanbanThroughputMetrics'+#pName", cacheManager="timeoutCacheManager")
	public Map getKanbanThroughputMetrics(String pName) {

		Map<String, CycleTimeResponse> throughputMetric = new HashedMap();
		getMetrices(pName);

		List<String> closedStates = Arrays.asList(almconfig.get(0).getCloseState());

		List<MetricsModel> filterdMetrices = metrics.stream().filter((m) -> {
			if (closedStates.contains(m.getState()) && (m.getResDate() != 0)) {
				return true;
			}
			return false;
		}).collect(Collectors.toList());

		List<String> cycleTimes = new ArrayList<>();
		List<String> throughputStates = new ArrayList<String>();

		if (almconfig.get(0).getThroughputStates() != null) {
			throughputStates = Arrays.asList(almconfig.get(0).getThroughputStates());
		}

		for (MetricsModel metric : filterdMetrices) {
			long frequencyTime = eng.stateTimeTransitionCalculation(transitionsGrouped.get(metric.getwId()),
					throughputStates, almconfig.get(0));
			String days = eng.convertCycleTimeToDays(frequencyTime);
			cycleTimes.add(days);
		}

		CycleTimeResponse cycleTimeResponse;

		for (String day : cycleTimes) {
			cycleTimeResponse = new CycleTimeResponse();
			List<Map> map = new ArrayList<>();
			Map map1 = new HashMap<>();
			Long count = 0L;
			for (int i = 0; i < filterdMetrices.size(); i++) {
				long frequencyTimesecond = eng.stateTimeTransitionCalculation(
						transitionsGrouped.get(filterdMetrices.get(i).getwId()), throughputStates, almconfig.get(0));
				String dayssecond = eng.convertCycleTimeToDays(frequencyTimesecond);
				if (day.equalsIgnoreCase(dayssecond)) {
					count = count + 1;
					map1 = new HashedMap();
					map1.put("id", filterdMetrices.get(i).getwId());
					map.add(map1);
				}
			}
			cycleTimeResponse.setIssueList(map);
			int noOfDays = Integer.parseInt(day);
			if (noOfDays != 0) {
				int throughput = (int) (count / noOfDays);
				cycleTimeResponse.setTotalCount(count);
				cycleTimeResponse.setThroughput(throughput);

				throughputMetric.put(day, cycleTimeResponse);
			}
		}

		return throughputMetric;
	}

	private void getMetrices(String pName) {

		metrics = metricsRepo.findByPName(pName);

		transitions = transitionRepo.findByPName(pName);

		almconfig = almConfigRepo.findByProjectName(pName);

		transitionsGrouped = transitions.stream().collect(Collectors.groupingBy(TransitionModel::getwId));
	}

	private void getMetrices(String pName, String pAlmType) {

		metrics = metricsRepo.findByPName(pName);

		transitions = transitionRepo.findByPName(pName);

		almconfig = almConfigRepo.findByProjectName(pName);

		transitionsGrouped = transitions.stream().collect(Collectors.groupingBy(TransitionModel::getwId));
	}

	@Override
//	@Cacheable(value="KanbanMetricsgetKanbanDefectTrend", key ="'KanbanMetricsgetKanbanDefectTrend'+#pName+#pAlmType", cacheManager="timeoutCacheManager")
	public Map<String, List<Long>> getKanbanDefectTrend(String pName, String pAlmType) {
		getMetrices(pName, pAlmType);
		final long ONE_DAY = 24 * 60 * 60 * 1000L;
		List<Long> added = new ArrayList<Long>();
		List<Long> closed = new ArrayList<Long>();
		List<Long> dates = new ArrayList<Long>();
		List<String> addedDefects = new ArrayList<String>();
		List<String> closedDefetcs = new ArrayList<String>();

		Map<String, List<Long>> response = new HashMap<String, List<Long>>();
		String defectName = almconfig.get(0).getDefectName();
		List<String> closedStates = Arrays.asList(almconfig.get(0).getCloseState());

		List<MetricsModel> defects = metrics.stream().filter((m) -> {
			if (m.getType().equalsIgnoreCase(defectName)) {
				return true;
			} else {
				return false;
			}
		}).collect(Collectors.toList());
		defects.sort(Comparator.comparing(MetricsModel::getCreateDate));

		long now = defects.get(defects.size() - 1).getCreateDate();
		for (int i = 0; i < 53; i++) {

			now = now - (7 * ONE_DAY);

			long add = 0, close = 0;
			Iterator<MetricsModel> it = defects.iterator();
			while (it.hasNext()) {
				MetricsModel defect = it.next();
				if (!closedDefetcs.contains(defect.getwId())) {
					List<TransitionModel> trans = transitionsGrouped.get(defect.getwId());
					if (trans != null) {
						trans.sort(Comparator.comparing(TransitionModel::getMdfDate));
						TransitionModel lastTrans = trans.get(trans.size() - 1);
						if (closedStates.contains(lastTrans.getCrState())) {
							if (lastTrans.getMdfDate() >= now) {
								close++;
								closedDefetcs.add(lastTrans.getwId());
							}
						}
					}
				}

				if (!addedDefects.contains(defect.getwId()) && defect.getCreateDate() >= now) {
					add++;
					addedDefects.add(defect.getwId());
				}
			}
			added.add(add);
			closed.add(close);
			dates.add(now);

			if (now <= defects.get(0).getCreateDate()) {
				break;
			}

		}

		response.put("added", added);
		response.put("closed", closed);
		response.put("dates", dates);

		return response;
	}

	@Override
//	@Cacheable(value="KanbanMetricsgeKanbantWeeks", key ="'KanbanMetricsgeKanbantWeeks'+#pName+#pAlmType", cacheManager="timeoutCacheManager")
	public List<ScoreCardSprintData> geKanbantWeeks(String pName, String pAlmType) {
		WeeklyAddedClosedCalculations weeks = new WeeklyAddedClosedCalculations();
		authorData = authorRepo.findByPNameAndPAlmType(pName, pAlmType);
		if (authorData != null && authorData.size() > 0) {
			return weeks.getWeeks(pName, pAlmType, authorData.get(0).getMetrics());
		}
		return null;

	}

//	public static void main(String[] args) {
//		new KanbanMetricsServiceImplementation().getKanbanProductionSlippage("Project J", "JIRA-KANBAN");
//	}

	@Override
//	@Cacheable(value="KanbanMetricsgetKanbanProductionSlippage", key ="'KanbanMetricsgetKanbanProductionSlippage'+#pName+#pAlmType", cacheManager="timeoutCacheManager")
	public List<DefectProductionSlippage> getKanbanProductionSlippage(String pName, String pAlmType) {
		List<DefectProductionSlippage> prodSlippage = new ArrayList<DefectProductionSlippage>();
		getMetrices(pName, pAlmType);
		DecimalFormat df = new DecimalFormat("###.##");
		float prodCount = 0;
		float testCount = 0;

		final long ONE_DAY = 24 * 60 * 60 * 1000L;
		List<Double> productionSlippage = new ArrayList<Double>();
		List<Double> testEffective = new ArrayList<Double>();

		List<String> categorySlippage = new ArrayList<String>();
		String defectName = almconfig.get(0).getDefectName();

		List<MetricsModel> totalBugsTable = metrics.stream().filter((m) -> {
			if (m.getType().equalsIgnoreCase(defectName)) {
				return true;
			} else {
				return false;
			}
		}).collect(Collectors.toList());
		totalBugsTable.sort(Comparator.comparing(MetricsModel::getCreateDate));

		long now = new Date().getTime();
		Date currentDate = new Date(now);

		// printing value of Date
		System.out.println("current Date: " + currentDate);

		DateFormat df1 = new SimpleDateFormat("MMM-dd");

		// formatted value of current Date

		for (int i = 0; i < 53; i++) {

			System.out.println("Milliseconds to Date: " + df1.format(currentDate));
			long startDate = now - ((53 - i) * 7 * ONE_DAY);
			Date startDate1 = new Date(startDate);
			System.out.println("startDate1 Date: " + startDate1);

			long endDate = startDate + (7 * ONE_DAY);
			Date endDate1 = new Date(endDate);
			System.out.println("endDate1 Date: " + endDate1);

			long startDatePresentBugs = now - ((53 - i) * 7 * ONE_DAY);
			long endDatePresentBugs = startDate + (7 * ONE_DAY);
			long startDateNextBugs, endDateNextBugs;
			if (i < 52) {
				startDateNextBugs = endDatePresentBugs;
				endDateNextBugs = startDateNextBugs + (7 * ONE_DAY);
			} else {
				startDateNextBugs = now - (7 * ONE_DAY);
				endDateNextBugs = now;
			}

			if (almconfig.get(0).getTestingPhase() != null && almconfig.get(0).getTestingPhase().length > 0) {
				prodCount = totalBugsTable.stream()
						.filter(o -> o.getCreateDate() > endDatePresentBugs && o.getCreateDate() < endDateNextBugs
								&& !(Arrays.asList(almconfig.get(0).getTestingPhase()).contains(o.getWhenFound())))
						.collect(Collectors.toList()).size();

				testCount = totalBugsTable.stream()
						.filter(o -> o.getCreateDate() > startDatePresentBugs && o.getCreateDate() < startDateNextBugs
								&& Arrays.asList(almconfig.get(0).getTestingPhase()).contains(o.getWhenFound()))
						.collect(Collectors.toList()).size();

			}

			if (i > 53) {
				prodCount = 0;
				testCount = 0;

			}

			if ((testCount + prodCount) != 0) {
				double val = ((prodCount / (testCount + prodCount)) * 100);
				productionSlippage.add(Double.valueOf(df.format(val)));
				double valtest = ((testCount / (testCount + prodCount)) * 100);
				testEffective.add(Double.valueOf(df.format(valtest)));
			} else {
				productionSlippage.add(0.0);
				testEffective.add(100.0);
			}
			categorySlippage.add(df1.format(endDate1));
		}

		DefectProductionSlippage prodSlippageObj = new DefectProductionSlippage();
		prodSlippageObj.setCategory(categorySlippage);
		prodSlippageObj.setTestEffective(testEffective);
		prodSlippageObj.setProductionSlippage(productionSlippage);
		prodSlippage.add(prodSlippageObj);

		return prodSlippage;
	}

	@Override
//	@Cacheable(value="KanbanMetricsgetKanbanDefectDenisty", key ="'KanbanMetricsgetKanbanDefectDenisty'+#pName+#pAlmType", cacheManager="timeoutCacheManager")
	public List<DefectDensity> getKanbanDefectDenisty(String pName, String pAlmType) {
		List<DefectDensity> defectDensity = new ArrayList<DefectDensity>();
		getMetrices(pName, pAlmType);
		DecimalFormat df = new DecimalFormat("###.##");
		final long ONE_DAY = 24 * 60 * 60 * 1000L;
		List<MonogOutMetrics> sprintsData = new ArrayList<MonogOutMetrics>();

		String defectName = almconfig.get(0).getDefectName();
		Query q = new Query();
		q.addCriteria(Criteria.where("pName").is(pName).and("sName").nin("BACKLOG", "Backlog", "backlog", "BackLog")
				.and("state").nin("FUTURE", "Future", "future"));
		q.with(new Sort(Sort.Direction.ASC, "stDate"));
		List<IterationOutModel> activeSprints = mongo.find(q, IterationOutModel.class, "Author");
		List<Double> sprintDefectDensity = new ArrayList<Double>();
		List<String> category = new ArrayList<String>();

		List<MetricsModel> totalBugsTable = metrics.stream().filter((m) -> {
			if (m.getType().equalsIgnoreCase(defectName)) {
				return true;
			} else {
				return false;
			}
		}).collect(Collectors.toList());
		totalBugsTable.sort(Comparator.comparing(MetricsModel::getCreateDate));

		long now = new Date().getTime();
		Date currentDate = new Date(now);

		DateFormat df1 = new SimpleDateFormat("MMM-dd");
		// one year back, start date
		firstSprintStartDate = now - (52 * 7 * ONE_DAY);
		for (IterationOutModel m : activeSprints) {
			if (m.getMetrics() != null)
				sprintsData.addAll(m.getMetrics());
		}

		for (int i = 0; i < 53; i++) {
			long startDate = now - ((53 - i) * 7 * ONE_DAY);
			Date startDate1 = new Date(startDate);
			System.out.println("startDate1 Date: " + startDate1);

			long endDate = startDate + (7 * ONE_DAY);
			Date endDate1 = new Date(endDate);
			System.out.println("endDate1 Date: " + endDate1);

			List<MonogOutMetrics> completeSp = new ArrayList<MonogOutMetrics>();
			List<MonogOutMetrics> defects = new ArrayList<MonogOutMetrics>();

			if (i != 53 - 1) {
				completeSp = getMerics(sprintsData, "Story", endDate);
				defects = getMerics(sprintsData, almconfig.get(0).getDefectName(), endDate);
			} else {
				completeSp = getMerics(sprintsData, "Story", startDate);
				defects = getMerics(sprintsData, almconfig.get(0).getDefectName(), startDate);
			}

			double storypoint = getStorypoint(completeSp);
			if (storypoint == 0) {
				sprintDefectDensity.add(0.0);
			} else {
				double valtest = (defects.size() / storypoint * 100) / 100;
				sprintDefectDensity.add(Double.valueOf(df.format(valtest)));
			}
			category.add(df1.format(endDate1));
		}

		DefectDensity density = new DefectDensity();
		density.setCategory(category);
		density.setDefectDenisty(sprintDefectDensity);
		defectDensity.add(density);

		return defectDensity;
	}

	public double getStorypoint(List<MonogOutMetrics> storydata) {
		int stpoint = 0;
		for (MonogOutMetrics s : storydata) {
			if (s.getStoryPoints() != null) {

				Map<Long, Double> spData = s.getStoryPoints();
				TreeMap<Long, Double> sorted = new TreeMap<>();
				// Copy all data from hashMap into TreeMap
				sorted.putAll(spData);

				stpoint += ((Double) sorted.values().toArray()[sorted.size() - 1]);
			}

		}

		return stpoint;
	}

	public List<MonogOutMetrics> getMerics(List<MonogOutMetrics> metricsdata, String issue, long nextSp) {
		List<MonogOutMetrics> cStory = new ArrayList<MonogOutMetrics>();
		if (issue.equals("Story")) {
			cStory = metricsdata.stream().filter(m -> (m.getType().equals(issue)
					&& m.getCreateDate() >= firstSprintStartDate && m.getResDate() > 0 && m.getCreateDate() <= nextSp))
					.collect(Collectors.toList());

		} else {
			cStory = metricsdata.stream().filter(m -> (m.getType().equals(issue)
					&& m.getCreateDate() >= firstSprintStartDate && m.getCreateDate() <= nextSp))
					.collect(Collectors.toList());

		}
		return cStory;
	}

}