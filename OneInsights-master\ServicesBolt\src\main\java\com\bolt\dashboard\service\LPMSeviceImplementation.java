package com.bolt.dashboard.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.CustomFields;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.repository.LPMRepo;
import com.bolt.dashboard.core.repository.OpsRampRepo;
import com.bolt.dashboard.jenkins.JenkinsApplication;

@Service
public class LPMSeviceImplementation implements LPMService {
	private static final Logger LOGGER = LogManager.getLogger(OpsRampSeviceImplementation.class);
	LPMRepo lpmRepo;
	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
	String projectName;
	@Autowired
	public LPMSeviceImplementation(LPMRepo repo)  {
		this.lpmRepo = repo;
		
	}


	@Override
//	@Cacheable(value="LPMgetPortfolioHorizonAndEpicInvestment", key ="'LPMgetPortfolioHorizonAndEpicInvestment'+#projName", cacheManager="timeoutCacheManager")
	public HashMap<String, List<MetricsModel>> getPortfolioHorizonAndEpicInvestment(String projName) {

		// get the EPIC , category by Portfolio
		this.projectName = projName;
		List<MetricsModel> response = new ArrayList<MetricsModel>();

		List<MetricsModel> issueList = lpmRepo.findByPNameAndTypeIgnoreCase(projName, "Epic");
		List<MetricsModel> issuePortfolioList = lpmRepo.findByPNameAndTypeIgnoreCase(projName, "Portfolio");
		response.addAll(issuePortfolioList);
		response.addAll(issueList);
		

		LOGGER.info(issueList.size());
		HashMap<String, List<MetricsModel>> mappedData = mapWithPortfolio(response);
		return mappedData;
	}

	@Override
//	@Cacheable(value="LPMgetStrategicThemeData", key ="'LPMgetStrategicThemeData'+#projName", cacheManager="timeoutCacheManager")
	public HashMap<String, List<MetricsModel>> getStrategicThemeData(String projName) {
		this.projectName = projName;
		List<MetricsModel> strategicThemeList = lpmRepo.findByPNameAndTypeIgnoreCase(projName, "Strategic Theme");
		HashMap<String,List<MetricsModel>> response = mapWithPortfolio(strategicThemeList);
		return response;
	}
	
	HashMap<String,List<MetricsModel>> mapWithPortfolio(List<MetricsModel> metrics){

		List<MetricsModel> portfolios = lpmRepo.findByPNameAndTypeIgnoreCase(projectName, "Portfolio");
		HashMap<String,List<MetricsModel>> mappedData =new HashMap<String, List<MetricsModel>>();
		HashMap<String,String> portfolioIdAndNames = new HashMap<String, String>();
		for(MetricsModel ele:portfolios) {
			portfolioIdAndNames.put(ele.getwId(), ele.getSumm());
		}
		
		
		for(MetricsModel m:metrics) {
			if(m.getType().equalsIgnoreCase("Portfolio")) {
				if(mappedData.get(m.getSumm()) ==null) {
					mappedData.put(m.getSumm(), new ArrayList<MetricsModel>());
				}
				mappedData.get(m.getSumm()).add(m);
			}else {
				if(m.getInWardIssueLink() != null) {
					for(int i=0;i<m.getInWardIssueLink().size();i++) {
						String portfolioName = portfolioIdAndNames.get(m.getInWardIssueLink().get(i));
						if( portfolioName != null) {
							if(mappedData.get(portfolioName) ==null) {
								mappedData.put(portfolioName, new ArrayList<MetricsModel>());
							}
							mappedData.get(portfolioName).add(m);
						}
					}
				}
			}


		}
		return mappedData;
	}

	@Override
//	@Cacheable(value="LPMgetPortfolioNames", key ="'LPMgetPortfolioNames'+#projName", cacheManager="timeoutCacheManager")
	public List<String> getPortfolioNames(String projName) {

		List<MetricsModel> portfolios = lpmRepo.findByPNameAndTypeIgnoreCase(projName, "Portfolio");
		List<String> portfolioNames = new ArrayList<String>();
		for(MetricsModel m:portfolios) {
			portfolioNames.add(m.getSumm());
		}
		return portfolioNames;
	}


	@Override
//	@Cacheable(value="LPMgetPortfolioData", key ="'LPMgetPortfolioData'+#projName", cacheManager="timeoutCacheManager")
	public List<MetricsModel> getPortfolioData(String projName) {
		this.projectName = projName;
		return lpmRepo.findByPNameAndTypeIgnoreCase(projName, "Portfolio");
	}
}
