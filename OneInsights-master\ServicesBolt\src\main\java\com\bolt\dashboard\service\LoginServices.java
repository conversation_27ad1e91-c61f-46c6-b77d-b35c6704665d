package com.bolt.dashboard.service;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.bolt.dashboard.core.model.ManageUser;
import com.bolt.dashboard.request.ManageUserReq;
import com.bolt.dashboard.response.DataResponse;
import com.fasterxml.jackson.core.JsonGenerationException;
import com.fasterxml.jackson.databind.JsonMappingException;

public interface LoginServices {

    DataResponse<Iterable<ManageUser>> getSignIn();

    boolean destroy(HttpServletRequest request, HttpSession httpSession);

    Object checkLogin(ManageUserReq req, HttpServletRequest request);

    Object checkEmail(String req, HttpServletRequest request);
    
    Object checkUserSSO(String req, HttpServletRequest request, HttpServletResponse response) throws IOException;
    
    Object checkLoginForActiveDirectory(ManageUserReq req, HttpServletRequest request, HttpSession httpSession);
    
    void refreshToken(HttpServletRequest request, HttpServletResponse response) throws JsonGenerationException, JsonMappingException, IOException;
}
