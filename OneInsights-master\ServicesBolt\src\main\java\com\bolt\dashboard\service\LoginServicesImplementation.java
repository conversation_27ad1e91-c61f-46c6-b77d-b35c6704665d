package com.bolt.dashboard.service;

import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.HttpStatus.FORBIDDEN;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Pattern;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.naming.Context;
import javax.naming.NamingEnumeration;
import javax.naming.directory.Attributes;
import javax.naming.directory.SearchControls;
import javax.naming.directory.SearchResult;
import javax.naming.ldap.InitialLdapContext;
import javax.naming.ldap.LdapContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.mindrot.jbcrypt.BCrypt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.stereotype.Service;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.bolt.dashboard.bo.ActiveDirectoryBO;
import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ActiveDirectoryConfiguration;
import com.bolt.dashboard.core.model.ManageUser;
import com.bolt.dashboard.core.model.ProductLicenceConfig;
import com.bolt.dashboard.core.repository.ADConfigurationRepo;
import com.bolt.dashboard.core.repository.UserRepo;
import com.bolt.dashboard.request.ManageUserReq;
import com.bolt.dashboard.response.DataResponse;
import com.fasterxml.jackson.core.JsonGenerationException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class LoginServicesImplementation implements LoginServices {
    private UserRepo loginRepository;
    private UserService userService;
    private ProductLicenceService productLicenceService;
    private ADConfigurationRepo repo;
    static String fullName = null;
    String userEmailId = null;
    LdapContext ctx = null;
    AnnotationConfigApplicationContext context = null;
    final static String HASH_STARTSWITH = "$2a$";
    String activeState = "Active";
    private static final Logger LOG = LogManager.getLogger(LoginServicesImplementation.class);
    
    @Value("${config.secret}")
	private String secret;
	InputStream in = null;

    @Autowired
    public LoginServicesImplementation(UserRepo loginRepository,ADConfigurationRepo repo,UserService service,ProductLicenceService productLicenceService) {
	this.loginRepository = loginRepository;
	this.repo = repo;
	this.userService = service;
	this.productLicenceService = productLicenceService;
	this.context = DataConfig.getContext();
	

	
    }
    @Override
    public DataResponse<Iterable<ManageUser>> getSignIn() {
	long lastUpdate = 1;
	Iterable<ManageUser> result = loginRepository.findAll();
	return new DataResponse<Iterable<ManageUser>>(result, lastUpdate);
    }

    public boolean getActiveStatus(String userName) {
	List<ActiveDirectoryConfiguration> list = repo.findAll();
	ActiveDirectoryConfiguration adConfiguration = list.get(0);
	String ldapHost = adConfiguration.getHost();
	loginRepository.findByEmail(userName.concat("@").concat(ldapHost).toLowerCase());

	for (ManageUser login : loginRepository.findAll()) {
	    if (login.getEmail().equalsIgnoreCase(userName.concat("@").concat(ldapHost).toLowerCase())
		    && activeState.equals(login.getStatus())) {
		return true;
	    }
	}
	return false;
    }

    public boolean checkExpiry(String key) {
	try {

	    String info = new ProductLicenceServiceImplementation().decrypt(key);
	    Date expiryDate = null;
	    if (info.contains("@#")) {
		String expDate = info.split(Pattern.quote("@#"))[0];
		DateFormat fromDF = new SimpleDateFormat("dd/MMM/yyyy");
		fromDF.setLenient(false);
		expiryDate = fromDF.parse(expDate);

		if (expiryDate.before(new Date())) {
		    return false;
		}
	    } else {
		return false;
	    }
	} catch (InvalidKeyException | NoSuchAlgorithmException | InvalidKeySpecException | NoSuchPaddingException
		| InvalidAlgorithmParameterException | IllegalBlockSizeException | BadPaddingException | IOException
		| ParseException e) {
	    LOG.info(e);
	    return false;
	}
	return true;
    }

    @Override
    public Object checkLogin(ManageUserReq req, HttpServletRequest request) {
	String userName = req.getUserName();
	String password = req.getPassword();
	List<ManageUser> result = loginRepository.findByUserName(userName);
	ActiveDirectoryBO bo = new ActiveDirectoryBO();
	boolean deleteMsgFlag =false;
	boolean checkExpiryFlag =false;
	for (ManageUser login : result) {
	    String dbUserName = login.getUserName();
	    String dbUserpassword = login.getPassword();
	    String dbUserStatus = login.getStatus();
	    String userRole = login.getUserRole();
	    if (dbUserName.equalsIgnoreCase(userName) && checkPassword(password, dbUserpassword)) {
	    	if (!activeState.equalsIgnoreCase(dbUserStatus) || login.getDeleteFlag()) {
	    		deleteMsgFlag=true;
	    	}
		if (activeState.equalsIgnoreCase(dbUserStatus) && !login.getDeleteFlag()) {
			
		    checkExpiryFlag = checkExpiry(login.getLicenceKey());
		    if (checkExpiryFlag) {
			HttpSession session = request.getSession();
			//Random randomGenerator = new Random();
			SecureRandom randomGenerator = new SecureRandom();
			long randomInt = randomGenerator.nextInt(1000000000);
			String random = Long.toString(randomInt);
			String myString = userName;
			String ab = myString.substring(2);
			String bb = myString.substring(2, 4);
			String token = ab;
			token = token.concat(random);
			token = token.concat(bb);
			session.setAttribute(ConstantVariable.KYWRD_UNAME, token);
			bo.setSession(session.getAttribute(ConstantVariable.KYWRD_UNAME));
			bo.setUserRole(userRole);
			return bo;

		    } 
          } 
	    }else {
	    	return "Check your Username/Password";
	    }
	}
	if(deleteMsgFlag) {
	    return "Access Denied. Kindly contact BrillioOne.ai insights team to activate or create user id.";
	}
	if(!checkExpiryFlag) {
			return "Your product lincese has expired. Contact BrillioOne.ai insights team";
	}
	return false;
    }

    public static boolean checkPassword(String passwordPlaintext, String storedHash) {
	boolean passwordVerified = false;

	if (null == storedHash || !storedHash.startsWith(HASH_STARTSWITH))
	    throw new java.lang.IllegalArgumentException("Invalid hash provided for comparison");

	passwordVerified = BCrypt.checkpw(passwordPlaintext, storedHash);

	return (passwordVerified);
    }

    @Override
    public boolean destroy(HttpServletRequest request, HttpSession httpSession) {
	if (request.getParameter(ConstantVariable.KYWRD_UNAME) != null) {
	    httpSession.invalidate();
	    return true;
	}
	return false;
    }

    @Override
    public Object checkLoginForActiveDirectory(ManageUserReq req, HttpServletRequest request, HttpSession httpSession) {
	String userName = req.getUserName();
	String password = req.getPassword();
	boolean adStatus = getActiveDirectoryStatus(userName,
		password);
	List<ActiveDirectoryConfiguration> list = repo.findAll();
	ActiveDirectoryConfiguration adConfiguration = list.get(0);
	String ldapHost = adConfiguration.getHost();
	String emailId = null;
	if (userName.contains("@")) {
	    emailId = userName;
	} else {
	    emailId = userName.concat("@").concat(ldapHost.toLowerCase());
	}
	if (adStatus) {
	    String fromMongo = getLicenceKeyForAD(emailId);
	    String licKey = null;
	    String userStatus = null;
	    String userRole = null;
	    if (fromMongo.contains("@#@")) {
		String[] temp = fromMongo.split(Pattern.quote("@#@"));
		licKey = temp[0];
		userStatus = temp[1];
		userRole = temp[2];
	    }
	    boolean flag = checkExpiry(licKey);
	    boolean activeStatus = getActiveStatus(userName);
	    if (activeState.equals(userStatus) || activeStatus) {
		if (flag) {
		    HttpSession session = request.getSession();
		    //Random randomGenerator = new Random();
		    SecureRandom randomGenerator = new SecureRandom();
		    long randomInt = randomGenerator.nextInt(1000000000);
		    String random = Long.toString(randomInt);
		    String myString = userName;
		    String ab = myString.substring(2);
		    String bb = myString.substring(2, 4);
		    String token = ab;
		    token = token.concat(random);
		    token = token.concat(bb);
		    session.setAttribute(ConstantVariable.KYWRD_UNAME, token);
		    ActiveDirectoryBO bo = new ActiveDirectoryBO();
		    String name = getFullName(fullName);
		    bo.setFullName(name);
		    bo.setEmialId(userEmailId);
		    bo.setUserRole(userRole);
		    bo.setSession(session.getAttribute(ConstantVariable.KYWRD_UNAME));
		    LOG.info("full name  " + name);
		    return bo;

		} else {
		    return "Your product lincese has expired. Contact BrillioOne.ai insights team";
		}
	    } else {
		return "Access Denied. Kindly contact BrillioOne.ai insights team to activate or create user id.";
	    }
	}
	return "Invalid Username/Password combination";
    }

    public String getFullName(String fullName) {
	String[] fullNameSplit;
	String tmpFullName = fullName;
	if (fullName.contains(",")) {
	    fullNameSplit = fullName.split(Pattern.quote(","));
	    tmpFullName = fullNameSplit[0];
	}
	return tmpFullName;

    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    public boolean getActiveDirectoryStatus(String userName, String password) {
	String concatUserName = null;
	String attributeUserName = null;
	// context = DataConfig.getContext();
//	ADConfigurationRepo repo = context.getBean(ADConfigurationRepo.class);
	List<ActiveDirectoryConfiguration> list = repo.findAll();
	ActiveDirectoryConfiguration adConfiguration = list.get(0);
	String ldapHost = adConfiguration.getHost();
	String ldapPort = adConfiguration.getPort();
	String baseDN = adConfiguration.getBasedn();
	String authMethod = "simple";
	if (userName.contains("@")) {
	    concatUserName = userName;
	    String[] nameArray = new String[3];
	    if (concatUserName.contains("@")) {
		nameArray = concatUserName.split(Pattern.quote("@"));
		attributeUserName = nameArray[0];
	    }
	} else {
	    concatUserName = userName + "@" + ldapHost.toLowerCase();
	    attributeUserName = userName;
	}
	String ldapVersion = "3";
	Hashtable env = new Hashtable();
	env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
	env.put(Context.PROVIDER_URL, "ldap://" + ldapHost + ":" + ldapPort);
	env.put(Context.SECURITY_AUTHENTICATION, authMethod);
	env.put(Context.SECURITY_PRINCIPAL, concatUserName);
	env.put(Context.SECURITY_CREDENTIALS, password);
	env.put("java.naming.ldap.version", ldapVersion);
	try {
	    LOG.info("Connecting to host " + ldapHost + " at port " + ldapPort + "...");
	    ctx = new InitialLdapContext(env, null);
	    LOG.info("LDAP authentication successful!");
	    SearchControls constraints = new SearchControls();
	    constraints.setSearchScope(SearchControls.SUBTREE_SCOPE);
	    String[] attrIDs = { "distinguishedName", "sn", "givenname", "mail", "telephonenumber", "canonicalName",
		    "userAccountControl", "accountExpires" };
	    constraints.setReturningAttributes(attrIDs);
	    NamingEnumeration answer = ctx.search(baseDN, "sAMAccountName=" + attributeUserName, constraints);
	    if (answer.hasMore()) {
		Attributes attrs = ((SearchResult) answer.next()).getAttributes();
		String name = attrs.get("distinguishedName").toString();
		fullName = getName(name);
		userEmailId = attrs.get("mail").toString();
	    }
	    return true;
	} catch (Exception e) {
	    LOG.info(e);
	    return false;
	}
	
	
    }

    public String getName(String name) {
	String[] fileNameArray = null;
	String[] nameArray = null;
	String[] actualNameArray = null;
	String tmpName = null;
	if (name.contains(":")) {
	    fileNameArray = name.split(Pattern.quote(":"));
	    tmpName = fileNameArray[1];
	    if (name.contains(",")) {
		nameArray = name.split(Pattern.quote(","));
		tmpName = nameArray[0];
		if (name.contains("=")) {
		    actualNameArray = name.split(Pattern.quote("="));
		    tmpName = actualNameArray[1];
		}
	    }
	}
	return tmpName;
    }

    public String getLicenceKeyForAD(String email) {
	String key = null;
	for (ManageUser user : loginRepository.findByEmail(email)) {
	    key = user.getLicenceKey() + "@#@" + user.getStatus() + "@#@" + user.getUserRole();
	}
	return key;
    }

	@Override
	public Object checkEmail(String email, HttpServletRequest request) {
		List<ManageUser> result =  (List<ManageUser>) loginRepository.findAll();
		ManageUser dbUser = null;
		
		for(ManageUser user:result) {
			if(user.getEmail().equalsIgnoreCase(email)) {
				dbUser = user;
			}
		}
		if(dbUser!=null) {
			String dbUserStatus = dbUser.getStatus();
			
			boolean deleteMsgFlag =false;
			
			if (!activeState.equalsIgnoreCase(dbUserStatus) || dbUser.getDeleteFlag()) {
	    		deleteMsgFlag=true;
	    	}
			if (activeState.equalsIgnoreCase(dbUserStatus) && !dbUser.getDeleteFlag()) {
				return true;
			}
			if(deleteMsgFlag) {
			    return "Access Denied. Kindly contact BrillioOne.ai insights team to activate or create user id.";
			}
		}

		
		return false;
	}

	@Override
	public Object checkUserSSO(String email, HttpServletRequest request, HttpServletResponse response) throws IOException {
		ManageUser result =  loginRepository.findUsersByEmail(email);
		Object res = checkEmail(email, request);
		try {
			Properties properties = new Properties();
			in = getClass().getClassLoader().getResourceAsStream("application.properties");
			properties.load(in);
			secret = properties.getProperty("config.secret");
		} catch (IOException e1) {
			LOG.error("Error in secret Key in Login");
		}
		boolean checkExpiryFlag =false;
		if(result==null) {
			ManageUser user=new ManageUser();
			user.setEmail(email);
			user.setPassword("bolt");
			user.setUserRole("General User");
			user.setUserName(email);
			user.setStatus("Active");
			ProductLicenceConfig currentLic = getLicense();
			if(currentLic != null) {
				user.setLicenceKey(currentLic.getLicenceKey());
			}else {
				return "Product lincese has expired. Contact BrillioOne.ai insights team";
			}
			String isCreated = userService.saveNewUser(user, true);
			if("User Added!!".equals(isCreated)) {
	            try {
	                Algorithm algorithm = Algorithm.HMAC256(secret.getBytes());
	                Algorithm algo2 = Algorithm.HMAC512(secret.getBytes());
	                List<Object> roles = new ArrayList<>();
	                roles.add(user.getUserRole());
	                String access_token = JWT.create()
	                        .withSubject(user.getUserName())
	                        .withExpiresAt(new Date(System.currentTimeMillis() + 30 * 60 * 1000))
	                        .withIssuer(request.getRequestURI().toString())
	                        .withClaim("roles",roles)
	                        .sign(algorithm);
	                String refresh_token = JWT.create()
	                        .withSubject(user.getUserName())
	                        .withExpiresAt(new Date(System.currentTimeMillis() + 60 * 60 * 1000))
	                        .withIssuer(request.getRequestURI().toString())
	                        .withClaim("roles",roles)
	                        .sign(algo2);
	                Map<String, Object> tokens = new HashMap<>();
	                tokens.put("access_token",access_token);
	                tokens.put("refresh_token",refresh_token);
	                tokens.put("userName",user.getUserName());
	                tokens.put("userRole",user.getUserRole());

	                response.setContentType(APPLICATION_JSON_VALUE);

	                new ObjectMapper().writeValue(response.getOutputStream(),tokens);
	            }catch (Exception e){
	                response.setHeader("error",e.getMessage());
	                response.setStatus(FORBIDDEN.value());
//	                    response.sendError(FORBIDDEN.value());
	                Map<String, String> tokens = new HashMap<>();
	                tokens.put("error_message",e.getMessage());

	                response.setContentType(APPLICATION_JSON_VALUE);

	                new ObjectMapper().writeValue(response.getOutputStream(),tokens);
	            }
			}
		}
		else if(res.equals(true)) {
			String dbUserName = result.getUserName();
		    String userRole = result.getUserRole();
			checkExpiryFlag = checkExpiry(result.getLicenceKey());
		    if (checkExpiryFlag) {
		    	Algorithm algorithm = Algorithm.HMAC256(secret.getBytes());
		    	Algorithm algo2 = Algorithm.HMAC512(secret.getBytes());
		    	List<Object> roles = new ArrayList<>();
		    	roles.add(userRole);
                String access_token = JWT.create()
                        .withSubject(dbUserName)
                        .withExpiresAt(new Date(System.currentTimeMillis() + 30 * 60 * 1000))
                        .withIssuer(request.getRequestURI().toString())
                        .withClaim("roles",roles)
                        .sign(algorithm);
                String refresh_token = JWT.create()
                        .withSubject(dbUserName)
                        .withExpiresAt(new Date(System.currentTimeMillis() + 60 * 60 * 1000))
                        .withIssuer(request.getRequestURI().toString())
                        .sign(algo2);
                Map<String, Object> tokens = new HashMap<>();
                tokens.put("access_token",access_token);
                tokens.put("refresh_token",refresh_token);
                tokens.put("userName",dbUserName);
                tokens.put("userRole",userRole);

                response.setContentType(APPLICATION_JSON_VALUE);

                new ObjectMapper().writeValue(response.getOutputStream(),tokens);

		    }
		    if(!checkExpiryFlag) {
		    	throw new BadCredentialsException("Your product license has expired. Contact BrillioOne.ai insights team");
//				return "Your product lincese has expired. Contact BrillioOne.ai insights team";
		    }
		}
		return false;
	}
	private ProductLicenceConfig getLicense() {
		List<ProductLicenceConfig> licenses = productLicenceService.getData().getResult();
		if(licenses.isEmpty()) return null;
		return licenses.get(licenses.size()-1);
	     
	}

	@Override
	public void refreshToken(HttpServletRequest request, HttpServletResponse response) throws JsonGenerationException, JsonMappingException, IOException {
		 String authorizationHeader = request.getHeader(AUTHORIZATION);
		 	try {
				Properties properties = new Properties();
				in = getClass().getClassLoader().getResourceAsStream("application.properties");
				properties.load(in);
				secret = properties.getProperty("config.secret");
			} catch (IOException e1) {
				LOG.error("Error in secret Key in Login");
			}
	        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")){
	            try {
	                String token = authorizationHeader.substring("Bearer ".length());
	                Algorithm algorithm = Algorithm.HMAC512(secret.getBytes());
	                Algorithm algorithm2 = Algorithm.HMAC256(secret.getBytes());
	                JWTVerifier verifier = JWT.require(algorithm).build();
	                DecodedJWT decodedJWT = verifier.verify(token);
	                String username = decodedJWT.getSubject();
	                List<ManageUser> user =  loginRepository.findByUserName(username);
	                if(!user.isEmpty()) {
	                	List<Object> roles = new ArrayList<>();
				    	roles.add(user.get(0).getUserRole());
		                String access_token = JWT.create()
		                        .withSubject(user.get(0).getUserName())
		                        .withExpiresAt(new Date(System.currentTimeMillis() + 10 * 60 * 1000))
		                        .withIssuer(request.getRequestURI().toString())
		                        .withClaim("roles",roles)
		                        .sign(algorithm2);
		                Map<String, Object> tokens = new HashMap<>();
		                tokens.put("access_token",access_token);
		                tokens.put("refresh_token",token);

		                response.setContentType(APPLICATION_JSON_VALUE);

		                new ObjectMapper().writeValue(response.getOutputStream(),tokens);
	                }else {
	                	throw new BadCredentialsException("User not found! Can't refresh token.");
	                }
	            }catch (Exception e){
	                response.setHeader("error",e.getMessage());
	                response.setStatus(FORBIDDEN.value());
//	                    response.sendError(FORBIDDEN.value());
	                Map<String, String> tokens = new HashMap<>();
	                tokens.put("error_message",e.getMessage());

	                response.setContentType(APPLICATION_JSON_VALUE);

	                new ObjectMapper().writeValue(response.getOutputStream(),tokens);
	            }
	        }else{
	            throw new RuntimeException("Refresh Token is missing");
	        }
	}
	
	
}

