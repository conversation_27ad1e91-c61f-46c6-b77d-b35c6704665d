package com.bolt.dashboard.service;

import java.util.Properties;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.MailSetup;
import com.bolt.dashboard.core.model.MailerAssociation;
import com.bolt.dashboard.core.repository.MailSetupRepo;

@Service
public class MailServiceImplementation implements MailerService {
    private static final Logger LOG = LogManager.getLogger(MailServiceImplementation.class);
    private MailSetupRepo mailSetupRepository;

    @Autowired
    public MailServiceImplementation(MailSetupRepo mailSetupRepository) {
        this.mailSetupRepository = mailSetupRepository;
    }

    @Override
    public boolean sentMail(MailerAssociation req) {

        Iterable<MailSetup> result = mailSetupRepository.findAll();
        result.iterator().next().getUserName();

        final String username = result.iterator().next().getUserName();
        final String password = result.iterator().next().getPassword();

        Properties props = new Properties();
        props.put(ConstantVariable.MAIL_SMTP_AUTH, "true");
        props.put(ConstantVariable.MAIL_SMTP_ENABLE, result.iterator().next().isStarttls());
        props.put(ConstantVariable.MAIL_SMTP_HOST, result.iterator().next().getHost());
        props.put(ConstantVariable.MAIL_SMTP_PORT, result.iterator().next().getPort());
        props.put(ConstantVariable.MAIL_SMTP_SSL_TRUST, result.iterator().next().getHost());
        props.setProperty(ConstantVariable.MAIL_SMTP_SSL_PROTOCOLS, "TLSv1.2");
        LOG.info("property file updated.....");
        Session session = Session.getInstance(props, new javax.mail.Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username, password);
            }
        });
        LOG.info("Successfully Authenticated.........");

        try {

            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(username));
            LOG.info(req.getToAdd().toString());

            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(req.getToAdd().toString()));
            if (req.getCcAdd() != null) {
                message.setRecipients(Message.RecipientType.CC, InternetAddress.parse(req.getCcAdd().toString()));
            }
            if (req.getBccAdd() != null) {
                message.setRecipients(Message.RecipientType.BCC, InternetAddress.parse(req.getBccAdd().toString()));
            }
            message.setSubject(req.getSubject());
            message.setText(req.getMsgBody());
            // Create the message body part
            BodyPart messageBodyPart = new MimeBodyPart();
            messageBodyPart.setText("This is a  new testing mail for conformation ," + "\n\n Please ignore it!"
                    + "\n\n Thanks & Regards,\n BOLT");
            // Create a multipart message for attachment
            Multipart multipart = new MimeMultipart();

            // Set text message part
            multipart.addBodyPart(messageBodyPart);

            // Second part is attachment
            messageBodyPart = new MimeBodyPart();
            String filename = "C://Users/<USER>/Desktop/2684500.jpg";

            DataSource source = new FileDataSource(filename);
            messageBodyPart.setDataHandler(new DataHandler(source));
            messageBodyPart.setFileName(filename);
            multipart.addBodyPart(messageBodyPart);

            // Send the complete message parts
            message.setContent(multipart);
            LOG.info("All datad addd to Mutltipart....");
            Transport.send(message);

            LOG.info("Message sent successfully.............");

        } catch (MessagingException e) {
            LOG.info("Message sending failed.............");
            LOG.info(e);
        }

        return false;
    }

}
