package com.bolt.dashboard.service;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.MailSetup;
import com.bolt.dashboard.core.repository.MailSetupRepo;
import com.bolt.dashboard.response.DataResponse;

@Service
public class MailSetupServiceImplementation implements MailSetupService {

    private MailSetupRepo mailSetupRepository;

    @Autowired
    public MailSetupServiceImplementation(MailSetupRepo mailSetupRepository) {
        this.mailSetupRepository = mailSetupRepository;
    }

    @Override
//    @Cacheable(value="MailSetupgetSetup", key ="'MailSetupgetSetup'", cacheManager="timeoutCacheManager")
    public DataResponse<Iterable<MailSetup>> getSetup() {

        long lastUpdated = 1;
        Iterable<MailSetup> result = mailSetupRepository.findAll();
        return new DataResponse<Iterable<MailSetup>>(result, lastUpdated);
    }

    @Override
//    @CacheEvict(value="MailSetupgetSetup", key ="'MailSetupgetSetup'", cacheManager="timeoutCacheManager")
    public MailSetup save(MailSetup req) {
        Date date = new Date();

        if (mailSetupRepository.count() != 0) {

            deleteSetup(req);

        }
        long timeStamp = date.getTime();
        req.setTimestamp((long) timeStamp);
        return mailSetupRepository.save(req);
    }

    @Override
    public int deleteSetup(MailSetup mailSetup) {
        mailSetupRepository.deleteAll();
        return 0;
    }
}
