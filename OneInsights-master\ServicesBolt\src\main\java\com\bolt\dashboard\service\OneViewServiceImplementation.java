package com.bolt.dashboard.service;

import java.util.List;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;


import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.OneView;
import com.bolt.dashboard.core.repository.OneViewRepositary;

@Service
public class OneViewServiceImplementation implements OneViewService {

	private static final Logger LOGGER=LogManager.getLogger(OneViewServiceImplementation.class);
	@Autowired
	OneViewRepositary oneViewRepositary;
	MongoTemplate mongoTemplate = null;

	@Override
	public List<OneView> find(String projectName) {
		return oneViewRepositary.findByProjectName(projectName);
	}

	@Override
	public OneView findByProjectNameAndUser(String projectName, String user) {
		return oneViewRepositary.findByProjectNameAndUser(projectName, user);
	}

	@Override
	public OneView save(OneView oneview) {

		OneView response = oneViewRepositary.findByProjectNameAndUser(oneview.getProjectName(), oneview.getUser());
		if (response != null) {

			response = oneview;
			oneViewRepositary.save(response);
		} else {
			response = oneViewRepositary.save(oneview);
		}
		return response;

	}

	public MongoTemplate getMongoTemplate() {
		try {
			mongoTemplate = DataConfig.getInstance().mongoTemplate();
		} catch (Exception e) {
			
			LOGGER.error(e.getMessage());
		}
		return mongoTemplate;
	}

}
