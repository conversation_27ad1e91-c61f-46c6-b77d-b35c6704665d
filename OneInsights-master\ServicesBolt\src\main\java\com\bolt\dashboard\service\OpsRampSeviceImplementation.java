package com.bolt.dashboard.service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.IncidentOpenClosed;
import com.bolt.dashboard.core.model.OpsResources;
import com.bolt.dashboard.core.repository.OpsRampRepo;

@Service
public class OpsRampSeviceImplementation implements OpsRampService {
	private static final Logger LOGGER = LogManager.getLogger(OpsRampSeviceImplementation.class);
	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
	List<OpsResources> resourceList = null;
	List<IncidentOpenClosed> openClosedArr = new ArrayList<IncidentOpenClosed>();

	private void getInititialDetails() {
		OpsRampRepo opsRampRepo = ctx.getBean(OpsRampRepo.class);
		resourceList = opsRampRepo.findAll();
		openClosedArr.clear();
	}

//	public static void main(String[] args) {
//		new OpsRampSeviceImplementation().getServerResources();
//	}

	@Override
//	@Cacheable(value="OpsRampgetServerResources", key ="'OpsRampgetServerResources'", cacheManager="timeoutCacheManager")
	public List<OpsResources> getServerResources() {
		getInititialDetails();
		List<OpsResources> filteredResource = resourceList.stream().filter(
				m -> m.getType().equals("DEVICE"))
				.collect(Collectors.toList());
		LOGGER.info(filteredResource.size());
//		
		return filteredResource;
	}

	@Override
//	@Cacheable(value="OpsRampgetAllResources", key ="'OpsRampgetAllResources'", cacheManager="timeoutCacheManager")
	public List<OpsResources> getAllResources() {
		getInititialDetails();
		LOGGER.info("resourceList: "+resourceList.size());

		return resourceList;
	}

	

}
