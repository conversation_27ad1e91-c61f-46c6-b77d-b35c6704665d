package com.bolt.dashboard.service;

/**
 * 
 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.PUMABitBucketRepository;
import com.bolt.dashboard.response.DataResponse;

@Service
public class PUMABitBucketServiceImplementation implements PUMABitBucketService {
    private PUMABitBucketRepository scmToolRepository;

    @Autowired
    public PUMABitBucketServiceImplementation(PUMABitBucketRepository scmToolRepository) {
        this.scmToolRepository = scmToolRepository;
    }

    @Override
    public DataResponse<Iterable<SCMTool>> search(String scType, String projectName) {
        long lastUpdate = 1;

        Iterable<SCMTool> result = scmToolRepository.findByScTypeAndProjectName(scType, projectName);
        return new DataResponse<Iterable<SCMTool>>(result, lastUpdate);
    }

    @Override
//    @Cacheable(value="PUMABitBucketsearch", key ="'PUMABitBucketsearch'+#projectName+#scType+#sDate+#eDate", cacheManager="timeoutCacheManager")
    public DataResponse<Iterable<SCMTool>> search(String scType, String projectName, long sDate, long eDate,
            boolean flag) {
        boolean flagnew = flag;
        if (!flagnew) {

            return search(scType, projectName);
        } else {

            long lastUpdate = 1;

            Iterable<SCMTool> result = scmToolRepository.findByScTypeAndProjectNameAndCommitTSBetween(scType,
                    projectName, sDate, eDate);

            return new DataResponse<Iterable<SCMTool>>(result, lastUpdate);

        }

    }
}
