package com.bolt.dashboard.service;

import java.util.List;

import com.bolt.dashboard.core.model.PortfolioConfig;
import com.bolt.dashboard.response.DataResponse;

public interface PortfolioConfigService {
    PortfolioConfig savePortfolioConfigData(PortfolioConfig portfolioConfig);

    DataResponse<Iterable<PortfolioConfig>> fetchPortfolioConfigData();

    DataResponse<Iterable<PortfolioConfig>> getPortfolioDetails(String projectName);

	String saveEngScore(String projectName, String month, double engScore);

	List<String> fetchPortfolioTowers();

	String fetchTowerByProj(String pName);
}
