package com.bolt.dashboard.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.PortfolioConfig;
import com.bolt.dashboard.core.repository.PortfolioConfigRepo;
import com.bolt.dashboard.response.DataResponse;

@Service
public class PortfolioConfigServiceImplementation implements PortfolioConfigService {
	private PortfolioConfigRepo portfolioConfigRepo;
	 private static final Logger LOG = LogManager.getLogger(PortfolioConfigServiceImplementation.class);

	@Autowired
	public PortfolioConfigServiceImplementation(PortfolioConfigRepo portfolioConfigRepo) {
		this.portfolioConfigRepo = portfolioConfigRepo;
	}

	@Override
//	@CacheEvict(value="fetchPortfolioConfigData", key ="'fetchPortfolioConfigData'", cacheManager="timeoutCacheManager")
	public PortfolioConfig savePortfolioConfigData(PortfolioConfig req) {
		Date date = new Date();
		long timeStamp = date.getTime();
		req.setTimestamp((long) timeStamp);
		if (portfolioConfigRepo.findByProjectName(req.getProjectName()) != null) {
			portfolioConfigRepo.deleteByProjectName(req.getProjectName());
		}
		return portfolioConfigRepo.save(req);
	}

	@Override
//	@Cacheable(value="fetchPortfolioConfigData", key ="'fetchPortfolioConfigData'", cacheManager="timeoutCacheManager")
	public DataResponse<Iterable<PortfolioConfig>> fetchPortfolioConfigData() {
		long lastUpdated = 1;
		Iterable<PortfolioConfig> result = portfolioConfigRepo.findAll();
		return new DataResponse<Iterable<PortfolioConfig>>(result, lastUpdated);
	}

	@Override
//	@Cacheable(value="getPortfolioDetails", key ="'getPortfolioDetails'+#projectName", cacheManager="timeoutCacheManager")
	public DataResponse<Iterable<PortfolioConfig>> getPortfolioDetails(String projectName) {
		long lastUpdated = 1;
		Iterable<PortfolioConfig> result = portfolioConfigRepo.findByProjectName(projectName);
		return new DataResponse<Iterable<PortfolioConfig>>(result, lastUpdated);
	}

	@Override
//	@Cacheable(value="PortfolioConfigsaveEngScore", key ="'PortfolioConfigsaveEngScore'+#projectName+#month+#engScore", cacheManager="timeoutCacheManager")
	public String saveEngScore(String projectName, String month, double engScore) {
		
		try {
			List<PortfolioConfig> result = portfolioConfigRepo.findByProjectName(projectName);
			PortfolioConfig proj=null;
			if(!result.isEmpty()){
			proj=    result.get(0);
			}
			else {
				return "No Project";
			}
			SortedMap<String,Double> engScrMap=proj.getEngScores();
                  
			if (engScrMap == null) {
				engScrMap = new TreeMap<String, Double>();
			}
			     engScrMap.put(month, engScore);
			     proj.setEngScores(engScrMap);
			    portfolioConfigRepo.save(proj);
			     return "Success";
		} catch (Exception e) {
			
			LOG.info(e.getMessage());
			return "Failure";
		}
		
	}

	@Override
	public List<String> fetchPortfolioTowers() {

		List<PortfolioConfig> result = portfolioConfigRepo.findAll();
		List<String> towers=new ArrayList<>();  
		for(PortfolioConfig portfolioConf:result) {
			if(!towers.contains(portfolioConf.getTowerName())) {
			towers.add(portfolioConf.getTowerName());
			}
		}
		
		return towers;
	}

	@Override
	public String fetchTowerByProj(String pName) {
		List<PortfolioConfig> result = portfolioConfigRepo.findByProjectName(pName);
		return result.get(0).getTowerName();
	}
}
