package com.bolt.dashboard.service;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.PortfolioViewConfig;
import com.bolt.dashboard.core.repository.PortfolioViewConfigRepo;
import com.bolt.dashboard.request.PortfolioViewConfigReq;

@Service
public class PortfolioViewConfigServiceImplementation implements PortfolioViewConfigService {
	
	@Autowired
	PortfolioViewConfigRepo portfolioViewConfigRepo;

	@Override
//	@Cacheable(value="PortfolioViewgetPortfolioViewConfig", key ="'PortfolioViewgetPortfolioViewConfig'+#projectName", cacheManager="timeoutCacheManager")
	public PortfolioViewConfig getPortfolioViewConfig(String projectName) {
		return portfolioViewConfigRepo.findByProjectName(projectName);
		
	}

	@Override
//	@CacheEvict(value="PortfolioViewgetPortfolioViewConfig", key ="'PortfolioViewgetPortfolioViewConfig'+#portfolioViewConfig.getProjectName()", cacheManager="timeoutCacheManager")
	public PortfolioViewConfig savePortfolioViewConfig(PortfolioViewConfigReq portfolioViewConfig) {
		
		portfolioViewConfigRepo.deleteByProjectName(portfolioViewConfig.getProjectName());
		PortfolioViewConfig newPortfolioViewConfig = new PortfolioViewConfig();
		BeanUtils.copyProperties(portfolioViewConfig, newPortfolioViewConfig);
		return portfolioViewConfigRepo.save(newPortfolioViewConfig);
	}

}
