/**
 * 
 */
package com.bolt.dashboard.service;

import java.util.List;

import com.bolt.dashboard.core.model.ProductLicenceConfig;
import com.bolt.dashboard.response.DataResponse;

public interface ProductLicenceService {

    int fetchUserCount(String projectName);

    public DataResponse<List<ProductLicenceConfig>> getData();

    public DataResponse<ProductLicenceConfig> updateData(String licKey);

    public DataResponse<Boolean> sendMail();

}
