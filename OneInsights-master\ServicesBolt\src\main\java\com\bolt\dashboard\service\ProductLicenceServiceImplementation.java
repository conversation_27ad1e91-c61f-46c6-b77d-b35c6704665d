/**
 * 
 */
package com.bolt.dashboard.service;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.AlgorithmParameterSpec;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.KeySpec;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Pattern;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.PBEParameterSpec;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.ProjectCollector;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ProductLicenceConfig;
import com.bolt.dashboard.core.repository.ProductLicenceConfigRepo;
import com.bolt.dashboard.response.DataResponse;

@Service
public class ProductLicenceServiceImplementation implements ProductLicenceService {

	private static final Logger LOG = LogManager.getLogger(ProductLicenceServiceImplementation.class);
	AnnotationConfigApplicationContext ctx = null;

	@Override
//	@Cacheable(value="ProductLicencefetchUserCount", key ="'ProductLicencefetchUserCount'+#licenceKey", cacheManager="timeoutCacheManager")
	public int fetchUserCount(String licenceKey) {

		return checkLicence(licenceKey);
	}

	public int checkLicence(String lkey) {

		//String[] infoArray=null;
		String[] infoArray =new String[4];

		try {
			String info = decrypt(lkey);

			if (info.contains(" ")) {
				infoArray = info.split(Pattern.quote(" "));

			}
		} catch ( NoSuchAlgorithmException| InvalidKeySpecException|
				NoSuchPaddingException | InvalidKeyException | InvalidAlgorithmParameterException |  
				IllegalBlockSizeException | BadPaddingException |  IOException e) {

			LOG.info(e);
		}
		if(infoArray.length == 0)
		{return 0;}
		else {
		return Integer.parseInt(String.valueOf(infoArray[4]));
		}
	}

	@Override
//	@Cacheable(value="ProductLicencegetData", key ="'ProductLicencegetData'", cacheManager="timeoutCacheManager")
	public DataResponse<List<ProductLicenceConfig>> getData() {
		int lastUpdated = 1;
		ctx = DataConfig.getContext();
		ProductLicenceConfigRepo repo = ctx.getBean(ProductLicenceConfigRepo.class);
		List<ProductLicenceConfig> configList = repo.findAll();
		Iterator<ProductLicenceConfig> iterator = configList.iterator();
		ProductLicenceConfig productLicenceConfig;
		List<ProductLicenceConfig> configCollection = new ArrayList<>();
		while (iterator.hasNext()) {
			productLicenceConfig = iterator.next();

			String[] infoArray = null;
			String encryptedText = productLicenceConfig.getLicenceKey();
			try {
				String info = decrypt(encryptedText);

				if (info.contains("@#")) {
					infoArray = info.split(Pattern.quote("@#"));
					productLicenceConfig.setExpiryDate(infoArray[0]);
					productLicenceConfig.setSenNumber(infoArray[1]);
					productLicenceConfig.setLicenceType(infoArray[2]);
					productLicenceConfig.setOrganizationName(infoArray[3]);
					productLicenceConfig.setUseerCount(Integer.parseInt(infoArray[4].toString()));
					productLicenceConfig.setLicenceKey(encryptedText);

					configCollection.add(productLicenceConfig);
				}

			} catch (InvalidKeyException | NoSuchAlgorithmException | InvalidKeySpecException | NoSuchPaddingException
					| InvalidAlgorithmParameterException | IllegalBlockSizeException | BadPaddingException
					| IOException e) {
				LOG.info(e);
			}
		}
		// ctx.close();
		return new DataResponse<List<ProductLicenceConfig>>(configCollection, lastUpdated);
	}

	public String decrypt(String encryptedText) throws NoSuchAlgorithmException, InvalidKeySpecException,
			NoSuchPaddingException, InvalidKeyException, InvalidAlgorithmParameterException,
			UnsupportedEncodingException, IllegalBlockSizeException, BadPaddingException, IOException {
		Cipher dcipher;
		// 8-byte Salt
		byte[] salt = { (byte) 0xA9, (byte) 0x9B, (byte) 0xC8, (byte) 0x32, (byte) 0x56, (byte) 0x35, (byte) 0xE3,
				(byte) 0x03 };
		// Iteration count
		int iterationCount = 19;
		String secretKey = "ezeon8547";
		// Key generation for enc and desc
		KeySpec keySpec = new PBEKeySpec(secretKey.toCharArray(), salt, iterationCount);
		SecretKey key = SecretKeyFactory.getInstance("PBEWithMD5AndDES").generateSecret(keySpec);
		// Prepare the parameter to the ciphers
		AlgorithmParameterSpec paramSpec = new PBEParameterSpec(salt, iterationCount);
		// Decryption process; same key will be used for decr
		dcipher = Cipher.getInstance(key.getAlgorithm());
		dcipher.init(Cipher.DECRYPT_MODE, key, paramSpec);
		//@SuppressWarnings("restriction")
		//byte[] enc = new sun.misc.BASE64Decoder().decodeBuffer(encryptedText);
			byte[] enc = java.util.Base64.getMimeDecoder().decode(encryptedText);

		byte[] utf8 = dcipher.doFinal(enc);
		String charSet = "UTF-8";
		return new String(utf8, charSet);
	}

	@Override
//	@CacheEvict(value="ProductLicencegetData", key ="'ProductLicencegetData'", cacheManager="timeoutCacheManager")
	public DataResponse<ProductLicenceConfig> updateData(String licKey) {
		ctx = DataConfig.getContext();
		ProductLicenceConfigRepo repo = ctx.getBean(ProductLicenceConfigRepo.class);

		int lastUpdated = 1;
		ProductLicenceConfig productLicenceConfig = new ProductLicenceConfig();
		String[] infoArray = null;
		// ctx.close();
		String updatedEncryptedText = licKey;
		try {
			String info = decrypt(updatedEncryptedText);

			if (info.contains("@#")) {
				infoArray = info.split(Pattern.quote("@#"));
				productLicenceConfig.setExpiryDate(infoArray[0]);
				productLicenceConfig.setSenNumber(infoArray[1]);
				productLicenceConfig.setLicenceType(infoArray[2]);
				productLicenceConfig.setOrganizationName(infoArray[3]);
				productLicenceConfig.setUseerCount(Integer.parseInt(infoArray[4].toString()));
				productLicenceConfig.setLicenceKey(updatedEncryptedText);
				repo.save(productLicenceConfig);

			}
		} catch (InvalidKeyException | NoSuchAlgorithmException | InvalidKeySpecException | NoSuchPaddingException
				| InvalidAlgorithmParameterException | IllegalBlockSizeException | BadPaddingException
				| IOException e) {
			LOG.info(e);
		}
		return new DataResponse<ProductLicenceConfig>(productLicenceConfig, lastUpdated);
	}

	@Override
	public DataResponse<Boolean> sendMail() {
		String toAddress = "<EMAIL>";
		String msgBody = "This mail is from BrillioOne.ai insights Licence team ";
		String subject = "Message from BrillioOne.ai insights team";
		boolean status = new ProjectCollector().sendMail(toAddress, msgBody, subject);

		return new DataResponse<Boolean>(status, 1);

	}
}