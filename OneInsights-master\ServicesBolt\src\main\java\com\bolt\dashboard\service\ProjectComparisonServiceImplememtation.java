package com.bolt.dashboard.service;

import java.util.List;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.ProjectComparison;
import com.bolt.dashboard.core.repository.ProjectComparisonRepo;
import com.bolt.dashboard.request.ProjectComparisonReq;
import com.bolt.dashboard.response.DataResponse;

@Service
public class ProjectComparisonServiceImplememtation implements ProjectComparisonService {
	private ProjectComparisonRepo projectComparisonRepo;
	private static final Logger LOG = LogManager.getLogger(ProjectComparisonServiceImplememtation.class);

	@Autowired
	public ProjectComparisonServiceImplememtation(ProjectComparisonRepo repository) {
		this.projectComparisonRepo = repository;
	}

	public ProjectComparisonServiceImplememtation() {
	}

	@Override
	public DataResponse<ProjectComparison> search(ProjectComparisonReq request) {
		long lastUpdated = 1;

		List<ProjectComparison> projectComparisonsList = (List<ProjectComparison>) projectComparisonRepo.findAll();
		if (!projectComparisonsList.isEmpty()) {
			ProjectComparison projectComparison = projectComparisonsList.get(projectComparisonsList.size() - 1);
			return new DataResponse<ProjectComparison>(projectComparison, lastUpdated);
		} else {
			LOG.info("ProjectComparison data not found in DB  search() ProjectComparisonServiceImplementation()... ");
			return null;
		}
	}

}
