package com.bolt.dashboard.service;


import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.ProjectCoverageDetails;
import com.bolt.dashboard.core.model.RepoCodeCoverageStatus;
import com.bolt.dashboard.core.repository.ProjectCoverageDetailsRepo;
import com.bolt.dashboard.core.repository.RepoCodeCoverageStatusRepo;


@Service
public class ProjectCoverageServiceImplementation implements ProjectCoverageService {

	@Autowired
	ProjectCoverageDetailsRepo projectCoverageRepo;
	@Autowired
	RepoCodeCoverageStatusRepo codeCoverageStatusRepo;
	
	
	@Override
//	@Cacheable(value="ProjectCoveragegetProjectCoverage", key ="'ProjectCoveragegetProjectCoverage'+#pName", cacheManager="timeoutCacheManager")
	public List<ProjectCoverageDetails> getProjectCoverage(String pName) {
		
		return projectCoverageRepo.findByPName(pName);
	}


	@Override
//	@Cacheable(value="ProjectCoveragegetRepoCoverageStatus", key ="'ProjectCoveragegetRepoCoverageStatus'+#pName", cacheManager="timeoutCacheManager")
	public List<RepoCodeCoverageStatus> getRepoCoverageStatus(String pName) {
		
		return codeCoverageStatusRepo.findByPName(pName);
	}


}
