package com.bolt.dashboard.service;

import java.util.List;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.ProjectHealth;
import com.bolt.dashboard.core.repository.ProjectHealthRep;
import com.bolt.dashboard.response.DataResponse;

@Service
public class ProjectHealthServiceImplementation implements ProjectHealthService {
	private ProjectHealthRep projectHealthRepo;
	private static final Logger LOG = LogManager.getLogger(ProjectHealthServiceImplementation.class);

	/**
	 * 
	 */
	@Autowired
	public ProjectHealthServiceImplementation(ProjectHealthRep projectHealthRepo) {
		this.projectHealthRepo = projectHealthRepo;
	}

	@Override
//	@Cacheable(value="fetchprojectHealthData", key ="'fetchprojectHealthData'+#projectName", cacheManager="timeoutCacheManager")
	public DataResponse<Iterable<ProjectHealth>> fetchprojectHealthData(String projectName) {

		long lastUpdated = 1;
		Iterable<ProjectHealth> result = projectHealthRepo.findByProjectName(projectName);
		return new DataResponse<Iterable<ProjectHealth>>(result, lastUpdated);
	}

	@Override
//	@CacheEvict(value="fetchprojectHealthData", key ="'fetchprojectHealthData'+#projServiceInstance.getProjectName()", cacheManager="timeoutCacheManager")
	public Boolean saveProjectHealthData(ProjectHealth projServiceInstance) {
		Boolean status;

		List<ProjectHealth> healthObject = projectHealthRepo.findByProjectNameAndSprintName(
				projServiceInstance.getProjectName(), projServiceInstance.getSprintName());

		if (healthObject.isEmpty()) {

			projectHealthRepo.save(projServiceInstance);
			status = true;
		} else {

			ProjectHealth projectHealth = healthObject.get(healthObject.size() - 1);
			projectHealth.setApplicationPhaseList(projServiceInstance.getApplicationPhaseList());
			projectHealth.setSprintName(projServiceInstance.getSprintName());
			projectHealth.setProjectName(projServiceInstance.getProjectName());
			projectHealth.setConfig(projServiceInstance.getConfig());

			updateRagConfig(projectHealth, projServiceInstance.getProjectName());

			projectHealthRepo.save(projectHealth);
			LOG.info("Record for " + projectHealth.getProjectName() + " updated ...");
			status = true;

		}

		return status;
	}

	public void updateRagConfig(ProjectHealth health, String projectName) {
		List<ProjectHealth> objects = projectHealthRepo.findByProjectName(projectName);
		for (ProjectHealth object : objects) {
			object.setConfig(health.getConfig());
			projectHealthRepo.save(object);

		}
	}

}
