/**
 * 
 */
package com.bolt.dashboard.service;

import java.util.List;

import com.bolt.dashboard.core.model.Retrospective;
import com.bolt.dashboard.response.DataResponse;

/**
 * <AUTHOR>
 *
 */
public interface RetrospectiveService {

    Retrospective saveRetrospectiveDetails(Retrospective retrospective);

    DataResponse<List<Retrospective>> retrieveRetrospectiveDetails();

    void deleteRetrospective(Retrospective retrospective);

	DataResponse<List<Retrospective>> retrieveRetrospectiveDetailsByProject(String projectName);

}
