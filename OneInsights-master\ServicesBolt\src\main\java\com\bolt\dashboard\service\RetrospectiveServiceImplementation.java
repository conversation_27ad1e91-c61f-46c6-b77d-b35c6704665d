/**
 * 
 */
package com.bolt.dashboard.service;

import java.util.List;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.Retrospective;
import com.bolt.dashboard.core.repository.RetrospectiveRepo;
import com.bolt.dashboard.response.DataResponse;

/**
 * <AUTHOR>
 *
 */
@Service
public class RetrospectiveServiceImplementation implements RetrospectiveService {
	private RetrospectiveRepo retrospectiveRepo;
	private static final Logger LOG = LogManager.getLogger(RetrospectiveServiceImplementation.class);

	@Autowired
	public RetrospectiveServiceImplementation(RetrospectiveRepo repo) {
		this.retrospectiveRepo = repo;
	}

	@Override
//	@Caching(evict = {
//			@CacheEvict(value="retrieveRetrospectiveDetails", key ="'retrieveRetrospectiveDetails'", cacheManager="timeoutCacheManager"),
//			@CacheEvict(value="retrieveRetrospectiveDetails", key ="'retrieveRetrospectiveDetails'+#req.getProjectName()", cacheManager="timeoutCacheManager")
//		})
	public Retrospective saveRetrospectiveDetails(Retrospective req) {

		if (retrospectiveRepo.findByUniqueIDAndSprintNameAndProjectName(req.getUniqueID(), req.getSprintName(),
				req.getProjectName()) != null) {
			retrospectiveRepo.deleteByUniqueIDAndSprintNameAndProjectName(req.getUniqueID(), req.getSprintName(),
					req.getProjectName());
		}
		LOG.info("Retrospective Data saved successfully...");
		return retrospectiveRepo.save(req);

	}

	@Override
//	@Caching(evict = {
//			@CacheEvict(value="retrieveRetrospectiveDetails", key ="'retrieveRetrospectiveDetails'", cacheManager="timeoutCacheManager"),
//			@CacheEvict(value="retrieveRetrospectiveDetails", key ="'retrieveRetrospectiveDetails'+#req.getProjectName()", cacheManager="timeoutCacheManager")
//		})
	public void deleteRetrospective(Retrospective req) {

		if (retrospectiveRepo.findByUniqueIDAndSprintNameAndProjectName(req.getUniqueID(), req.getSprintName(),
				req.getProjectName()) != null) {
			retrospectiveRepo.deleteByUniqueIDAndSprintNameAndProjectName(req.getUniqueID(), req.getSprintName(),
					req.getProjectName());
		}
		LOG.info("Retrospective Data deleted successfully...");

	}

	@Override
//	@Cacheable(value="retrieveRetrospectiveDetails", key ="'retrieveRetrospectiveDetails'", cacheManager="timeoutCacheManager")
	public DataResponse<List<Retrospective>> retrieveRetrospectiveDetails() {
		long lastUpdate = 1;
		List<Retrospective> result = retrospectiveRepo.findAll();
		return new DataResponse<List<Retrospective>>(result, lastUpdate);
	}

	@Override
//	@Cacheable(value="retrieveRetrospectiveDetails", key ="'retrieveRetrospectiveDetails'+#projectName", cacheManager="timeoutCacheManager")
	public DataResponse<List<Retrospective>> retrieveRetrospectiveDetailsByProject(String projectName) {
		List<Retrospective> result = retrospectiveRepo.findByProjectName(projectName);
		return new DataResponse<List<Retrospective>>(result, 1);
	}

}
