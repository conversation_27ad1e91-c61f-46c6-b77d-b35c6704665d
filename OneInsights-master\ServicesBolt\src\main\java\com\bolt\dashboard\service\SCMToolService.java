package com.bolt.dashboard.service;

import org.json.JSONObject;

import com.bolt.dashboard.core.model.SCMMR;
import com.bolt.dashboard.core.model.SCMToolBO;
import com.bolt.dashboard.response.DataResponse;

public interface SCMToolService {
	public DataResponse<JSONObject> searchJson(String url, String userName, String password);

	DataResponse<Iterable<SCMToolBO>> getCommitDetails(String scType, String projectName);

	public DataResponse<Iterable<SCMMR>> getMergeRequestDetails(String scType, String projectName);

}
