package com.bolt.dashboard.service;

import java.util.List;

import org.json.JSONObject;
import org.json.JSONTokener;
/**
 * 
 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.SCMMR;
import com.bolt.dashboard.core.model.SCMToolBO;
import com.bolt.dashboard.core.repository.SCMMergeRepository;
import com.bolt.dashboard.core.repository.SCMToolRepository;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.tfsversion.TFSVersionControllerClientImplementation;

@Service
public class SCMToolServiceImplementation implements SCMToolService {
	private SCMToolRepository scmToolRepository;
	private SCMMergeRepository scmMergeReqRepo ;

	@Autowired
	public SCMToolServiceImplementation(SCMToolRepository scmToolRepository, SCMMergeRepository scmMergeReqRepo ) {
		this.scmToolRepository = scmToolRepository;
		this.scmMergeReqRepo= scmMergeReqRepo;
	}

	public SCMToolServiceImplementation() {
	}

	@Override
//	@Cacheable(value="SCMToolgetCommitDetails", key ="'SCMToolgetCommitDetails'+#scType+#projectName", cacheManager="timeoutCacheManager")
	public DataResponse<Iterable<SCMToolBO>> getCommitDetails(String scType, String projectName) {
		long lastUpdate = 1;
		List<SCMToolBO> data = (List<SCMToolBO>) scmToolRepository.findByScTypeAndProjectNameIgnoreCase(scType,
				projectName);
		return new DataResponse<Iterable<SCMToolBO>>(data, lastUpdate);
	}

	@Override
	public DataResponse<JSONObject> searchJson(String url, String userName, String password) {
		long lastUpdate = 1;
		JSONObject jsonObject = null;
		jsonObject = (JSONObject) new JSONTokener(
				new TFSVersionControllerClientImplementation().makeRestCall(url, userName, password).toJSONString())
						.nextValue();
		return new DataResponse<JSONObject>(jsonObject, lastUpdate);
	}

	@Override
//	@Cacheable(value="SCMToolgetMergeRequestDetails", key ="'SCMToolgetMergeRequestDetails'+#scType+#projectName", cacheManager="timeoutCacheManager")
	public DataResponse<Iterable<SCMMR>> getMergeRequestDetails(String scType, String projectName) {
		long lastUpdate = 1;
		List<SCMMR> data = (List<SCMMR>) scmMergeReqRepo.findByProjectName(
				projectName);
		return new DataResponse<Iterable<SCMMR>>(data, lastUpdate);
	}
}
