/**
 * 
 */
package com.bolt.dashboard.service;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.SLAConfiguration;
import com.bolt.dashboard.core.repository.SLAConfigRepo;

/**
 * <AUTHOR>
 *
 */
@Service
public class SLAConfigServiceImplementation implements SLAConfigService {
	private SLAConfigRepo slaConfigRepo;
	private static final Logger LOG = LogManager.getLogger(SLAConfigServiceImplementation.class);

	@Autowired
	public SLAConfigServiceImplementation(SLAConfigRepo repo) {
		this.slaConfigRepo = repo;
	}

	@Override
	public SLAConfiguration saveConfig(SLAConfiguration req) {

		if (slaConfigRepo.findByProjectName(req.getProjectName()) != null) {
			slaConfigRepo.deleteByProjectName(req.getProjectName());
		}
		LOG.info("SLA Configuration saved successfully...");
		return slaConfigRepo.save(req);

	}

	@Override
	public Iterable<SLAConfiguration> getSLA() {
		return slaConfigRepo.findAll();
	}
}
