package com.bolt.dashboard.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/*import com.bolt.dashboard.core.model.ALMProject;
import com.bolt.dashboard.core.model.Iteration;*/
import com.bolt.dashboard.core.model.SRCommits;
import com.bolt.dashboard.core.repository.SRCommitsRep;
import com.bolt.dashboard.response.DataResponse;

@Service
public class SRCommitsServiceImplementation implements SRCommitsService {
	@SuppressWarnings("unused")
	private SRCommitsRep srCommitsRepository;
	SRCommits srCommits = new SRCommits();
	/*
	 * ALMProject almProjectJIRA = new ALMProject(); Iteration iterationJIRA =
	 * new Iteration();
	 */

	@Autowired
	public SRCommitsServiceImplementation(SRCommitsRep srCommitsRepository) {

		this.srCommitsRepository = srCommitsRepository;
	}

	public DataResponse<Iterable<SRCommits>> searchSR() {
		long lastUpdate = 1;

		Iterable<SRCommits> result = srCommitsRepository.findAll();
		return new DataResponse<Iterable<SRCommits>>(result, lastUpdate);
	}

}
