package com.bolt.dashboard.service;

import java.util.List;
import java.util.Map;

import com.bolt.dashboard.core.model.IncidentCategory;
import com.bolt.dashboard.core.model.IncidentOpenClosed;
import com.bolt.dashboard.core.model.InflowTrend;
import com.bolt.dashboard.core.model.ServiceNowInc;

public interface ServiceNowService {

	List<IncidentCategory> getCategoryInsights();

	List<IncidentOpenClosed> getOpenClosedInsights();

	Map<String, Map<String , Integer>> getResoultionSLA();
	
	List<InflowTrend> getInflowTrend();
	List<ServiceNowInc> getAllIncidents();

}
