package com.bolt.dashboard.service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.IncidentCategory;
import com.bolt.dashboard.core.model.IncidentOpenClosed;
import com.bolt.dashboard.core.model.InflowTrend;
import com.bolt.dashboard.core.model.ServiceNowInc;
import com.bolt.dashboard.core.model.ServiceNowSCReqItem;
import com.bolt.dashboard.core.model.ServiceNowSLA;
import com.bolt.dashboard.core.repository.ServiceNowInflowTrendRepo;
import com.bolt.dashboard.core.repository.ServiceNowRepo;
import com.bolt.dashboard.core.repository.ServiceNowSLARepo;

@Service
public class ServiceNowSeviceImplementation implements ServiceNowService {
	private static final Logger LOGGER = LogManager.getLogger(ServiceNowSeviceImplementation.class);
	String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";
	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
	List<ServiceNowInc> incidentList = null;
	List<ServiceNowSCReqItem> scReqItemList = null;
	List<IncidentOpenClosed> openClosedArr = new ArrayList<IncidentOpenClosed>();
	List<InflowTrend> inflowTrendArr = new ArrayList<InflowTrend>();
	
	List<ServiceNowSLA> slaList = null;

	private void getInititialDetails() {
		ServiceNowRepo snmRepo = ctx.getBean(ServiceNowRepo.class);
		incidentList = snmRepo.findAll();
		openClosedArr.clear();
		
		ServiceNowSLARepo slaRepo = ctx.getBean(ServiceNowSLARepo.class);
		slaList = (ArrayList<ServiceNowSLA>) slaRepo.findAll();
	}
	
	private void getITDetails() {
		ServiceNowInflowTrendRepo repo = ctx.getBean(ServiceNowInflowTrendRepo.class);
		scReqItemList = (List<ServiceNowSCReqItem>) repo.findAll();
		inflowTrendArr.clear();
	}
	
	private void getSLADetails() {
		ServiceNowSLARepo slaRepo = ctx.getBean(ServiceNowSLARepo.class);
		slaList = (ArrayList<ServiceNowSLA>) slaRepo.findAll();
	}

//	public static void main(String[] args) {
//		new ServiceNowSeviceImplementation().getInflowTrend();
//	}

	@Override
//	@Cacheable(value="ServiceNowgetCategoryInsights", key ="'ServiceNowgetCategoryInsights'", cacheManager="timeoutCacheManager")
	public List<IncidentCategory> getCategoryInsights() {
		getInititialDetails();
		List<IncidentCategory> categoryArr = new ArrayList<IncidentCategory>();
		Map<String, List<ServiceNowInc>> incidentGrouped = incidentList.stream()
				.collect(Collectors.groupingBy(w -> w.getCategory()));
		for (Entry<String, List<ServiceNowInc>> incident : incidentGrouped.entrySet()) {
			if (incident.getKey() != null) {
				IncidentCategory incCategory = new IncidentCategory();
				incCategory.setCategoryName(incident.getKey());
				incCategory.setValue(incident.getValue().size());
				categoryArr.add(incCategory);
			}
		}
		return categoryArr;
	}

	@Override
//	@Cacheable(value="ServiceNowgetOpenClosedInsights", key ="'ServiceNowgetOpenClosedInsights'", cacheManager="timeoutCacheManager")
	public List<IncidentOpenClosed> getOpenClosedInsights() {
		getInititialDetails();
//		Long currentMillseconds = new Date().getTime();
		
		DateFormat formatter = new SimpleDateFormat(DEFAULT_PATTERN);

		try {
			Calendar aCalendar = Calendar.getInstance();
			aCalendar.set(Calendar.DATE, 1);
			Date firstDate = aCalendar.getTime();
			aCalendar.set(Calendar.DATE, aCalendar.getActualMaximum(Calendar.DAY_OF_MONTH));
			Date lastDate = aCalendar.getTime();
			addOpenClosedData(firstDate, lastDate);

			aCalendar.set(Calendar.DATE, 1);
			for (int i = 1; i <= 12; i++) {

				aCalendar.add(Calendar.DAY_OF_MONTH, i * -1);
				lastDate = aCalendar.getTime();
				aCalendar.set(Calendar.DATE, 1);
				firstDate = aCalendar.getTime();
//			long startTime = formatter.parse("2021-"+month+"-01 00:00:00").getTime();
//			long currentMillseconds = formatter.parse("2021-09-01 00:00:00").getTime();
				addOpenClosedData(firstDate, lastDate);

			}
		} catch (Exception e) {
			LOGGER.error(e);
		}
		return openClosedArr;
	}

	public void addOpenClosedData(Date firstDate, Date lastDate) {
		String monthPattern = "MMM yy";
		DateFormat formatter2 = new SimpleDateFormat(monthPattern);
		long startTime = firstDate.getTime();
		long endTime = lastDate.getTime();
		List<ServiceNowInc> filteredClosedInc = incidentList.stream().filter(
				m -> m.getClosedAt() != null ? (m.getClosedAt() > startTime && m.getClosedAt() < endTime) : false)
				.collect(Collectors.toList());
		List<ServiceNowInc> filteredOpenedInc = incidentList.stream()
				.filter(m -> (m.getOpenedAt() > startTime && m.getOpenedAt() < endTime)).collect(Collectors.toList());

		IncidentOpenClosed inc = new IncidentOpenClosed();
		inc.setClosed(filteredClosedInc.size());
		inc.setOpened(filteredOpenedInc.size());
		inc.setMonth(formatter2.format(firstDate));
		openClosedArr.add(inc);
	}

	@Override
//	@Cacheable(value="ServiceNowgetResoultionSLA", key ="'ServiceNowgetResoultionSLA'", cacheManager="timeoutCacheManager")
	public Map<String, Map<String, Integer>> getResoultionSLA() {
		getSLADetails();
		Map<String, Integer> metPercentage = new HashMap<>();
		
		Map<String, Integer> met = new HashMap<>();
		Map<String, Integer> target = new HashMap<>();
		target.put("1", 90);
		target.put("2", 85);
		target.put("3", 85);
		target.put("4", 85);
		target.put("5", 90);
		
		Map<String, Integer> total = new HashMap<>();
		
		for (ServiceNowSLA sla : slaList) {
			String priority = sla.getPriority();
			if (total.containsKey(priority)) {
				total.put(priority,total.get(priority)+1);
			}else {
				total.put(priority, 1);
			}
			
			String hasBreached = sla.getHas_breached();
			if(hasBreached.equalsIgnoreCase("true")) {
				if (met.containsKey(priority)) {
					met.put(priority,met.get(priority)+1);
				}else {
					met.put(priority, 1);
				}
			}
			
		}
		
		for(String priority: met.keySet()) {
			double m = met.get(priority);
			int t = total.get(priority);
			float p = (float)((m*100)/t);
			metPercentage.put(priority, Math.round(p));
		}
		
		Map<String, Map<String, Integer>> result = new HashMap<>();
		result.put("metPercentage", metPercentage);
		result.put("targetPercentage", target);
		return result;
	}

	@Override
//	@Cacheable(value="ServiceNowgetInflowTrend", key ="'ServiceNowgetResoultionSLA'", cacheManager="timeoutCacheManager")
	public List<InflowTrend> getInflowTrend() {
		getInititialDetails();
		getITDetails();
//		Long currentMillseconds = new Date().getTime();
		DateFormat formatter = new SimpleDateFormat(DEFAULT_PATTERN);

		try {
			Calendar aCalendar = Calendar.getInstance();
			aCalendar.set(Calendar.DATE, 1);
			Date firstDate = aCalendar.getTime();
			aCalendar.set(Calendar.DATE, aCalendar.getActualMaximum(Calendar.DAY_OF_MONTH));
			Date lastDate = aCalendar.getTime();
			int totalIncident = addTotalIncidentData(firstDate, lastDate);
			int totalSCReqItem = addTotalSCReqItemData(firstDate, lastDate);
			
			String monthPattern = "MMM yy";
			DateFormat formatter2 = new SimpleDateFormat(monthPattern);
			InflowTrend inc = new InflowTrend();
			inc.setIncident(totalIncident);
			inc.setScReqItem(totalSCReqItem);
			inc.setMonth(formatter2.format(firstDate));
			inflowTrendArr.add(inc);

			aCalendar.set(Calendar.DATE, 1);
			for (int i = 1; i < 12; i++) {
				inc = new InflowTrend();

				aCalendar.add(Calendar.DAY_OF_MONTH, i * -1);
				lastDate = aCalendar.getTime();
				aCalendar.set(Calendar.DATE, 1);
				firstDate = aCalendar.getTime();
//			long startTime = formatter.parse("2021-"+month+"-01 00:00:00").getTime();
//			long currentMillseconds = formatter.parse("2021-09-01 00:00:00").getTime();
				totalIncident = addTotalIncidentData(firstDate, lastDate);
				totalSCReqItem = addTotalSCReqItemData(firstDate, lastDate);
				inc.setIncident(totalIncident);
				inc.setScReqItem(totalSCReqItem);
				inc.setMonth(formatter2.format(firstDate));
				inflowTrendArr.add(inc);

			}
			Collections.reverse(inflowTrendArr);
		} catch (Exception e) {
			LOGGER.error(e);
		}
		
		return inflowTrendArr;
	}
	
	public int addTotalIncidentData(Date firstDate, Date lastDate) {
		long startTime = firstDate.getTime();
		long endTime = lastDate.getTime();
		List<ServiceNowInc> filteredOpenedInc = incidentList.stream()
				.filter(m -> (m.getOpenedAt() > startTime && m.getOpenedAt() < endTime)).collect(Collectors.toList());

		return filteredOpenedInc.size();
	}
	
	public int addTotalSCReqItemData(Date firstDate, Date lastDate) {
		
		DateFormat formatter = new SimpleDateFormat(DEFAULT_PATTERN);
		
		long startTime = firstDate.getTime();
		long endTime = lastDate.getTime();
		List<ServiceNowSCReqItem> filteredSCReqItemInc = scReqItemList.stream()
				.filter(m -> {
					try {
						return formatter.parse(m.getOpened_at()).getTime() > startTime && formatter.parse(m.getOpened_at()).getTime() < endTime;
					} catch (ParseException e) {
						LOGGER.info(e);
					}
					return false;
				} ).collect(Collectors.toList());

		return filteredSCReqItemInc.size();
	}
	@Override
//	@Cacheable(value="getAllIncidents", key ="'getAllIncidents'", cacheManager="timeoutCacheManager")
	public List<ServiceNowInc> getAllIncidents() {
		getInititialDetails();
		return incidentList;
	}
	
	

}
