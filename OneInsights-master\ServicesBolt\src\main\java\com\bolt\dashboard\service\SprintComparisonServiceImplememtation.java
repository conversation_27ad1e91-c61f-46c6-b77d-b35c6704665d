package com.bolt.dashboard.service;

import java.util.List;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.SprintComparison;
import com.bolt.dashboard.core.repository.SprintComparisonRepo;
import com.bolt.dashboard.request.SprintComparisonReq;
import com.bolt.dashboard.response.DataResponse;

@Service
public class SprintComparisonServiceImplememtation implements SprintComparisonService {
    private SprintComparisonRepo sprintComparisonRepo;
    private static final Logger LOG = LogManager.getLogger(SprintComparisonServiceImplememtation.class);
    @Autowired
    public SprintComparisonServiceImplememtation(SprintComparisonRepo repository) {
        this.sprintComparisonRepo = repository;
    }

    public SprintComparisonServiceImplememtation() {
    }

    @Override
//    @Cacheable(value="SprintComparisonsearch", key ="'SprintComparisonsearch'+#projectName", cacheManager="timeoutCacheManager")
    public DataResponse<SprintComparison> search(SprintComparisonReq request, String projectName) {
        long lastUpdated = 1;

        List<SprintComparison> sprintComparisonsList = sprintComparisonRepo.findByProjectName(projectName);
        if(!sprintComparisonsList.isEmpty())
        {
        SprintComparison sprintComparison = sprintComparisonsList.get(sprintComparisonsList.size() - 1);
        return new DataResponse<SprintComparison>(sprintComparison, lastUpdated);
        }else
        {LOG.info("SprintComparison data not found for project   "+projectName);
        	return null;
        }
    }

}
