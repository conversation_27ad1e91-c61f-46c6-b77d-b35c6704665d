package com.bolt.dashboard.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.SsoSetup;
import com.bolt.dashboard.core.repository.SsoConfigRepo;
import com.bolt.dashboard.response.DataResponse;

@Service
public class SsoConfigServiceImplemantation implements SsoConfigService {

	@Autowired
	private SsoConfigRepo repository;
	
	@Override
	public DataResponse<SsoSetup> getSsoConfig(String type) {
		long lastUpdated = 1;
		SsoSetup result = repository.findBySsoType(type);
        return new DataResponse<SsoSetup>(result, lastUpdated);
	}

	@Override
	public DataResponse<Iterable<SsoSetup>> save(List<SsoSetup> req) {
//		if (repository.count() != 0) {
//
//            deleteSetup(req);
//
//        };
//        return repository.save(req);
		for(SsoSetup setup: req) {
			SsoSetup temp = repository.findBySsoType(setup.getSsoType());
			if(temp != null) {
				temp.setAuthority(setup.getAuthority());
				temp.setClientId(setup.getClientId());
				temp.setRedirectUri(setup.getRedirectUri());
				repository.save(temp);
			}else {
				repository.save(setup);
			}
		}
		long lastUpdated = 1;
		return new DataResponse<Iterable<SsoSetup>>(repository.findAll(), lastUpdated);
	}

	public int deleteSetup(SsoSetup ssoSetup) {
		repository.deleteAll();
        return 0;
    }

	@Override
	public DataResponse<Iterable<SsoSetup>> getSsoConfigs() {
		long lastUpdated = 1;
		Iterable<SsoSetup> result = repository.findAll();
        return new DataResponse<Iterable<SsoSetup>>(result, lastUpdated);
	}
}
