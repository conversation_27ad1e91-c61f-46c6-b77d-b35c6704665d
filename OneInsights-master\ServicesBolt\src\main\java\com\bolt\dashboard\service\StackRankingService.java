package com.bolt.dashboard.service;

import java.util.List;

import com.bolt.dashboard.core.model.StackGrade;
import com.bolt.dashboard.core.model.StackGradeMetrics;
import com.bolt.dashboard.core.model.StackRanking;
import com.bolt.dashboard.request.StackGradingReq;
import com.bolt.dashboard.response.DataResponse;

public interface StackRankingService {
   

    StackRanking addStack(StackRanking req);

    int deleteConfig(StackRanking stackRanking);

    DataResponse<Iterable<StackGrade>> getStackGradingData(List<StackGradingReq> req);

    DataResponse<List<StackGradeMetrics>> fetchStackGradingData(String pName, String role, String ruleName);

    DataResponse<List<StackGrade>> fetchAllStackGradingData(StackGradingReq req);

    DataResponse<Boolean> deleteStackGradingData(StackGradingReq req);

	DataResponse<StackRanking> getStackByProject(String projectName);
}
