package com.bolt.dashboard.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.StackGrade;
import com.bolt.dashboard.core.model.StackGradeMetrics;
import com.bolt.dashboard.core.model.StackRanking;
import com.bolt.dashboard.core.model.StackRuleName;
import com.bolt.dashboard.core.repository.StackRankingGradeRep;
import com.bolt.dashboard.core.repository.StackRankingRep;
import com.bolt.dashboard.request.StackGradingMetricsReq;
import com.bolt.dashboard.request.StackGradingReq;
import com.bolt.dashboard.request.StackRuleNameReq;
import com.bolt.dashboard.response.DataResponse;

@Service
public class StackRankingServiceImplementation implements StackRankingService {
    private StackRankingRep stackRepository;
    private StackRankingGradeRep stackRankingGradeRep;

    @Autowired
    public StackRankingServiceImplementation(StackRankingRep stackRepository,
            StackRankingGradeRep stackRankingGradeRep) {
        this.stackRepository = stackRepository;
        this.stackRankingGradeRep = stackRankingGradeRep;

    }

 

    @Override
    public StackRanking addStack(StackRanking req) {
        Date date = new Date();
        if (stackRepository.count() != 0) {

            deleteConfig(req);

        }
        long timeStamp = date.getTime();
        req.setTimestamp((long) timeStamp);
        return stackRepository.save(req);
    }

    @Override
    public int deleteConfig(StackRanking stackRanking) {
        return stackRepository.deleteByProjectName(stackRanking.getProjectName());
    }

    @Override
    public DataResponse<Iterable<StackGrade>> getStackGradingData(List<StackGradingReq> stackReq) {

        StackGrade stackRankingForproject = new StackGrade();
        List<StackRuleName> rule = new ArrayList<StackRuleName>();
        List<StackGradeMetrics> stackGradeeRanking = new ArrayList<StackGradeMetrics>();

        List<StackGradingMetricsReq> stackgradinglist = new ArrayList<StackGradingMetricsReq>();
        StackRuleName stackRanking = new StackRuleName();

        for (StackGradingReq req : stackReq) {

            StackRuleNameReq stackgrading = new StackRuleNameReq();
            stackgrading = req.getRulemetric();
            stackRanking.setRuleName(stackgrading.getRuleName());
            stackgradinglist = stackgrading.getGrademetric();
            for (int i = 0; i < stackgradinglist.size(); i++) {
                StackGradingMetricsReq stackmetrics = new StackGradingMetricsReq();
                StackGradeMetrics stackgradingmetric = new StackGradeMetrics();
                stackmetrics = stackgradinglist.get(i);
                stackgradingmetric.setFrom(stackmetrics.getFrom());
                stackgradingmetric.setTo(stackmetrics.getTo());
                stackgradingmetric.setGrade(stackmetrics.getGrade());
                stackgradingmetric.setSelect(stackmetrics.isStatus());
                stackGradeeRanking.add(stackgradingmetric);
            }
            stackRanking.setGrademetrics(stackGradeeRanking);
            rule.add(stackRanking);
            stackRankingForproject.setProjectName(req.getProjectName());
            stackRankingForproject.setRole(req.getRole());

            stackRankingForproject.setRulemetrics(rule);
        }

        List<StackGrade> stackRankingList = stackRankingGradeRep
                .findByProjectNameAndRole(stackReq.get(0).getProjectName(), stackReq.get(0).getRole());
        StackGrade updatedstackRankingForproject = null;

        if (stackRankingList.isEmpty()) {
            stackRankingGradeRep.save(stackRankingForproject);

        } else {
            for (StackGradingReq req : stackReq) {

                List<StackGrade> stackRankingListnew = stackRankingGradeRep
                        .findByProjectNameAndRoleAndRulemetricsRuleName(stackReq.get(0).getProjectName(),
                                stackReq.get(0).getRole(), req.getRulemetric().getRuleName());

                if (stackRankingListnew.isEmpty()) {
                    stackRankingList.get(0).getRulemetrics().add(stackRanking);
                    stackRankingGradeRep.save(stackRankingList.get(0));
                } else {
                    updatedstackRankingForproject = stackRankingListnew.get(stackRankingListnew.size() - 1);
                    int sizek = updatedstackRankingForproject.getRulemetrics().size();
                    for (int m = 0; m < sizek; m++) {
                        if (updatedstackRankingForproject.getRulemetrics().get(m).getRuleName()
                                .equalsIgnoreCase(stackRanking.getRuleName())) {
                            updatedstackRankingForproject.getRulemetrics().get(m)
                                    .setGrademetrics(stackRanking.getGrademetrics());
                        }
                    }
                    stackRankingGradeRep.save(updatedstackRankingForproject);
                }
            }
        }
        return null;
    }

    public DataResponse<List<StackGradeMetrics>> fetchStackGradingData(String pName, String role, String ruleName) {
        long lastUpdated = 1;
//        String projectName = req.getProjectName();
//        String role = req.getRole();
       List<StackGradeMetrics> responsen = null;
//        String ruleName = req.getRulemetric().getRuleName();
        List<StackGrade> response = stackRankingGradeRep.findByProjectNameAndRoleAndRulemetricsRuleName(pName,
                role, ruleName);

        int size = response.get(0).getRulemetrics().size();
        for (int i = 0; i < size; i++) {

            if (response.get(0).getRulemetrics().get(i).getRuleName().equalsIgnoreCase(ruleName)) {
                responsen = response.get(0).getRulemetrics().get(i).getGrademetrics();
            }

        }
        return new DataResponse<>(responsen, lastUpdated);
    }

    public DataResponse<List<StackGrade>> fetchAllStackGradingData(StackGradingReq req) {
        long lastUpdated = 1;
        String projectName = req.getProjectName();
        String role = req.getRole();

        List<StackGrade> response = stackRankingGradeRep.findByProjectNameAndRole(projectName, role);

        return new DataResponse<List<StackGrade>>(response, lastUpdated);
    }

    public DataResponse<Boolean> deleteStackGradingData(StackGradingReq req) {

        String projectName = req.getProjectName();
        String roleName = req.getRole();
        String ruleName = req.getRulemetric().getRuleName();
        StackGrade updatedstackRankingForproject = null;
        List<StackGradeMetrics> responsene = null;

        List<StackGrade> response = stackRankingGradeRep.findByProjectNameAndRoleAndRulemetricsRuleName(projectName,
                roleName, ruleName);

        updatedstackRankingForproject = response.get(response.size() - 1);
        int requestGradeMetricSize = req.getRulemetric().getGrademetric().size();
        int counter = 0;
        int sizeOfRuleMetricsInDB = updatedstackRankingForproject.getRulemetrics().size();
        for (int i = 0; i < sizeOfRuleMetricsInDB; i++) {
            responsene = response.get(0).getRulemetrics().get(i).getGrademetrics();
            int sizeOfGradeMetricsInDB = responsene.size();

            if (response.get(0).getRulemetrics().get(i).getRuleName().equals(ruleName)) {
                for (int j = 0; j < sizeOfGradeMetricsInDB; j++) {
                    if (counter < requestGradeMetricSize && req.getRulemetric().getGrademetric().get(counter).getFrom()
                            .equals(responsene.get(j).getFrom())) {
                        responsene.remove(j);
                        j--;
                        sizeOfGradeMetricsInDB--;
                        counter++;
                    }

                }
                updatedstackRankingForproject.getRulemetrics().get(i).setGrademetrics(responsene);
            }

        }

        stackRankingGradeRep.save(updatedstackRankingForproject);
        return null;

    }

	@Override
	public DataResponse<StackRanking> getStackByProject(String projectName) {
		 long lastUpdated = 1;
	     StackRanking result = stackRepository.findByProjectName(projectName);
	     return new DataResponse<StackRanking>(result, lastUpdated);
	}
}
