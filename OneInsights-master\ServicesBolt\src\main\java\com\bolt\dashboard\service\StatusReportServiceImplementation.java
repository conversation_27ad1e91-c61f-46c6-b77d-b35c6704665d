package com.bolt.dashboard.service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.PortfolioConfig;
import com.bolt.dashboard.core.model.StatusReportModel;
import com.bolt.dashboard.core.repository.CollaborativeMailRepo;
import com.bolt.dashboard.core.repository.IterationRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.PortfolioConfigRepo;
import com.bolt.dashboard.exception.StatusReportException;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.util.ItextPdfUtils;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;

@Service
public class StatusReportServiceImplementation implements StatusReportService {
    PdfStamper stamper = null;
    FileOutputStream fos = null;
    FileInputStream fileInputStreamReader =null;
    static CollaborativeMailRepo collaborativeMailRepo;

    @Autowired
    ItextPdfUtils itextPdfUtils;

    @Autowired
    PortfolioConfigService portfolioConfigService;

    @Override
    public DataResponse<String> sendMail(StatusReportModel model) throws StatusReportException, IOException {
	String encodedBase64 = null;

	String resourceBasePath = System.getProperty("user.dir") + "\\classes\\pdf_Templates\\";
	String path = resourceBasePath + "template.pdf";
	try {
	    PdfReader pdfReader = new PdfReader(path);
	    fos = new FileOutputStream(resourceBasePath + "output.pdf");
	    stamper = new PdfStamper(pdfReader, fos);

	    Image image = Image.getInstance(resourceBasePath + "projectHealth.png");
	    Image storyTrackerimage = Image.getInstance(resourceBasePath + "StoryTracker.png");
	    DataResponse<Iterable<PortfolioConfig>> portFolioData = /* portfolioConfigService */
	    new PortfolioConfigServiceImplementation(DataConfig.getContext().getBean(PortfolioConfigRepo.class))
		    .getPortfolioDetails(model.getProjectName());
	    PortfolioConfig portfolioConfig = portFolioData.getResult().iterator().next();
	    // add header information to pdf
	    itextPdfUtils = new ItextPdfUtils();
	    itextPdfUtils.populateHeaderInfo(stamper, portfolioConfig);
	    // add image to "projectHealth" field
	    itextPdfUtils.addImage(stamper, stamper.getAcroFields(), "projectHealth", image);

	    itextPdfUtils.addImage(stamper, stamper.getAcroFields(), "storyTracker", storyTrackerimage);
	    Map<String, List<MetricsModel>> almData = getCurrentSprintData(portfolioConfig.getProjectName());
	    // itextPdfUtils.addTableUsingRectangles(pdfReader, almData,
	    // stamper, "activitiesThisWeekField");

	    stamper.close();
	    pdfReader.close();
	    File file = new File(resourceBasePath + "output.pdf");
	     fileInputStreamReader = new FileInputStream(file);
	    byte[] bytes = new byte[(int) file.length()];
	    //fileInputStreamReader.read(bytes);
	    int count=0;
	    while((count=fileInputStreamReader.read(bytes))>0)
	    encodedBase64 = new String(Base64.getEncoder().encode(bytes));

	   // fileInputStreamReader.close();
	    return new DataResponse<String>(encodedBase64, 0);
	} catch (Exception e) {
	    throw new StatusReportException(e);
	}finally {
		fileInputStreamReader.close();
	}
    }

    public Map<String, List<MetricsModel>> getCurrentSprintData(String projectName) {
	Map<String, List<MetricsModel>> listOfAlmData = new HashMap<String, List<MetricsModel>>();
	List<MetricsModel> inprogressDataForReport = new ArrayList<>();
	List<MetricsModel> doneDataForReport = new ArrayList<>();
	MetricRepo metricsRepo = DataConfig.getContext().getBean(MetricRepo.class);
	List<MetricsModel> metrics = new ArrayList<>();
	// get current Iteration info
	List<String> activeSprints = getCurrentIterationName(projectName);
	if (activeSprints != null) {
	    Iterator<String> it = activeSprints.iterator();
	    while (it.hasNext()) {
		String sprintName = it.next();
		metrics = metricsRepo.findByPNameAndSName(projectName, sprintName);
	    }
	}

	for (MetricsModel metric : metrics) {
	    if ("In Progress".equals(metric.getStatusCategory())) {
		inprogressDataForReport.add(metric);
	    } else if ("Done".equals(metric.getStatusCategory())) {
		doneDataForReport.add(metric);
	    }
	}
	listOfAlmData.put("inprogress", inprogressDataForReport);
	listOfAlmData.put("done", doneDataForReport);
	return listOfAlmData;
    }

    public List<String> getCurrentIterationName(String projectName) {
	List<String> activeSprints = new ArrayList<>();
	List<IterationModel> iteratiobnList = DataConfig.getContext().getBean(IterationRepo.class)
		.findByPName(projectName);
	Iterator<IterationModel> it = iteratiobnList.iterator();
	while (it.hasNext()) {
	    IterationModel model = it.next();
	    if (!model.getsName().equals("BackLog") && model.getState().equals("ACTIVE")) {
		activeSprints.add(model.getsName());

	    }

	}
	return activeSprints;
    }
}
