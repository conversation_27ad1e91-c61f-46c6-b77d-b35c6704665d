package com.bolt.dashboard.service;

import java.util.List;

import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.TeamIndexConfiguration;
import com.bolt.dashboard.core.repository.TeamIndexConfigRepo;

@Service
public class TeamIndexConfigServiceImplementation implements TeamIndexConfigService {

	@Autowired 
 TeamIndexConfigRepo teamIndexConfRepo;
	
	@Override
	public boolean saveTeamIndexConfig(TeamIndexConfiguration teamIndexConfig) {
		teamIndexConfig.setTimestamp(new DateTime().getMillis());
		// TODO Auto-generated method stub
		//List<TeamIndexConfiguration> teamIndexConfigData= this.teamIndexConfRepo.findByPName(teamIndexConfig.getpName());
//		if(!teamIndexConfigData.isEmpty()) {
//			teamIndexConfig.setId(teamIndexConfigData.get(0).getId());
//			
//			
//			
//		}
		TeamIndexConfiguration temp=	this.teamIndexConfRepo.save(teamIndexConfig);
		if(temp==null) {
			return false;
		}
		return true; 
	}

	@Override
	public List<TeamIndexConfiguration> getTeamIndexConfigData(String pName) {
		// TODO Auto-generated method stub
		List<TeamIndexConfiguration> teamIndexConfigData=this.teamIndexConfRepo.findByPName(pName);
		if(teamIndexConfigData.isEmpty()) {
			return null;
		}
		return teamIndexConfigData;
	}

	@Override
	public TeamIndexConfiguration getTeamIndexLastRecord(String pName) {
		// TODO Auto-generated method stub
		
		List<TeamIndexConfiguration> teamIndexConfigData=this.teamIndexConfRepo.findByPNameOrderByTimestamp(pName);
		if(teamIndexConfigData.isEmpty()) {
			return null;
		}
		return teamIndexConfigData.get(teamIndexConfigData.size()-1);
	}

}
