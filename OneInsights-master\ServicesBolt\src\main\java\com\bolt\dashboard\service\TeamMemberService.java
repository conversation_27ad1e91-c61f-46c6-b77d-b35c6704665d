package com.bolt.dashboard.service;

import com.bolt.dashboard.core.model.TeamMember;
import com.bolt.dashboard.request.TeamMemberReq;
import com.bolt.dashboard.response.DataResponse;

public interface TeamMemberService {

	String updateMemberDetails(TeamMemberReq teamMember);

	String addNewMember(TeamMemberReq teamMember);

	Boolean DeleteTeamMemer(TeamMemberReq key);

	DataResponse<Iterable<TeamMember>> getProjectMember(TeamMemberReq key);
	DataResponse<Iterable<TeamMember>> getTeamByProject(String pName);
	TeamMember getMemberDetails(String email, String pName);

}
