package com.bolt.dashboard.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.TeamMember;
import com.bolt.dashboard.core.repository.TeamMemberRepo;
import com.bolt.dashboard.request.TeamMemberReq;
import com.bolt.dashboard.response.DataResponse;

@Service
public class TeamMemberServiceImplementation implements TeamMemberService {
	private TeamMemberRepo teamMemberRepo;

	@Autowired
	public TeamMemberServiceImplementation(TeamMemberRepo teamMemberRepo) {
		this.teamMemberRepo = teamMemberRepo;

	}

	@Override
	public String updateMemberDetails(TeamMemberReq key) {
		TeamMember tm1 = teamMemberRepo.findByEmailAndProjectName(key.getEmail(), key.getProjectName());
		teamMemberRepo.delete(tm1);
		TeamMember tm2 = setData(key);
		teamMemberRepo.save(tm2);
		return "Data updated for Member :" + key.getName();
	}

	@Override
	public String addNewMember(TeamMemberReq key) {
		TeamMember tm = setData(key);
		teamMemberRepo.save(tm);
		return "Data saved for member" + key.getName();
	}

	public TeamMember setData(TeamMemberReq key) {
		TeamMember tm = new TeamMember();
		tm.setAlmId(key.getAlmId());
		tm.setBuildId(key.getBuildId());
		tm.setEmail(key.getEmail());
		tm.setName(key.getName());
		tm.setRepoId(key.getRepoId());
		tm.setRole(key.getRole());
		tm.setTestId(key.getTestId());
		tm.setProjectName(key.getProjectName());
		tm.setComponent(key.getComponent());
		return tm;

	}

	@Override
	public Boolean DeleteTeamMemer(TeamMemberReq key) {
		TeamMember toBeDeletedRow = teamMemberRepo.findByEmailAndProjectName(key.getEmail(), key.getProjectName());
		teamMemberRepo.delete(toBeDeletedRow);
		return true;
	}

	@Override
	public DataResponse<Iterable<TeamMember>> getProjectMember(TeamMemberReq key) {
		Iterable<TeamMember> result = teamMemberRepo.findByProjectName(key.getProjectName());
		return new DataResponse<Iterable<TeamMember>>(result, 1);
	}

	@Override
	public TeamMember getMemberDetails(String email, String pName) {
		return teamMemberRepo.findByEmailAndProjectName(email, pName);
	}

	@Override
	public DataResponse<Iterable<TeamMember>> getTeamByProject(String pName) {
		
		Iterable<TeamMember> result = teamMemberRepo.findByProjectName(pName);
		return new DataResponse<Iterable<TeamMember>>(result, 1);
	}
}
