package com.bolt.dashboard.service;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.PortfolioViewConfig;
import com.bolt.dashboard.core.model.TeamQuality;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.PortfolioViewConfigRepo;
import com.bolt.dashboard.core.repository.TeamQualityRepo;
import com.bolt.dashboard.response.DataResponse;

@Service
public class TeamQualityServiceImplementation implements TeamQualityService {
	private AnnotationConfigApplicationContext ctx = null;
	private TeamQualityRepo teamQualityRepo;
	private static final Logger LOGGER = LogManager.getLogger(TeamQualityServiceImplementation.class);

	@Autowired
	public TeamQualityServiceImplementation(TeamQualityRepo teamQualityRepo) {

		this.teamQualityRepo = teamQualityRepo;
		
	}

	@Override
//	@Cacheable(value="getTeamQualityOfDelivery", key ="'getTeamQualityOfDelivery'+#projectName", cacheManager="timeoutCacheManager")
	public DataResponse<List<TeamQuality>> getTeamQualityOfDelivery(String projectName) {
		ctx = DataConfig.getContext();
		PortfolioViewConfigRepo portfolioViewConfigRepo = ctx.getBean(PortfolioViewConfigRepo.class);
		PortfolioViewConfig pConfig = portfolioViewConfigRepo.findByProjectName(projectName);
		 long lastUpdated = 1;
		List<TeamQuality> response = teamQualityRepo.findByProjectNameAndEnvironment(projectName, pConfig.getEnvironment());
		
		return new DataResponse<List<TeamQuality>>(response, lastUpdated);
	}

}
