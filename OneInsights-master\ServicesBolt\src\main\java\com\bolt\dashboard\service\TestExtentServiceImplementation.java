package com.bolt.dashboard.service;

/**
 * 
 */
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bolt.dashboard.core.model.TestReportSummary;
import com.bolt.dashboard.core.repository.TestExtentRepo;
import com.bolt.dashboard.request.TestExtentReq;
import com.bolt.dashboard.response.DataResponse;

@Service
public class TestExtentServiceImplementation implements TestExtentSevice {
    private TestExtentRepo repo;

    @Autowired
    public TestExtentServiceImplementation(TestExtentRepo repo) {
        this.repo = repo;
    }

    public DataResponse<TestReportSummary> search(TestExtentReq request, String projectName) {

        long lastUpdated = 1;
        TestReportSummary result = repo.findByName(projectName);
        return new DataResponse<TestReportSummary>(result, lastUpdated);
    }

}
