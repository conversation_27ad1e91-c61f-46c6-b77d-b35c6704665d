package com.bolt.dashboard.service;

import java.util.List;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.TestManagementTool;
import com.bolt.dashboard.core.repository.TestManagementRepo;
import com.bolt.dashboard.response.DataResponse;

@Service
public class TestManagementToolServiceImplementation implements TestManagementToolService {

	private TestManagementRepo testToolRepository;
	private static final Logger LOG = LogManager.getLogger(TestManagementToolServiceImplementation.class);

	@Autowired
	public TestManagementToolServiceImplementation(TestManagementRepo testToolRepository) {
		this.testToolRepository = testToolRepository;
	}

	@Override
	public DataResponse<TestManagementTool> search(String projectName, String testType) {
		long lastUpdated = 1;
		List<TestManagementTool> data = (List<TestManagementTool>) testToolRepository
				.findByTestTypeAndProjectName(testType, projectName);
		if (!data.isEmpty()) {
			TestManagementTool lastRecord = data.get(data.size() - 1);

			return new DataResponse<TestManagementTool>(lastRecord, lastUpdated);
		} else {
			LOG.info("No data found search() TestManagementToolController() for Project " + projectName);
			return null;
		}
	}

}
