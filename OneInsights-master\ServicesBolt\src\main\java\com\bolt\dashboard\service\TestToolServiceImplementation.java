package com.bolt.dashboard.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.TestTool;
import com.bolt.dashboard.core.repository.TestRepository;
import com.bolt.dashboard.request.TestToolReq;
import com.bolt.dashboard.response.DataResponse;

@Service
public class TestToolServiceImplementation implements TestToolService {

    private TestRepository testToolRepository;

    @Autowired
    public TestToolServiceImplementation(TestRepository testToolRepository) {
        this.testToolRepository = testToolRepository;
    }

    @Override
    public DataResponse<Iterable<TestTool>> search(TestToolReq request) {
        long lastUpdated = 1;
        Iterable<TestTool> result = testToolRepository.findAll();
        if(result.iterator().hasNext()){
        return new DataResponse<Iterable<TestTool>>(result, lastUpdated);
        }else{
        	return null;
        }
    }

    @Override
    public DataResponse<Iterable<TestTool>> search(TestToolReq request, String projectName) {
        long lastUpdated = 1;

        Iterable<TestTool> result = testToolRepository.findByName(projectName);
        if(result.iterator().hasNext())
        {
        return new DataResponse<Iterable<TestTool>>(result, lastUpdated);
        }else{
        	return null;
        }
    }
}
