package com.bolt.dashboard.service;

import java.util.List;
import java.util.Set;

import com.bolt.dashboard.core.model.AssociatedUsers;
import com.bolt.dashboard.core.model.PortfolioOutModel;
import com.bolt.dashboard.core.model.UserAssociation;
import com.bolt.dashboard.request.UserAssociationReq;
import com.bolt.dashboard.request.UserAssosiationMetricsReq;

public interface UserAssociationService {
	/*
	 * DataResponse<Iterable<UserAssociation>> getUser();
	 * 
	 * DataResponse<Iterable<UserAssociation>> getUser(String projectName);
	 * 
	 * UserAssociation addUser(UserAssociation req);
	 * 
	 * int deleteUserForProject(UserAssociation userAssociation);
	 * 
	 * DataResponse<Boolean> deleteUserData(UserAssosiationMetricsReq req);
	 */

	List<PortfolioOutModel> getProjectAssosiatedWithUser(String userName);

	Set<AssociatedUsers> getProjectUsers(String pName);

	String deleteAssociatedUser(UserAssociation req);

	String updateAssociatedUser(UserAssociationReq req);
	
	PortfolioOutModel getProjectdetails(String pName, String userName);

	Boolean deleteUserData(UserAssosiationMetricsReq userAssosiationMetricsReq);
	
	UserAssociation addUser(UserAssociation userAssociation);

}