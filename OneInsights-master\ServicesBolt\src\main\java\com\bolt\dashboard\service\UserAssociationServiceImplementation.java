package com.bolt.dashboard.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.AssociatedUsers;
import com.bolt.dashboard.core.model.HealthData;
import com.bolt.dashboard.core.model.ManageUser;
import com.bolt.dashboard.core.model.PortfolioConfig;
import com.bolt.dashboard.core.model.PortfolioOutModel;
import com.bolt.dashboard.core.model.UserAssociation;
import com.bolt.dashboard.core.repository.HealthDataRepo;
import com.bolt.dashboard.core.repository.PortfolioConfigRepo;
import com.bolt.dashboard.core.repository.UserAssociationRep;
import com.bolt.dashboard.core.repository.UserRepo;
import com.bolt.dashboard.request.UserAssociationReq;
import com.bolt.dashboard.request.UserAssosiationMetricsReq;

@Service
public class UserAssociationServiceImplementation implements UserAssociationService {
	private UserAssociationRep userAssosiationRep;
	private UserRepo userRepo;
	private PortfolioConfigRepo portfolioConfigRepo;
	private HealthDataRepo healthRepo;
	private static final Logger LOG = LogManager.getLogger(UserAssociationServiceImplementation.class);

	@Autowired
	public UserAssociationServiceImplementation(UserAssociationRep userAss, UserRepo userRepo, PortfolioConfigRepo port,
			HealthDataRepo health) {
		this.userAssosiationRep = userAss;
		this.userRepo = userRepo;
		this.portfolioConfigRepo = port;
		this.healthRepo = health;
	}

	@Override
//	@Cacheable(value="getProjectAssosiatedWithUser", key ="'getProjectAssosiatedWithUser'+#userName", cacheManager="timeoutCacheManager")
	  public List<PortfolioOutModel> getProjectAssosiatedWithUser(String userName) {
        List<PortfolioOutModel> finalArray = new ArrayList<>();
        List<PortfolioConfig> portConfig = new ArrayList<>();
        List<ManageUser> mainUserDb = userRepo.findByUserName(userName);
        ManageUser deleteFilterUsers=mainUserDb.stream().filter(p->
        	!p.getDeleteFlag()
        ).collect(Collectors.toList()).get(0);
         
        String projectAccess = null;
        if ("Admin".equalsIgnoreCase(deleteFilterUsers.getUserRole().toString())
                || "Executive".equalsIgnoreCase(deleteFilterUsers.getUserRole().toString())) {
            portConfig = portfolioConfigRepo.findAll();
            projectAccess = deleteFilterUsers.getUserRole().toString();
        } else {
            Query findQuery = Query.query(Criteria.where("users.userName").is(userName));
            findQuery.fields().include("pName");
            findQuery.fields().include("almType");
            findQuery.fields().include("users.$");
            List<UserAssociation> datas = new ArrayList<>();
            try {
                datas = DataConfig.getInstance().mongoTemplate().find(findQuery, UserAssociation.class);
            } catch (Exception e) {
                LOG.info(e);
            }
            for (UserAssociation projects : datas) {
                portConfig.add(portfolioConfigRepo.findByProjectName(projects.getpName()).get(0));
                projectAccess = projects.getUsers().iterator().next().getAccess();
            }
        }
        
        List<HealthData> healthData = (List<HealthData>) healthRepo.findAll();
        Map<String, List<HealthData>> grouped = healthData.stream().collect(Collectors.groupingBy(HealthData::getProjectName));
        
        for (PortfolioConfig config : portConfig) {

 

            
            List<HealthData> helInd=grouped.get(config.getProjectName());
            HealthData healthLast=new HealthData();
            if (helInd !=null && helInd.size()>0) {
                healthLast = helInd.get(helInd.size() - 1);
            }
            PortfolioOutModel obj = new PortfolioOutModel();
            obj.setEmail(deleteFilterUsers.getEmail());
            obj.setProjectAccess(projectAccess);
            
            BeanUtils.copyProperties(config, obj);

 

            
            obj.setHealth(healthLast);

 

            finalArray.add(obj);
        }
        return finalArray;
    }

	@Override
	public Set<AssociatedUsers> getProjectUsers(String pName) {
//		UserAssociation dbProject = userAssosiationRep.findByPName(pName).get(0);
//		return dbProject != null ? dbProject.getUsers() : null;
		List<UserAssociation> dbProjects = userAssosiationRep.findByPName(pName);
		if(!dbProjects.isEmpty()) {
			UserAssociation dbProject = dbProjects.get(0);
			return dbProject != null ? dbProject.getUsers() : null;
		}
		return null;
	}

	@Override
//	@Caching(evict = {
//			@CacheEvict(value="getProjectAssosiatedWithUser", cacheManager="timeoutCacheManager"),
//			@CacheEvict(value="getProjectdetails", cacheManager="timeoutCacheManager")
//	})
	public String updateAssociatedUser(UserAssociationReq pDetails) {
		List<UserAssociation> dbUser = userAssosiationRep.findByPName(pDetails.getpName());
		if (dbUser != null && dbUser.size()>0) {
			userAssosiationRep.delete(dbUser);
		}
		UserAssociation details = new UserAssociation();
		
		BeanUtils.copyProperties(pDetails, details);
		userAssosiationRep.save(details);
		return "Operation done";
	}

	@Override
	public String deleteAssociatedUser(UserAssociation req) {
		UserAssociation dbUser = userAssosiationRep.findByPNameAndAlmType(req.getpName(), req.getAlmType());
		if (dbUser != null) {
			userAssosiationRep.delete(dbUser);
			userAssosiationRep.save(req);
			return "User Deleted Succesfully";
		}
		return "Not a valid project name and alm type combination";
	}

	@Override
//	@Cacheable(value="getProjectdetails", key ="'getProjectdetails'+#pName+#userName", cacheManager="timeoutCacheManager")
	public PortfolioOutModel getProjectdetails(String pName, String userName) {
		for (PortfolioOutModel data : getProjectAssosiatedWithUser(userName)) {
			if (new String(pName).equals(data.getProjectName())) {
				return data;
			}
		}
		return null;
	}

	@Override
//	@Caching(evict = {
//			@CacheEvict(value="getProjectAssosiatedWithUser", cacheManager="timeoutCacheManager"),
//			@CacheEvict(value="getProjectdetails", cacheManager="timeoutCacheManager")
//	})
	public Boolean deleteUserData(UserAssosiationMetricsReq userAssosiationMetricsReq) {
		UserAssociation dbUser = userAssosiationRep.findByPNameAndAlmType(userAssosiationMetricsReq.getProject(),userAssosiationMetricsReq.getAlmType() );
		if (dbUser != null) {
			userAssosiationRep.delete(dbUser);
			return true;
		}
		return false;
	}

	@Override
	public UserAssociation addUser(UserAssociation req) {
		
		return userAssosiationRep.save(req);
	}

}