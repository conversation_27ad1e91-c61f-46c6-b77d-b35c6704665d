package com.bolt.dashboard.service;

import java.util.List;

import com.bolt.dashboard.core.model.ManageUser;

public interface UserService {

	/*
	 * boolean delete(String string);
	 * 
	 * ManageUser saveUser(ManageUser req);
	 * 
	 * DataResponse<Iterable<ManageUser>> getUserData();
	 * 
	 * DataResponse<Iterable<ManageUser>> getProjectUsers(String projectName);
	 * 
	 * WriteResult updateUser(ManageUser manageUser) throws IOException;
	 */

	List<ManageUser> getAllUserData();

	String saveNewUser(ManageUser user, Boolean flag);

	String deleteUser(ManageUser user);

	List<ManageUser> getUserDataByUserName(String userName);

	List<ManageUser> getAllUserDataAssc();

	String updatePassword(ManageUser user);

}
