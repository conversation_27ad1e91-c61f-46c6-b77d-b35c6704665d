package com.bolt.dashboard.service;

import java.util.ArrayList;
import java.util.List;

import org.mindrot.jbcrypt.BCrypt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.ManageUser;
import com.bolt.dashboard.core.repository.UserRepo;

@Service
public class UserServiceImplementation implements UserService {
	private UserRepo userRepo;

	@Autowired
	public UserServiceImplementation(UserRepo userRepository) {
		this.userRepo = userRepository;

	}
	
	@Override
	public List<ManageUser> getUserDataByUserName(String userName) {
		return  userRepo.findByUserName(userName);
	}

	@Override
	public List<ManageUser> getAllUserData() {
		List<ManageUser> users = new ArrayList<>();
		for (ManageUser user : userRepo.findAll()) {
			if (Boolean.FALSE.equals(user.getDeleteFlag())) {
				user.setPassword("****");
				user.setOldpassword("****");
				users.add(user);
			}
		}
		return users;
	}

	
	@Override
	public List<ManageUser> getAllUserDataAssc() {
		
		List<ManageUser> users = new ArrayList<>();
		for (ManageUser user : userRepo.findAll()) {
			if (Boolean.FALSE.equals(user.getDeleteFlag())) {
				user.setPassword("****");
				user.setOldpassword("****");
				user.setImage(null);
				user.setFullName(null);
				user.setLicenceKey(null);
				user.setUserRole(null);
				users.add(user);
			}
		}
		return users;
	}
	@Override
	public String saveNewUser(ManageUser user, Boolean flag) {
		ManageUser dbUser = userRepo.findByUserNameAndEmail(user.getUserName(), user.getEmail());
boolean status= false;	
	if (dbUser != null && !flag) {
		
		if (user.getPassword().equals("****") ) {
			user.setPassword(dbUser.getPassword());
		} else {
			user.setPassword(hashPassword(user.getPassword()));
		}
		userRepo.delete(dbUser);
		userRepo.save(user);
		return "User details updated!!";
	}
	else if (flag) {
			if(dbUser!=null) {
				userRepo.delete(dbUser);
			}
			user.setPassword(hashPassword(user.getPassword()));
			user.setImage(user.getImage());
			user.setFullName(user.getFullName());	
			user.setEmail(user.getEmail());
			user.setDeleteFlag(false);
			userRepo.save(user);
			return "User Added!!";
		}
		return "Something went wrong";
	}

	@Override
	public String deleteUser(ManageUser user) {
		ManageUser dbUser = userRepo.findByUserNameAndEmail(user.getUserName(), user.getEmail());
		if (dbUser != null) {
			userRepo.delete(dbUser);
			dbUser.setDeleteFlag(true);
			userRepo.save(dbUser);
			return "deleted";
		}
		return "Invalid User " + user.getUserName();
	}

	public static String hashPassword(String password_plaintext) {
		String salt = BCrypt.gensalt(12);
		String hashed_password = BCrypt.hashpw(password_plaintext, salt);
		return (hashed_password);
	}

	public static boolean matchHashPasswords(String oldpassword, String password)
	{
	
		boolean result = BCrypt.checkpw(oldpassword, password) ;
		return result;
	}
	/*
	 * private static final Log LOG =
	 * LogFactory.getLog(UserServiceImplementation.class); private ManageUserRep
	 * userRepository; private static int workload = 12; ManageUserMetricReq
	 * manageUserSettingReq; private String orqPswd;
	 * 
	 * @Autowired public UserServiceImplementation(ManageUserRep userRepository)
	 * { this.userRepository = userRepository;
	 * 
	 * }
	 * 
	 * @Override public boolean delete(String email) { Iterable<ManageUser>
	 * metric = userRepository.findByEmail(email);
	 * 
	 * if (metric.iterator().hasNext() &&
	 * userRepository.deleteById(metric.iterator().next().getId()) == 0) {
	 * return false; } return true;
	 * 
	 * }
	 * 
	 * @Override public DataResponse<Iterable<ManageUser>> getUserData() { long
	 * lastUpdate = 1; Iterable<ManageUser> result = userRepository.findAll();
	 * return new DataResponse<Iterable<ManageUser>>(result, lastUpdate); }
	 * 
	 * @Override public ManageUser saveUser(ManageUser req) { Date date = new
	 * Date(); long timeStamp = date.getTime(); req.setTimestamp((long)
	 * timeStamp); if (req.getMetric().iterator().next().getEmail() != null) {
	 * delete(req.getMetric().iterator().next().getEmail()); } orqPswd =
	 * req.getMetric().iterator().next().getPassword();
	 * req.getMetric().iterator().next().setPassword(hashPassword(orqPswd));
	 * return userRepository.save(req); }
	 * 
	 * public static String hashPassword(String password_plaintext) { String
	 * salt = BCrypt.gensalt(workload); String hashed_password =
	 * BCrypt.hashpw(password_plaintext, salt);
	 * 
	 * return (hashed_password); }
	 * 
	 * {
	 * @SuppressWarnings("unchecked")
	 * 
	 * @Override public WriteResult updateUser(ManageUser req) throws
	 * IOException { MongoClient client = null;
	 * 
	 * Properties properties = new Properties();
	 * 
	 * InputStream in = getClass().getClassLoader().getResourceAsStream(
	 * "ServerDataConfig.properties"); properties.load(in);
	 * 
	 * String host = properties.getProperty("config.host"); int port =
	 * Integer.parseInt(properties.getProperty("config.port")); String dbUserId
	 * = properties.getProperty("config.userId"); String dbPassword =
	 * properties.getProperty("config.password");
	 * 
	 * ServerAddress serverAddr = new ServerAddress(host, port); MongoClient
	 * client = new MongoClient(host); MongoCredential mongoCredential =
	 * MongoCredential.createScramSha1Credential(dbUserId, "admin",
	 * dbPassword.toCharArray()); client = new MongoClient(serverAddr,
	 * Collections.singletonList(mongoCredential));
	 * 
	 * try { client = DataConfig.getInstance().mongo(); } catch (Exception e) {
	 * LOG.info("Exception in ManageUserServiceImplementation UpdateUser()");
	 * LOG.info(e); }
	 * 
	 * 
	 * DB databaseName =
	 * client.getDB(DataConfig.getInstance().getDatabaseName()); DBCollection
	 * collectionName = databaseName.getCollection("User");
	 * 
	 * 
	 * DBCollection collectionName = null; try { collectionName =
	 * DataConfig.getInstance().mongoTemplate().getCollection("User"); } catch
	 * (Exception e) {
	 * 
	 * e.printStackTrace(); } DBObject dbObject = new BasicDBObject("_id",
	 * req.getMetric().iterator().next().getId()); ObjectId userId = new
	 * ObjectId("" + dbObject.get("_id")); BasicDBObject searchObject = new
	 * BasicDBObject(); searchObject.put("_id", userId); DBObject modifiedObject
	 * = new BasicDBObject(); String password =
	 * req.getMetric().iterator().next().getPassword(); if (password == null) {
	 * Iterable<ManageUser> rew =
	 * DataConfig.getContext().getBean(ManageUserRep.class)
	 * .findByEmail(req.getMetric().iterator().next().getEmail()); for
	 * (ManageUser manageUser : rew) { Set<ManageUserMetric> metricSet =
	 * manageUser.getMetric(); password =
	 * metricSet.iterator().next().getPassword(); }
	 * modifiedObject.put("password", password); } else {
	 * modifiedObject.put("password",
	 * hashPassword(req.getMetric().iterator().next().getPassword())); }
	 * modifiedObject.put("userName",
	 * req.getMetric().iterator().next().getUserName());
	 * modifiedObject.put("email",
	 * req.getMetric().iterator().next().getEmail());
	 * modifiedObject.put("projectName",
	 * req.getMetric().iterator().next().getProjectName());
	 * modifiedObject.put("userType",
	 * req.getMetric().iterator().next().getUserType());
	 * modifiedObject.put("almId",
	 * req.getMetric().iterator().next().getAlmId());
	 * modifiedObject.put("repoId",
	 * req.getMetric().iterator().next().getRepoId());
	 * modifiedObject.put("image",
	 * req.getMetric().iterator().next().getImage());
	 * modifiedObject.put("fullName",
	 * req.getMetric().iterator().next().getFullName());
	 * modifiedObject.put("testId",
	 * req.getMetric().iterator().next().getTestId());
	 * modifiedObject.put("userRole",
	 * req.getMetric().iterator().next().getUserRole());
	 * modifiedObject.put("status",
	 * req.getMetric().iterator().next().getStatus());
	 * modifiedObject.put("licenceKey",
	 * req.getMetric().iterator().next().getLicenceKey()); DBObject finalObject
	 * = new BasicDBObject();
	 * 
	 * List list = new ArrayList(); list.add(modifiedObject);
	 * finalObject.put("_id", userId); finalObject.put("timestamp",
	 * req.getTimestamp()); finalObject.put("projectName",
	 * req.getMetric().iterator().next().getProjectName());
	 * 
	 * finalObject.put("metric", list);
	 * 
	 * return collectionName.update(searchObject, finalObject, true, false); }
	 * 
	 * @Override public DataResponse<Iterable<ManageUser>>
	 * getProjectUsers(String projectName) { long lastUpdate = 1;
	 * Iterable<ManageUser> result =
	 * userRepository.findByprojectName(projectName); return new
	 * DataResponse<Iterable<ManageUser>>(result, lastUpdate); }
	 */

	@Override
	public String updatePassword(ManageUser user) {
		ManageUser dbUser = userRepo.findByUserNameAndEmail(user.getUserName(), user.getEmail());
		boolean status= false;
		if (dbUser != null ) {
			
			if(matchHashPasswords(user.getOldpassword(),dbUser.getPassword()))
			{
				user.setPassword(hashPassword(user.getPassword()));
				status=true;
			}
			else {
				user.setPassword(dbUser.getPassword());
				status= false;
			}
			user.setOldpassword(hashPassword(user.getOldpassword()));
			userRepo.delete(dbUser);
			userRepo.save(user);
		}
			if (status) {
              return "User details updated!!";
			}
			else {
			return	"Incorrect old password";
			}	

		

	}
}
