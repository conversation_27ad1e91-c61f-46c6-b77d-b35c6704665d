package com.bolt.dashboard.service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.WSRActionItemsModel;
import com.bolt.dashboard.core.model.WSRKeyUpdatesModel;
import com.bolt.dashboard.core.model.WSRLnHModel;
import com.bolt.dashboard.core.model.WSRModel;
import com.bolt.dashboard.core.model.WSRProjectStatusModel;
import com.bolt.dashboard.core.model.WSRRisksModel;
import com.bolt.dashboard.core.repository.WSRRepo;
import com.bolt.dashboard.request.WSRReq;
import com.bolt.dashboard.response.DataResponse;

@Service
public class WSRServiceImplementation implements WSRService {
	private WSRRepo wsrRepo;

	@Autowired
	public WSRServiceImplementation(WSRRepo wsrRepo) {
		this.wsrRepo = wsrRepo;

	}

	@Override
	public DataResponse<Iterable<WSRModel>> fetchDetails(String pName) {
		List<WSRModel> data = wsrRepo.findByPName(pName);
		return new DataResponse<Iterable<WSRModel>>(data, 1);
	}

	@Override
	public String updateWsr(WSRReq data) {
		WSRModel getData=wsrRepo.findByPNameAndItrAndStDateAndEndDate(data.getpName(), data.getItr(), data.getStDate(), data.getEndDate());
		WSRModel model = setMetrics(data);
		wsrRepo.delete(getData);
		wsrRepo.save(model);
		return "Row upadted successfully!!";
	}

	@Override
	public String addWsr(WSRReq data) {
		WSRModel model = setMetrics(data);
		wsrRepo.save(model);
		return "Row Added successfully!!";
	}

	@Override
	public String deleteWsr(WSRReq data) {
		WSRModel getData=wsrRepo.findByPNameAndItrAndStDateAndEndDate(data.getpName(), data.getItr(), data.getStDate(), data.getEndDate());
		wsrRepo.delete(getData);
		return "Row deleted Sucessfully!!";
	}

	public WSRModel setMetrics(WSRReq data) {
		WSRModel temp = new WSRModel();
		temp.setSts(setProjectStatusData(data.getSts()));
		temp.setAct(setActionItems(data.getAct()));
		temp.setUpd(setKeyUpdates(data.getUpd()));
		temp.setLnh(setHolidays(data.getLnh()));
		temp.setpName(data.getpName());
		temp.setRisks(setRisks(data.getRisks()));		
		temp.setStDate(data.getStDate());
		temp.setEndDate(data.getEndDate());
		temp.setItr(data.getItr());
		temp.setaCmt(data.getaCmt());
		temp.setkCmt(data.getkCmt());
		temp.setlCmt(data.getlCmt());
		temp.setrCmt(data.getrCmt());
		temp.setpCmt(data.getpCmt());
		return temp;
	}

	public List<WSRProjectStatusModel> setProjectStatusData(List<WSRProjectStatusModel> datas) {
		WSRProjectStatusModel pSts = null;;
		List<WSRProjectStatusModel> array = new ArrayList<WSRProjectStatusModel>();
		Iterator<WSRProjectStatusModel> iterator = datas.iterator();
		while (iterator.hasNext()) {
			pSts=new WSRProjectStatusModel();
			WSRProjectStatusModel data = iterator.next();
			pSts.setCmt(data.getCmt());
			pSts.setCr(data.getCr());
			pSts.setFt(data.getFt());			
			pSts.setPre(data.getPre());
			pSts.setSpr(data.getSpr());	
			array.add(pSts);
			
		}
		pSts=null;
		return array;
	}

	public List<WSRActionItemsModel> setActionItems(List<WSRActionItemsModel> datas) {
		WSRActionItemsModel aItems = null;
		List<WSRActionItemsModel> array = new ArrayList<WSRActionItemsModel>();
		Iterator<WSRActionItemsModel> iterator = datas.iterator();
		while (iterator.hasNext()) {
			aItems=new WSRActionItemsModel();
			WSRActionItemsModel data = iterator.next();
			aItems.setCmt(data.getCmt());
			aItems.setFrm(data.getFrm());
			aItems.setItr(data.getItr());
			aItems.setOwner(data.getOwner());
			aItems.setSts(data.getSts());
			aItems.setTo(data.getTo());
			aItems.setItms(data.getItms());
			array.add(aItems);
		}
		aItems=null;
		return array;
	}

	public List<WSRRisksModel> setRisks(List<WSRRisksModel> datas) {
		WSRRisksModel risks = null;
		List<WSRRisksModel> array = new ArrayList<WSRRisksModel>();
		Iterator<WSRRisksModel> iterator = datas.iterator();
		while (iterator.hasNext()) {
			risks = new WSRRisksModel();
			WSRRisksModel data = iterator.next();
			risks.setCat(data.getCat());
			risks.setDesc(data.getDesc());
			risks.setItr(data.getItr());
			risks.setOwner(data.getOwner());
			risks.setPlan(data.getPlan());
			risks.setSts(data.getSts());
			risks.setTo(data.getTo());
			array.add(risks);
		}
		risks=null;
		return array;
	}

	public List<WSRLnHModel> setHolidays(List<WSRLnHModel> datas) {
		WSRLnHModel lnh = null;
		List<WSRLnHModel> array = new ArrayList<WSRLnHModel>();
		Iterator<WSRLnHModel> iterator = datas.iterator();
		while (iterator.hasNext()) {
			lnh = new WSRLnHModel();
			WSRLnHModel data = iterator.next();
			lnh.setFrm(data.getFrm());
			lnh.setName(data.getName());
			lnh.setTo(data.getTo());
			array.add(lnh);
		}
		lnh=null;
		return array;
	}
	public List<WSRKeyUpdatesModel> setKeyUpdates(List<WSRKeyUpdatesModel> datas) {
		WSRKeyUpdatesModel key= null;
		List<WSRKeyUpdatesModel> array = new ArrayList<WSRKeyUpdatesModel>();
		Iterator<WSRKeyUpdatesModel> iterator = datas.iterator();
		while (iterator.hasNext()) {
			key= new WSRKeyUpdatesModel();
			WSRKeyUpdatesModel data = iterator.next();
			key.setAccmp(data.getAccmp());
			key.setOpp(data.getOpp());
			key.setTa(data.getTa());
			key.setTp(data.getTp());
			array.add(key);
		}
		key=null;
		return array;
	}
}
