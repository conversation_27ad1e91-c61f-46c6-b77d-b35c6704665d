package com.bolt.dashboard.util;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.util.Base64;

import javax.imageio.ImageIO;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import com.bolt.dashboard.exception.UtilException;

@Component
public class ImagesUtils {

    private static final Logger LOG = LogManager.getLogger(ImagesUtils.class);

    public void base64ToImage(String imageData, String imageName) throws UtilException {

        String finalpath = System.getProperty("user.dir") + "/classes/pdf_Templates";

        LOG.info("Resorce path:" + finalpath);

        BufferedImage image = null;
        try{
        byte[] valueDecoded = Base64.getDecoder().decode(imageData);
        ByteArrayInputStream bis = new ByteArrayInputStream(valueDecoded);
        image = ImageIO.read(bis);
        bis.close();

        // write the image to a file
        File outputfile = new File(finalpath, imageName);

        if (outputfile.exists()) {
            ImageIO.write(image, "png", outputfile);

        } else {
           if(outputfile.createNewFile())
            ImageIO.write(image, "png", outputfile);

        }
        }catch(Exception e){
            throw new UtilException(e);
        }

    }

}