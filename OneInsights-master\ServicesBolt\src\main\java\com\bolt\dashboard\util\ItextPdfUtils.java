package com.bolt.dashboard.util;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.PortfolioConfig;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Font;
import com.itextpdf.text.Image;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.ColumnText;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPRow;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;

@Component
public class ItextPdfUtils {
	 private static final Logger LOG = LogManager.getLogger(ItextPdfUtils.class);
    @Autowired
    DateUtil dateUtil;

    public void addImage(PdfStamper stamper, AcroFields form, String field, Image img) {
	try {
	    java.util.List<AcroFields.FieldPosition> photograph = form.getFieldPositions(field);

	    int page = photograph.get(0).page;
	    Rectangle rect = photograph.get(0).position;
	    Image image = Image.getInstance(img);
	    image.scaleToFit(rect.getWidth(), rect.getHeight());
	    image.setAbsolutePosition(rect.getLeft(), rect.getBottom());
	    PdfContentByte canvas = stamper.getOverContent(page);
	    canvas.addImage(image);
	} catch (Exception e) {
	    LOG.info(e);
	}
    }

    public void populateHeaderInfo(PdfStamper stamper, PortfolioConfig data) {
	dateUtil = new DateUtil();
	Map<String, String> lastWeek = dateUtil.getLastWeekWorkingDateRange();
	try {
	    stamper.getAcroFields().setField("brillioPM", data.getProductOwner());
	    stamper.getAcroFields().setField("brillioOnsiteManager", data.getScrumMaster());
	    stamper.getAcroFields().setField("reportFromTime", lastWeek.get("start"));
	    stamper.getAcroFields().setField("reportEndTime", lastWeek.get("end"));
	    stamper.getAcroFields().setField("todaysDate", dateUtil.getDateInFormat("yyyy/MM/dd", new Date()));
	} catch (Exception e) {
	    LOG.info(e);
	}
    }

    public void addTableUsingRectangles(PdfReader pdfReader, Map<String, List<MetricsModel>> almData,
	    PdfStamper stamper, String fieldName) throws DocumentException {
	int page = 1;
	int status1;
	int pagecount = 0;
	Rectangle rectPage1 = null, rect1 = null;
	Rectangle rectPage2 = null;
	ColumnText column = null;
	Rectangle pagesize = null;
	Object[] keys = almData.keySet().toArray();
	column = new ColumnText(stamper.getOverContent(page));
	for (int j = 0; j < keys.length; j++) {
	    List<MetricsModel> metrics = almData.get(keys[j].toString());
	    pagesize = pdfReader.getPageSize(page);
	    PdfPTable table = new PdfPTable(4);
	    Font font = new Font();
	    font.setColor(BaseColor.WHITE);
	    table.addCell(new PdfPCell(new Phrase("JIRA-ID", font)));
	    table.addCell(new PdfPCell(new Phrase("Assignee", font)));
	    table.addCell(new PdfPCell(new Phrase("Type", font)));
	    table.addCell(new PdfPCell(new Phrase("State", font)));
	    table.setHeaderRows(1);

	    table.setWidths(new int[] { 8, 15, 15, 15 });
	    for (int i = 0; i < metrics.size(); i++) {
		table.addCell(metrics.get(i).getwId());
		table.addCell(metrics.get(i).getAssgnTo());
		table.addCell(metrics.get(i).getType());
		table.addCell(metrics.get(i).getState());
	    }
	    boolean b = true, firstRow = true;
	    for (PdfPRow r : table.getRows()) {
		for (PdfPCell c : r.getCells()) {
		    if (firstRow) {
			c.setBackgroundColor(new BaseColor(4, 137, 181));

		    } else {
			c.setBackgroundColor(b ? BaseColor.WHITE : new BaseColor(220, 223, 224));
		    }

		}
		b = !b;
		firstRow = false;
	    }

	    if (j != keys.length - 1) {

		if (stamper.getAcroFields().getFieldPositions(fieldName) != null) {
		    Rectangle r1 = stamper.getAcroFields().getFieldPositions(fieldName).get(0).position;
		    float llx = r1.getLeft();
		    float lly = r1.getLeft();
		    float urx = r1.getRight();
		    float ury = r1.getBottom() + r1.getHeight();

		    rect1 = new Rectangle(llx - 40, lly - 40, urx + 50, ury - 50);
		    rectPage1 = new Rectangle(rect1);
		}
	    } else {
		column = new ColumnText(stamper.getUnderContent(page));

		rectPage1 = new Rectangle(rect1);
		column.addElement(new Paragraph("Completed Tasks"));
	    }
	    if (rectPage1 != null)
		column.setSimpleColumn(rectPage1);
	    if (table != null)
		column.addElement(table);
	    if (page != 0)
		pagecount = page;
	    rectPage2 = new Rectangle(36, 36, 559, 806);
	    status1 = column.go();

	    while (ColumnText.hasMoreText(status1)) {
		rectPage2 = new Rectangle(36, 36, 559, 806);
		Map<String, Integer> map = triggerNewPage(stamper, pagesize, column, rectPage2, ++pagecount);
		status1 = map.get("column");
		pagecount = map.get("pagecount");
		page = pagecount;
	    }
	    if (ColumnText.hasMoreText(pagecount)) {
		float x = rectPage2.getLeft();
		float x1 = rectPage2.getLeft();
		float r = rectPage2.getRight();
		float r11 = rectPage2.getBottom() + rectPage2.getHeight();

		rect1 = new Rectangle(x, x1, r + 50, r11 + rectPage2.getHeight());
		rectPage1 = new Rectangle(rect1);
	    }

	}

    }

    public Map<String, Integer> triggerNewPage(PdfStamper stamper, Rectangle pagesize, ColumnText column,
	    Rectangle rect, int pagecount) throws DocumentException {
	stamper.insertPage(pagecount, pagesize);
	PdfContentByte canvas = stamper.getOverContent(pagecount);
	column.setCanvas(canvas);
	column.setSimpleColumn(rect);
	Map<String, Integer> map = new HashMap<>();
	map.put("column", column.go());
	map.put("pagecount", pagecount);
	return map;
    }
}
