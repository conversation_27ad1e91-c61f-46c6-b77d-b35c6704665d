package com.bolt.dashboard.util;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.config.MongoAggregate;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.core.model.CodeQualityMetric;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.GoalMetric;
import com.bolt.dashboard.core.model.GoalSetting;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.ProjectCoverageDetails;
import com.bolt.dashboard.core.model.ProjectHealthConfig;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.CodeQualityRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.GoalSettingRep;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.ProjectCoverageDetailsRepo;
import com.bolt.dashboard.core.repository.ProjectIterationRepo;
import com.bolt.dashboard.service.ALMServiceImplementation;

public class ProjectHomeCalculation {

	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
	String successState = "SUCCESS";
	String failedState = "FAILURE";
	List<BuildTool> buildDataSprint = null;
	BuildToolRep buildToolRepository = null;
	List<IterationOutModel> activeIterations = null;
	CommonFunctions commonFunc = new CommonFunctions();
	ProjectIterationRepo iterRepo = null;
	MongoTemplate mongo=null;
	ProjectCoverageDetailsRepo projectCoverageRepo;
	GoalSettingRep goalsRepo;
	private static final Logger LOGGER = LogManager.getLogger(ALMServiceImplementation.class);
	private void getInitialDetailsCommon(String projName, String almType) {
		
		 iterRepo = ctx.getBean(ProjectIterationRepo.class);
		 try {
			 mongo =  DataConfig.getInstance().mongoTemplate();
		 }catch(Exception e){
			 LOGGER.error("Mongo error");
		 }
		 Query query = new Query();
		 query.addCriteria(Criteria.where("pName").is(projName).and("pAlmType").is(almType).and("state").in("active","Active","ACTIVE","ACTIVE"));
		 activeIterations = mongo.find(query, IterationOutModel.class,"Author");
		 
		//activeIterations = iterRepo.findByPNameAndPAlmTypeAndState(projName, almType, "ACTIVE");

	}

	private void getInitialDetailsBuild(String projName) {
		
		ConfigurationSettingRep configRepo = ctx.getBean(ConfigurationSettingRep.class);
		ConfigurationSetting config = configRepo.findByProjectName(projName).get(0);
		String toolName = "JENKINS";
		buildToolRepository = ctx.getBean(BuildToolRep.class);
		if (config != null) {

			for (ConfigurationToolInfoMetric metric : config.getMetrics()) {
				if (metric.getToolType().equals("Build")) {
					toolName = metric.getToolName();
				}
			}
			if (toolName.contains("BITBUCKET")) {
				toolName = "BITBUCKET";
				failedState = "FAILED";
				successState = "SUCCESSFUL";
			}
		}

	}

	public List<Map<String, String>> getBuildDetailsHome(String projName, String almType) {
		

		getInitialDetailsCommon(projName, almType);
		getInitialDetailsBuild(projName);
		int averageBuild = getAverageBuildsPerSprint(projName, almType);
		List<Map<String, String>> buildAllSprintData = new ArrayList<Map<String, String>>();
		DecimalFormat df = new DecimalFormat("#.00");
		for (IterationOutModel iter : activeIterations) {
			Map<String, String> valueSprint = new HashMap<String, String>();
			valueSprint.put("Average Builds", String.valueOf(averageBuild));
			boolean failed = false;
			double buildSuccessRate = 0;
			int successCount = 0;
			int mttrCount = 0;
			double averageBuildTime = 0;
			long startDate = iter.getStDate();
			long endDate = iter.getEndDate();
			long totalDuration = 0;
			long mttrDuration = 0;
			long lastFailureTime = 0;
			boolean lastBuildFailedFlag=false;
			if (iter.getCompletedDate() != 0) {
				endDate = iter.getCompletedDate();
			}

			buildDataSprint = buildToolRepository.findByNameAndTimestampBetween(projName, startDate, endDate);
			// Collections.reverse(buildDataSprint);
			buildDataSprint.sort(Comparator.comparing(BuildTool::getTimestamp));
			for (int index = 0; index < buildDataSprint.size(); index++) {
				BuildTool build = buildDataSprint.get(index);
				for (BuildToolMetric metric : build.getMetrics()) {
					if (metric.getName().equals("result") && metric.getValue().equals(failedState)) {
						if(index==buildDataSprint.size()-1) {
							lastBuildFailedFlag=true;
						}
						if (!failed) {
							lastFailureTime = build.getTimestamp();
							failed = true;
						}

					} else if (metric.getName().equals("result") && metric.getValue().equals(successState)) {

						successCount++;
						if (failed) {
							failed = false;
							mttrDuration = mttrDuration + (build.getTimestamp() - lastFailureTime);
							mttrCount++;
						}

					} else if (metric.getName().equals("duration")) {
						totalDuration = totalDuration + Long.parseLong(metric.getValue().toString());
					}

				}

			}
			String mttr = "0m";
			if (mttrCount > 0) {
				mttrDuration = mttrDuration / mttrCount;
				mttr = commonFunc.convertToDisplayValues(mttrDuration, 24);
			}

			if (!buildDataSprint.isEmpty()) {

				if (totalDuration > 0) {
					averageBuildTime = totalDuration / 1000f;
					averageBuildTime = averageBuildTime / buildDataSprint.size();
				}
				if (successCount > 0) {
					buildSuccessRate = (successCount * 100) / (float)buildDataSprint.size();
				}
			}
			String averageBuildTimeFormat="0.00sec";
			if(averageBuildTime>0.0) {
				averageBuildTimeFormat=df.format(averageBuildTime) + "sec";
			}
			valueSprint.put("Average Build Time", averageBuildTimeFormat);
			valueSprint.put("MTTR", mttr);
			valueSprint.put("Build Success Rate", String.valueOf(buildSuccessRate));
			valueSprint.put("Last Build Failed", String.valueOf(lastBuildFailedFlag));
			valueSprint.put("Sprint Name", iter.getsName());
			valueSprint.put("Start Date", String.valueOf(startDate));
			valueSprint.put("End Date", String.valueOf(endDate));
			
			buildAllSprintData.add(valueSprint);

		}

		return buildAllSprintData;

	}

	private int getAverageBuildsPerSprint(String projName, String almType) {
		

		MongoAggregate aggr = new MongoAggregate();
		long buildCount = aggr.getBuildsCount(projName);
		long sprintCount = aggr.getTotalSprintCount(projName, almType);
		return (int) (buildCount / sprintCount);
	}

	public List<Map<String, List<String>>> getSprintProgressData(String projName, String almType) {
		
		List<Map<String, List<String>>> sprintProgressDataList = new ArrayList<Map<String, List<String>>>();
		getInitialDetailsCommon(projName, almType);

		for (IterationOutModel iter : activeIterations) {
			long endDate = iter.getEndDate();
			long startDate = iter.getStDate();
			if (iter.getCompletedDate() != 0) {
				endDate = iter.getCompletedDate();
			}
			Map<String, List<String>> sprintProgressData = new HashMap<String, List<String>>();
			List<String> sprintDetails = new ArrayList<String>();
			sprintDetails.add(iter.getsName());
			sprintDetails.add(String.valueOf(startDate));
			sprintDetails.add(String.valueOf(endDate));
			sprintProgressData.put("Sprint Details", sprintDetails);
			List<String> teamSize = new ArrayList<>();
			if (iter.getTeamSize() != null) {
				teamSize.add(iter.getTeamSize().toString());
			} else {
				teamSize.add("0");
			}

			sprintProgressData.put("Team Size", teamSize);
			List<MonogOutMetrics> metricData = iter.getMetrics();
			List<String> types = new ArrayList<String>();
			if (metricData != null) {

				Map<String, List<MonogOutMetrics>> typeGroupedMetrics = metricData.stream()
						.collect(Collectors.groupingBy(w -> w.getType()));

				typeGroupedMetrics.entrySet().forEach(val -> {
					types.add(String.valueOf(val.getKey()));
				});

				Map<String, List<MonogOutMetrics>> stateGroupedMetrics = iter.getMetrics().stream()
						.collect(Collectors.groupingBy(w -> w.getState()));

				for (Map.Entry<String, List<MonogOutMetrics>> stateEntry : stateGroupedMetrics.entrySet()) {
					sprintProgressData.put(stateEntry.getKey(), new ArrayList<String>(types.size()));
					String key = stateEntry.getKey();
					Map<String, List<MonogOutMetrics>> typeGrouping = stateEntry.getValue().stream()
							.collect(Collectors.groupingBy(w -> w.getType()));

					for (Map.Entry<String, List<MonogOutMetrics>> typeEntry : typeGrouping.entrySet()) {
						// sprintProgressData.put(stateEntry.getKey());
						int index = types.indexOf(typeEntry.getKey());
						List<String> values = sprintProgressData.get(key);
						if (values.size() < (index + 1)) {
							for (int i = values.size() - 1; i < types.size() - 1; i++) {
								values.add("0");
							}
						}
						sprintProgressData.get(key).add(index, String.valueOf(typeEntry.getValue().size()));

					}

				}
			}
			sprintProgressData.put("Status/Type", types);
			sprintProgressDataList.add(sprintProgressData);
		}
		
		return sprintProgressDataList;
	}

	public List<Map<String, String>> getDefectsSummaryData(String projName, String almType,
			ALMConfiguration almConfig) {
		
		List<Map<String, String>> defectsSummaryDataList= new ArrayList<Map<String,String>>();
		getInitialDetailsCommon(projName, almType);
		List<String> criticalPriority= Arrays.asList(almConfig.getCriticalPriority());        
		List<String> closeState = Arrays.asList(almConfig.getCloseState());
	    MetricRepo metricRepo= ctx.getBean(MetricRepo.class);
		List<MetricsModel> allBugs=metricRepo.findByPNameAndType(projName, almConfig.getDefectName());
		
		List<MetricsModel> allBugsOpen = allBugs.stream()
				.filter(metric ->!(closeState.indexOf(metric.getState())>-1)).collect(Collectors.toList());
		
		Map<String, String> allBugsData = new HashMap<String, String>();
		allBugsData.put("Total Open Defects", String.valueOf(allBugsOpen.size()));
		
		Map<String, List<MetricsModel>> priorityGroupedBugs =
				allBugsOpen.stream().collect(Collectors.groupingBy(w -> w.getPriority()));

		for (Map.Entry<String, List<MetricsModel>> bugEntry : priorityGroupedBugs.entrySet()) {
			
			allBugsData.put(bugEntry.getKey(), String.valueOf(bugEntry.getValue().size()));
			
		}
		
		
		
		
		for (IterationOutModel iter : activeIterations) {
			Map<String, String> defectDataSprint = new HashMap<String, String>();
			long endDate=iter.getEndDate();
            long startDate= iter.getStDate();
            int totalBugs=0;
			int closedBugs= 0;
            boolean criticalBugFlag=false;
			if (iter.getCompletedDate() != 0) {
				endDate = iter.getCompletedDate();
			}
			
			
			defectDataSprint.putAll(allBugsData);
			List<MonogOutMetrics> metricData=iter.getMetrics();
			if(metricData!=null) {
			List<MonogOutMetrics> filteredMetricsBugs = metricData.stream()
					.filter(metric ->metric.getType().equals(almConfig.getDefectName())).collect(Collectors.toList());
            
			
			List<MonogOutMetrics> filteredCriticalBugs = filteredMetricsBugs.stream()
					.filter(metric ->criticalPriority.indexOf(metric.getPriority())>-1 && !(closeState.indexOf(metric.getState())>-1)).collect(Collectors.toList());
			 criticalBugFlag=false;
			if(filteredCriticalBugs!=null && filteredCriticalBugs.size()>0) {
				criticalBugFlag=true;
			}

			List<MonogOutMetrics> closedBugsData = filteredMetricsBugs.stream()
					.filter(metric ->(closeState.indexOf(metric.getState())>-1)).collect(Collectors.toList());
			
			 totalBugs=filteredMetricsBugs.size();
			 closedBugs= closedBugsData.size();
			}
			defectDataSprint.put("Total Bugs", String.valueOf(totalBugs));
			defectDataSprint.put("Closed Bugs", String.valueOf(closedBugs));
			defectDataSprint.put("Sprint Name", iter.getsName());
			defectDataSprint.put("Start Date", String.valueOf(startDate));
			defectDataSprint.put("End Date", String.valueOf(endDate));
			defectDataSprint.put("Critical Bugs Flag", String.valueOf(criticalBugFlag));
			defectsSummaryDataList.add(defectDataSprint);
		
		}
		return defectsSummaryDataList;
	}

	public List<Map<String, String>> getCodeQualityData(String projName, String almType) {
		
		List<Map<String, String>> codeQualityData= new ArrayList<Map<String,String>>();
		getInitialDetailsCommon(projName, almType);
		CodeQualityRep codeQualityRepo= ctx.getBean(CodeQualityRep.class);
		GoalSettingRep goalSettingRep = ctx.getBean(GoalSettingRep.class);
		GoalSetting goalSetting = goalSettingRep.findFirstByProjectNameAndNameOrderByTimestampDesc(projName,"CodeQuality");
		GoalMetric goalMet=goalSetting.getMetrics().stream()
		.filter(met ->met.getName().equals("sqale_index")).collect(Collectors.toList()).get(0);
	     long goalValTechDebt = Long.parseLong(goalMet.getGoal());
		for (IterationOutModel iter : activeIterations) {
			Map<String, String> codeQualitySprint = new HashMap<String, String>();
			long endDate=iter.getEndDate();
            long startDate= iter.getStDate();
			if (iter.getCompletedDate() != 0) {
				endDate = iter.getCompletedDate();
			}
			codeQualitySprint.put("Sprint Name", iter.getsName());
			codeQualitySprint.put("Start Date", String.valueOf(startDate));
			codeQualitySprint.put("End Date", String.valueOf(endDate));
			List<CodeQuality> codeQualityRecords=(List<CodeQuality>) codeQualityRepo.findByName(projName);
			
			double ncloc=0;
			double blocker=0;
			double critical=0;
			double techdebt=0.0;
			                  Map<String,List<CodeQuality>> codeQualityGroupByRepo=
			                		  codeQualityRecords.stream().collect(Collectors.groupingBy(CodeQuality::getVersion));
			                  for (Map.Entry<String, List<CodeQuality>> entry : codeQualityGroupByRepo.entrySet()) {
			                	  List<CodeQuality> sortedList = entry.getValue().stream()
			                		        .sorted(Comparator.comparingLong(CodeQuality::getTimestamp))
			                		        .collect(Collectors.toList());
			                	  CodeQuality selected =sortedList.get(sortedList.size()-1);
			                	  CodeQualityMetric metric= selected.getMetrics().stream()
			          					.filter(met ->met.getName().equals("ncloc")).collect(Collectors.toList()).get(0);
			                	  ncloc=ncloc+Double.parseDouble(metric.getValue().toString());
			                	  metric= selected.getMetrics().stream()
			          					.filter(met ->met.getName().equals("blocker_violations")).collect(Collectors.toList()).get(0);
			                	  blocker=blocker+Double.parseDouble(metric.getValue().toString());
			                	  metric= selected.getMetrics().stream()
			          					.filter(met ->met.getName().equals("critical_violations")).collect(Collectors.toList()).get(0);
			                	  critical=critical+Double.parseDouble(metric.getValue().toString());
			                		metric= selected.getMetrics().stream()
			            					.filter(met ->met.getName().equals("sqale_index")).collect(Collectors.toList()).get(0);
			                		techdebt=techdebt+Double.parseDouble(metric.getValue().toString());
			              		System.out.println("Key : " + entry.getKey());
			              	}
			//CodeQuality codeQualityRecord= codeQualityRepo.findFirstByNameOrderByTimestampDesc(projName);
			
			
//			CodeQualityMetric metric= codeQualityRecord.getMetrics().stream()
//					.filter(met ->met.getName().equals("ncloc")).collect(Collectors.toList()).get(0);
			codeQualitySprint.put("Lines of Code", String.valueOf(ncloc));
			
//			metric= codeQualityRecord.getMetrics().stream()
//					.filter(met ->met.getName().equals("blocker_violations")).collect(Collectors.toList()).get(0);
			codeQualitySprint.put("Blockers", String.valueOf(blocker));
//			metric= codeQualityRecord.getMetrics().stream()
//					.filter(met ->met.getName().equals("critical_violations")).collect(Collectors.toList()).get(0);
			codeQualitySprint.put("Critical", String.valueOf(critical));
			
//			metric= codeQualityRecord.getMetrics().stream()
//					.filter(met ->met.getName().equals("sqale_index")).collect(Collectors.toList()).get(0);
//			double minutes= Double.parseDouble(metric.getValue().toString());
			double hours=techdebt/60;
			String techDebt= String.valueOf(((long)hours/8))+"PD";
			codeQualitySprint.put("Technical Debt", techDebt);
			String techDebtBetter="true";
			if(goalValTechDebt< techdebt) {
			techDebtBetter ="false";	
			}
			codeQualitySprint.put("Code Quality Flag", techDebtBetter);
			codeQualityData.add(codeQualitySprint);
		}
		
		return codeQualityData;
	}

	
	public List<Map<String, String>> coverageSummary(String pName,String almType) {
		getInitialDetailsCommon(pName, almType);
		projectCoverageRepo = (ctx).getBean(ProjectCoverageDetailsRepo.class);
		goalsRepo = (ctx).getBean(GoalSettingRep.class);
		double totalCoverage = 0 ;
		int totalRepos = 0;
		List<Map<String, String>> response = new ArrayList<Map<String,String>>();
		List<ProjectCoverageDetails> allRepoCoverage = projectCoverageRepo.findByPName(pName);
		Iterable<GoalSetting> goals =  goalsRepo.findByProjectName(pName);
		List<ProjectHealthConfig> coverageGoals = getCoverageGoal(goals,pName);
		
		allRepoCoverage.sort(Comparator.comparing(ProjectCoverageDetails::getCreated_timestamp));
		Map<String,List<ProjectCoverageDetails>> groupdedByRepo = allRepoCoverage.stream().collect(Collectors.groupingBy(ProjectCoverageDetails::getRepoName));
		
		
		for(Entry<String, List<ProjectCoverageDetails>> repo:groupdedByRepo.entrySet()) {
			List<ProjectCoverageDetails> repoData = repo.getValue();
			ProjectCoverageDetails lastValue = repoData.get(repoData.size()-1);
			totalCoverage +=lastValue.getCovered_statements_percentage();
			totalRepos++;
		}
		
		for (IterationOutModel iter : activeIterations) {
			Map coverageSummary = new HashMap<String,String>();
			long endDate=iter.getEndDate();
            long startDate= iter.getStDate();
			if (iter.getCompletedDate() != 0) {
				endDate = iter.getCompletedDate();
			}
			double overallCoverage = totalCoverage/totalRepos;
			coverageSummary.put("Sprint Name", iter.getsName());
			coverageSummary.put("Start Date", String.valueOf(startDate));
			coverageSummary.put("End Date", String.valueOf(endDate));
			coverageSummary.put("Overall Coverage", String.valueOf(overallCoverage));
			
			
			for(ProjectHealthConfig config:coverageGoals) {
				int from = config.getFrom();
				int to = config.getTo();
				String regName = config.getRagName();
				if(from <= overallCoverage && overallCoverage <= to ) {
					coverageSummary.put("RegName", regName);
				}
				
			}
			
			response.add(coverageSummary);
		}
		return response;
	}
	
	public List<ProjectHealthConfig> getCoverageGoal(Iterable<GoalSetting> goalsItr,String pName) {
		List<GoalSetting> goals = new ArrayList<GoalSetting>();
		for(GoalSetting g:goalsItr) {
			if(g.getName().equalsIgnoreCase("Code Coverage")) {
				goals.add(g);
			}
		}
		if(!goals.isEmpty()) {
			goals.sort(Comparator.comparing(GoalSetting::getTimestamp));
			return goals.get(goals.size()-1).getConfig();
		}else {
			List<ProjectHealthConfig> defaultGoals = new ArrayList<ProjectHealthConfig>();
			ProjectHealthConfig config = new ProjectHealthConfig();
			config.setFrom(0);
			config.setTo(29);
			config.setProjectName(pName);
			config.setRagName("Red");
			defaultGoals.add(config);
			
			config = new ProjectHealthConfig();
			config.setFrom(30);
			config.setTo(80);
			config.setRagName("Amber");
			config.setProjectName(pName);
			defaultGoals.add(config);
			
			config = new ProjectHealthConfig();
			config.setFrom(81);
			config.setTo(100);
			config.setRagName("Green");
			config.setProjectName(pName);
			defaultGoals.add(config);
			return defaultGoals;
		}
	
	}
}
