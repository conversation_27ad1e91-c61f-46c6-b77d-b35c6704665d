package com.bolt.dashboard.bitbucket_collector;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;


import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.bolt.dashboard.bitbucket_collector.BitBucketClientImplementation;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.SCMToolRepository;

public class BitBucketApplication {


	 final static Logger lOG = Logger.getLogger(BitBucketApplication.class);

	/**
	 * Private Constructor
	 */
	private AnnotationConfigApplicationContext ctx=null
	public BitBucketApplication() {
		ctx=DataConfig.getContext();
		}
	
	
	public void bitBucketMain(String projectName)  {
	//	String projectName="BOLT_Dashboard";
		//AnnotationConfigApplicationContext ctx = DataConfig.getContext();
		SCMToolRepository repo = ctx.getBean(SCMToolRepository.class);
		String instanceURL = "";
		String branch = "";
		String user = null;
		String pass = null;
		String getFirstRunHistoryDays = null;
		boolean firstRun = false;		
		BitBucketClientImplementation scmToolMetricsimpl = new BitBucketClientImplementation();
		ConfigurationSettingRep configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		ConfigurationSetting configurationColection = configurationRepo.findByProjectName(projectName);

		Collection<ConfigurationSetting> list = new ArrayList<ConfigurationSetting>();
		Set<ConfigurationToolInfoMetric> metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOG.info("Tool name  " + metric1.getToolName());
			if ("BIT Bucket Version Control Server".equals(metric1.getToolName())) {
				LOG.info("URL  " + metric1.getUrl());
				instanceURL = metric1.getUrl();
				user = metric1.getUserName();
				pass = metric1.getPassword();
				break;
			}

		}


		try {
			File fXmlFile = new File("src/main/java/com/bolt/dashboard/bitbucket_collector/BitBucketMetrics.xml");
			DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
			DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
			Document doc = dBuilder.parse(fXmlFile);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("Tool");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;

					LOG.info("Instance URL is as :" + instanceURL);
					getFirstRunHistoryDays = eElement.getElementsByTagName("getFirstRunHistoryDays").item(0)
							.getTextContent();
					branch = eElement.getElementsByTagName("branch").item(0).getTextContent();

					firstRun = eElement.getElementsByTagName("firstRun").item(0).getTextContent() != null;
					LOG.info("GetfirstRunHistoryDays value is:  " + getFirstRunHistoryDays);
					LOG.info("Branch values is:  " + branch);
					LOG.info("value of  Run is :" + firstRun);
				}
			}
			List<SCMTool> scmTool = scmToolMetricsimpl.getCommits(instanceURL, repo, firstRun, branch,
					getFirstRunHistoryDays, user, pass);
			repo.save(scmTool);
			//////catx.close();
		} catch (Exception e) {

			LOG.error(e.getMessage());
			LOG.info(e.getStackTrace());
			LOG.info(e);
		}
	}
}
