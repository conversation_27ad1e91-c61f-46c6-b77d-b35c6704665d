package com.bolt.dashboard.bitbucket_collector;

import java.util.List;

import com.bolt.dashboard.bitbucket_collector.BitBucketExceptions;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.SCMToolRepository;

public interface BitBucketClient {
	 public List<SCMTool> getCommits(String url, SCMToolRepository repo, boolean firstRun, String branch,
	            String getFirstRunHistoryDays,String user, String pass) throws BitBucketExceptions;

}