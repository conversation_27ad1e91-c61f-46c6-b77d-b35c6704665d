package com.bolt.dashboard.core.model;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "TEST")
public class TestTool extends BaseModel {
    private ObjectId collectorItemId;
    private long timestamp;
    private String name;
    private String url;
    private String fullDisplayName;
    private long duration;
    private long estimatedDuration;
   
    /**
     * @return the estimatedDuration
     */
    public long getEstimatedDuration() {
        return estimatedDuration;
    }

    /**
     * @param estimatedDuration the estimatedDuration to set
     */
    public void setEstimatedDuration(long estimatedDuration) {
        this.estimatedDuration = estimatedDuration;
    }

    private int failCount;
    private int skipcount;
    private int totalcount;
    private long testTimestamp;
    private String result;
    private String affectedPaths;
    private String author;
    /**
     * @return the collectorItemId
     */
    public ObjectId getCollectorItemId() {
        return collectorItemId;
    }

    /**
     * @param collectorItemId
     *            the collectorItemId to set
     */
    public void setCollectorItemId(ObjectId collectorItemId) {
        this.collectorItemId = collectorItemId;
    }

    /**
     * @return the timestamp
     */
    public long getTimestamp() {
        return timestamp;
    }

    /**
     * @param timestamp
     *            the timestamp to set
     */
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    /**
     * @return the name
     */
    public String getName() {
        return name;
    }

    /**
     * @param name
     *            the name to set
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * @return the url
     */
    public String getUrl() {
        return url;
    }

    /**
     * @param url
     *            the url to set
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * @return the fullDisplayName
     */
    public String getFullDisplayName() {
        return fullDisplayName;
    }

    /**
     * @param fullDisplayName
     *            the fullDisplayName to set
     */
    public void setFullDisplayName(String fullDisplayName) {
        this.fullDisplayName = fullDisplayName;
    }

    /**
     * @return the duration
     */
    public long getDuration() {
        return duration;
    }

    /**
     * @param duration
     *            the duration to set
     */
    public void setDuration(long duration) {
        this.duration = duration;
    }

    /**
     * @return the failCount
     */
    public int getFailCount() {
        return failCount;
    }

    /**
     * @param failCount
     *            the failCount to set
     */
    public void setFailCount(int failCount) {
        this.failCount = failCount;
    }

    /**
     * @return the skipcount
     */
    public int getSkipcount() {
        return skipcount;
    }

    /**
     * @param skipcount
     *            the skipcount to set
     */
    public void setSkipcount(int skipcount) {
        this.skipcount = skipcount;
    }

    /**
     * @return the totalcount
     */
    public int getTotalcount() {
        return totalcount;
    }

    /**
     * @param totalcount
     *            the totalcount to set
     */
    public void setTotalcount(int totalcount) {
        this.totalcount = totalcount;
    }

    /**
     * @return the testTimestamp
     */
    public long getTestTimestamp() {
        return testTimestamp;
    }

    /**
     * @param testTimestamp
     *            the testTimestamp to set
     */
    public void setTestTimestamp(long testTimestamp) {
        this.testTimestamp = testTimestamp;
    }

    /**
     * @return the result
     */
    public String getResult() {
        return result;
    }

    /**
     * @param result
     *            the result to set
     */
    public void setResult(String result) {
        this.result = result;
    }

    /**
     * @return the affectedPaths
     */
    public String getAffectedPaths() {
        return affectedPaths;
    }

    /**
     * @param affectedPaths
     *            the affectedPaths to set
     */
    public void setAffectedPaths(String affectedPaths) {
        this.affectedPaths = affectedPaths;
    }

    /**
     * @return the author
     */
    public String getAuthor() {
        return author;
    }

    /**
     * @param author
     *            the author to set
     */
    public void setAuthor(String author) {
        this.author = author;
    }

   

}
