package com.bolt.dashboard.jira;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.Set;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;


import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.bolt.dashboard.jira.JIRAClient;
import com.bolt.dashboard.jira.JIRAClientImplementation;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.repository.ALMRepository;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;

/**
 * <AUTHOR>
 *
 */
public class JIRAApplication {
	private static fina.getLog(JIRAApplication.class);

	/**
	 * Private constructor
	 */
	private AnnotationConfigApplicationContext ctx=null
	public JIRAApplication() {
		ctx=DataConfig.getContext();
	}
//public static void main(String[] args) {
	

	public void jiraMain(String projectName){
		LOG.info("JIRA started");
		//AnnotationConfigApplicationContext ctx = DataConfig.getContext();
        ALMRepository repo = ctx.getBean(ALMRepository.class);
        String instanceURL = "";
        JIRAClient almClientMetrics = new JIRAClientImplementation();
        String user = null;
        String pass = null;
        ConfigurationSettingRep configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
       
		ConfigurationSetting configurationColection = configurationRepo.findByProjectName(projectName);

		Collection<ConfigurationSetting> list = new ArrayList<ConfigurationSetting>();
		Set<ConfigurationToolInfoMetric> metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOG.info("Tool name  " + metric1.getToolName());
			if ("Jira".equals(metric1.getToolName())) {
				LOG.info("URL  " + metric1.getUrl());
				instanceURL = metric1.getUrl();
				user = metric1.getUserName();
				pass = metric1.getPassword();
				break;
			}

		}
        try {
            File fXmlFile = new File("src/main/java/com/bolt/dashboard/jira/JiraPoc.xml");
            DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
            Document doc = dBuilder.parse(fXmlFile);
            doc.getDocumentElement().normalize();
            NodeList nList = doc.getElementsByTagName("Tool");
            for (int temp = 0; temp < nList.getLength(); temp++) {
                Node nNode = nList.item(temp);
                if (nNode.getNodeType() == Node.ELEMENT_NODE) {
                    Element eElement = (Element) nNode;
                    instanceURL = instanceURL+ "search";
                    LOG.info("This is what we have done" + instanceURL);
                }
            }
            almClientMetrics.getALMTool(instanceURL, repo,user,pass);
            ////////catx.close();
        } catch (Exception e) {
            LOG.error(e.getMessage());
            LOG.info(e.getStackTrace());
            LOG.info(e);
        }
	}

}

