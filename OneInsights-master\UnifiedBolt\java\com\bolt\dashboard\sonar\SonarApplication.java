package com.bolt.dashboard.sonar;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import org.json.simple.parser.ParseException;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.web.client.RestClientException;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.repository.CodeQualityRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;

/**
 * Application configuration and bootstrap
 */
@SpringBootApplication
public class SonarApplication {
	
	private final static Logger LOG = Logger.getLogger(SonarApplication.class);
	
	/* public static void main(String[] args) {
	 
	  
	  AnnotationConfigApplicationContext ctx = new
	 AnnotationConfigApplicationContext(DataConfig.class);
	  
	  CodeQualityRep repo = ctx.getBean(CodeQualityRep.class); String
	 instanceURL = ""; String user = null; String pass = null; LOG.info(
	 "Start of sonar Main");
	  
	 SonarClient sonar = new SonarClientImplementation();
	  ConfigurationSettingRep configurationRepo =
	 ctx.getBean(ConfigurationSettingRep.class);
	  Iterable<ConfigurationSetting> configurationColection =
	  configurationRepo.findAll();
	  
	  Collection<ConfigurationSetting> list = new
	  ArrayList<ConfigurationSetting>();
	  for (ConfigurationSetting item : configurationColection) {
		  Set<ConfigurationToolInfoMetric> metric = item.getMetrics(); Iterator iter = metric.iterator(); while
	  (iter.hasNext()) { Object configuration1 = iter.next();
	  ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric)
	  configuration1; LOG.info("Tool name  " + metric1.getToolName()); if
	  ("Sonar".equals(metric1.getToolName())) { LOG.info("URL  " +
	  metric1.getUrl()); instanceURL = metric1.getUrl(); user =
	  metric1.getUserName(); pass = metric1.getPassword(); break; }
	  
	  } }
	  
	  try {
	  
	  File fXmlFile = new
	  File("src/main/java/com/bolt/dashboard/sonar/CodeQualityMetrics.xml");
	  DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
	  DocumentBuilder dBuilder = dbFactory.newDocumentBuilder(); Document doc =
	  dBuilder.parse(fXmlFile);
	  
	  doc.getDocumentElement().normalize();
	  
	  NodeList nList = doc.getElementsByTagName("Tool");
	  
	  for (int temp = 0; temp < nList.getLength(); temp++) {
	 
	  Node nNode = nList.item(temp);
	  
	  if (nNode.getNodeType() == Node.ELEMENT_NODE) {
	  
	  Element eElement = (Element) nNode; instanceURL = instanceURL+
	  "/api/resources?format=json"+"&resource="+eElement.getElementsByTagName(
	  "ProjectName").item(0).getTextContent();
	  
	  
	  instanceURL = instanceURL + "&metrics=" +
	  eElement.getElementsByTagName("SnapshotMetric").item(0).getTextContent();
	  
	  
	  } }
	  
	 List<CodeQuality> cq = sonar.getCodeQuality(instanceURL, user, pass);
	  
	  repo.save(cq); //////catx.close(); LOG.info("End of sonar main"); } catch
	  (Exception e) { LOG.error(e); }
	  
	  }
	 */

	/**
	 * This the replica of Main function written in this file. It was written in
	 * case this function has to be called from outside the application.
	 */
	//public static void main(String[] args) {
		
	
public void sonarMain(String projectName) {
		AnnotationConfigApplicationContext ctx = DataConfig.getContext();

		CodeQualityRep repo = ctx.getBean(CodeQualityRep.class);
		String instanceURL = "";
		String user = null;
		String pass = null;
		LOG.info("Start of sonar Main");

		SonarClient sonar = new SonarClientImplementation();
		ConfigurationSettingRep configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		ConfigurationSetting configurationColection = configurationRepo.findByProjectName(projectName);

		Collection<ConfigurationSetting> list = new ArrayList<ConfigurationSetting>();
		Set<ConfigurationToolInfoMetric> metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOG.info("Tool name  " + metric1.getToolName());
			if ("Sonar".equals(metric1.getToolName())) {
				LOG.info("URL  " + metric1.getUrl());
				instanceURL = metric1.getUrl();
				user = metric1.getUserName();
				pass = metric1.getPassword();
				break;
			}

		}
		 String projectCode="";
		 String[] projectSeparationString = null;
		 String[] projectCodeSeparationString = null;
	        if (instanceURL.contains("/")) {
	            projectSeparationString = instanceURL.split(Pattern.quote("/"));
	             projectCode=projectSeparationString[projectSeparationString.length-1]; 
	           
	            if (projectCode.contains("id")) {
	            	projectCodeSeparationString=projectCode.split(Pattern.quote("="));
	            	projectCode= 	projectCodeSeparationString[projectCodeSeparationString.length-1];
	            	
	            			 instanceURL=instanceURL.replace("overview?id=", "api/resources?format=json&resource=");
	            			LOG.info("Sonar URL",instanceURL );
				}else {
					instanceURL=instanceURL.replace("dashboard/index/", "api/resources?format=json&resource=");
					LOG.info("Sonar URL",instanceURL );
				}
	        }
	            
	       
	            
	        
	      
		
		
		try {

			File fXmlFile = new File("src/main/java/com/bolt/dashboard/sonar/CodeQualityMetrics.xml");
			DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
			DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
			Document doc = dBuilder.parse(fXmlFile);

			doc.getDocumentElement().normalize();

			NodeList nList = doc.getElementsByTagName("Tool");

			for (int temp = 0; temp < nList.getLength(); temp++) {

				Node nNode = nList.item(temp);

				if (nNode.getNodeType() == Node.ELEMENT_NODE) {

					Element eElement = (Element) nNode;
					

					instanceURL = instanceURL + "&metrics="
							+ eElement.getElementsByTagName("SnapshotMetric").item(0).getTextContent();

				}
			}

			List<CodeQuality> cq = sonar.getCodeQuality(instanceURL, user, pass);

			repo.save(cq);
			//////catx.close();
			LOG.info("End of sonar main");
		} catch (Exception e) {
			LOG.error(e);
		}
	}

	public void xThyread(String projectName) {
		Thread t1 = new Thread(projectName);
	}

}