package com.bolt.dashboard.CodeClimate;

import java.util.Iterator;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.repository.CodeQualityRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class CodeClimateApplication {

	private static final Logger LOG = LogManager.getLogger(CodeClimateApplication.class);
	
	AnnotationConfigApplicationContext ctx = null;
	CodeQualityRep repo = null;
	ConfigurationSettingRep configurationRepo = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	Iterator iter = null;
	CodeClimateClientImplementation codeClimateClientImplementation = null;
	ConfigurationSetting configuration = null;
	ConfigurationToolInfoMetric metric1 = null;

	/**
	 * Private Constructor
	 */
	public CodeClimateApplication() {

	}

	public void codeClimateMain(String projectName) {
		LOG.info("Git Hub Collector started for " + projectName);
		ctx = DataConfig.getContext();
		repo = ctx.getBean(CodeQualityRep.class);
		String instanceURL = "";
		String apiToken = null;
		String username = null, password = null;
		codeClimateClientImplementation = new CodeClimateClientImplementation();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configuration = configurationRepo.findByProjectName(projectName).get(0);
		metric = configuration.getMetrics();
		iter = metric.iterator();
		LOG.info("Project name  " + configuration.getProjectName());
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOG.info("Tool name  " + metric1.getToolName());
			if ("CodeClimate".equals(metric1.getToolName())) {
				LOG.info("URL  " + metric1.getUrl());
				instanceURL = metric1.getUrl();
				username = metric1.getUserName();
				
					password=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
			
				apiToken = metric1.getProjectCode();
				break;
			}

		}

		try {
			codeClimateClientImplementation.getData(instanceURL, apiToken, projectName, repo);
			cleanObject();
			LOG.info("Git Hub Collector ended for " + projectName);
		} catch (Exception e) {
			LOG.info(e.getMessage());
			cleanObject();
		}

	}

	public void cleanObject() {
		repo = null;
		configurationRepo = null;
		metric = null;
		iter = null;
		codeClimateClientImplementation = null;
		configuration = null;
		metric1 = null;

	}
}