package com.bolt.dashboard.CodeClimate;

import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.regex.Pattern;

import javax.ws.rs.core.MediaType;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.springframework.http.HttpHeaders;

import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.core.model.CodeQualityMetric;
import com.bolt.dashboard.core.model.FileRating;
import com.bolt.dashboard.core.repository.CodeQualityRep;
import com.bolt.dashboard.sonar.SonarApplication;
import com.sun.jersey.api.client.Client;
import com.sun.jersey.api.client.WebResource;
import com.sun.jersey.api.client.config.DefaultClientConfig;

public class CodeClimateClientImplementation implements CodeClimateClient {

	 private static final Logger LOG = LogManager.getLogger(CodeClimateClientImplementation.class);
	CodeQuality codeQuality = null;
	Set<FileRating> fileRatingSet = new LinkedHashSet<>();
	Map<String, Integer> issueCountMap = new LinkedHashMap<>();

	@Override
	public CodeQuality getData(String url, String apiToken, String projectName, CodeQualityRep repo) {
	  LOG.info("Code Climate Url   " + url);
		codeQuality = new CodeQuality();
		codeQuality.setTimestamp(new Date().getTime());
		codeQuality.setName(projectName);
		String[] xPathArray = url.split(Pattern.quote("/"));
		String host = xPathArray[0];
		String protocol = xPathArray[2] + "/api";
		String repository = xPathArray[4];
		String instanceUrl = host + "//" + protocol + "/v1/repos";
		String repoUrl = instanceUrl + "/" + repository;

		JSONObject repoJson = makeRestCall(repoUrl, apiToken);
	 JSONObject snapshotJson=	makeRestCall(repoUrl+"/ref_points", apiToken);
	JSONObject snapshot=(JSONObject) ((JSONObject)((JSONObject)((JSONArray) snapshotJson.get("data")).get(0)).get("relationships")).get("snapshot");
	String snapshotId=((JSONObject)	snapshot.get("data")).get("id").toString();
	String[] repoArray = getRepoId(repoJson);
		String repoId = repoArray[0];
		String gpa = repoArray[1];
		CodeQualityMetric metricNew = new CodeQualityMetric("gpa");
		metricNew.setValue(gpa);
		metricNew.setFormattedValue(gpa);
		codeQuality.getMetrics().add(metricNew);

		String issueUrl = instanceUrl + "/" + repoId + "/snapshots/" + snapshotId + "/issues";
		JSONObject issueJson = makeRestCall(issueUrl, apiToken);

		String nextPageUrl = ((JSONObject) issueJson.get("links")).get("next").toString();
		int issueCount = Integer.parseInt(((JSONObject) issueJson.get("meta")).get("total_count").toString());

		if (!(issueJson == null))
			getIssueDetails(issueJson);
		while (!(nextPageUrl == null)) {

			LOG.info("issues url  " + nextPageUrl);
			JSONObject issueJsonObj = makeRestCall(nextPageUrl, apiToken);
			if (issueJsonObj == null) {
				nextPageUrl = null;
				continue;
			} else {
				nextPageUrl = ((JSONObject) issueJsonObj.get("links")).get("next").toString();
				getIssueDetails(issueJsonObj);
			}

		}
		Set set = (Set) issueCountMap.entrySet();
		Iterator it = set.iterator();
		while (it.hasNext()) {
			Map.Entry entry = (Entry) it.next();
			CodeQualityMetric totalFileMetric = new CodeQualityMetric(entry.getKey().toString());
			totalFileMetric.setValue(entry.getValue());
			int totalFile = fileRatingSet.size();
			totalFileMetric.setFormattedValue(String.valueOf(entry.getValue()));
			codeQuality.getMetrics().add(totalFileMetric);

		}
		CodeQualityMetric totalIssueMetric = new CodeQualityMetric("totalIssue");
		totalIssueMetric.setValue(issueCount);
		totalIssueMetric.setFormattedValue(String.valueOf(issueCount));
		codeQuality.getMetrics().add(totalIssueMetric);
		String fileUrl = instanceUrl + "/" + repoId + "/snapshots/" + snapshotId + "/files?page%5Bsize%5D=100";
		JSONObject fileJson = makeRestCall(fileUrl, apiToken);
		int[] pageCount = getPageCount(fileJson);
		getFileInfo(fileJson, pageCount, fileUrl, apiToken);
		CodeQualityMetric totalFileMetric = new CodeQualityMetric("totalfile");
		totalFileMetric.setValue(fileRatingSet.size());
		totalFileMetric.setFormattedValue(String.valueOf(fileRatingSet.size()));
		codeQuality.getMetrics().add(totalFileMetric);
		repo.save(codeQuality);
		return codeQuality;

	}

	public JSONObject makeRestCall(String url, String apiToken) {
		try {
			WebResource resource = Client.create(new DefaultClientConfig()).resource(url);

			WebResource.Builder builder = resource.accept(MediaType.APPLICATION_JSON);
			builder.type(MediaType.APPLICATION_JSON);
			builder.header(HttpHeaders.AUTHORIZATION, "Token token=" + apiToken);

			String something = builder.get(String.class);
			JSONParser parser = new JSONParser();
			JSONObject json = (JSONObject) parser.parse(something);
			return json;
		} catch (Exception e) {
			LOG.info(e.getMessage());

			return null;
		}

	}

	public String[] getRepoId(JSONObject repoJson) {
		String[] repoArray = new String[4];
		//JSONObject idJson =  ((JSONObject) repoJson.get("data"));
		String repoId = ((JSONObject)repoJson.get("data")).get("id").toString();
		String pgaScore =((JSONObject) ((JSONObject) repoJson.get("data")).get("attributes")).get("score").toString();
		
		repoArray[0] = repoId;
		repoArray[1] = pgaScore;
	
		return repoArray;

	}

	public void getIssueDetails(JSONObject issueJsonObj) {
		JSONArray datasArray = (JSONArray) issueJsonObj.get("data");
		for (int i = 0; i < datasArray.size(); i++) {
			JSONObject dataJson = (JSONObject) datasArray.get(i);
			JSONObject attributeJson = (JSONObject) dataJson.get("attributes");
			String severity = attributeJson.get("severity").toString();

			String catagoryType = ((JSONArray) attributeJson.get("categories")).get(0).toString();

			if (issueCountMap.isEmpty()) {
				issueCountMap.put(catagoryType, 1);
			} else {
				boolean flag = true;
				Set set = (Set) issueCountMap.entrySet();
				Iterator it = set.iterator();
				while (it.hasNext()) {
					Map.Entry entry = (Entry) it.next();
					if (entry.getKey().equals(catagoryType)) {
						flag = false;
						issueCountMap.put(entry.getKey().toString(), (int) entry.getValue() + 1);
					}
				}
				if (flag) {
					issueCountMap.put(catagoryType, 1);
				}
			}
		}
	}

	public int[] getPageCount(JSONObject fileJson) {
		String firstPageCount = null;
		String lastPageCount = null;
		String currentPage = ((JSONObject) fileJson.get("links")).get("self").toString();
		String LastPage = ((JSONObject) fileJson.get("links")).get("last").toString();
		if (currentPage.contains("page%5Bnumber%5D=")) {
			Pattern ptn = Pattern.compile("page%5Bnumber%5D=");
			String[] parts = ptn.split(currentPage);
			if (parts[1].contains("&")) {
				firstPageCount = parts[1].split(Pattern.quote("&"))[0];
			}

		}
		if (LastPage.contains("page%5Bnumber%5D=")) {
			Pattern ptn = Pattern.compile("page%5Bnumber%5D=");
			String[] parts = ptn.split(LastPage);
			if (parts[1].contains("&")) {
				lastPageCount = parts[1].split(Pattern.quote("&"))[0];
			}

		}
		int[] pageCountArray = new int[2];
		pageCountArray[0] = Integer.parseInt(firstPageCount);
		pageCountArray[1] = Integer.parseInt(lastPageCount);
		return pageCountArray;
	}

	public void getFileInfo(JSONObject fileJson, int[] pageCount, String fileUrl, String apitoken) {
		
		int currentPageCount = 0;
		String newFileUrl = fileUrl + "&page%5Bnumber%5D=";
		for (int i = pageCount[0]; i <= pageCount[1]; i++) {
			currentPageCount = i;
			JSONObject Json = makeRestCall(newFileUrl + currentPageCount, apitoken);
			JSONArray dataArray = (JSONArray) Json.get("data");
			String[] fileRate = getFileRatting(dataArray);

			currentPageCount++;
		}
	}

	public String[] getFileRatting(JSONArray dataArray) {
		String fileName = null;
		String fileRating = null;
		String fileRate[] = new String[2];
		FileRating fileRateObj = null;
		for (int i = 0; i < dataArray.size(); i++) {
			fileRateObj = new FileRating();
			JSONObject dataJson = (JSONObject) ((JSONObject) dataArray.get(i)).get("attributes");
			fileName = dataJson.get("path").toString();
			fileRate[0] = fileName;

			if (!(null == dataJson.get("rating"))) {
				fileRating = dataJson.get("rating").toString();
			}
			fileRate[1] = fileRating;
			fileRateObj.setFileName(fileRate[0]);
			fileRateObj.setFileRating(fileRate[1]);
			fileRatingSet.add(fileRateObj);
		}

		return fileRate;
	}
}
