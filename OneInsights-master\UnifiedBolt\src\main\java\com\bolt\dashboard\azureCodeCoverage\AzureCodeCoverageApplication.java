package com.bolt.dashboard.azureCodeCoverage;

import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;
import java.util.Set;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.ProjectCoverageDetails;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.ProjectCoverageDetailsRepo;
import com.bolt.dashboard.util.AzureDevOpsUtils;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class AzureCodeCoverageApplication {
	private static final Logger LOGGER = LogManager.getLogger(AzureCodeCoverageApplication.class);
	AzureDevOpsUtils utils = new AzureDevOpsUtils();
	
	private ConfigurationSettingRep configurationRepo;
	private ProjectCoverageDetailsRepo repo = null;
	
	public AzureCodeCoverageApplication() {
		super();
	}

	private ConfigurationSetting configuration = null;
	private AnnotationConfigApplicationContext ctx = null;
	private ConfigurationToolInfoMetric config=null;
	private int pipeline = Integer.MIN_VALUE;
	private String technology = "";
	private String projectName = "";
	private String minTime = "";
	
//	public static void main(String[] args) {
//		new AzureCodeCoverageApplication().azureCoverageMain("BrillioOne");
//	}
	
	public void azureCoverageMain(String projectName) {
		
		this.projectName = projectName;
		LOGGER.info("Azure Code Coverage Collector Started for "+ projectName);
		ctx = DataConfig.getContext();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		repo = ctx.getBean(ProjectCoverageDetailsRepo.class);
		
		List<ProjectCoverageDetails> temp = repo.findByPName(projectName);
		if(!temp.isEmpty()) {
			ProjectCoverageDetails twmp2 = temp.get(temp.size()-1);
			
			DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
			Calendar calendar = Calendar.getInstance();
			calendar.setTimeInMillis(twmp2.getStarted_timestamp());
			minTime = formatter.format(calendar.getTime());
		}

		configuration = configurationRepo.findByProjectName(projectName).get(0);
		
		Set<ConfigurationToolInfoMetric> metrics = configuration.getMetrics();
		
		metrics.forEach((ConfigurationToolInfoMetric obj)->{
			if("Azure CodeCoverage".equals(obj.getToolName())) {
				config = obj;
				config.setPassword(EncryptionDecryptionAES.decrypt(obj.getPassword(), ConstantVariable.SECRET_KEY));
			}
		});
		if (config!=null) {
			findCodes();
		}
	}
	private void findCodes() {
		String url =  config.getUrl();
		String urls[]=config.getUrl().split(",");
		for(int index=0;index<urls.length;index++) {
		String projectCodes = urls[index].split("\\?")[1];
		config.setUrl(urls[index].split("\\?")[0]);
		String[] codes = projectCodes.split("&");
		for(int i=0;i<codes.length;i++) {
			String[] code = codes[i].split("=");
			if("pipeline".equals(code[0])) {
				pipeline = Integer.parseInt(code[1]);
			}else if("technology".equals(code[0])) {
				technology = code[1];
			}
		}
		
		if(pipeline!= Integer.MIN_VALUE) {
			try {
			getPipelineBuilds();
			}catch(Exception ex) {
				LOGGER.info("Azure Code Coverage Collector was Failed for "+ projectName);
				LOGGER.info(ex);
			}
		}
	  }
		LOGGER.info("Azure Code Coverage Collector was successfull for "+ projectName);
	}

	void getPipelineBuilds() {
		if(pipeline != Integer.MIN_VALUE) {
//			minTime - query - If specified, filters to builds that finished/started/queued after this date based on the queryOrder specified
			String url = config.getUrl()+"/_apis/build/builds?definitions="+pipeline+"&minTime="+minTime;
			ResponseEntity<String> runs = utils.makeRestCall(url,config.getUserName(),config.getPassword());
			JSONObject o = new JSONObject(runs.getBody());
			JSONArray arr = o.getJSONArray("value");
			JSONArray newJsonArray = new JSONArray();
			for (int i = arr.length()-1; i>=0; i--) {
			    newJsonArray.put(arr.get(i));
			}
			arr = newJsonArray;
			AzureCoverage azure=new AzureCoverageImpl();
			
			switch(technology) {
				case "java":

					azure.azureForJava(config, arr, projectName);
					break;
				case "angular":
					azure.azureForAngular(config,arr,projectName);
					break;
				default:
					break;
				
			}
		}else {
			LOGGER.warn("Pipeline DefinitionId not found");
			LOGGER.info("Stopping the collector...");
		}

		
	}
}
