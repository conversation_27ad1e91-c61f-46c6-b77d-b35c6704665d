package com.bolt.dashboard.azureCodeCoverage;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.ProjectCoverageDetails;
import com.bolt.dashboard.core.model.RepoCodeCoverageStatus;
import com.bolt.dashboard.core.repository.ProjectCoverageDetailsRepo;
import com.bolt.dashboard.core.repository.RepoCodeCoverageStatusRepo;
import com.bolt.dashboard.projectcoveragedetails.ProjectCoverageApplication;
import com.bolt.dashboard.util.AzureDevOpsUtils;
import com.bolt.dashboard.util.RestClient;

public class AzureCoverageImpl implements AzureCoverage {

	private static final Logger LOGGER = LogManager.getLogger(ProjectCoverageApplication.class);
	private ProjectCoverageDetailsRepo repo = null;
	private AnnotationConfigApplicationContext ctx = DataConfig.getContext();;
	private RepoCodeCoverageStatusRepo codeCoverageStatusRepo = ctx.getBean(RepoCodeCoverageStatusRepo.class);;
	private AzureDevOpsUtils utils=new AzureDevOpsUtils();
	RestClient restClient = new RestClient();
	String statementPattern = "Statements\\s+:\\s(\\d+.?\\d+)%+\\s+\\(+\\s+\\d+/\\d+\\s+\\)";
	String branchPattern = "Branches\\s+:\\s(\\d+.?\\d+)%+\\s+\\(+\\s+\\d+/\\d+\\s+\\)";
	String functionsPattern = "Functions\\s+:\\s(\\d+?.?\\d+)%+\\s+\\(+\\s+\\d+/\\d+\\s+\\)";
	String linesPattern = "Lines\\s+:\\s(\\d+.?\\d+)%+\\s+\\(+\\s+\\d+/\\d+\\s+\\)";
	String coveragePattern = "(\\d+.?\\d+)";

	@Override
	public void azureForAngular(ConfigurationToolInfoMetric config, JSONArray buildArr, String projectName) {
//		Important - file name should be in coverageReport/summary.txt,unitTestReport/unit-tests.xml
		
		LOGGER.info("Processing build's artifacts...");
		repo = ctx.getBean(ProjectCoverageDetailsRepo.class);
		for (int i = 0; i < buildArr.length(); i++) {
			try {
				ProjectCoverageDetails pCoverage = new ProjectCoverageDetails();
				pCoverage.setpName(projectName);

				JSONObject build = buildArr.getJSONObject(i);
				pCoverage.setBranchName(build.getString("sourceBranch").replace("refs/heads/", ""));

				int buildId = build.getInt("id");
				pCoverage.setJobId(buildId);
				long createtime = utils.getTimeInMiliseconds(build.getString("queueTime"));
				long starttime = utils.getTimeInMiliseconds(build.getString("startTime"));

				pCoverage.setCreated_timestamp(createtime);
				pCoverage.setStarted_timestamp(starttime);

				JSONObject requestee = build.getJSONObject("requestedFor");
				String commiter = requestee.getString("displayName");
				pCoverage.setCommiter(commiter);

				String groupName = (build.getJSONObject("repository")).getString("name");
				String repoName = (build.getJSONObject("definition")).getString("name");
				pCoverage.setRepoName(repoName);
				pCoverage.setGroupName(groupName);
				
				boolean isCoverage=false,isTests=false;
				String coverageArtifact=null,unitTestsArtifact=null;
				//check if artifacts are present
				String artifactUrls = config.getUrl() + "/_apis/build/builds/" + buildId+ "/artifacts?";
				ResponseEntity<String> artifacts = utils.makeRestCall(artifactUrls, config.getUserName(),
						config.getPassword());
				if (artifacts.getStatusCode() == HttpStatus.OK) {
					JSONObject artifactsObj = new JSONObject(artifacts.getBody());
					JSONArray artifactsJSON = artifactsObj.getJSONArray("value");
					for(int j=0;j<artifactsJSON.length();j++) {
						JSONObject artifact = artifactsJSON.getJSONObject(j);
						if("coverageReport".equals(artifact.get("name"))) {
							coverageArtifact = artifact.getJSONObject("resource").getString("downloadUrl");;
						}
						if("unitTestReport".equals(artifact.get("name"))) {
							unitTestsArtifact = artifact.getJSONObject("resource").getString("downloadUrl");;
						}
					}
				}
				//For getting coverage
				try {
					if (coverageArtifact!=null) {
						boolean isdownloaded = restClient.downloadXML(coverageArtifact, config.getUserName(), config.getPassword());
						
						if(isdownloaded) {
							File file = new File("test/coverageReport/summary.txt"); 
							if(file.exists()) isCoverage = true;
							BufferedReader br = null;
							try {
							br = new BufferedReader(new FileReader(file));
							String st;
					        while ((st = br.readLine()) != null) {
					        	List<String> statementCoverage = matchFinder(statementPattern,st);
					        	List<String> coverageValues = null;
					        	if (statementCoverage.size() > 0) {
					    			coverageValues = matchFinder(coveragePattern, statementCoverage.get(0));
					    			pCoverage.setCovered_statements_percentage(Double.parseDouble(coverageValues.get(0)));
					    			String splitValues[] = coverageValues.get(1).split("/");
					    			pCoverage.setCovered_statements(Integer.parseInt(splitValues[0]));
					    			pCoverage.setTotal_statements(Integer.parseInt(splitValues[1]));
					    		}
					        	List<String> linesCoverage = matchFinder(linesPattern, st);
					    		if (linesCoverage.size() > 0) {
					    			coverageValues = matchFinder(coveragePattern, linesCoverage.get(0));
					    			pCoverage.setCovered_lines_percentage(Double.parseDouble(coverageValues.get(0)));
					    			String splitValues[] = coverageValues.get(1).split("/");
					    			pCoverage.setCovered_lines(Integer.parseInt(splitValues[0]));
					    			pCoverage.setTotal_lines(Integer.parseInt(splitValues[1]));
					    		} 
					    		List<String> branchCoverage = matchFinder(branchPattern, st);
					    		if (branchCoverage.size() > 0) {
					    			coverageValues = matchFinder(coveragePattern, branchCoverage.get(0));
					    			pCoverage.setCovered_branches_percentage(Double.parseDouble(coverageValues.get(0)));
					    			String splitValues[] = coverageValues.get(1).split("/");
					    			pCoverage.setCovered_branches(Integer.parseInt(splitValues[0]));
					    			pCoverage.setTotal_branches(Integer.parseInt(splitValues[1]));

					    		}
					    		List<String> functionCoverage = matchFinder(functionsPattern, st);
					    		if (functionCoverage.size() > 0) {
					    			coverageValues = matchFinder(coveragePattern, functionCoverage.get(0));
					    			pCoverage.setCovered_function_percentage(Double.parseDouble(coverageValues.get(0)));
					    			String[] splitValues = coverageValues.get(1).split("/");
					    			pCoverage.setCovered_functions(Integer.parseInt(splitValues[0]));
					    			pCoverage.setTotal_functions(Integer.parseInt(splitValues[1]));
					    		}
					        }
					        setRepoStatus(groupName, repoName,"",true,projectName);
							}catch(Exception e) {
								LOGGER.error(e.getMessage());
							}finally {
						        br.close();
							}
						}
					}

				} catch (Exception e) {
				}finally {
					restClient.cleanUp();
				}
				//For getting test results
				try {

					if (unitTestsArtifact!=null) {
						boolean isdownloaded = restClient.downloadXML(unitTestsArtifact, config.getUserName(), config.getPassword());
						
						if(isdownloaded) {
							File file = new File("test/unitTestReport/unit-tests.xml");
							if(file.exists()) isTests = true;
							
							DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();  
							dbf.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
							dbf.setFeature("http://xml.org/sax/features/external-general-entities", false);
							DocumentBuilder db = dbf.newDocumentBuilder();  
							Document doc = db.parse(file);  
							doc.getDocumentElement().normalize(); 
							NodeList nodeList = doc.getElementsByTagName("testsuite");
							Node currentItem = nodeList.item(0);
							int totalTestCases = Integer.parseInt(currentItem.getAttributes().getNamedItem("tests").getNodeValue());
							int failedTestCases = Integer.parseInt(currentItem.getAttributes().getNamedItem("failures").getNodeValue());
							int passedTestCases = totalTestCases - failedTestCases;
						    pCoverage.setTotal_test_cases(totalTestCases);
						    pCoverage.setTest_cases_failed(failedTestCases);
						    pCoverage.setTest_cases_passed(passedTestCases);
						    
						    if(failedTestCases>0) setRepoStatus(groupName, repoName,"Test cases failed",false,projectName);
						    else setRepoStatus(groupName, repoName,"",true,projectName);
							
						}
					}
				}catch(Exception e) {
				}finally {
					restClient.cleanUp();
				}
				
				if(isCoverage && isTests) {
					repo.save(pCoverage);
				}else {
					setRepoStatus(groupName, repoName,"No Artifact found",false,projectName);
				}
			}catch(Exception e) {
			}
		}
		LOGGER.info("Processing finished...");

	}
	public static List<String> matchFinder(String patternRegex, String value) {
		Pattern pattern = Pattern.compile(patternRegex);
		Matcher matcher = pattern.matcher(value);

		List<String> listMatches = new ArrayList<String>();
		while (matcher.find()) {
			listMatches.add(matcher.group());
		}
		/*
		 * for (String match : listMatches) { LOGGER.info(match); }
		 */
		return listMatches;

	}
	
	private void setRepoStatus(String groupName, String repoName, String cause,boolean status,String projectName) {

		RepoCodeCoverageStatus codeCoverageStatus=codeCoverageStatusRepo.findByRepoNameAndGroupName(repoName,groupName);
		if(codeCoverageStatus==null) {
			codeCoverageStatus = new RepoCodeCoverageStatus();
		}
		codeCoverageStatus.setRepoName(repoName);
		codeCoverageStatus.setGroupName(groupName);
		codeCoverageStatus.setCoverageStatus(status);
		codeCoverageStatus.setCauseDescription(cause);
		codeCoverageStatus.setpName(projectName);
		codeCoverageStatusRepo.save(codeCoverageStatus);
	}
	@Override
	public void azureForJava(ConfigurationToolInfoMetric config, JSONArray buildArr, String projectName) {
		
		List<ProjectCoverageDetails> codeCoveraageData= new ArrayList<ProjectCoverageDetails>();
		String url=config.getUrl();
		for(int index=0;index<buildArr.length();index++) {
			try {
				JSONObject buildObj=buildArr.getJSONObject(index);
			String artifactUrl= url+"/_apis/build/builds/"+
					buildObj.getInt("id")+"/artifacts?artifactName=coverage&api-version=4.1";
			ResponseEntity<String> artifact = restClient.makeGetRestCall(artifactUrl,config.getUserName(),config.getPassword());
			JSONObject artfiResp = new JSONObject(artifact.getBody());
			System.out.println(artfiResp.getJSONObject("resource").getString("downloadUrl"));
			
			String containerid = artfiResp.getJSONObject("resource").getString("data");
			containerid = containerid.split("#")[1];
			String artifactDownloadUrl = artfiResp.getJSONObject("resource").getString("downloadUrl");
			
			
			 ProjectCoverageDetails projectCoverage = new ProjectCoverageDetails(); 
			 projectCoverage.setpName(projectName);
			 codeCoveraageData.add(projectCoverage);
			 projectCoverage.setCommiter(buildObj.getJSONObject("requestedFor").getString("displayName"));
			 handleArtifactCoverageJava(projectCoverage,artifactDownloadUrl,config);
			 
			
			//runs = makeRestCall(resp.getJSONObject("resource").getString("downloadUrl"),config.getUserName(),config.getPassword());
		    String logsurl= url+"/_apis/build/builds/"+buildObj.getInt("id")+"/logs";
		   
		    projectCoverage.setJobId(buildObj.getInt("id"));
		    String repoName=buildObj.getJSONObject("definition").getString("name");
		    String groupName = (buildObj.getJSONObject("repository")).getString("name");
		    projectCoverage.setGroupName(groupName);
		    projectCoverage.setRepoName(repoName);
		    projectCoverage.setBranchName(buildObj.getString("sourceBranch").replace("refs/heads/", ""));
		    projectCoverage.setStarted_timestamp(ConstantVariable.timestamp(buildObj.getString("startTime"),projectName));
		    projectCoverage.setCreated_timestamp(ConstantVariable.timestamp(buildObj.getString("queueTime"),projectName));
			processLogsJava(logsurl,config, projectCoverage);
			setRepoStatus(groupName,repoName,"",true,projectName);
			}catch (Exception ex) {
				LOGGER.info(ex);
				
			}
			saveProjectCodeCoverage(codeCoveraageData);
			
		}
		
	}
	
	private void saveProjectCodeCoverage(List<ProjectCoverageDetails> codeCoveraageData) {
		
	 AnnotationConfigApplicationContext ctx = null;
		ctx = DataConfig.getContext();
		ProjectCoverageDetailsRepo codeCoverRepo = ctx.getBean(ProjectCoverageDetailsRepo.class);
		codeCoverRepo.save(codeCoveraageData);
		
	}

	private void handleArtifactCoverageJava(ProjectCoverageDetails projectCoverage, String artifactDownloadUrl, ConfigurationToolInfoMetric config) {
		
		boolean isTests=false;
		RestClient restClient =  new RestClient();
		try {
		boolean isdownloaded = restClient.downloadXML(artifactDownloadUrl, config.getUserName(), config.getPassword());
		if(isdownloaded) {
			
			File file = new File("test/coverage/coverage.xml");
			if(file.exists()) isTests = true;
			
			LOGGER.info("Creating documentBuilder");
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();  
			dbf.setFeature("http://xml.org/sax/features/external-general-entities", false);
			dbf.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
//			dbf.setAttribute(XMLConstants.ACCESS_EXTERNAL_DTD, ""); // Compliant
//			dbf.setAttribute(XMLConstants.ACCESS_EXTERNAL_SCHEMA, ""); // compliant
			DocumentBuilder db = dbf.newDocumentBuilder();  
//		

			LOGGER.info("Parsing document");
			Document doc = db.parse(file);  
			doc.getDocumentElement().normalize(); 
			NodeList nodeList = doc.getElementsByTagName("coverage");
			Node currentItem = nodeList.item(0);
			   projectCoverage.setTotal_lines(Integer.parseInt(currentItem.getAttributes().getNamedItem("lines-valid").getNodeValue()));
			   projectCoverage.setCovered_lines(Integer.parseInt(currentItem.getAttributes().getNamedItem("lines-covered").getNodeValue()));
			   projectCoverage.setTotal_statements(Integer.parseInt(currentItem.getAttributes().getNamedItem("lines-valid").getNodeValue()));
			   projectCoverage.setCovered_statements(Integer.parseInt(currentItem.getAttributes().getNamedItem("lines-covered").getNodeValue()));
			   projectCoverage.setTotal_branches(Integer.parseInt(currentItem.getAttributes().getNamedItem("branches-valid").getNodeValue()));
			   projectCoverage.setCovered_branches(Integer.parseInt(currentItem.getAttributes().getNamedItem("branches-covered").getNodeValue()));
			   BigDecimal bd=BigDecimal.valueOf(Double.parseDouble(currentItem.getAttributes().getNamedItem("line-rate").getNodeValue())*100).setScale(2,RoundingMode.HALF_DOWN);
		         projectCoverage.setCovered_lines_percentage(bd.doubleValue());
		         projectCoverage.setCovered_statements_percentage(bd.doubleValue());
		         bd=BigDecimal.valueOf(Double.parseDouble(currentItem.getAttributes().getNamedItem("branch-rate").getNodeValue())*100).setScale(2,RoundingMode.HALF_DOWN);
		         projectCoverage.setCovered_branches_percentage(bd.doubleValue());
		         
		         NodeList nodeList2 = doc.getElementsByTagName("method");
		         int length = nodeList2.getLength();
		         int functionCovered=0;
		         for (int k = 0; k < length; k++) {
		        	 try {
		         Node el = nodeList2.item(k);
		         float lineRate = Float.parseFloat(el.getAttributes().getNamedItem("line-rate").getNodeValue());
		           if(lineRate==1.0) {
		        	   functionCovered++;
		           }
		         }catch (Exception e) {
		        	 LOGGER.info(e);
					// TODO: handle exception
				}
		        	 }
		         bd=new BigDecimal((functionCovered*100/length)).setScale(2,RoundingMode.HALF_DOWN);
		         projectCoverage.setCovered_function_percentage(bd.doubleValue());
		         projectCoverage.setCovered_functions(functionCovered);
		         projectCoverage.setTotal_functions(length);
		         
		}
			} catch(Exception ex) {
				LOGGER.info(ex);
		
		}finally {
			restClient.cleanUp();
		}
		
	}

	private  void processLogsJava(String logsurl,ConfigurationToolInfoMetric config, ProjectCoverageDetails projectCoverage){
		String testRun = "Tests\\s+run:\\s+\\d+";
		String testNumPattern="Tests\\s+run:\\s+";
		String failurepattern = "Failures:\\s+\\d+";
		String failureNumpattern = "Failures:\\s+";
		String skipPattern= "Skipped:\\s+\\d+";
		String skipNumPattern= "Skipped:\\s+";
		String errorPattern= "Errors:\\s+\\d+";
		String errorNumPattern= "Errors:\\s+";
		
		ResponseEntity<String> logs = restClient.makeGetRestCall(logsurl,config.getUserName(),config.getPassword());
		JSONObject logsResp = new JSONObject(logs.getBody());
		JSONArray logsURLs=logsResp.getJSONArray("value");
		for(int index=0;index<logsURLs.length();index++) {
			JSONObject logsUrlJsonobj= logsURLs.getJSONObject(index);
			String logurl=logsUrlJsonobj.getString("url");
			logs = restClient.makeGetRestCall(logurl,config.getUserName(),config.getPassword());
		String  logsResponse = logs.getBody();
		if(logsResponse.contains("Starting: Maven") && logsResponse.contains("Results:") ) {
			String [] responseResultArr=logsResponse.split("Results:");
			System.out.println(responseResultArr[2]);
			List<String> statementCoverage = matchFinder(testRun, responseResultArr[2]);
			String[] splitRes= statementCoverage.get(0).split(testNumPattern);
			int totalTestCases=Integer.parseInt(splitRes[1]);
			projectCoverage.setTotal_test_cases(totalTestCases);
			statementCoverage = matchFinder(failurepattern, responseResultArr[2]);
			splitRes= statementCoverage.get(0).split(failureNumpattern);
		    int failedTestCases=Integer.parseInt(splitRes[1]);
		    projectCoverage.setTest_cases_failed(failedTestCases);
		    statementCoverage = matchFinder(errorPattern, responseResultArr[2]);
			splitRes= statementCoverage.get(0).split(errorNumPattern);
		    int errorTestCases=Integer.parseInt(splitRes[1]);
		    statementCoverage = matchFinder(skipPattern, responseResultArr[2]);
			splitRes= statementCoverage.get(0).split(skipNumPattern);
		    int skipTestCases=Integer.parseInt(splitRes[1]);
		    int passedtestCases=totalTestCases-(failedTestCases+errorTestCases+skipTestCases);
	        projectCoverage.setTest_cases_passed(passedtestCases);
	        if(failedTestCases>0) setRepoStatus(projectCoverage.getGroupName(), projectCoverage.getRepoName(),"Test cases failed",false,projectCoverage.getpName());
		    else setRepoStatus(projectCoverage.getGroupName(), projectCoverage.getRepoName(),"",true,projectCoverage.getpName());
		}
		  
		
		
		
	}
 }
}
