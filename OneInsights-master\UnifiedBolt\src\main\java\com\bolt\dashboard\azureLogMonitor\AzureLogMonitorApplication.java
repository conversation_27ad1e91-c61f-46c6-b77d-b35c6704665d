package com.bolt.dashboard.azureLogMonitor;

import java.util.List;




import com.bolt.dashboard.core.model.AzureLogMonitorResource;

public class AzureLogMonitorApplication {
//	public static void main(String[] args) {
//		new AzureLogMonitorApplication().azureLogMonitorMain("");
//		
//	}
	
	
	public List<AzureLogMonitorResource> azureLogMonitorMain(String pName) {
		
	AzureLogMonitor azureLogMon = new AzureLogMonitorImplementation();
	List<AzureLogMonitorResource> resourceData=azureLogMon.getResourceData();
	System.out.println(resourceData);
	return resourceData;
		
	}

	

}
