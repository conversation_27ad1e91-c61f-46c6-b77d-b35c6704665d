package com.bolt.dashboard.azureLogMonitor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;

import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.AzureLogMonitorResource;
import com.bolt.dashboard.core.model.AzureLogResourceMetric;
import com.bolt.dashboard.core.model.AzureResourceMetricData;

public class AzureLogMonitorImplementation implements AzureLogMonitor {
	String url="https://management.azure.com/subscriptions/726bb1a2-7488-49b7-ade1-bde6cf66f6ae"
			+ "/resourceGroups/brilliobolt/providers/Microsoft.Compute/virtualMachines"
			+ "/BOLTApp/providers/Microsoft.Insights/metrics?api-version=2018-01-01&interval="
			+ "P1D&timespan=2021-10-20T00:00:00Z/2021-11-19T00:00:00Z&metricnames=Percentage CPU,Available Memory Bytes";
	List<AzureLogMonitorResource> azureLogdata=null;
	
	String url2="https://management.azure.com//subscriptions/726bb1a2-7488-49b7-ade1-bde6cf66f6ae/resourceGroups/brilliobolt/providers/Microsoft.Storage/storageAccounts/brillioboltdiag712/providers/"
			+ "Microsoft.Insights/metrics?api-version=2018-01-01&timespan=2021-10-24T11%3A25%3A40Z%2F2021-11-22T12%3A25%3A40Z";
	@Override
	public List<AzureLogMonitorResource> getResourceData() {
		
		
		ResponseEntity<String> tokenRes = getToken();
	
		JSONObject tokenValues=parseAsNewObject(tokenRes);
		azureLogdata= new ArrayList<AzureLogMonitorResource>();
		
		ResponseEntity<String> res=makeRestCall(url, tokenValues.getString("access_token"));
		JSONObject resourcesResponse1=parseAsNewObject(res);
		res=makeRestCall(url2, tokenValues.getString("access_token"));
		JSONObject resourcesResponse2=parseAsNewObject(res);
		AzureLogMonitorResource azureLogMonitor1 = new AzureLogMonitorResource();
		azureLogdata.add(azureLogMonitor1);
		 
		azureLogMonitor1.setResourceType(resourcesResponse1.getString("namespace"));
		JSONArray values= resourcesResponse1.getJSONArray("value");
		
		
		proceessValues(values,azureLogMonitor1);
		
		AzureLogMonitorResource azureLogMonitor2 = new AzureLogMonitorResource();
		azureLogMonitor2.setResourceType(resourcesResponse2.getString("namespace"));
		 values= resourcesResponse2.getJSONArray("value");
		 proceessValues(values,azureLogMonitor2);
		
		azureLogdata.add(azureLogMonitor2);
		
		
		return azureLogdata;
	}
	
	private void proceessValues(JSONArray values, AzureLogMonitorResource azureLogMonitor) {
		List<AzureLogResourceMetric> metrics=new ArrayList<AzureLogResourceMetric>();
		for(int index=0;index<values.length();index++) {
			JSONObject metricValue=values.getJSONObject(index);
			String id= metricValue.getString("id");
			azureLogMonitor.setResourceId(id);
			String resources[]=id.split("/");
			azureLogMonitor.setResourceName(resources[8]);
			
			AzureLogResourceMetric metric= new AzureLogResourceMetric();
			metric.setMetricName(metricValue.getJSONObject("name").getString("value"));
			JSONObject timeSeriesObj=metricValue.getJSONArray("timeseries").getJSONObject(0);
		    processMetricData(metric,timeSeriesObj.getJSONArray("data"));			
			metrics.add(metric);
			azureLogMonitor.setMetrics(metrics);
		}
	}
	
	
	
	private void processMetricData(AzureLogResourceMetric metric, JSONArray data) {
		 String timestamp="";
		try {
		List<AzureResourceMetricData> azureResourceMetrics= new ArrayList<AzureResourceMetricData>();
		for(int index=0;index<data.length();index++) {
			 try {
			JSONObject metricData=data.getJSONObject(index);
			AzureResourceMetricData metricD=new AzureResourceMetricData();
			 timestamp= metricData.getString("timeStamp");
			 BigDecimal bd=BigDecimal.valueOf(metricData.getDouble("average")).setScale(2,RoundingMode.HALF_DOWN);
			if(metric.getMetricName().equals("Available Memory Bytes")) {
				 bd=BigDecimal.valueOf(metricData.getDouble("average")/1000000000).setScale(2,RoundingMode.HALF_DOWN);
			}else if(metric.getMetricName().equals("UsedCapacity")) {
				 bd=BigDecimal.valueOf(metricData.getDouble("average")/1000000).setScale(2,RoundingMode.HALF_DOWN);
			}
			 
			  metricD.setValue(bd.doubleValue());
			 
			  
			long timeStamp=ConstantVariable.timestamp(timestamp, "BrillioOne");
			metricD.setTimestamp(timeStamp);
			azureResourceMetrics.add(metricD);
			 }catch(Exception ex) {
				 System.out.println("Exception");
			 }
		}
		metric.setMetricData(azureResourceMetrics);
		}catch(Exception ex) {
			System.out.println("Timestamp error "+ timestamp );
		
		}
		
		
	}




	private JSONArray parseAsNewArray(ResponseEntity<String> response) {
		return (JSONArray) new JSONTokener(response.getBody()).nextValue();
	}
	private JSONObject parseAsNewObject(ResponseEntity<String> response) {
		return (JSONObject) new JSONTokener(response.getBody()).nextValue();
	}
	public RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000);
		requestFactory.setReadTimeout(20000);
		return new RestTemplate(requestFactory);
	}
	
	public ResponseEntity<String> getToken(){
		String tokenUrl="https://login.microsoftonline.com/97984c2b-a229-4609-8185-ae84947bc3fc/oauth2/v2.0/token";
		MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
		map.add("client_id","600c1a13-2307-4994-b517-f58269f9b9cc");
		map.add("scope","https://management.azure.com/.default");
		map.add("client_secret", "**********************************");
		map.add("grant_type", "client_credentials");
		
		return get().postForEntity(tokenUrl, new HttpEntity<>(map, createHeadersToken()),
				String.class);
		//return null;

	}

	public ResponseEntity<String> makeRestCall(String url, String token) {
		// Basic Auth only.

		
		
				UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(url);
				UriComponents uriComponents = builder.build();
				URI uri = uriComponents.toUri();
				return get().exchange(uri, HttpMethod.GET, new HttpEntity<>(createHeaders(token)),
						String.class);
			

			
		

	}
	private HttpHeaders createHeadersToken() {
		

		HttpHeaders headers = new HttpHeaders();
		//headers.setContentType(MediaType.APPLICATION_JSON);
       // headers.add("Accept", "application/json");
        //headers.add("Accept-Encoding", "gzip, deflate");
		headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

		return headers;
	}
	

	private HttpHeaders createHeaders(final String token) {
		
		String authHeader = "Bearer "+token ;

		HttpHeaders headers = new HttpHeaders();
		//headers.setContentType(MediaType.APPLICATION_JSON);
       // headers.add("Accept", "application/json");
        //headers.add("Accept-Encoding", "gzip, deflate");
		headers.set("Authorization", authHeader);

		return headers;
	}
	
	
	


}
