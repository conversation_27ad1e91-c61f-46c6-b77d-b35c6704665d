package com.bolt.dashboard.azurerepo;

import java.util.Date;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.atlassian.jira.rest.client.api.RestClientException;
import com.bolt.dashboard.bitbucketpipeline.BitbucketPipelineApplication;
import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.repository.CollectorLastRunRepo;
import com.bolt.dashboard.core.repository.SCMToolRepository;


public class AzureRepoApplication {
	private static final Logger LOG = LogManager.getLogger(AzureRepoApplication.class);
	AnnotationConfigApplicationContext applicationContext = null;
	SCMToolRepository repo = null;
	AzureRepoImplementation azureRepoImplementation;
	String type="Azure Repo";
	
	
//	 public static void main(String[] args) { 
//		 new AzureRepoApplication().azureRepoMain("BrillioOne"); 
//		 }
	 
	 
	
	public void azureRepoMain(String projectName) throws RestClientException {
		LOG.info("repo Collector started for " + projectName);
		applicationContext = DataConfig.getContext();
		repo = applicationContext.getBean(SCMToolRepository.class);
	
		azureRepoImplementation = new AzureRepoImplementation();
		try {
			azureRepoImplementation.getRepoData(repo, projectName);
			ConstantVariable.getLastRun(projectName, type, new Date().getTime(), ConstantVariable.COLLECTOR_STATUS_SUCCESS);
			
		} catch (Exception e) {
					ConstantVariable.getLastRun(projectName, type, new Date().getTime(), ConstantVariable.COLLECTOR_STATUS_FAILURE);
			cleanObject();
			LOG.error(e);
			LOG.error("Azure Repo Exception ", e.fillInStackTrace());

			LOG.info("Azure Repo failed for " + projectName);
		}
		cleanObject();
		LOG.info("Azure Repo Collector ended for " + projectName);
	}

	public void cleanObject() {
		repo = null;
		azureRepoImplementation = null;
	}
}
