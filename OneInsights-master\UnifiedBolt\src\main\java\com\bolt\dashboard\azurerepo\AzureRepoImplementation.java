package com.bolt.dashboard.azurerepo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.ResponseEntity;

import com.bolt.dashboard.bitbucketpipeline.BitbucketPipelineImplementation;
import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;

import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.DeletedFileDetails;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.SCMToolRepository;
import com.bolt.dashboard.util.AzureDevOpsUtils;
import com.bolt.dashboard.util.EncryptionDecryptionAES;


public class AzureRepoImplementation implements AzureRepo {
	AzureDevOpsUtils utils = new AzureDevOpsUtils();
	private static final Logger LOG = LogManager.getLogger(AzureRepoImplementation.class);
	AnnotationConfigApplicationContext ctx = null;
	ConfigurationToolInfoMetric config;
	ConfigurationSettingRep configRepo;
	MongoTemplate mongo;
	String instanceurl;
	String password;
	String userName;
	String last = null;
	List<String> repos;

	@Override
	public void getRepoData(SCMToolRepository repo, String projectName) {
		getConfigurations(projectName);
		if (repos == null || (repos != null && (repos.size() == 0 || repos.get(0).equals("")))) {
			repos = getUrls();
		}
		//
		// Map<String, List<String>> repoAndBranches = getBranches(repos);
		// List<SCMTool> commits = getCommitsOnBranches(repoAndBranches, projectName);
		List<SCMTool> commits = getCommits(repos, projectName);
		repo.save(commits);
	}

	private List<SCMTool> getCommits(List<String> urls, String projectName) {
		List<SCMTool> commits = new ArrayList<SCMTool>();
		for (String url : urls) {
			String repoNameArr[]=url.split("/");
			String repoName=repoNameArr[repoNameArr.length-1];
			last = null;
			Query query = new Query();
			query.addCriteria(Criteria.where("projectName").is(projectName).and("repoName").is(repoName));
			query.with(new Sort(new Order(Direction.DESC, "commitTS")));
			List<SCMTool> lastCommitlist = mongo.find(query, SCMTool.class, "SCM");
			if (lastCommitlist != null && lastCommitlist.size() > 0) {
				last = new DateTime(lastCommitlist.get(0).getCommitTS() + 1000).toString();
				String[] temp = last.split("[.]");
				last = temp[0] + "Z";
			}
			String tempUrl;
			if (last != null) {
				tempUrl = url + "/commits?&searchCriteria.fromDate=" + last
						+ "&api-version=6.0&searchCriteria.showOldestCommitsFirst=true";
				LOG.info("Fetching Commits under " + repoName + " repo");
				List<SCMTool> newPage = new ArrayList<SCMTool>();
				List<SCMTool> previousPage = new ArrayList<SCMTool>();
				List<SCMTool> filteredNewPage = new ArrayList<SCMTool>();
				ResponseEntity<String> response = utils.makeRestCall(tempUrl, userName, password);
					if (response != null) {
						
						newPage = processResponse(response, projectName, repoName,url);
						for (SCMTool newCommmit : newPage) {
							boolean flag = true;
							for (SCMTool oldCommit : lastCommitlist) {
								if (newCommmit.getCommitId().equalsIgnoreCase(oldCommit.getCommitId())) {
									flag = false;
								}
							}
							if (flag) {
								filteredNewPage.add(newCommmit);
							}
						}
						commits.addAll(filteredNewPage);
						for (SCMTool commit : filteredNewPage)
							previousPage.add(commit);
					}
					if (newPage.size() > 0) {

						do {
							String lowerBound = previousPage.get(previousPage.size()-1).getCommitTsFormatted();
							tempUrl = url
									+ "/commits?&api-version=6.0&searchCriteria.fromDate=" + lowerBound+"&searchCriteria.showOldestCommitsFirst=true";
							response = utils.makeRestCall(tempUrl, userName, password);
							newPage = processResponse(response, projectName, repoName,url);
							filteredNewPage = new ArrayList<SCMTool>();
							for (SCMTool newCommmit : newPage) {
								boolean flag = true;
								for (SCMTool oldCommit : previousPage) {
									if (newCommmit.getCommitId().equalsIgnoreCase(oldCommit.getCommitId())) {
										flag = false;
									}
								}
								if (flag) {
									filteredNewPage.add(newCommmit);
								}
							}
							commits.addAll(filteredNewPage);
							previousPage = filteredNewPage;

						} while (filteredNewPage.size() > 0);

					}
				java.util.Collections.reverse(commits);
				

			} else {
				tempUrl =  url+"/commits?&api-version=6.0";
				ResponseEntity<String> response = utils.makeRestCall(tempUrl, userName, password);
				List<SCMTool> newPage = new ArrayList<SCMTool>();
				List<SCMTool> previousPage = new ArrayList<SCMTool>();
				List<SCMTool> filteredNewPage = new ArrayList<SCMTool>();
				if (response != null) {
					newPage = processResponse(response, projectName, repoName,url);
					for (SCMTool commit : newPage)
						previousPage.add(commit);
					commits.addAll(newPage);
				}
				if (newPage.size() > 0) {

					do {
						String upperBound = previousPage.get(previousPage.size() - 1).getCommitTsFormatted();
						tempUrl =  url
								+ "/commits?&api-version=6.0&searchCriteria.toDate=" + upperBound;
						response = utils.makeRestCall(tempUrl, userName, password);
						newPage = processResponse(response, projectName, repoName,url);
						filteredNewPage = new ArrayList<SCMTool>();
						for (SCMTool newCommmit : newPage) {
							boolean flag = true;
							for (SCMTool oldCommit : previousPage) {
								if (newCommmit.getCommitId().equalsIgnoreCase(oldCommit.getCommitId())) {
									flag = false;
								}
							}
							if (flag) {
								filteredNewPage.add(newCommmit);
							}
						}
						commits.addAll(filteredNewPage);
						previousPage = filteredNewPage;

					} while (filteredNewPage.size() > 0);

				}

			}
//			LOG.info("Fetching Commits under "+repoName+" repo");
//			ResponseEntity<String> response = utils.makeRestCall(url, userName, password);
//			if (response != null) {
//				List<SCMTool> newCommitts = processResponse(response, projectName, repoName);
//				commits.addAll(newCommitts);
//			}
		}
		return commits;
	}

	private List<SCMTool> processResponse(ResponseEntity<String> response, String projectName, String repoName, String url) {
		List<SCMTool> newCommits = new ArrayList<SCMTool>();
		JSONObject commitJson = utils.parseAsObject(response);
		JSONArray commitArray = commitJson.getJSONArray("value");
		for (int i = 0; i < commitArray.length(); i++) {
			JSONObject commitObject = commitArray.getJSONObject(i);
			SCMTool commit = new SCMTool();
			commit.setProjectName(projectName);
			commit.setRepoName(repoName);
			commit.setCommiter(commitObject.getJSONObject("committer").getString("name"));
			long time = utils.getTimeInMiliseconds(commitObject.getJSONObject("committer").getString("date"));
			commit.setCommitTS(time);
			commit.setTimestamp(time);
			commit.setCommitTsFormatted(commitObject.getJSONObject("committer").getString("date"));
			commit.setCommitLog(commitObject.getString("comment"));
			commit.setScType("Azure Repo");
			commit.setAddition(commitObject.getJSONObject("changeCounts").getInt("Add"));
			commit.setModification(commitObject.getJSONObject("changeCounts").getInt("Edit"));
			commit.setDeletion(commitObject.getJSONObject("changeCounts").getInt("Delete"));
			commit.setCommitId(commitObject.getString("commitId"));
			if (commit.getDeletion() > 0) {
				List<DeletedFileDetails> deletedFiles = getDeletedFiles(repoName, commitObject.getString("commitId"),
						commit.getCommiter(), time,url);
				commit.setDeletedFileDetails(deletedFiles);
			}

			newCommits.add(commit);
		}
		return newCommits;
	}

	private List<SCMTool> getCommitsOnBranches(Map<String, List<String>> repoAndBranches, String projectName) {
		List<SCMTool> commits = new ArrayList<SCMTool>();
		for (Map.Entry<String, List<String>> entry : repoAndBranches.entrySet()) {
			String repoName = entry.getKey();
			List<String> branches = entry.getValue();
			for (String branchName : branches) {
				String url;
				if (last != null) {
					url = instanceurl + "/_apis/git/repositories/" + repoName
							+ "/commits?searchCriteria.compareVersion.version=" + branchName
							+ "&searchCriteria.itemVersion.versionOptions=firstParent&searchCriteria.fromDate=" + last
							+ "&api-version=6.0";
					if (branchName.equalsIgnoreCase("master")) {
						url = instanceurl + "/_apis/git/repositories/" + repoName
								+ "/commits?searchCriteria.compareVersion.version=" + branchName
								+ "&searchCriteria.fromDate=" + last + "&api-version=6.0";
					}
				} else {
					url = instanceurl + "/_apis/git/repositories/" + repoName
							+ "/commits?searchCriteria.compareVersion.version=" + branchName
							+ "&searchCriteria.itemVersion.versionOptions=firstParent&api-version=6.0";
					if (branchName.equalsIgnoreCase("master")) {
						url = instanceurl + "/_apis/git/repositories/" + repoName
								+ "/commits?searchCriteria.compareVersion.version=" + branchName + "&api-version=6.0";
					}

				}

				ResponseEntity<String> response = utils.makeRestCall(url, userName, password);
				if (response != null) {
					JSONObject commitJson = utils.parseAsObject(response);
					JSONArray commitArray = commitJson.getJSONArray("value");
					for (int i = 0; i < commitArray.length(); i++) {

						try {
							JSONObject commitObject = commitArray.getJSONObject(i);
							SCMTool commit = new SCMTool();
							commit.setProjectName(projectName);
							commit.setRepoName(repoName);
							commit.setBranch(branchName);
							commit.setCommiter(commitObject.getJSONObject("committer").getString("name"));
							long time = utils
									.getTimeInMiliseconds(commitObject.getJSONObject("committer").getString("date"));
							commit.setCommitTS(time);
							commit.setCommitLog(commitObject.getString("comment"));
							commit.setScType("Azure Repo");
							commit.setAddition(commitObject.getJSONObject("changeCounts").getInt("Add"));
							commit.setModification(commitObject.getJSONObject("changeCounts").getInt("Edit"));
							commit.setDeletion(commitObject.getJSONObject("changeCounts").getInt("Delete"));
							if (commit.getDeletion() > 0) {
								List<DeletedFileDetails> deletedFiles = getDeletedFiles(repoName,
										commitObject.getString("commitId"), commit.getCommiter(), time,url);
								commit.setDeletedFileDetails(deletedFiles);
							}

							commits.add(commit);
						} catch (Exception e) {
							LOG.error(e.getMessage());
						}
					}

				}
			}
		}
		return commits;
	}

	private List<DeletedFileDetails> getDeletedFiles(String repoName, String commitdId, String committer, long time, String url2) {
		List<DeletedFileDetails> deletedFiles = new ArrayList<DeletedFileDetails>();
		String url = url2 + "/commits/" + commitdId
				+ "/changes?api-version=6.0";
		ResponseEntity<String> response = utils.makeRestCall(url, userName, password);
		if (response != null) {
			JSONObject changeJson = utils.parseAsObject(response);
			JSONArray changeArray = changeJson.getJSONArray("changes");
			for (int i = 0; i < changeArray.length(); i++) {
				JSONObject changeObj = changeArray.getJSONObject(i);
				if (changeObj.getString("changeType").equalsIgnoreCase("delete")) {
					JSONObject item = changeObj.getJSONObject("item");
					String[] path = item.getString("path").split("/");
					String fileName = path[path.length - 1];
					DeletedFileDetails d = new DeletedFileDetails();
					d.setFileName(fileName);
					d.setCommitter(committer);
					d.setDeletedDateTime(time);
					deletedFiles.add(d);
				}
			}
		}
		return deletedFiles;
	}

	private Map<String, List<String>> getBranches(List<String> repos) {
		Map<String, List<String>> repoAndBranches = new HashMap<String, List<String>>();
		for (String repoName : repos) {
			ArrayList<String> branches = new ArrayList<String>();
			String url = instanceurl + "/_apis/git/repositories/" + repoName + "/stats/branches?api-version=6.0";
			ResponseEntity<String> response = utils.makeRestCall(url, userName, password);
			if (response != null) {
				JSONObject branchJson = utils.parseAsObject(response);
				JSONArray branchArray = branchJson.getJSONArray("value");
				for (int i = 0; i < branchArray.length(); i++) {
					String branchName = branchArray.getJSONObject(i).getString("name");
					branches.add(branchName);

				}

			}
			repoAndBranches.put(repoName, branches);
		}
		return repoAndBranches;
	}

	private List<String> getUrls() {
//		List<String> repolist = new ArrayList<String>();
//		//String url = instanceurl + "/_apis/git/repositories?api-version=6.0";
//		ResponseEntity<String> response = utils.makeRestCall(url, userName, password);
//		if (response != null) {
//			JSONObject repoJson = utils.parseAsObject(response);
//			JSONArray repoArray = repoJson.getJSONArray("value");
//			for (int i = 0; i < repoArray.length(); i++) {
//				String repoName = repoArray.getJSONObject(i).getString("name");
//				repolist.add(repoName);
//
//			}
//
//		}
		String [] repos= instanceurl.split(",");
		List<String> urlList= Arrays.asList(repos);
		
		return urlList;
	}

	private void getConfigurations(String projectName) {
		ctx = DataConfig.getContext();
		try {
			mongo = DataConfig.getInstance().mongoTemplate();
		} catch (Exception e) {

			LOG.error("Mongotemplate Error");
		}
		configRepo = ctx.getBean(ConfigurationSettingRep.class);
		ConfigurationSetting configs = configRepo.findByProjectName(projectName).get(0);
		Set<ConfigurationToolInfoMetric> metrics = configs.getMetrics();
		Iterator<ConfigurationToolInfoMetric> iter = metrics.iterator();
		while (iter.hasNext()) {

			ConfigurationToolInfoMetric metric = (ConfigurationToolInfoMetric) iter.next();

			if ("Azure Repo".equals(metric.getToolName())) {
				config = metric;
			}
		}

		instanceurl = config.getUrl();
//		if (instanceurl.charAt(instanceurl.length() - 1) == '/') {
//			instanceurl = instanceurl.substring(0, instanceurl.length() - 1);
//		}
		userName = config.getUserName();
		password = EncryptionDecryptionAES.decrypt(config.getPassword(), ConstantVariable.SECRET_KEY);

//		try {
//			repos = Arrays.asList(config.getProjectCode().split(","));
//
//		} catch (Exception e) {
//			LOG.info("Fetching all repos");
//		}

	}

}
