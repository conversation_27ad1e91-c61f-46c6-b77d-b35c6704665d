package com.bolt.dashboard.bitbucketclient;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.SCMToolRepository;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class BitBucketApplication {

	private static final Logger LOGGER = LogManager.getLogger(BitBucketApplication.class.getName());
	SCMToolRepository repo = null;
	ConfigurationSettingRep configurationRepo = null;
	BitBucketClientImplementation scmToolMetricsimpl = null;
	ConfigurationSetting configurationColection = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	Iterator iter = null;
	ConfigurationToolInfoMetric metric1 = null;
	int retry=0;

	/**
	 * Private Constructor
	 */
//public static void main(String[] args) {
//	new BitBucketApplication().bitBucketMain("Benefit Builder");
//}
	public BitBucketApplication() {

	}

	public void bitBucketMain(String projectName) {
		LOGGER.info("Bit Bucket collector starts for " + projectName);
		repo = DataConfig.getContext().getBean(SCMToolRepository.class);
		String instanceURL = "";
		String branch = "master";
		String user = null;
		String pass = null;
		int getFirstRunHistoryDays = 7;
		boolean firstRun = false;
		String scType="BITBUCKET";
		String result="SUCCESS";
		scmToolMetricsimpl = new BitBucketClientImplementation();
		configurationRepo = DataConfig.getContext().getBean(ConfigurationSettingRep.class);
		configurationColection = configurationRepo.findByProjectName(projectName).get(0);

		metric = configurationColection.getMetrics();
		iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
			if ("BITBUCKET".equals(metric1.getToolName())) {
				instanceURL = metric1.getUrl();
				user = metric1.getUserName();
					pass=EncryptionDecryptionAES.decrypt(metric1.getPassword(),ConstantVariable.SECRET_KEY);
				break;
			}

		}
          String urlArray[]=instanceURL.split(",");
		try {
			for(int i=0;i<urlArray.length;i++) {
			List<SCMTool> scmTool = scmToolMetricsimpl.getCommits(urlArray[i], repo, firstRun, branch,
					getFirstRunHistoryDays, user, pass, projectName);
			if (!scmTool.isEmpty()) {
				Collections.sort(BitbucketFailureHandler.SCMDATA);
			repo.save(BitbucketFailureHandler.SCMDATA);
			}
				//repo.save(scmTool);
			retry=0;
			}
			cleanObject();
		} catch (Exception e) {
			
			result="FAIL";
			if(retry<3) {
				System.out.println(BitbucketFailureHandler.URLS.size());
				retry++;
			bitBucketMain(projectName);
			
			}
			else {
				Collections.sort(BitbucketFailureHandler.SCMDATA);
				repo.save(BitbucketFailureHandler.SCMDATA);
			}
			ConstantVariable.getLastRun(projectName, scType, new Date().getTime(), result);
			
			cleanObject();
			LOGGER.error(e.getMessage());
			LOGGER.info(e.getStackTrace());
			LOGGER.info(e);
			cleanObject();
			LOGGER.info("Bit Bucket collector failed for " + projectName);
		}
		ConstantVariable.getLastRun(projectName, scType,new Date().getTime(), result);
		LOGGER.info("Bit Bucket collector ended for " + projectName);
	}

	public void cleanObject() {

		repo = null;
		configurationRepo = null;
		scmToolMetricsimpl = null;
		configurationColection = null;
		metric = null;
		iter = null;
		metric1 = null;
	}
}
