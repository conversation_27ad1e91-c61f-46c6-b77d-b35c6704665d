package com.bolt.dashboard.bitbucketclient;

import java.util.List;

import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.SCMToolRepository;

public interface BitBucketClient {
    public List<SCMTool> getCommits(String url, SCMToolRepository repo, boolean firstRun, String branch,
            int getFirstRunHistoryDays, String user, String pass, String projectName) throws BitBucketExceptions;

}