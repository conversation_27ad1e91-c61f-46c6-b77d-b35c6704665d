package com.bolt.dashboard.bitbucketclient;

import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.codec.binary.Base64;
import org.apache.http.ParseException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.DeletedFileDetails;
import com.bolt.dashboard.core.model.FileDetails;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.SCMToolRepository;
import com.google.common.base.CharMatcher;

/**
 * <AUTHOR>
 *
 */
@Component
public class BitBucketClientImplementation implements BitBucketClient {

	private static final String SEGMENT_API = "/api/v3/repos/";
	private static final String PUBLIC_BITBUCKET_REPO_HOST = "api.bitbucket.org/2.0/repositories";
	private static final String PUBLIC_BITBUCKET_HOST_NAME = "bitbucket.org";
	private static final String SCMTYPE = "BITBUCKET";
	private static final String INSERTION_STRING = "insertions(+)";
	private static final String DELETION_STRING = "deletions(-)";
	private static final Logger LOGGER = LogManager.getLogger(BitBucketClientImplementation.class);
	private RestOperations rest;
	JSONObject patch = null;
	String patchUrl = null, hashUrl = null;
	private String statsURL;
	SCMTool tool = null;
	int sindex, eindex;
	String changesinfile = null;

	private static long time = 0;
	private static long newscmCommitTimestamp;

	public BitBucketClientImplementation() {
		this.rest = get();
	}

	@SuppressWarnings("unused")
	private Boolean bool(JSONObject json, String key) {
		Object obj = json.get(key);
		return obj == null ? null : Boolean.valueOf(obj.toString());
	}

	@SuppressWarnings("unused")
	private BigDecimal decimal(JSONObject json, String key) {
		Object obj = json.get(key);
		return obj == null ? null : new BigDecimal(obj.toString());
	}

	private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000);
		requestFactory.setReadTimeout(20000);
		return new RestTemplate(requestFactory);
	}

	@SuppressWarnings("unused")
	private Integer integer(JSONObject json, String key) {
		Object obj = json.get(key);
		return obj == null ? null : (Integer) obj;
	}

	private String str(JSONObject json, String key) {
		Object obj = json.get(key);
		return obj == null ? null : obj.toString();
	}

	public List<SCMTool> getCommits(String baseUrl, SCMToolRepository repo, boolean firstRun, String branch,
			int getFirstRunHistoryDays, String user, String pass, String pName) throws BitBucketExceptions {

		List<SCMTool> scmtool = new ArrayList<>();
		String apiUrl = baseUrl;

		if (apiUrl.endsWith(".git")) {
			apiUrl = apiUrl.substring(0, apiUrl.lastIndexOf(".git"));
		}
		URL url = null;
		String hostName = "";
		String protocol = "";
		try {
			url = new URL(apiUrl);
			hostName = url.getHost();
			protocol = url.getProtocol();
		} catch (MalformedURLException e) {
			LOGGER.error(e.getMessage());
			throw new BitBucketExceptions();
		}

		String hostUrl = protocol + "://" + hostName + "/";
		String repoName = apiUrl.substring(hostUrl.length(), apiUrl.length());

		if (hostName.startsWith(PUBLIC_BITBUCKET_HOST_NAME)) {
			apiUrl = protocol + "://" + PUBLIC_BITBUCKET_REPO_HOST + "/" + repoName;
		} else {
			apiUrl = protocol + "://" + hostName + SEGMENT_API + repoName;

			LOGGER.debug("API URL IS:" + apiUrl);
		}
		statsURL = apiUrl + "/commits/";
		Date dt = null;
		if (firstRun) {

			if (getFirstRunHistoryDays > 0) {
				dt = getDate(new Date(), -getFirstRunHistoryDays, 0);

			}
		} else {
			dt = getDate(new Date(), -1, 0);

		}
		Calendar calendar = new GregorianCalendar();
		TimeZone timeZone = calendar.getTimeZone();
		Calendar cal = Calendar.getInstance(timeZone);
		cal.setTime(dt);
		String thisMoment = String.format("%tFT%<tRZ", cal, cal);
		String queryUrl = apiUrl.concat("/commits?sha=" + branch + "&since=" + thisMoment + "&pagelen=100");

		boolean lastPage = false;
		int pageNumber = 1;
		String queryUrlPage = queryUrl;

		List<SCMTool> collection = repo.findByScTypeAndProjectName(SCMTYPE, pName);
		if (!collection.isEmpty())
			time = collection.get(collection.size() - 1).getCommitTS();

		while (!lastPage) {
			try {
				ResponseEntity<String> response = makeRestCall(queryUrlPage, user, pass);
				JSONObject jsonObjectnew = parseAsArray(response);

				JSONArray jsonArray = (JSONArray) jsonObjectnew.get("values");
				if (jsonArray.length() != 0) {
					JSONObject obj = (JSONObject) jsonArray.get(0);
					newscmCommitTimestamp = getNextChangeSetID(obj);
				}
				if (newscmCommitTimestamp <= time) {
					LOGGER.info("No ChangeSet to be stored    ");
					lastPage = true;
				} else {

					for (int i = 0; i < jsonArray.length(); i++) {
						try {

							SCMTool commit;
							int totalAdditions = 0;
							int totalDeletions = 0;
							int totalModifications = 0;
							String noofDeletions = null, noofInsertions = null;
							int indexOfInsertion, indexOfDeletion;

							JSONObject jsonObject = (JSONObject) jsonArray.get(i);
							String revision = str(jsonObject, "hash");
							hashUrl = statsURL.concat(revision);
							JSONObject author = (JSONObject) jsonObject.get("author");
							String[] authornew = str(author, "raw").split(" ");
							String authorname = authornew[0];

							String message = str(jsonObject, "message");
							List<DeletedFileDetails> deletedFileDetailsList = new ArrayList<DeletedFileDetails>();
							JSONObject links = (JSONObject) jsonObject.get("links");
							long currentTimestamp = ConstantVariable.timestamp(jsonObject.get("date"),pName);
							if (currentTimestamp <= time) {
								lastPage = true;
								break;
							}

							findPatch(links);
							if (patch != null) {
								commit = new SCMTool();
								patchUrl = str(patch, "href");
								ResponseEntity<String> patchresponse = makeRestCall(patchUrl, user, pass);
								int beginIndex = patchresponse.toString().indexOf("---");
								int endIndex = patchresponse.toString().indexOf("diff --git");
								int indexvalueofchangesline = patchresponse.toString().indexOf("changed,");
								String checkedString = patchresponse.toString().substring(indexvalueofchangesline,
										endIndex);
								int y = beginIndex + 3;

								if (checkedString.contains(INSERTION_STRING)
										|| checkedString.contains(DELETION_STRING)) {
									indexOfInsertion = patchresponse.toString().indexOf(INSERTION_STRING);
									if (checkedString.contains(INSERTION_STRING)) {

										String[] noofinsertionsInFile = patchresponse.toString()
												.substring(indexvalueofchangesline, indexOfInsertion).split(" ");
										noofInsertions = noofinsertionsInFile[1];
									}
									if (checkedString.contains(DELETION_STRING)) {
										if (checkedString.contains(INSERTION_STRING)) {
											indexOfDeletion = patchresponse.toString().indexOf(DELETION_STRING);
											String[] noOfDeletionInFile = patchresponse.toString()
													.substring(indexOfInsertion, indexOfDeletion).split(" ");
											noofDeletions = noOfDeletionInFile[1];
										} else {
											indexOfDeletion = patchresponse.toString().indexOf(DELETION_STRING);
											String[] noOfDeletionInNewFile = patchresponse.toString()
													.substring(indexvalueofchangesline, indexOfDeletion).split(" ");
											noofDeletions = noOfDeletionInNewFile[1];
										}
									}
								}
								if (checkedString.contains("delete mode")) {
									String[] deletedFiles = checkedString.split("delete mode");
									getDeletedFileDetails(deletedFiles, authorname, currentTimestamp,
											deletedFileDetailsList);

								}

								String nameoffilechanged = patchresponse.toString().substring(y,
										indexvalueofchangesline);
								String[] arrayofchangesfile = nameoffilechanged.split("\n");
								int countlengthofchangedfiles = arrayofchangesfile.length - 1;
								String value;
								List<FileDetails> filesChangedDetails = new ArrayList<FileDetails>();
								for (int p = 1; p < countlengthofchangedfiles; p++) {
									FileDetails fileDetailsofSinglefile = new FileDetails();
									value = arrayofchangesfile[p];
									if (!(value.contains("bytes")) && !value.isEmpty()) {
										String fileName = getFileName(value);
										if (!(fileName == null))
											fileDetailsofSinglefile.setFilename(fileName);

										if (changesinfile.contains("+-")) {
											int modificationinFile = Integer
													.parseInt(CharMatcher.DIGIT.retainFrom(changesinfile));

											fileDetailsofSinglefile.setModificationinFile(modificationinFile);

										} else if (!(changesinfile.contains("+-"))) {
											if (changesinfile.contains("-") && (changesinfile.matches("[a-zA-Z]")
													|| changesinfile.matches("[0-9]"))) {
												int deletioninFile = Integer
														.parseInt(CharMatcher.DIGIT.retainFrom(changesinfile));
												fileDetailsofSinglefile.setDeletioninFile(deletioninFile);

											} else if (changesinfile.matches("[a-zA-Z]")
													|| changesinfile.matches("[0-9]")) {
												int insertioninFile = Integer
														.parseInt(CharMatcher.DIGIT.retainFrom(changesinfile));
												fileDetailsofSinglefile.setInsertioninFile(insertioninFile);

											}

										}
									}
									filesChangedDetails.add(fileDetailsofSinglefile);
								}

								String filechanges = patchresponse.toString().substring(y, endIndex);
								Pattern p = Pattern.compile("create");
								Pattern q = Pattern.compile("delete");
								Matcher m = p.matcher(filechanges);
								Matcher n = q.matcher(filechanges);
								while (m.find()) {
									totalAdditions++;
								}
								while (n.find()) {
									totalDeletions++;
								}
								totalModifications = (arrayofchangesfile.length - 2)
										- (totalAdditions + totalDeletions);
								long timestamp = new DateTime(str(jsonObject, "date")).getMillis();
								if (timestamp > time) {
									int totalChange = totalAdditions + totalDeletions + totalModifications;
									commit.setScType(SCMTYPE);
									commit.setTimestamp(System.currentTimeMillis());
									commit.setRevisionNo(revision);
									commit.setUrl(patchUrl);
									commit.setCommiter(authorname);
									commit.setCommitLog(message);
									commit.setCommitTS(timestamp);
									commit.setNoOfChanges(totalChange);
									commit.setModification(totalModifications);
									commit.setAddition(totalAdditions);
									commit.setDeletion(totalDeletions);
									commit.setFileDetails(filesChangedDetails);
									commit.setInsertionInCommit(noofInsertions);
									commit.setDeletioninfileInCommit(noofDeletions);
									commit.setProjectName(pName);
									if (!deletedFileDetailsList.isEmpty()) {
										commit.setDeletedFileDetails(deletedFileDetailsList);
									}

									scmtool.add(commit);
								} else {
									break;
								}

							} else {
								commit = new SCMTool();
								long timestampnew = new DateTime(str(jsonObject, "date")).getMillis();
								commit.setScType(SCMTYPE);
								commit.setTimestamp(System.currentTimeMillis());
								commit.setUrl(patchUrl);
								commit.setCommiter(authorname);
								commit.setCommitLog(message);
								commit.setCommitTS(timestampnew);
								commit.setProjectName(pName);
								scmtool.add(commit);
							}

						} catch (Exception e) {
							LOGGER.info(e.getStackTrace());
							LOGGER.info("Records Collected "+scmtool.size());
							Collections.sort(scmtool);
							repo.save(scmtool);
							throw new BitBucketExceptions(e);
						}

					}
				}
				if (jsonArray == null || jsonArray.length() == 0) {
					lastPage = true;
				} else {

					pageNumber++;
					queryUrlPage = queryUrl + "&page=" + pageNumber;
				}

			} catch (Exception re) {
				LOGGER.error(re.getMessage() + ":" + queryUrl);
				
				lastPage = true;
				throw new BitBucketExceptions(re);

			}
		}
		Collections.sort(scmtool);
		return scmtool;

	}

	@SuppressWarnings({})
	public long getNextChangeSetID(JSONObject json) {
		newscmCommitTimestamp = new DateTime(json.get("date")).getMillis();
		return newscmCommitTimestamp;

	}

	private String getFileName(String path) {
		String className = null;
		String fileName = null;
		String[] newpath = path.replaceAll("\\s+", "").split("\\|");
		String[] fileSeparationString = null;
		String[] classSeparationString = null;
		changesinfile = newpath[newpath.length - 1];
		if (newpath[0].contains("/")) {
			fileSeparationString = newpath[0].split(Pattern.quote("/"));

		} else {
			return newpath[0];
		}

		if (!(fileSeparationString.length == 2)) {
			className = fileSeparationString[fileSeparationString.length - 1];
			classSeparationString = className.split(Pattern.quote("."));
			if ((classSeparationString.length == 2) && (!(classSeparationString[0].equals(" ")))) {
				fileName = className;
			}
		} else {
			fileName = fileSeparationString[1];
			return fileName;
		}
		return fileName;

	}

	private Date getDate(Date dateInstance, int offsetDays, int offsetMinutes) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dateInstance);
		cal.add(Calendar.DATE, offsetDays);
		cal.add(Calendar.MINUTE, offsetMinutes);
		return cal.getTime();
	}

	private ResponseEntity<String> makeRestCall(String url, String userId, String password) {
		// Basic Auth only.
		if (!"".equals(userId) && !"".equals(password)) {
			return get().exchange(url, HttpMethod.GET, new HttpEntity<>(createHeaders(userId, password)), String.class);

		} else {
			return get().exchange(url, HttpMethod.GET, null, String.class);
		}

	}

	private JSONObject parseAsArray(ResponseEntity<String> response) {
		try {
			return (JSONObject) new JSONTokener(response.getBody()).nextValue();

		} catch (ParseException pe) {
			LOGGER.error(pe.getMessage());
		}
		return new JSONObject();
	}

	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}

	private void findPatch(JSONObject links) {
		try {
			patch = (JSONObject) links.get("patch");
		} catch (JSONException e) {
			
			LOGGER.error("-- Merge is happening here --");
			patchUrl = hashUrl;
		}
	}

	private void getDeletedFileDetails(String[] data, String committer, long commitTime,
			List<DeletedFileDetails> deletedFileDetailsList) {

		for (int i = 1; i < data.length; i++) {
			DeletedFileDetails info = new DeletedFileDetails();
			String filename = data[i].substring(data[i].lastIndexOf("/") + 1, data[i].length());
			info.setFileName(filename.trim());
			info.setCommitter(committer);
			info.setDeletedDateTime(commitTime);
			deletedFileDetailsList.add(info);
		}

	}
}