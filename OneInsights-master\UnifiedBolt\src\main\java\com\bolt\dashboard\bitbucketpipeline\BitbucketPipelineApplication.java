package com.bolt.dashboard.bitbucketpipeline;

import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.atlassian.jira.rest.client.api.RestClientException;
import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.repository.BuildToolRep;


public class BitbucketPipelineApplication {

	
	
	private static final Logger LOG = LogManager.getLogger(BitbucketPipelineApplication.class);
	AnnotationConfigApplicationContext applicationContext = null;
	BuildToolRep repo = null;
	BitbucketPipeline bitBucketPipelineMetrics = null;
	String buildType = "BITBUCKET Pipeline";

	
	/*
	 * public static void main(String[] args) { new
	 * BitbucketPipelineApplication().bitbucketPipelineMain("Integration Layer"); }
	 */
	 
	
	public void bitbucketPipelineMain(String projectName) throws RestClientException {
		LOG.info("Bitbucket Pipeline Collector started for " + projectName);
		applicationContext = DataConfig.getContext();
		repo = applicationContext.getBean(BuildToolRep.class);
	
		bitBucketPipelineMetrics = new BitbucketPipelineImplementation();
		try {
			bitBucketPipelineMetrics.getBuildToolData(repo, projectName);
			ConstantVariable.getLastRun(projectName, buildType, new Date().getTime(), ConstantVariable.COLLECTOR_STATUS_SUCCESS);
		} catch (Exception e) {
			
			ConstantVariable.getLastRun(projectName, buildType, new Date().getTime(), ConstantVariable.COLLECTOR_STATUS_FAILURE);
			cleanObject();
			LOG.error(e.getMessage());
			LOG.error("Bitbucket Pipeline Exception ", e.fillInStackTrace());

			LOG.info("Bitbucket Collector failed for " + projectName);
		}
		cleanObject();
		LOG.info("Bitbucket Collector ended for " + projectName);
	}

	public void cleanObject() {
		repo = null;
		bitBucketPipelineMetrics = null;
	}
	
	
	
}
