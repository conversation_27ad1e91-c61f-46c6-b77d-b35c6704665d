package com.bolt.dashboard.bitbucketpipeline;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.json.JSONArray;
import org.json.JSONException;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.BuildFailurePatternForProjectInJenkinsModel;
import com.bolt.dashboard.core.model.BuildFailurePatternMetrics;
import com.bolt.dashboard.core.model.BuildSteps;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.repository.BuildFailurePatternForProjectRepo;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.sonar.SonarApplication;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class BitbucketPipelineImplementation implements BitbucketPipeline {
	private static final Logger LOG = LogManager.getLogger(BitbucketPipelineImplementation.class);

	AnnotationConfigApplicationContext ctx = null;
	String projectName = "";
	String userName = "";
	String password = "";
	List<String> jobsList = new ArrayList<>();
	BuildToolRep repo = null;
	List<String> jobCollection = new ArrayList<>();
	String pipelineUrl = null;
	String bitbucketUrl = null;
	String stepUrl = null;
	int size = 0;
	int pagelen = 100;
	int page = 1;
	int lastPage = 0;
	double lastBuildid;
	String pipeline_uuid = null;
	List<BuildTool> builds = new ArrayList<>();
	BuildTool build;
	List<BuildSteps> stepsList;
	BuildFailurePatternForProjectRepo failurePatternRepo = null;

	public void getBuildToolData(BuildToolRep repo, String projectName) {
		
		this.projectName = projectName;
		this.repo = repo;
		getConfigurationDetails();
		ResponseEntity<String> response = makeRestCall(bitbucketUrl + "/pipelines/");

		BuildTool tool = repo.findOneByNameOrderByBuildIDDesc(projectName);
		if (tool != null) {
			LOG.info("Already Collected Build " + tool.getBuildID() + " for ProjectName " + projectName);
			lastBuildid = tool.getBuildID();
			page = (int) Math.ceil(lastBuildid / 100);
		} else {
			lastBuildid = 0;
			page = 1;
		}
		// repo.delete(repo.findByName(projectName));
		try {
			JSONObject jsonObject = parseAsNewArray(response);
			double size = (int) jsonObject.get("size");
			lastPage = (int) Math.ceil((size / 100));
			while (page <= lastPage) {
				builds = new ArrayList<>();
				pipelineUrl = bitbucketUrl + "/pipelines/?page=" + page + "&pagelen=" + pagelen;
				response = makeRestCall(pipelineUrl);
				jsonObject = parseAsNewArray(response);
				JSONArray valueArray = (JSONArray) jsonObject.get("values");
				processPipelineData(valueArray);

				repo.save(builds);
				LOG.info((((page - 1) * 100) + builds.size()) + " builds are saved:" + this.projectName);
				page++;
			}
		} catch (Exception e) {
			LOG.error(e.getMessage());
		}

	}

	private void processPipelineData(JSONArray pipelineValues) {
		ResponseEntity<String> response = null;
		JSONObject jsonObject = null;
		try {
			for (int i = 0; i < pipelineValues.length(); i++) {
				JSONObject pipeline_Obj = pipelineValues.getJSONObject(i);
				build = new BuildTool();
				build.setBuildID(pipeline_Obj.optInt("build_number"));
				LOG.info("Build Number " + build.getBuildID());
				if (build.getBuildID() <= lastBuildid)
					continue;

				JSONObject tempJson = (JSONObject) pipeline_Obj.opt("target");
				build.setBranchName(tempJson.optString("ref_name"));
				build.setBuildType("BITBUCKET");
				tempJson = (JSONObject) pipeline_Obj.opt("creator");
				build.setCreatedBy(tempJson.optString("display_name"));
				build.setName(projectName);
				JSONObject trigger = (JSONObject) pipeline_Obj.opt("trigger");
				build.setTriggerType(trigger.optString("name"));
				String temp_date = null;
				if (pipeline_Obj.has("created_on")) {
					temp_date = pipeline_Obj.optString("created_on");
				}
				long createTime = 0;
				if (temp_date != null) {
					createTime = getTimeInMiliseconds(temp_date);
				}
				build.setTimestamp(createTime);
				temp_date = null;
				if (pipeline_Obj.has("completed_on")) {
					temp_date = pipeline_Obj.getString("completed_on");
				}
				long completeTime = 0;
				if (temp_date != null) {
					completeTime = getTimeInMiliseconds(temp_date);
				}
				BuildToolMetric durationMetric = new BuildToolMetric("duration");
				long duration = pipeline_Obj.optLong("duration_in_seconds") * 1000;
				durationMetric.setValue(duration);
				BuildToolMetric resultMetric = new BuildToolMetric("result");
				tempJson = (JSONObject) pipeline_Obj.get("state");
				if (tempJson.has("result")) {
					tempJson = tempJson.getJSONObject("result");
				}
				resultMetric.setValue(null);
				if (tempJson != null) {
					resultMetric.setValue(tempJson.optString("name"));
				}

				build.getMetrics().add(resultMetric);
				build.getMetrics().add(durationMetric);

				BuildToolMetric timestampMetric = new BuildToolMetric("timestamp");
				timestampMetric.setValue(createTime);
				build.getMetrics().add(timestampMetric);

				this.pipeline_uuid = pipeline_Obj.getString("uuid");
				processStepsData(this.pipeline_uuid);

				builds.add(build);
			}
		} catch (JSONException ex) {
			
			LOG.error(ex.getMessage());

			LOG.error("Bitbucket Pipeline Pipeline JSON parsing Error ", ex.fillInStackTrace());
		}

	}

	private void processStepsData(String pipeline_uuid2) {
		
		stepUrl = bitbucketUrl + "/pipelines/" + this.pipeline_uuid + "/steps/";

		ResponseEntity<String> response = makeRestCall(stepUrl);
		if(response != null) {
			JSONObject jsonObject = parseAsNewArray(response);
			JSONArray stepsValues = (JSONArray) jsonObject.get("values");
			stepsList = new ArrayList<BuildSteps>();
			for (int j = 0; j < stepsValues.length(); j++) {
				BuildSteps tempSteps = new BuildSteps();
				try {
					JSONObject stepsObj = stepsValues.getJSONObject(j);
					JSONObject tempJson = stepsObj.getJSONObject("state");
					tempSteps.setDuration(stepsObj.optLong("duration_in_seconds") * 1000);
					String completeDate = null;
					if (stepsObj.has("completed_on")) {
						completeDate = stepsObj.getString("completed_on");
					}
					long tempDate = 0;

					if (completeDate != null) {
						tempDate = getTimeInMiliseconds(completeDate);
					}
					tempSteps.setCompletedTime(tempDate);
					completeDate = null;
					if (stepsObj.has("started_on")) {
						completeDate = stepsObj.getString("started_on");
					}
					if (completeDate != null) {
						tempDate = getTimeInMiliseconds(completeDate);
					}
					tempSteps.setStartedTime(tempDate);

					tempSteps.setStepName(stepsObj.optString("name"));
					if (tempJson != null && tempJson.has("result")) {
						tempJson = tempJson.getJSONObject("result");
						String result = tempJson.optString("name");
						if (result.toLowerCase().equals("failed")) {
							processFailure(stepUrl, stepsObj.getString("uuid"));
						}
						tempSteps.setResult(tempJson.optString("name"));
					}
				} catch (JSONException e) {
					
					LOG.error(e.getMessage());
					LOG.error("Bitbucket Pipeline steps JSON parsing Error ", e.fillInStackTrace());
				}
				stepsList.add(tempSteps);
			}
		}
		
		build.setStepsList(stepsList);
	}

	private void processFailure(String stepUrl2, String uuid) {
		
		ctx = DataConfig.getContext();
		failurePatternRepo = ctx.getBean(BuildFailurePatternForProjectRepo.class);
		List<BuildFailurePatternForProjectInJenkinsModel> failurePattern = failurePatternRepo
				.findByProjectName(projectName);
		if (!failurePattern.isEmpty()) {
			BuildFailurePatternForProjectInJenkinsModel tempBuildFailure = failurePattern.get(0);
			List<BuildFailurePatternMetrics> failureMetrics = tempBuildFailure.getPatternMetrics();
			String url = stepUrl2 + uuid + "/log";

			ResponseEntity<String> logResponseData = makeRestCall(url);
			if(logResponseData != null) {
				
			
			String failureLog = logResponseData.getBody();
			for (BuildFailurePatternMetrics temp : failureMetrics) {
				if (failureLog.contains(temp.getPatternDefined())) {
					temp.setPatternCount(temp.getPatternCount() + 1);
				}
			}
			}
			failurePatternRepo.save(tempBuildFailure);
		}
	}

	private void getValueFromJson(JSONObject pipeline_Obj, String string) {
		

	}

	private long getTimeInMiliseconds(String temp_date) {
		
		String[] splitDate = temp_date.split("T");
		String[] temp = splitDate[1].split("\\.");
		temp = temp[0].split("Z");
		// temp=temp_date.split(".");
		String tempTime = splitDate[0] + " " + temp[0];

		DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
		DateTime createdDate = fmt.parseDateTime(tempTime);
		return createdDate.getMillis();
	}

	private JSONObject parseAsNewArray(ResponseEntity<String> response) {
		return (JSONObject) new JSONTokener(response.getBody()).nextValue();
	}

	private ResponseEntity<String> makeRestCall(String pipelineUrl) {
		
		if (!"".equals(this.userName) && !"".equals(this.password)) {
			try {
				UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(pipelineUrl);
				UriComponents uriComponents = builder.build();
				URI uri = uriComponents.toUri();
				return get().exchange(uri, HttpMethod.GET, new HttpEntity<>(createHeaders(userName, password)),
						String.class);
			} catch (Exception e) {
				LOG.error(e.getMessage());
				LOG.error("Bitbucket Pipeline API ERROR", e.fillInStackTrace());
				return null;
			}
		} else {
			try {
				return get().exchange(pipelineUrl, HttpMethod.GET, null, String.class);
			} catch (Exception e) {
				LOG.error(e.getMessage());
				LOG.info("Error in Bitbucket API URL " + pipelineUrl);
				LOG.error("Bitbucket Pipeline API ERROR", e.fillInStackTrace());
				return null;
			}

		}
	}

	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}

	public void getConfigurationDetails() {
		ctx = DataConfig.getContext();
		ConfigurationSettingRep configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		ConfigurationSetting configurationColection = configurationRepo.findByProjectName(projectName).get(0);
		// JobDetailsRepo jobDetailsRepo = ctx.getBean(JobDetailsRepo.class);
		// JobDetails jobDetails = jobDetailsRepo.findByProjectName(projectName);
		/*
		 * if (jobDetails != null) { List<JobDetailsMetrics> jobDetailsMetrics =
		 * jobDetails.getMetrics(); Iterator jobDetailsIter =
		 * jobDetailsMetrics.iterator(); while (jobDetailsIter.hasNext()) {
		 * 
		 * Object jobDetail = jobDetailsIter.next(); JobDetailsMetrics obj =
		 * (JobDetailsMetrics) jobDetail; String jobName = obj.getJobName();
		 * jobsList.add(jobName); } }
		 */

		Set<ConfigurationToolInfoMetric> metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
			// LOG.info("Tool name " + metric1.getToolName());
			if ("BITBUCKET Pipeline".equals(metric1.getToolName())) {
				LOG.info("URL  " + metric1.getUrl());
				bitbucketUrl = metric1.getUrl();
				userName = metric1.getUserName();
					password=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				LOG.info("URL for Bitbucket Pipelines " + bitbucketUrl);
				break;
			}

		}
		String[] jobNameArray = null;

	}

	private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000);
		requestFactory.setReadTimeout(20000);
		return new RestTemplate(requestFactory);
	}

}
