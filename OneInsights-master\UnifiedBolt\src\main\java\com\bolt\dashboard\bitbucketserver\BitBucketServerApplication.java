package com.bolt.dashboard.bitbucketserver;

import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.SCMToolRepository;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class BitBucketServerApplication {

	private static final Logger LOGGER = LogManager.getLogger(BitBucketServerApplication.class.getName());
	AnnotationConfigApplicationContext ctx = null;
	SCMToolRepository repo = null;
	BitBucketServerClientImplementation scmToolMetricsimpl = null;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configurationColection = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	Iterator iter = null;
	ConfigurationToolInfoMetric metric1 = null;

	/**
	 * Private Constructor
	 */
	public BitBucketServerApplication() {

	}
	
	public void bitBucketServerMain(String projectName) {
		LOGGER.info("Bit Bucket collector starts for " + projectName);
		ctx = DataConfig.getContext();
		repo = ctx.getBean(SCMToolRepository.class);
		String instanceURL = "";
		String user = null;
		String pass = null;
		int getFirstRunHistoryDays = 7;
		String projectCode = null;
		boolean firstRun = false;
		String repoName = "";
		String scType = "BIT Server";
		String result = "SUCCESS";
		scmToolMetricsimpl = new BitBucketServerClientImplementation();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configurationColection = configurationRepo.findByProjectName(projectName).get(0);

		metric = configurationColection.getMetrics();
		iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
			if ("BITBUCKET".equals(metric1.getToolName()) || "BIT Server".equals(metric1.getToolName())) {
				instanceURL = metric1.getUrl();
				user = metric1.getUserName();
				
					pass=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				
				projectCode = metric1.getProjectCode();
				repoName = metric1.getRepoName();
				break;
			}

		}

		try {

			List<SCMTool> scmTool = scmToolMetricsimpl.getCommits(instanceURL, repo, firstRun, getFirstRunHistoryDays,
					user, pass, projectName, projectCode, repoName);
			repo.save(scmTool);
			cleanObject();
		} catch (Exception e) {
			result = "FAIL";
			ConstantVariable.getLastRun(projectName, scType, new Date().getTime(), result);
			LOGGER.error(e.getMessage());
			LOGGER.info(e.getStackTrace());
			LOGGER.info(e);
			cleanObject();
			LOGGER.info("Bit Bucket collector failed for  " + projectName);

		}
		ConstantVariable.getLastRun(projectName, scType, new Date().getTime(), result);
		LOGGER.info("Bit Bucket collector ended for " + projectName);
	}

	public void cleanObject() {

		repo = null;
		scmToolMetricsimpl = null;
		configurationRepo = null;
		configurationColection = null;
		metric = null;
		iter = null;
		metric1 = null;
	}
}
