package com.bolt.dashboard.bitbucketserver;

import java.util.List;

import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.SCMToolRepository;

public interface BitBucketServerClient {
	public List<SCMTool> getCommits(String url, SCMToolRepository repo, boolean firstRun, int getFirstRunHistoryDays,
			String user, String pass, String projectName, String projectCode, String repoName)
					throws BitBucketExceptions;

}