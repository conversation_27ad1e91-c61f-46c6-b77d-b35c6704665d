package com.bolt.dashboard.bitbucketserver;

import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.TimeZone;

import org.apache.commons.codec.binary.Base64;

import org.apache.http.ParseException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.model.DeletedFileDetails;
import com.bolt.dashboard.core.model.FileDetails;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.SCMToolRepository;

/**
 * <AUTHOR>
 *
 */
@Component
public class BitBucketServerClientImplementation implements BitBucketServerClient {

	private static final String SEGMENT_API = "/api/v3/repos/";
	private static final String PUBLIC_BITBUCKET_REPO_HOST = "/rest/api/1.0/projects/";

	private static final Logger LOG = LogManager.getLogger(BitBucketServerClientImplementation.class);
	@SuppressWarnings("unused")
	private RestOperations rest;
	@SuppressWarnings("unused")
	private String statsURL;
	List<SCMTool> scmtool = new ArrayList<>();
	int sindex, eindex;
	String changesinfile = null;
	String projectName = "";
	private static long time = 0;
	private static long newscmCommitTimestamp;
	String authorname, message;
	long commitedTimestamp;
	List<DeletedFileDetails> list = null;
	int linesAdded;
	int linesesModified;
	int linesDeleted;
	int totalChanges;

	public BitBucketServerClientImplementation() {
		this.rest = get();
	}

	public Boolean bool(JSONObject json, String key) {
		Object obj = json.get(key);
		return obj == null ? null : Boolean.valueOf(obj.toString());
	}

	public BigDecimal decimal(JSONObject json, String key) {
		Object obj = json.get(key);
		return obj == null ? null : new BigDecimal(obj.toString());
	}

	public RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(100000);
		requestFactory.setReadTimeout(100000);
		return new RestTemplate(requestFactory);
	}

	public Integer integer(JSONObject json, String key) {
		Object obj = json.get(key);
		return obj == null ? null : (Integer) obj;
	}

	public String str(JSONObject json, String key) {
		Object obj = json.get(key);
		return obj == null ? null : obj.toString();
	}

	public List<SCMTool> getCommits(String baseUrl, SCMToolRepository repo, boolean firstRun,
			int getFirstRunHistoryDays, String user, String pass, String projectName, String projectCode,
			String repositoryName) throws BitBucketExceptions {
		this.projectName = projectName;
		String apiUrl = baseUrl;

		if (apiUrl.endsWith(".git")) {
			apiUrl = apiUrl.substring(0, apiUrl.lastIndexOf(".git"));
		}
		URL url = null;
		String hostName = "";
		String protocol = "";
		String port = null;
		String hostUrl = null;
		try {
			url = new URL(apiUrl);
			hostName = url.getHost();
			protocol = url.getProtocol();
			port = String.valueOf(url.getPort());
		} catch (MalformedURLException e) {
			LOG.error(e.getMessage());
			throw new BitBucketExceptions();
		}

		hostUrl = protocol + "://" + hostName + ":" + port + "/";
		String repoName = apiUrl.substring(hostUrl.length(), apiUrl.length());

		apiUrl = protocol + "://" + hostName + PUBLIC_BITBUCKET_REPO_HOST + projectCode + "/repos/" + repositoryName;

		LOG.debug("API URL IS:" + apiUrl);
		if (!(Integer.parseInt(port) == -1)) {
			apiUrl = protocol + "://" + hostName + ":" + port + PUBLIC_BITBUCKET_REPO_HOST + projectCode + "/repos/"
					+ repositoryName;
		} else {
			apiUrl = protocol + "://" + hostName + ":" + PUBLIC_BITBUCKET_REPO_HOST + projectCode + "/repos/"
					+ repositoryName;
		}
		String branchUrl = apiUrl + "/branches";

		statsURL = apiUrl + "/commits/";
		apiUrl = apiUrl + "/compare";
		Date dt = null;
		if (firstRun) {

			if (getFirstRunHistoryDays > 0) {
				dt = getDate(new Date(), -getFirstRunHistoryDays, 0);

			}
		} else {
			dt = getDate(new Date(), -1, 0);

		}
		Calendar calendar = new GregorianCalendar();
		TimeZone timeZone = calendar.getTimeZone();
		Calendar cal = Calendar.getInstance(timeZone);
		cal.setTime(dt);
		ResponseEntity<String> branchresponse = makeRestCall(branchUrl, user, pass);

		JSONObject branchUrlJson = parseAsArray(branchresponse);

		JSONArray branchesArray = (JSONArray) branchUrlJson.get("values");
		String queryUrl = null;
		for (int i = 0; i < branchesArray.length(); i++) {
			String displayId = ((JSONObject) branchesArray.get(i)).getString("displayId");
			
				queryUrl = apiUrl.concat("/commits?limit=1000&from=" + displayId);
				LOG.info("queryUrl  :" + queryUrl);
			

			String queryUrlPage = queryUrl;
			boolean lastPage = false;
			int pageNumber = 1;
			List<SCMTool> collection = repo.findByScTypeAndProjectName("BITBUCKET", projectName);
			if (!collection.isEmpty())
				time = collection.get(collection.size() - 1).getCommitTS();

			while (!lastPage) {
				try {

					SCMTool commit;

					ResponseEntity<String> response = makeRestCall(queryUrlPage, user, pass);
					JSONObject jsonObjectnew = parseAsArray(response);
					JSONArray jsonArray = (JSONArray) jsonObjectnew.get("values");
					lastPage = (boolean) jsonObjectnew.get("isLastPage");
					if (jsonArray.length() != 0) {
						JSONObject obj = (JSONObject) jsonArray.get(0);
						newscmCommitTimestamp = getNextChangeSetID(obj);
					}

					if (newscmCommitTimestamp <= time) {
						LOG.info("No ChangeSet to be stored ");
					} else {
						for (int j = 0; j < jsonArray.length(); j++) {
							try {
								commit = new SCMTool();

								String noofDeletions = null, noofInsertions = null;
								@SuppressWarnings("unused")
								String patchUrl = null;
								JSONObject jsonObject = (JSONObject) jsonArray.get(j);
								JSONObject author = (JSONObject) jsonObject.get("author");
								authorname = str(author, "name");
								String id = str(jsonObject, "id");
								message = str(jsonObject, "message");
								long currentTimestamp = (long) jsonObject.get("authorTimestamp");
								if (currentTimestamp <= time) {
									lastPage = true;
									break;
								}

								if (currentTimestamp > time) {
									list = new ArrayList<>();
									commitedTimestamp = jsonObject.getLong("authorTimestamp");
									commit = new SCMTool();
									commit.setScType("BITBUCKET");
									commit.setTimestamp(System.currentTimeMillis());
									commit.setProjectName(projectName);
									commit.setCommiter(authorname);
									commit.setCommitLog(message);
									commit.setCommitTS(commitedTimestamp);
									patchUrl = statsURL + id + "/diff";
									commit.setUrl(patchUrl);
									ResponseEntity<String> patchresponse = makeRestCall(patchUrl, user, pass);
									JSONObject obj = parseAsArray(patchresponse);
									JSONArray jsonArrays = (JSONArray) obj.get("diffs");
									List<FileDetails> filesChangedDetails = new ArrayList<FileDetails>();
									JSONArray diffeJsonArray = jsonArrays;
									getFileInfo(diffeJsonArray, filesChangedDetails, commit, filesChangedDetails);

									commit.setFileDetails(filesChangedDetails);
									commit.setInsertionInCommit(noofInsertions);
									commit.setDeletioninfileInCommit(noofDeletions);
									scmtool.add(commit);

								} else {
									break;
								}

							} catch (Exception e) {
								LOG.info(e);
							}
						}
					}
					if (jsonArray == null || jsonArray.length() == 0) {
						lastPage = true;
					} else {

						pageNumber++;
						queryUrlPage = queryUrl + "&page=" + pageNumber;
					}

				} catch (Exception re) {
					LOG.error(re.getMessage() + ":" + queryUrl);
					lastPage = true;
					continue;

				}
			}

		}
		Collections.reverse(scmtool);
		return scmtool;
	}

	private Date getDate(Date dateInstance, int offsetDays, int offsetMinutes) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dateInstance);
		cal.add(Calendar.DATE, offsetDays);
		cal.add(Calendar.MINUTE, offsetMinutes);
		return cal.getTime();
	}

	private ResponseEntity<String> makeRestCall(String url, String userId, String password) {
		// Basic Auth only.
		if (!"".equals(userId) && !"".equals(password)) {
			return get().exchange(url, HttpMethod.GET, new HttpEntity<>(createHeaders(userId, password)), String.class);

		} else {
			return get().exchange(url, HttpMethod.GET, null, String.class);
		}

	}

	private JSONObject parseAsArray(ResponseEntity<String> response) {
		try {
			return (JSONObject) new JSONTokener(response.getBody()).nextValue();

		} catch (ParseException pe) {
			LOG.info(pe);
		}
		return new JSONObject();
	}

	@SuppressWarnings({})
	public long getNextChangeSetID(JSONObject json) {
		newscmCommitTimestamp = new DateTime(json.get("authorTimestamp")).getMillis();
		return newscmCommitTimestamp;

	}

	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}

	@SuppressWarnings("unused")
	public FileDetails getFileInfo(JSONArray diffeJsonArray, List<FileDetails> filesChangedDetails, SCMTool commit,
			List<FileDetails> fileDetailsList) {

		int filesAdded = 0;
		int filesModified = 0;
		int filesDleted = 0;
		int totalFilesChanged = 0;
		linesAdded = 0;
		linesesModified = 0;
		linesDeleted = 0;
		totalChanges = 0;
		FileDetails fileDetails = null;
		DeletedFileDetails deletedFileDetails = null;

		for (int j = 0; j < diffeJsonArray.length(); j++) {
			fileDetails = new FileDetails();
			JSONObject diffJsonObject = (JSONObject) diffeJsonArray.get(j);
			String fileName = "";
			if ((boolean) (diffJsonObject.get("source") == null)) {
				fileName = ((JSONObject) diffJsonObject.get("destination")).getString("name");
				filesAdded++;
			} else if ((boolean) (diffJsonObject.get("destination") == null)) {
				fileName = ((JSONObject) diffJsonObject.get("source")).getString("name");
				filesDleted++;
				deletedFileDetails = new DeletedFileDetails();
				deletedFileDetails.setFileName(fileName);
				deletedFileDetails.setCommitter(authorname);
				deletedFileDetails.setDeletedDateTime(commitedTimestamp);
				list.add(deletedFileDetails);

			} else {
				fileName = ((JSONObject) diffJsonObject.get("destination")).getString("name");
				filesModified++;
			}

			if (!(fileName == null)) {
				fileDetails.setFilename(fileName);
			}
			if (!(diffJsonObject.toString().contains("hunk"))) {

				fileDetailsList.add(fileDetails);

				continue;
			}

			JSONArray hunkJsonArray = diffJsonObject.getJSONArray("hunks");
			for (int i = 0; i < hunkJsonArray.length(); i++) {

				JSONObject getSegment = hunkJsonArray.getJSONObject(i);
				JSONArray segmentArray = getSegment.getJSONArray("segments");

				try {
					getFilesSegmentInfo(segmentArray, fileDetails, fileDetailsList);
				} catch (Exception e) {
					LOG.error(e);
				}

			}
			totalChanges = linesAdded + linesesModified + linesDeleted;

			fileDetails.setDeletioninFile(linesDeleted);
			fileDetails.setTotalChengesInFile(totalChanges);
			fileDetails.setInsertioninFile(linesAdded);
			fileDetails.setModificationinFile(linesesModified);
			fileDetailsList.add(fileDetails);
		}
		totalFilesChanged = filesAdded + filesDleted + filesModified;
		commit.setNoOfChanges(totalFilesChanged);
		commit.setModification(filesModified);
		commit.setAddition(filesAdded);
		commit.setDeletion(filesDleted);
		commit.setProjectName(projectName);
		commit.setDeletedFileDetails(list);
		return fileDetails;
	}

	public void getFilesSegmentInfo(JSONArray segmentArray, FileDetails fileDetails,
			List<FileDetails> fileDetailsList) {
		String segmentType = "";
		String[] segmentTypeAray = new String[100];
		String segmentTypeList = new String();
		for (int k = 0; k < segmentArray.length(); k++) {

			JSONArray linesArray = ((JSONObject) segmentArray.getJSONObject(k)).getJSONArray("lines");
			int linesLength = linesArray.length();

			segmentType = ((JSONObject) segmentArray.getJSONObject(k)).getString("type");
			segmentTypeAray[k] = segmentType;

			segmentTypeList = segmentTypeList + segmentType;
			if ("ADDED".equals(segmentType)) {
				if ("REMOVED".equals(((JSONObject) segmentArray.getJSONObject(k - 1)).getString("type"))) {
					JSONArray previouslinesArray = ((JSONObject) segmentArray.getJSONObject(k - 1))
							.getJSONArray("lines");
					linesesModified = linesesModified + previouslinesArray.length();
				}
			}
			if ("ADDED".equals(segmentType)
					&& "CONTEXT".equals(((JSONObject) segmentArray.getJSONObject(k - 1)).getString("type"))
					&& "CONTEXT".equals(((JSONObject) segmentArray.getJSONObject(k + 1)).getString("type"))) {
				linesAdded = linesAdded + linesLength;
			} else if ("REMOVED".equals(segmentType)
					&& "CONTEXT".equals(((JSONObject) segmentArray.getJSONObject(k - 1)).getString("type"))
					&& "CONTEXT".equals(((JSONObject) segmentArray.getJSONObject(k + 1)).getString("type"))) {

				linesDeleted = linesDeleted + linesLength;

			}

		}
	}

}