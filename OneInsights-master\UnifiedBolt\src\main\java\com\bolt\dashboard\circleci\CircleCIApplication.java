package com.bolt.dashboard.circleci;


import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.LogManager;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.atlassian.jira.rest.client.api.RestClientException;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.repository.BuildToolRep;

public class CircleCIApplication {
	
	private static final Logger LOGGER = LogManager.getLogger(CircleCIApplication.class);
	AnnotationConfigApplicationContext ctx = null;
	BuildToolRep repo = null;

	/**
	 * Private Constructor
	 */
	public CircleCIApplication() {

	}

	public void circleCIMain(String projectName) throws RestClientException {
		LOGGER.info("Circle CI collector started for " + projectName);
		ctx = DataConfig.getContext();
		repo = ctx.getBean(BuildToolRep.class);

		CircleCIClient circleToolMetrics = new CircleCIClientImplementation();
		try {

			circleToolMetrics.getBuildTool(repo, projectName);
			//// catx.close();
		} catch (Exception e) {
			LOGGER.error(e.getMessage());
			LOGGER.info(e.getStackTrace());
			LOGGER.info(e);
			LOGGER.info("Circle CI collector failed for " + projectName);
			repo = null;
		}
		repo = null;
		LOGGER.info("Circle CI collector ended for " + projectName);
	}
}
