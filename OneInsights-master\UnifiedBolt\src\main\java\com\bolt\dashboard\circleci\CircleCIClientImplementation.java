package com.bolt.dashboard.circleci;

import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.BuildFileInfo;
import com.bolt.dashboard.core.model.BuildInfo;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.repository.BuildFailurePatternForProjectRepo;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class CircleCIClientImplementation implements CircleCIClient {

	private static final Logger LOGGER = LogManager.getLogger(CircleCIClientImplementation.class);
	AnnotationConfigApplicationContext ctx = null;
	private static int buildNumber;
	String projectName = "";
	String url = "";
	String circleCIUrl = "";
	int p = 0;
	String pass = "";
	String user = "";
	List<String> jobsList = new ArrayList<>();
	BuildToolRep repo = null;
	List<String> jobCollection = new ArrayList<>();
	String projectUrl = null;
	private BuildFailurePatternForProjectRepo buildFailurePatternForProjectRepo;

	private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000);
		requestFactory.setReadTimeout(20000);
		return new RestTemplate(requestFactory);
	}

	@SuppressWarnings({})
	private BuildTool getBuildMetrics(JSONObject json, String baseUrl, String user, String pass) {
		BuildTool tool1 = new BuildTool();
		tool1.setBuildType("CircleCI");
		tool1.setJobList(jobCollection);
		tool1.setJobCount(jobsList.size());
		Object jobName = json.get("job_name");
		String[] jobNameArray = null;
		/*
		 * if (jobName.contains("#")) { jobNameArray =
		 * jobName.split(Pattern.quote("#")); jobName = jobNameArray[0]; jobName
		 * = jobName.substring(0, jobName.length() - 1);
		 * 
		 * }
		 */
		tool1.setJobName(jobName.toString());
		BuildToolMetric durationMetric = new BuildToolMetric("duration");
		String timestamp = null;

		try {
			timestamp = convertStringToTimestamp(json.get("start_time").toString());
			tool1.setTimestamp(Long.parseLong(timestamp.toString()));
		
		BuildToolMetric timeStampmetric = new BuildToolMetric("timestamp");
		timeStampmetric.setValue(timestamp.toString());
		tool1.getMetrics().add(timeStampmetric);
		}catch(JSONException | ParseException e) {
			BuildToolMetric timeStampmetric = new BuildToolMetric("timestamp");
			timeStampmetric.setValue("");
			tool1.getMetrics().add(timeStampmetric);
			LOGGER.info(e.getMessage());
		}
		Object duration = json.get("build_time_millis");

		durationMetric.setValue(duration != null ? duration.toString() : "");
		tool1.getMetrics().add(durationMetric);

		String result = json.getString("outcome");
		int buildId = json.getInt("build_num");
		tool1.setBuildID(buildId);

		BuildToolMetric resultMetrics = new BuildToolMetric("result");
		resultMetrics.setValue(result != null ? result.toString() : "");
		tool1.getMetrics().add(resultMetrics);
		tool1.setName(projectName);
		return tool1;
	}

	public List<BuildInfo> getListBuildInfo(JSONObject json) {
		List<BuildInfo> buildInfoList = new ArrayList<>();
		JSONObject changeSetObject = (JSONObject) json.get("changeSet");
		String changeSetString = changeSetObject.toString();
		if (changeSetString.contains("items")) {
			JSONArray itemsArary = changeSetObject.getJSONArray("items");
			for (int i = 0; i < itemsArary.length(); i++) {
				BuildInfo buildInfo = new BuildInfo();
				JSONObject itemObject = (JSONObject) itemsArary.get(i);
				String fullName = ((JSONObject) itemObject.getJSONObject("author")).getString("fullName");
				String message = itemObject.getString("msg");
				buildInfo.setCommitter(fullName);
				buildInfo.setMessage(message);
				List<BuildFileInfo> buildFileInfoList = new ArrayList<>();
				JSONArray pathsJsonArray = itemObject.getJSONArray("paths");
				for (int j = 0; j < pathsJsonArray.length(); j++) {
					BuildFileInfo buildFileInfo = new BuildFileInfo();
					String fileName = ((JSONObject) pathsJsonArray.get(j)).getString("file");
					String[] fileSeparationString = null;
					if (fileName.contains("/")) {
						fileSeparationString = fileName.split(Pattern.quote("/"));
						fileName = fileSeparationString[fileSeparationString.length - 1];
					}
					String editType = ((JSONObject) pathsJsonArray.get(j)).getString("editType");
					buildFileInfo.setEditType(editType);
					buildFileInfo.setFileNames(fileName);
					buildFileInfoList.add(buildFileInfo);
				}
				buildInfo.setBuildFileInfoList(buildFileInfoList);
				buildInfoList.add(buildInfo);
			}

		}
		return buildInfoList;

	}

	private ResponseEntity<String> makeRestCall(String url, String userId, String password)
			throws CircleCICollectorException {
		// Basic Auth only.

		if (!"".equals(userId) && !"".equals(password)) {
			return get().exchange(url, HttpMethod.GET, new HttpEntity<>(createHeaders(userId, password)), String.class);

		} else {
			try {
				return get().exchange(url, HttpMethod.GET, null, String.class);
			} catch (Exception e) {
				return null;
			}

		}

	}

	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}

	private JSONArray parseAsNewArray(ResponseEntity<String> response) {
		return (JSONArray) new JSONTokener(response.getBody()).nextValue();
	}

	/*
	 * public List<String> getJobsList(JSONArray object) { JSONObject
	 * obj=object.getJSONObject(0); JSONArray jobsJsonArray =
	 * obj.getJSONArray("jobs"); for (int i = 0; i < jobsJsonArray.length();
	 * i++) { String jobName = ((JSONObject)
	 * jobsJsonArray.get(i)).getString("name"); jobCollection.add(jobName); }
	 * 
	 * return jobCollection;
	 * 
	 * }
	 */

	@SuppressWarnings({})
	public int getNextBuildNumber(JSONArray json) {
		JSONObject js = (JSONObject) json.get(0);
		buildNumber = (int) js.get("build_num");

		LOGGER.info("buildNumber  " + buildNumber);
		return buildNumber;

	}

	public BuildTool retrieveJenkinsData(String instanceUrl, String jobName) throws CircleCICollectorException {
		BuildTool tool = new BuildTool();

		ResponseEntity<String> response = makeRestCall(instanceUrl, user, pass);
		if(response != null) {

		JSONArray jsonObject = parseAsNewArray(response);
		buildNumber = getNextBuildNumber(jsonObject);
		int count = 0;
		Set<BuildTool> listOfBuildTools = repo.findByNameAndJobName(projectName, jobName);
		List<BuildTool> list = new ArrayList<BuildTool>(listOfBuildTools);
		if (list != null && !list.isEmpty()) {
			BuildTool buildTool = list.get(list.size() - 1);
			count = buildTool.getBuildID();
		}
		// count = listOfBuildTools.size();
		if (count >= (buildNumber)) {

			LOGGER.info("No Builds to be stored    ");
		} else {
			for (++count; count <= buildNumber; count++) {
				String baseUrl = "";
				if (circleCIUrl.contains("api/v1.1")) {
					baseUrl = circleCIUrl;

					LOGGER.info("URI " + baseUrl);
				}
				try {

					ResponseEntity<String> buildResponse = makeRestCall(baseUrl, user, pass);
					if (!(buildResponse == null)) {
						JSONArray buildObject = parseAsNewArray(buildResponse);

						JSONObject js = buildObject.getJSONObject(p);
						tool = getBuildMetrics(js, baseUrl, user, pass);
						p++;
						repo.save(tool);
					} else {
						continue;
					}

				} catch (RestClientException e) {
					LOGGER.error(e.getMessage());
					LOGGER.info(e);
					throw new CircleCICollectorException(e);
				}
			}
		}
		}
		return tool;

	}

	@Override
	public BuildTool getBuildTool(BuildToolRep repo, String projectName) throws CircleCICollectorException {
		this.projectName = projectName;
		this.repo = repo;
		BuildTool tool = null;
		String job = getConfigurationDetails();
		ResponseEntity<String> response = makeRestCall(projectUrl, user, pass);
		if(response != null) {
		JSONArray jsonObject = parseAsNewArray(response);
		// jobCollection = getJobsList(jsonObject);
		if (!jobsList.isEmpty()) {

			for (String jobName : jobsList) {
				circleCIUrl = projectUrl + "/job/" + jobName + "/api/json";
				tool = retrieveJenkinsData(circleCIUrl, jobName);
			}
		} else {

			tool = retrieveJenkinsData(circleCIUrl, job);
		}
		}

		return tool;
	}

	public String getConfigurationDetails() {

		ctx = DataConfig.getContext();
		ConfigurationSettingRep configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		ConfigurationSetting configurationColection = configurationRepo.findByProjectName(projectName).get(0);
		/*
		 * JobDetailsRepo jobDetailsRepo = ctx.getBean(JobDetailsRepo.class);
		 * JobDetails jobDetails =
		 * jobDetailsRepo.findByProjectName(projectName); if (jobDetails !=
		 * null) { List<JobDetailsMetrics> jobDetailsMetrics =
		 * jobDetails.getMetrics(); Iterator jobDetailsIter =
		 * jobDetailsMetrics.iterator(); while (jobDetailsIter.hasNext()) {
		 * 
		 * Object jobDetail = jobDetailsIter.next(); JobDetailsMetrics obj =
		 * (JobDetailsMetrics) jobDetail; String jobName = obj.getJobName();
		 * jobsList.add(jobName); } }
		 */

		String buildJobName = null;
		Set<ConfigurationToolInfoMetric> metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOGGER.info("Tool name  " + metric1.getToolName());
			if ("CircleCI".equals(metric1.getToolName())) {
				LOGGER.info("URL  " + metric1.getUrl());
				circleCIUrl = metric1.getUrl();
				user = metric1.getUserName();
				
					pass=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
	
				LOGGER.info("Circle CI URL" + circleCIUrl);
				break;
			}

		}
		/*
		 * String[] jobNameArray = null;
		 * 
		 * if (circleCIUrl.contains("/job/")) { jobNameArray =
		 * circleCIUrl.split(Pattern.quote("/job/")); buildJobName =
		 * jobNameArray[1]; projectUrl = jobNameArray[0];
		 * 
		 * }
		 */
		projectUrl = circleCIUrl;
		return circleCIUrl;

	}

	public static String convertStringToTimestamp(String str_date) throws ParseException {
		Date javaUtilDate = new Date();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");
		Date date = formatter.parse(str_date.replaceAll("Z$", "+0000"));

		long timestamp = date.getTime();

		return Long.toString(timestamp);
	}
}
