package com.bolt.dashboard.codecoverage;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.Iterator;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.repository.CodeCoverageRepository;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class CodeCoverageApplication {
	 private static final Logger LOG = LogManager.getLogger(CodeCoverageApplication.class);
	AnnotationConfigApplicationContext ctx = null;
	private static final String UICOBERTURAPATH = "UI/coverage/cobertura-coverage.xml";
	private static final String BACKENDCOBERTURAPATH = "/codecoverage/";

	CodeCoverageRepository repo = null;
	CodeCoverageClientImplementation codeCoverage = null;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configurationColection = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	Iterator iter = null;
	ConfigurationToolInfoMetric metric1 = null;
	String codeCoverageType = "Code Coverage";
	String result = "SUCCESS";

	public CodeCoverageApplication() {
	}

	public void codeCoverageMain(String projectName) {
		LOG.info("Code Coverage starts for " + projectName);
		String destinationLocation = "";
		String username = "";
		String apiKey = null;
		String url = null;
		ctx = DataConfig.getContext();
		repo = ctx.getBean(CodeCoverageRepository.class);
		codeCoverage = new CodeCoverageClientImplementation();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configurationColection = configurationRepo.findByProjectName(projectName).get(0);
		metric = configurationColection.getMetrics();
		iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOG.info("Tool name  " + metric1.getToolName());
			if (codeCoverageType.equals(metric1.getToolName())) {
				LOG.info("URL  " + metric1.getUrl());
				url = metric1.getUrl();
				username = metric1.getUserName() + "X";
				apiKey = metric1.getPassword();
				
				apiKey=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				break;
			}

		}

		String dwnldFolder = System.getProperty("user.dir");
		LOG.info("*************path************" + dwnldFolder);
		destinationLocation = dwnldFolder + BACKENDCOBERTURAPATH;
		File backEndCoverageFile = new File(destinationLocation);
		try {
			backEndCoverageFile.mkdirs();
//			backEndCoverageFile.createNewFile();

		} catch (Exception e1) {
			result = "FAIL";
			ConstantVariable.getLastRun(projectName, codeCoverageType, new Date().getTime(), result);
			cleanObject();
			LOG.info(e1);
		}

		File uiCoberaturaFile = new File(dwnldFolder + "\\" + UICOBERTURAPATH);

		try {
			codeCoverage.downLoadFileToLocal(destinationLocation + projectName + " coveragereport.xml", url, username,
					apiKey, repo, projectName);
			if (uiCoberaturaFile.exists()) {
				codeCoverage.getCoverageDataForUI(uiCoberaturaFile.getPath(), url, username, apiKey, repo, projectName);
			}

			cleanObject();
		} catch (Exception e) {
			result = "FAIL";
			ConstantVariable.getLastRun(projectName, codeCoverageType, new Date().getTime(), result);
			cleanObject();
			LOG.error(e.getMessage());
			LOG.info(e.getStackTrace());
			LOG.info(e);
			LOG.info("Code Coverage failed for " + projectName);

		}
		ConstantVariable.getLastRun(projectName, codeCoverageType, new Date().getTime(), result);
		cleanObject();
		LOG.info("Code Coverage ended for " + projectName);

	}

	public void cleanObject() {
		repo = null;
		codeCoverage = null;
		configurationRepo = null;
		configurationColection = null;
		metric = null;
		iter = null;
		metric1 = null;

	}
}