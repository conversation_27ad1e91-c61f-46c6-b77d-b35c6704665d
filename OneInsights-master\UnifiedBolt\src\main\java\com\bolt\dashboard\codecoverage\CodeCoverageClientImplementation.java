package com.bolt.dashboard.codecoverage;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.XML;
import org.springframework.http.HttpHeaders;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.model.ClassCoverageMetrics;
import com.bolt.dashboard.core.model.LinesCoverageMetrics;
import com.bolt.dashboard.core.model.MethodCoverageMetrics;
import com.bolt.dashboard.core.model.PackageCodeCoverage;
import com.bolt.dashboard.core.model.ProjectCodeCoverage;
import com.bolt.dashboard.core.repository.CodeCoverageRepository;

public class CodeCoverageClientImplementation implements CodeCoverageClient {
	private static final String CODE_COMPLEXITY = "complexity";
	private static final String CODE_LINERATE = "line-rate";
	private static final String CODE_BRANCHRATE = "branch-rate";
	private static final String CODE_PACKAGES = "packages";
	private static final String CODE_PACKAGE = "package";
	private static final String CODE_CLASSES = "classes";
	private static final String CODE_CLASS = "class";
	private static final String CODE_METHODS = "methods";
	private static final String CODE_METHOD = "method";
	private static final String CODE_CONDITION = "condition";
	private static final String CODE_CONDITIONS = "conditions";
	private static final String CODE_LINES = "lines";
	private static final String CODE_LINE = "line";
	String coverageType = null;
	String projectName = "";
	String packageLastName = null;
	int classHitLines;
	int classTotalLines;
	int methodHitLines;
	int methodTotalLines;
	int totalMethods;
	int methodsCovered;
	int classCovered;
	boolean methodCoverageFlag = false;
	boolean classCoverageFlag = false;
	int totalClasses;
	int totalFiles;
	int filesCovred;
	boolean filesCoverageFlag = false;
	int packageLevelTotalLines;
	int packageLevelLinesCovered;
	boolean packageLevelLinesFlag = false;
	int packegeLevelTotalMethods;
	int packageLevelMethodsCovered;
	boolean packageLevelMethodsFlag = false;
	int packegeLevelTotalClasses;
	int packageLevelClassesCovered;
	boolean packageLevelclassFlag = false;
	int projectLevelTotalPackages;
	int projectLevelPackagesCovered;
	boolean projectLevelPackageCoveredFlag;
	String projectLevelPackagesCoverage;
	double projectLevelPackagesCoveragePercentage;
	int projectLevelTotalClasses;
	int projectLevelClassesCovered;
	boolean projectLevelClassesCoveredFlag;
	String projectLevelClassCoverage;
	double projectLevelClassCoveragePercentage;
	int projectLevelTotalMethods;
	int projectLevelMethodsCovered;
	boolean projectLevelMethodsCoveredFlag;
	String projectLevelMethodCoverage;
	double projectLevelMethodCoveragePercentage;
	int projectLevelTotalLines;
	int projectLevelLinesCovered;
	String projectLevelLinesCoverage;
	double projectLevelLinessCoveragePercentage;
	List<String> classCoverageGreaterThanEighty = new ArrayList<>();
	int greaterThanEightyCount;
	List<String> classCoverageGreaterThanFifty = new ArrayList<>();
	Map<String, Double> fileCovarage = new HashMap<>();
	int greaterThanFiftyCount;
	List<String> classCoverageGreaterThanThirtyFive = new ArrayList<>();
	int greaterThanThirtyfiveCount;
	List<String> classCoverageBetwenThirtyfive = new ArrayList<>();
	int betweenThirtyFiveCount;
	List<String> classCoverageHundread = new ArrayList<>();
	int hundreadPercenteCount;

	 private static final Logger LOG = LogManager.getLogger(CodeCoverageClientImplementation.class);

	@SuppressWarnings("unused")
	private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000);
		requestFactory.setReadTimeout(20000);
		return new RestTemplate(requestFactory);
	}

	@SuppressWarnings("unused")
	private void authenticatioJenkinsAPIKey(String url, String username, String apiKey) throws CodeCoverageExceptions {
		URI thisUri = URI.create(url);
		String userInfo = thisUri.getUserInfo();
		try {
			thisUri.toURL().openConnection().addRequestProperty("User-Agent",
					"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 Safari/537.36");
		} catch (Exception ex) {
			throw new CodeCoverageExceptions(ex);
		}
		if (StringUtils.isEmpty(userInfo) && username != null && apiKey != null) {
			userInfo = username + ":" + apiKey;
		}

	}

	private HttpHeaders createHeaders(final String userInfo) {
		byte[] endocedAuth = Base64.encodeBase64(userInfo.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic" + new String(endocedAuth);
		HttpHeaders headers = new HttpHeaders();
		headers.set(HttpHeaders.AUTHORIZATION, authHeader);
		return headers;
	}

	public JSONObject convertXMLToJson(String xmlFileLocation) throws CodeCoverageExceptions, IOException {
		JSONObject jsonObject = null;
		InputStream inputStream = null ;
		try {
			File file = new File(xmlFileLocation);
			inputStream = new FileInputStream(file);
			StringBuilder builder = new StringBuilder();
			int ptr = 0;
			while ((ptr = inputStream.read()) != -1) {
				builder.append((char) ptr);
			}

			String xml = builder.toString();
			jsonObject = XML.toJSONObject(xml);
			inputStream.close();
		} catch (Exception e) {
			throw new CodeCoverageExceptions(e);
		} finally {
			if(inputStream != null) {
				inputStream.close();
			}
		}

		return jsonObject;

	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	public ProjectCodeCoverage getDataFromJson(JSONObject jsonObject, CodeCoverageRepository repo,
			String coverageType) {
		ProjectCodeCoverage projectCoverage = new ProjectCodeCoverage();
		projectCoverage.setCoverageType(coverageType);
		projectLevelTotalPackages = 0;
		projectLevelPackagesCovered = 0;
		projectLevelPackageCoveredFlag = false;

		projectLevelPackagesCoveragePercentage = 0;
		projectLevelTotalClasses = 0;
		projectLevelClassesCovered = 0;
		projectLevelClassesCoveredFlag = false;

		projectLevelClassCoveragePercentage = 0;
		projectLevelTotalMethods = 0;
		projectLevelMethodsCovered = 0;
		projectLevelMethodsCoveredFlag = false;

		projectLevelMethodCoveragePercentage = 0;
		projectLevelTotalLines = 0;
		projectLevelLinesCovered = 0;

		projectLevelLinessCoveragePercentage = 0;

		JSONObject coverageJsonObject = (JSONObject) jsonObject.get("coverage");
		int branchesCovered = Integer.parseInt(coverageJsonObject.get("branches-covered").toString());
		projectCoverage.setProjectBranchesCovered(branchesCovered);
		double projectComplexity = coverageJsonObject.getDouble(CODE_COMPLEXITY);
		projectCoverage.setProjectComplexity(projectComplexity);
		int linesValid = coverageJsonObject.getInt("lines-valid");
		projectCoverage.setProjectLinesValid(linesValid);
		double lineRate = Double.parseDouble(coverageJsonObject.get(CODE_LINERATE).toString());
		projectLevelLinessCoveragePercentage = lineRate * 100;
		projectCoverage.setProjectLevelLinessCoveragePercentage(projectLevelLinessCoveragePercentage);
		projectCoverage.setProjectLineRate(lineRate);
		double branchRate = Double.parseDouble(coverageJsonObject.get(CODE_BRANCHRATE).toString());
		projectCoverage.setPojectBranchRate(branchRate);
		projectCoverage.setTimestamp(coverageJsonObject.getLong("timestamp"));
		projectCoverage.setProjectLinesCovered(coverageJsonObject.getInt("lines-covered"));
		projectCoverage.setProjectBranchValid(coverageJsonObject.getInt("branches-valid"));
		if (coverageJsonObject.get("version") instanceof String) {
			projectCoverage.setVersion(coverageJsonObject.getString("version"));
		} else {
			projectCoverage.setVersionObject(coverageJsonObject.get("version"));
		}

		projectCoverage.setProjectName(projectName);
		JSONArray packegesInfoObject = new JSONArray();
		List<PackageCodeCoverage> packageCodeCoverageList = new ArrayList();
		if (coverageJsonObject.get(CODE_PACKAGES) instanceof JSONArray) {

			packegesInfoObject = (JSONArray) coverageJsonObject.get(CODE_PACKAGES);
		} else {
			JSONObject packagesJsonObject = (JSONObject) coverageJsonObject.get(CODE_PACKAGES);
			packegesInfoObject.put(packagesJsonObject);
		}
		packageCodeCoverageList = getPackageInfo(packegesInfoObject, packageCodeCoverageList);
		projectLevelPackagesCoverage = projectLevelPackagesCovered + "/" + projectLevelTotalPackages;
		projectLevelPackagesCoveragePercentage = (double) projectLevelPackagesCovered
				/ (double) projectLevelTotalPackages * 100;
		projectCoverage.setProjectLevelTotalPackages(projectLevelTotalPackages);
		projectCoverage.setProjectLevelPackagesCovered(projectLevelPackagesCovered);
		projectCoverage.setProjectLevelPackagesCoverage(projectLevelPackagesCoverage);
		projectCoverage.setProjectLevelPackagesCoveragePercentage(projectLevelPackagesCoveragePercentage);

		projectLevelClassCoverage = projectLevelClassesCovered + "/" + projectLevelTotalClasses;
		projectLevelClassCoveragePercentage = (double) projectLevelClassesCovered / (double) projectLevelTotalClasses
				* 100;
		projectCoverage.setProjectLevelTotalClasses(projectLevelTotalClasses);
		projectCoverage.setProjectLevelClassesCovered(projectLevelClassesCovered);
		projectCoverage.setProjectLevelClassCoverage(projectLevelClassCoverage);
		projectCoverage.setProjectLevelClassCoveragePercentage(projectLevelClassCoveragePercentage);

		projectLevelMethodCoverage = projectLevelMethodsCovered + "/" + projectLevelTotalMethods;
		projectLevelMethodCoveragePercentage = (double) projectLevelMethodsCovered / (double) projectLevelTotalMethods
				* 100;

		projectCoverage.setProjectLevelTotalMethods(projectLevelTotalMethods);
		projectCoverage.setProjectLevelMethodsCovered(projectLevelMethodsCovered);
		projectCoverage.setProjectLevelMethodCoverage(projectLevelMethodCoverage);
		projectCoverage.setProjectLevelMethodCoveragePercentage(projectLevelMethodCoveragePercentage);

		projectLevelLinesCoverage = projectLevelLinesCovered + "/" + projectLevelTotalLines;
		projectLevelLinessCoveragePercentage = (double) projectLevelLinesCovered / (double) projectLevelTotalLines
				* 100;

		projectCoverage.setProjectLevelTotalLines(projectLevelTotalLines);
		projectCoverage.setProjectLevelLinesCovered(projectLevelLinesCovered);
		projectCoverage.setProjectLevelLinesCoverage(projectLevelLinesCoverage);
		projectCoverage.setProjectLevelMethodCoveragePercentage(projectLevelLinessCoveragePercentage);

		projectCoverage.setPackageCoverageMetrics(packageCodeCoverageList);

		projectCoverage.setClassCoverageHundread(classCoverageHundread);
		projectCoverage.setGreaterThanEightyCount(greaterThanEightyCount);
		projectCoverage.setGreaterThanFiftyCount(greaterThanFiftyCount);
		projectCoverage.setGreaterThanThirtyFiveCount(greaterThanThirtyfiveCount);
		projectCoverage.setBetweenThirtyFiveCount(betweenThirtyFiveCount);
		projectCoverage.setClassCoverageGreaterThanEighty(classCoverageGreaterThanEighty);
		projectCoverage.setClassCoverageGreaterThanFifty(classCoverageGreaterThanFifty);
		projectCoverage.setClassCoverageGreaterThanThirtyFive(classCoverageGreaterThanThirtyFive);
		projectCoverage.setClassCoverageBetwenThirtyfive(classCoverageBetwenThirtyfive);
		projectCoverage.setHundraedPercentaCount(hundreadPercenteCount);
		projectCoverage.setFileCoverage(fileCovarage);
		repo.save(projectCoverage);
		greaterThanEightyCount = greaterThanFiftyCount = greaterThanThirtyfiveCount = betweenThirtyFiveCount = hundreadPercenteCount = 0;
		classCoverageHundread.clear();
		classCoverageGreaterThanEighty.clear();
		classCoverageGreaterThanFifty.clear();
		classCoverageGreaterThanThirtyFive.clear();
		classCoverageBetwenThirtyfive.clear();
		fileCovarage = new HashMap<>();
		return projectCoverage;
	}

	@SuppressWarnings("unused")
	public List<PackageCodeCoverage> getPackageInfo(JSONArray packegesInfoObject,
			List<PackageCodeCoverage> packageCodeCoverageList) {

		JSONArray packegeInfoJsonArray = new JSONArray();
		for (int i = 0; i < packegesInfoObject.length(); i++) {

			if ((((JSONObject) packegesInfoObject.get(i)).get(CODE_PACKAGE)) instanceof JSONArray) {

				packegeInfoJsonArray = (JSONArray) ((JSONObject) packegesInfoObject.get(i)).get(CODE_PACKAGE);
			} else {
				JSONObject packegeInfoJsonObject = (JSONObject) ((JSONObject) packegesInfoObject.get(i))
						.get(CODE_PACKAGE);
				packegeInfoJsonArray.put(packegeInfoJsonObject);
			}
			projectLevelTotalPackages = projectLevelTotalPackages + packegeInfoJsonArray.length();
			for (int j = 0; j < packegeInfoJsonArray.length(); j++) {

				packageLevelTotalLines = 0;
				packageLevelLinesCovered = 0;
				packageLevelLinesFlag = false;
				packegeLevelTotalMethods = 0;
				packageLevelMethodsCovered = 0;
				packageLevelMethodsFlag = false;
				packegeLevelTotalClasses = 0;
				packageLevelClassesCovered = 0;

				PackageCodeCoverage packageCodeCoverage = new PackageCodeCoverage();
				JSONObject packageJson = (JSONObject) packegeInfoJsonArray.get(j);
				if (packageJson.has(CODE_COMPLEXITY)) {
					double packageComplexity = packageJson.getDouble(CODE_COMPLEXITY);
					packageCodeCoverage.setPackageComplexity(packageComplexity);
				}

				String packageName = packageJson.getString("name");
				packageCodeCoverage.setPackageName(packageName);
				if (packageName.indexOf(".") > -1) {
					int lastindex = packageName.lastIndexOf(".");
					packageLastName = packageName.substring(lastindex + 1, packageName.length());
					packageCodeCoverage.setPackageLastName(packageLastName);
				} else {
					packageCodeCoverage.setPackageLastName(null);
				}
				if (packageJson.has(CODE_LINERATE)) {
					double packageLinerate = packageJson.getDouble(CODE_LINERATE);
					packageCodeCoverage.setPackageLineRate(packageLinerate);
					double packageCoveragePercentage = packageLinerate * 100;
					packageCodeCoverage.setPackageCoveragePercentage(packageCoveragePercentage);
				}

				if (packageJson.has(CODE_BRANCHRATE)) {
					double packageBranchrate = packageJson.getDouble(CODE_BRANCHRATE);
					packageCodeCoverage.setPackageBranchRate(packageBranchrate);
				}

				packageCodeCoverage.setTimestamp(new Date().getTime());
				JSONArray classesJsonArray = new JSONArray();
				JSONObject classesJsonObject = new JSONObject();

				if (packageJson.get(CODE_CLASSES) instanceof JSONArray) {
					classesJsonArray = (JSONArray) packageJson.get(CODE_CLASSES);
				} else {
					classesJsonObject = (JSONObject) packageJson.get(CODE_CLASSES);
					classesJsonArray.put(classesJsonObject);
				}

				Set<ClassCoverageMetrics> classList = getClassesInfo(classesJsonArray, packageCodeCoverageList,
						packageCodeCoverage);
				String packageLinesCoverage = packageLevelLinesCovered + "/" + packageLevelTotalLines;
				if (packageLevelClassesCovered > 0.0) {
					projectLevelPackagesCovered++;

				}
				packageCodeCoverage.setPackageLevelLinesCovered(packageLevelLinesCovered);
				packageCodeCoverage.setPackageLevelTotalLines(packageLevelTotalLines);
				packageCodeCoverage.setPackageLevelLinesCoverage(packageLinesCoverage);
				packageCodeCoverage.setPackageLevelLinesCoveredPercentage(
						(double) packageLevelLinesCovered / (double) packageLevelTotalLines * 100);
				packageCodeCoverage.setPackageLevelclasesCoveed(packageLevelClassesCovered);
				packageCodeCoverage.setPackageLevelTotalClasses(packegeLevelTotalClasses);
				packageCodeCoverage.setPackageLevelclasesCoveragePercentage(
						(double) packageLevelClassesCovered / (double) packegeLevelTotalClasses * 100);
				packageCodeCoverage
						.setPackageLevelClassesCoverage(packageLevelClassesCovered + "/" + packegeLevelTotalClasses);

				packageCodeCoverage.setPackageLevelTotalMethods(packegeLevelTotalMethods);
				packageCodeCoverage.setPackageLevelMethodsCovered(packageLevelMethodsCovered);
				packageCodeCoverage
						.setPackageLevelMethodCoverage(packageLevelMethodsCovered + "/" + packegeLevelTotalMethods);
				packageCodeCoverage.setPackageLevelMethodsPercentage(
						(double) packageLevelMethodsCovered / (double) packegeLevelTotalMethods * 100);

				packageCodeCoverage.setClassMetrics(classList);

				packageCodeCoverageList.add(packageCodeCoverage);

			}

		}
		return packageCodeCoverageList;

	}

	@SuppressWarnings({ "unused", "rawtypes", "unchecked" })
	public Set<ClassCoverageMetrics> getClassesInfo(JSONArray classesJsonArray,
			List<PackageCodeCoverage> packageCodeCoverageList, PackageCodeCoverage paCodeCoverage) {
		Set<ClassCoverageMetrics> classcoverageList = new HashSet();
		totalClasses = 0;

		for (int i = 0; i < classesJsonArray.length(); i++) {

			JSONArray getClassJsonArray = new JSONArray();
			JSONObject getClassJsonObject = new JSONObject();
			if ((((JSONObject) classesJsonArray.get(i)).get(CODE_CLASS)) instanceof JSONArray) {
				getClassJsonArray = (JSONArray) (((JSONObject) classesJsonArray.get(i)).get(CODE_CLASS));
			} else {
				getClassJsonObject = (JSONObject) (((JSONObject) classesJsonArray.get(i)).get(CODE_CLASS));
				getClassJsonArray.put(getClassJsonObject);
			}
			classCovered = 0;

			int totalClassCountFromFile = 0;
			int classCoveredFromFile = 0;
			totalClassCountFromFile = getClassJsonArray.length();
			for (int j = 0; j < getClassJsonArray.length(); j++) {

				classHitLines = 0;
				classTotalLines = 0;
				classCoverageFlag = false;

				ClassCoverageMetrics classCoverage = new ClassCoverageMetrics();
				JSONObject individualClassJsonObject = (JSONObject) getClassJsonArray.get(j);
				if (individualClassJsonObject.has(CODE_COMPLEXITY)) {
					double classComplexity = individualClassJsonObject.getDouble(CODE_COMPLEXITY);
					classCoverage.setClassComplexity(classComplexity);
				}

				String fileName = individualClassJsonObject.getString("filename");
				classCoverage.setFileName(fileName);

				String[] fileNameArray = null;
				if (fileName.contains(".")) {
					fileNameArray = fileName.split(Pattern.quote("."));
					fileName = fileNameArray[0];

				} else if (fileName.contains("\\")) {
					fileNameArray = fileName.split(Pattern.quote("\\"));
					fileName = fileNameArray[fileNameArray.length - 1];

				}
				double classLinerate = 0;
				String className = individualClassJsonObject.getString("name");
				classCoverage.setClassName(className);
				if (individualClassJsonObject.has(CODE_LINERATE)) {
					classLinerate = individualClassJsonObject.getDouble(CODE_LINERATE);
					classCoverage.setClassLineRate(classLinerate);
					double classLineCoveragePercentage = classLinerate * 100;
					classCoverage.setClassLineCoveragePercentage(classLineCoveragePercentage);
				}
				if (individualClassJsonObject.has(CODE_BRANCHRATE)) {
					double classBranchRate = individualClassJsonObject.getDouble(CODE_BRANCHRATE);
					classCoverage.setClassBranchRate(classBranchRate);
				}

				JSONArray methodsJsonArray = new JSONArray();
				JSONObject methodsJsonObject = new JSONObject();
				if (individualClassJsonObject.get(CODE_METHODS) instanceof String) {

					classCoverage.setClassLineCoverage("0");
					classCoverage.setClassLineCoveragePercentage(0.0);

					String individualClassMethodCoverage = 0 + "/" + 0;

					classCoverage.setMethodCoverage(individualClassMethodCoverage);

					classCoverage.setMehodCoveragePercentage(0.0);
					classCoverage.setTotalMethodsInClass(0);
					classCoverage.setMethodsHit(0);
					classcoverageList.add(classCoverage);
					continue;
				}
				if (individualClassJsonObject.get(CODE_METHODS) instanceof JSONArray) {
					methodsJsonArray = (JSONArray) individualClassJsonObject.get(CODE_METHODS);
				} else {
					methodsJsonObject = (JSONObject) individualClassJsonObject.get(CODE_METHODS);
					methodsJsonArray.put(methodsJsonObject);
				}

				totalClassCountFromFile++;
				packegeLevelTotalClasses++;
				projectLevelTotalClasses++;

				if (classLinerate > 0.00) {
					classCoveredFromFile++;
					packageLevelClassesCovered++;
					projectLevelClassesCovered++;
					getWidget(classLinerate, fileName);
				} else {
					fileCovarage.put(fileName, Math.floor(0));
				}
				Set<MethodCoverageMetrics> methodList = getMethodsInfo(methodsJsonArray);
				classCoverage.setMethodMetrics(methodList);

				String idividualClassLineCoverage = classHitLines + "/" + classTotalLines;

				classCoverage.setClassLineCoverage(idividualClassLineCoverage);
				classCoverage.setClassTotalLines(classTotalLines);
				classCoverage.setClassLinesCovered(classHitLines);

				double idividualClassLineCoveragePercentage = ((double) classHitLines / (double) classTotalLines) * 100;
				classCoverage.setClassLineCoveragePercentage(idividualClassLineCoveragePercentage);
				fileCovarage.put(fileName, Math.floor(idividualClassLineCoveragePercentage));
				String individualClassMethodCoverage = methodsCovered + "/" + totalMethods;

				classCoverage.setMethodCoverage(individualClassMethodCoverage);
				double classMethodCoveragePercentage = ((double) methodsCovered / (double) totalMethods) * 100;

				classCoverage.setMehodCoveragePercentage(classMethodCoveragePercentage);
				classCoverage.setTotalMethodsInClass(totalMethods);
				classCoverage.setMethodsHit(methodsCovered);

				classCoverage.setClassCoveragePercentage(classLinerate * 100);
				double classesCoveragePercentage = ((double) classCoveredFromFile / (double) totalClassCountFromFile)
						* 100;
				classCoverage.setClassCoveragePercentage(classesCoveragePercentage);

				classcoverageList.add(classCoverage);

			}

		}
		return classcoverageList;

	}

	private Set<MethodCoverageMetrics> getMethodsInfo(JSONArray methodsJsonArray) {
		@SuppressWarnings({ "rawtypes", "unchecked" })
		Set<MethodCoverageMetrics> methodList = new HashSet();
		methodsCovered = 0;
		classCovered = 0;

		for (int i = 0; i < methodsJsonArray.length(); i++) {

			JSONArray methodJsonArray = new JSONArray();
			JSONObject methodJsonObject = new JSONObject();
			if (((JSONObject) methodsJsonArray.get(i)).get(CODE_METHOD) instanceof JSONArray) {
				methodJsonArray = (JSONArray) ((JSONObject) methodsJsonArray.get(i)).get(CODE_METHOD);
			} else {
				methodJsonObject = (JSONObject) ((JSONObject) methodsJsonArray.get(i)).get(CODE_METHOD);
				methodJsonArray.put(methodJsonObject);
			}
			totalMethods = 0;

			int methodsJsonArayLength = methodJsonArray.length();
			totalMethods = totalMethods + methodsJsonArayLength;
			packegeLevelTotalMethods = packegeLevelTotalMethods + methodsJsonArayLength;
			projectLevelTotalMethods = projectLevelTotalMethods + methodsJsonArayLength;
			for (int j = 0; j < methodJsonArray.length(); j++) {

				methodTotalLines = 0;
				methodHitLines = 0;
				methodCoverageFlag = false;
				classCoverageFlag = false;
				packageLevelclassFlag = false;
				packageLevelMethodsFlag = false;
				packageLevelLinesFlag = false;

				MethodCoverageMetrics methodCoverage = new MethodCoverageMetrics();
				JSONObject methodJson = (JSONObject) methodJsonArray.get(j);
				String methodName = methodJson.getString("name");
				methodCoverage.setMethodName(methodName);
				double methodLineRate = 0;
				if (methodJson.has(CODE_LINERATE)) {
					methodLineRate = methodJson.getDouble(CODE_LINERATE);
					methodCoverage.setMethodLineRate(methodLineRate);
					double methodCoveagePercentage = methodLineRate * 100;
					methodCoverage.setMethodCoveragePercentage(methodCoveagePercentage);
				}
				if (methodJson.has(CODE_BRANCHRATE)) {
					double methodBranchRate = methodJson.getDouble(CODE_BRANCHRATE);
					methodCoverage.setMethodBranchRate(methodBranchRate);
				}

				JSONArray linesJsonAray = new JSONArray();
				JSONObject linesJsonObject = new JSONObject();
				if (methodJson.get(CODE_LINES) instanceof JSONArray) {
					linesJsonAray = (JSONArray) methodJson.get(CODE_LINES);
				} else if (methodJson.get(CODE_LINES) instanceof String) {
					methodList.add(methodCoverage);
					continue;
				} else {
					linesJsonObject = (JSONObject) methodJson.get(CODE_LINES);
					linesJsonAray.put(linesJsonObject);
				}
				if (methodLineRate > 0.00) {
					projectLevelMethodsCovered++;
				}
				List<LinesCoverageMetrics> linesList = getLineInfo(linesJsonAray, methodCoverage);
				methodCoverage.setLineMetrics(linesList);
				methodList.add(methodCoverage);

			}
		}
		return methodList;
	}

	private List<LinesCoverageMetrics> getLineInfo(JSONArray linesJsonAray, MethodCoverageMetrics methodCoverage) {
		int hitCountForLines = 0;
		int totallineCountForLines = 0;

		List<LinesCoverageMetrics> linesList = new ArrayList<>();
		for (int i = 0; i < linesJsonAray.length(); i++) {

			// LOG.info("Line Type " + ((JSONObject)
			// linesJsonAray.get(i)).get(CODE_LINE).getClass().getName());
			JSONArray lineJsonArray = new JSONArray();
			JSONObject lineJsonObject = new JSONObject();
			LinesCoverageMetrics lineCoverage = new LinesCoverageMetrics();
			if (((JSONObject) linesJsonAray.get(i)).get(CODE_LINE) instanceof JSONArray) {
				lineJsonArray = (JSONArray) ((JSONObject) linesJsonAray.get(i)).get(CODE_LINE);
			} else if ((((JSONObject) linesJsonAray.get(i)).get(CODE_LINE)) instanceof String) {
				linesList.add(lineCoverage);
				continue;
			} else {
				lineJsonObject = (JSONObject) ((JSONObject) linesJsonAray.get(i)).get(CODE_LINE);
				lineJsonArray.put(lineJsonObject);
			}
			int lineJsonArayLength = lineJsonArray.length();

			methodTotalLines += lineJsonArayLength;
			classTotalLines += lineJsonArayLength;
			packageLevelTotalLines = packageLevelTotalLines + lineJsonArayLength;
			projectLevelTotalLines += lineJsonArayLength;
			for (int j = 0; j < lineJsonArray.length(); j++) {
				totallineCountForLines = totallineCountForLines + 1;

				lineCoverage = new LinesCoverageMetrics();
				int lineshit = ((JSONObject) lineJsonArray.get(j)).getInt("hits");
				lineCoverage.setLinesHit(lineshit);
				if (lineshit > 0) {
					hitCountForLines = hitCountForLines + 1;

					classHitLines++;
					methodHitLines++;

					methodCoverageFlag = true;
					classCoverageFlag = true;

					packageLevelLinesCovered++;
					projectLevelLinesCovered++;

					packageLevelMethodsFlag = true;
					projectLevelMethodsCoveredFlag = true;

				}
				int lineNumber = ((JSONObject) lineJsonArray.get(j)).getInt("number");
				lineCoverage.setLineNumbert(lineNumber);
				if (((JSONObject) lineJsonArray.get(j)).has("branch")) {
					boolean lineBranch = ((JSONObject) lineJsonArray.get(j)).getBoolean("branch");
					lineCoverage.setBranch(lineBranch);
				}

				if (lineJsonArray.get(j).toString().contains("condition-coverage")) {
					String lineConditionCoverage = ((JSONObject) lineJsonArray.get(j)).getString("condition-coverage");
					lineCoverage.setConditionCoverage(lineConditionCoverage);
					JSONArray linesConditionAray = new JSONArray();
					JSONObject linesConditionJsonObject = new JSONObject();

					if (((JSONObject) lineJsonArray.get(j)).get(CODE_CONDITIONS) instanceof JSONArray) {
						linesConditionAray = (JSONArray) ((JSONObject) lineJsonArray.get(j)).get(CODE_CONDITIONS);
					} else {
						linesConditionJsonObject = (JSONObject) ((JSONObject) lineJsonArray.get(j))
								.get(CODE_CONDITIONS);
						linesConditionAray.put(linesConditionJsonObject);
					}
					lineCoverage = getLinesCondition(linesConditionAray, lineCoverage);

				}

				linesList.add(lineCoverage);

			}
			if (methodCoverageFlag) {
				methodsCovered++;
			}
			if (classCoverageFlag) {
				classCovered++;
			}

			if (packageLevelMethodsFlag) {
				packageLevelMethodsCovered++;
			}
			String individualMethodCoverage = "";
			individualMethodCoverage = hitCountForLines + "/" + totallineCountForLines;

			methodCoverage.setMethodLineCoverage(individualMethodCoverage);

		}

		return linesList;

	}

	private LinesCoverageMetrics getLinesCondition(JSONArray linesConditionAray, LinesCoverageMetrics lineCoverage) {
		for (int i = 0; i < linesConditionAray.length(); i++) {
			JSONArray conditionJsonArray = new JSONArray();
			JSONObject conditionJsonObject = new JSONObject();
			if (((JSONObject) linesConditionAray.get(i)).get(CODE_CONDITION) instanceof JSONArray) {
				conditionJsonArray = (JSONArray) ((JSONObject) linesConditionAray.get(i)).get(CODE_CONDITION);
			} else {
				conditionJsonObject = (JSONObject) ((JSONObject) linesConditionAray.get(i)).get(CODE_CONDITION);
				conditionJsonArray.put(conditionJsonObject);
			}
			for (int j = 0; j < conditionJsonArray.length(); j++) {
				JSONObject conditionObject = (JSONObject) conditionJsonArray.get(j);
				String conditionCoverage = conditionObject.getString("coverage");
				lineCoverage.setCoverage(conditionCoverage);
				double conditionNumber = conditionObject.getDouble("number");
				lineCoverage.setCoverageNumber(conditionNumber);
				String conditionType = conditionObject.getString("type");
				lineCoverage.setConditionType(conditionType);
			}
		}
		return lineCoverage;
	}

	@Override
	public void downLoadFileToLocal(String fileLocation, String url, String userName, String password,
			CodeCoverageRepository repo, String pName) throws CodeCoverageExceptions, IOException {
		projectName = pName;

		String dwnldFolder = fileLocation;
		InputStream in=null;
		
		
		try {

			URL link = new URL(url);
			URLConnection con = link.openConnection();

			in = new BufferedInputStream(con.getInputStream());

			 try(FileOutputStream out = new FileOutputStream(dwnldFolder)){
			int i;
			while ((i = in.read()) != -1) {

				out.write(i);

			}
			in.close();
			 }

			JSONObject convertedJsonObjet = convertXMLToJson(fileLocation);
			@SuppressWarnings("unused")

			ProjectCodeCoverage projectCoverage = getDataFromJson(convertedJsonObjet, repo, "Backend");
		} catch (Exception fe) {
			// LOG.error(fe.getCause().toString());
			LOG.error("Exception  " + fe.getMessage());
			throw new CodeCoverageExceptions(fe);
		} finally {
			if(in!=null) {
				in.close();
			}
				
		}

	}

	private void getWidget(double classLinerate, String fileName) {
		if (classLinerate >= 0.80 && classLinerate < 1.00) {

			classCoverageGreaterThanEighty.add(fileName);
			greaterThanEightyCount++;

		} else if (Double.compare(classLinerate, 1.0) == 0) {
			classCoverageHundread.add(fileName);
			hundreadPercenteCount++;
		} else if (classLinerate >= 0.50 && classLinerate < 0.80) {
			classCoverageGreaterThanFifty.add(fileName);
			greaterThanFiftyCount++;

		} else if (classLinerate >= 0.35 && classLinerate < 0.50) {
			classCoverageGreaterThanThirtyFive.add(fileName);
			greaterThanThirtyfiveCount++;
		} else {
			classCoverageBetwenThirtyfive.add(fileName);
			betweenThirtyFiveCount++;
		}

	}

	public void getCoverageDataForUI(String uiDestinationLocation, String url, String username, String apiKey,
			CodeCoverageRepository repo, String projectName) throws CodeCoverageExceptions {
		this.projectName = projectName;

		try {

			JSONObject convertedJsonObjet = convertXMLToJson(uiDestinationLocation);
			ProjectCodeCoverage projectCoverage = getDataFromJson(convertedJsonObjet, repo, "UI");
		} catch (Exception fe) {
			LOG.error("Exception  " + fe.getMessage());
			throw new CodeCoverageExceptions(fe);
		}

	}

}