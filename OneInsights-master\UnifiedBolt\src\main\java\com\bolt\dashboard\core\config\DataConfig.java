package com.bolt.dashboard.core.config;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Properties;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.mongodb.config.AbstractMongoConfiguration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.stereotype.Component;

import com.bolt.dashboard.core.repository.RepositoryPackage;
import com.bolt.dashboard.core.repository.TemplatePackage;
import com.mongodb.MongoClient;
import com.mongodb.MongoClientOptions;
import com.mongodb.MongoCredential;
import com.mongodb.ReadPreference;
import com.mongodb.ServerAddress;
import com.mongodb.WriteConcern;

@Component
@EnableMongoRepositories(basePackageClasses = RepositoryPackage.class)
@ComponentScan(basePackageClasses = TemplatePackage.class)
public class DataConfig extends AbstractMongoConfiguration {

	 private static final Logger LOG = LogManager.getLogger(DataConfig.class);
	private static DataConfig myObj;
	MongoTemplate mongoTemplate = null;
	static AnnotationConfigApplicationContext ctx = null;
	@Value("${config.host}")
	private String host;
	@Value("${config.port}")
	private String port;
	@Value("${config.userId}")
	private String dbUserId;
	@Value("${config.db}")
	private String DB;
	@Value("${config.password}")
	private String dbPassword;
	@Value("${config.secret}")
	private String secret;
	MongoClient client = null;
	InputStream in = null;

	private DataConfig() {

	}

	public static DataConfig getInstance() {

		if (myObj == null) {
			myObj = new DataConfig();
		}
		return myObj;
	}

	@Override
	public String getDatabaseName() {
		// Properties properties = new Properties();
		// try {
		//
		// in =
		// getClass().getClassLoader().getResourceAsStream("ServerDataConfig.properties");
		// properties.load(in);
		//
		// } catch (IOException e) {
		// LOG.info(e);
		// }
		return DB;
	}

	@Override
	@Bean
	public MongoClient mongo() throws Exception {

		if (client == null) {
			if (!Configuration.flag) {
				if (DB.equals("${config.db}")) {
					Properties properties = new Properties();
					try {
						LOG.info("Data config");
						in = getClass().getClassLoader().getResourceAsStream("application.properties");
						properties.load(in);
						host = properties.getProperty("config.host");
						port = properties.getProperty("config.port");
						dbUserId = properties.getProperty("config.userId");
						dbPassword = properties.getProperty("config.password");
						secret = properties.getProperty("config.secret");
						System.out.println(secret);
						DB = properties.getProperty("config.db");
					} catch (IOException e) {
						LOG.info(e);
					}
				}
				Configuration.db = DB;
				Configuration.host = host;
				Configuration.port = port;
				Configuration.password = dbPassword;
				 Configuration.secret=secret;
				Configuration.userId = dbUserId;
				Configuration.flag = true;
			} else {
				host = Configuration.host;
				port = String.valueOf(Configuration.port);
				DB = Configuration.db;
				secret = Configuration.secret;
				dbUserId = Configuration.userId;
				dbPassword = Configuration.password;
			}
			MongoClientOptions mongoClientOptions = MongoClientOptions.builder().connectionsPerHost(50)
					.connectTimeout(10000).socketKeepAlive(false).cursorFinalizerEnabled(true)
					.maxConnectionLifeTime(25000).writeConcern(WriteConcern.SAFE)
					.threadsAllowedToBlockForConnectionMultiplier(50).readPreference(ReadPreference.primary()).build();

			String hosts[] = host.split(",");
			String ports[] = port.split(",");
			List<ServerAddress> replicasList = new ArrayList<>();
			for (int i = 0; i < hosts.length; i++) {
				ServerAddress serverAddr = new ServerAddress(hosts[i], Integer.parseInt(ports[i]));
				replicasList.add(serverAddr);
			}

			MongoCredential mongoCredential = MongoCredential.createScramSha1Credential(dbUserId, DB,
					dbPassword.toCharArray());
			client = new MongoClient(replicasList, Collections.singletonList(mongoCredential), mongoClientOptions);
			return client;

		}
		return client;

	}

	@Override
	protected String getMappingBasePackage() {
		return "com.bolt.dashboard.Model";
	}

	@Bean
	public MongoTemplate mongoTemplate() throws Exception {
		if (mongoTemplate == null) {
			mongoTemplate = new MongoTemplate(mongo(), getDatabaseName());
			return mongoTemplate;
		} else {
			return mongoTemplate;
		}
	}

	public static AnnotationConfigApplicationContext getContext() {
		if (ctx == null || !ctx.isActive()) {
			ctx = new AnnotationConfigApplicationContext(DataConfig.class);
			return ctx;
		} else {
			return ctx;
		}

	}

}