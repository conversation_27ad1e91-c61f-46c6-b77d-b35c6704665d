package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "ADConfiguration")
public class ActiveDirectoryConfiguration extends BaseModel {
    private String organisationName;
    private String host;
    private String port;
    private String basedn;
    private String $$hashKey;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getBasedn() {
        return basedn;
    }

    public void setBasedn(String basedn) {
        this.basedn = basedn;
    }

    public String get$$hashKey() {
        return $$hashKey;
    }

    public void set$$hashKey(String $$hashKey) {
        this.$$hashKey = $$hashKey;
    }

    public String getOrganisationName() {
        return organisationName;
    }

    public void setOrganisationName(String organisationName) {
        this.organisationName = organisationName;
    }

}
