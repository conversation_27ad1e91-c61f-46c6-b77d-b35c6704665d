package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import com.bolt.dashboard.core.model.BaseModel;
import com.bolt.dashboard.core.model.BuildToolMetric;

/**
 * <AUTHOR>
 *
 */
@Document(collection = "Build")
public class BuildTool extends BaseModel implements Comparable<BuildTool>  {

    private ObjectId collectorItemId;
    private long timestamp;
    private String name;
    private String jobName;
    private String url;
    private String version;
    private String buildType;
    private int buildID;
    private List<BuildInfo> buildInfoList;
    private Set<BuildToolMetric> metrics = new HashSet<BuildToolMetric>();
    private List<BuildSteps> stepsList;
    private List<String> jobList;
    private int jobCount;
    private String createdBy;
    private String branchName;
    private String repoName;
    private String groupName;
    private String triggerType;
    private String definitionId;
    public ObjectId getCollectorItemId() {
		return collectorItemId;
	}

	public void setCollectorItemId(ObjectId collectorItemId) {
		this.collectorItemId = collectorItemId;
	}

	public List<BuildSteps> getStepsList() {
		return stepsList;
	}

	public void setStepsList(List<BuildSteps> stepsList) {
		this.stepsList = stepsList;
	}

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public String getBranchName() {
		return branchName;
	}

	public void setBranchName(String branchName) {
		this.branchName = branchName;
	}

	public void setMetrics(Set<BuildToolMetric> metrics) {
		this.metrics = metrics;
	}
	private BuildFailurePatternForProjectInJenkinsModel patternDetails;
    
    public int getBuildID() {
        return buildID;
    }

    public void setBuildID(int buildID) {
        this.buildID = buildID;
    }

    /**
     * 
     * @return
     */
    public ObjectId getJenkinsItemId() {
        return collectorItemId;
    }

    /**
     * 
     * @param jenkinsItemId
     */
    public void setJenkinsItemId(ObjectId jenkinsItemId) {
        this.collectorItemId = jenkinsItemId;
    }

    /**
     * 
     * @return
     */

    public long getTimestamp() {
        return timestamp;
    }

    /**
     * 
     * @param timestamp
     */

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    /**
     * 
     * @return
     */
    public String getName() {
        return name;
    }

    /**
     * 
     * @param name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 
     * @return
     */

    public String getUrl() {
        return url;
    }

    /**
     * 
     * @param url
     */

    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * 
     * @return
     */
    public String getVersion() {
        return version;
    }

    /**
     * 
     * @param version
     */
    public void setVersion(String version) {
        this.version = version;
    }

    /**
     * @return the metrics
     */
    public Set<BuildToolMetric> getMetrics() {
        return metrics;
    }

    public String getBuildType() {
        return buildType;
    }

    public void setBuildType(String buildType) {
        this.buildType = buildType;
    }

    /**
     * @return the jobName
     */

    public List<BuildInfo> getBuildInfoList() {
        return buildInfoList;
    }

    public void setBuildInfoList(List<BuildInfo> buildInfoList) {
        this.buildInfoList = buildInfoList;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public List<String> getJobList() {
        return jobList;
    }

    public void setJobList(List<String> jobList) {
        this.jobList = jobList;
    }

    public int getJobCount() {
        return jobCount;
    }

    public void setJobCount(int jobCount) {
        this.jobCount = jobCount;
    }

    /**
     * @return the patternDetails
     */
    public BuildFailurePatternForProjectInJenkinsModel getPatternDetails() {
        return patternDetails;
    }

    /**
     * @param patternDetails
     *            the patternDetails to set
     */
    public void setPatternDetails(BuildFailurePatternForProjectInJenkinsModel patternDetails) {
        this.patternDetails = patternDetails;
    }
    @Override
    public int compareTo(final BuildTool o) {
        return Long.compare(this.buildID, o.buildID);
    }

	public String getTriggerType() {
		return triggerType;
	}

	public void setTriggerType(String triggerType) {
		this.triggerType = triggerType;
	}

	public String getRepoName() {
		return repoName;
	}

	public void setRepoName(String repoName) {
		this.repoName = repoName;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public String getDefinitionId() {
		return definitionId;
	}

	public void setDefinitionId(String definitionId) {
		this.definitionId = definitionId;
	}
	
}