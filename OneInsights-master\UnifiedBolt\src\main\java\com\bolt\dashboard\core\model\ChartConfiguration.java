package com.bolt.dashboard.core.model;

import java.util.HashSet;
import java.util.Set;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "ChartConfig")
public class ChartConfiguration extends BaseModel {

	private String pName;
	private String almType;
	private Set<ChartMetric> metrics = new HashSet<>();


	public String getAlmType() {
		return almType;
	}

	public void setAlmType(String almType) {
		this.almType = almType;
	}

	public String getpName() {
		return pName;
	}

	public void setpName(String pName) {
		this.pName = pName;
	}

	public Set<ChartMetric> getMetrics() {
		return metrics;
	}

	public void setMetrics(Set<ChartMetric> metrics) {
		this.metrics = metrics;
	}

}
