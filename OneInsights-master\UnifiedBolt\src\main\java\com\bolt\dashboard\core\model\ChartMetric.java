package com.bolt.dashboard.core.model;

public class ChartMetric {
	
	private String chartName;
	private Boolean project;
	private Boolean user;
	private Boolean admin;
	private String tabName;

	public ChartMetric(String chartName) {
		this.chartName = chartName;
	}
	
	public ChartMetric() {
		super();
		// TODO Auto-generated constructor stub
	}

	public String getChartName() {
		return chartName;
	}

	public void setChartName(String chartName) {
		this.chartName = chartName;
	}

	public Boolean getProject() {
		return project;
	}

	public void setProject(Boolean project) {
		this.project = project;
	}

	public Boolean getUser() {
		return user;
	}

	public void setUser(Boolean user) {
		this.user = user;
	}

	public Boolean getAdmin() {
		return admin;
	}

	public void setAdmin(Boolean admin) {
		this.admin = admin;
	}

	public String getTabName() {
		return tabName;
	}

	public void setTabName(String tabName) {
		this.tabName = tabName;
	}

}
