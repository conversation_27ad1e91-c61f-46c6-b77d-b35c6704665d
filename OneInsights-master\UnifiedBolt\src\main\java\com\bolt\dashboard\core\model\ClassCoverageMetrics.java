package com.bolt.dashboard.core.model;

import java.util.Set;

public class ClassCoverageMetrics {
    private double classComplexity;
    private String className;
    private String fileName;
    private double classLineRate;
    private double classBranchRate;
    private Set<MethodCoverageMetrics> methodMetrics;
    private String classCoverage;
    private double classCoveragePercentage;
    private String classLineCoverage;
    private double classLineCoveragePercentage;
    private int totalMethodsInClass;
    private int methodsHit;
    private String methodCoverage;
    private double mehodCoveragePercentage;

    private int classTotalLines;
    private int classLinesCovered;

    /**
     * @return the fileName
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * @param fileName
     *            the fileName to set
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Set<MethodCoverageMetrics> getMethodMetrics() {
        return methodMetrics;
    }

    public void setMethodMetrics(Set<MethodCoverageMetrics> methodMetrics) {
        this.methodMetrics = methodMetrics;
    }

    /**
     * @return the classLinesCovered
     */
    public int getClassLinesCovered() {
        return classLinesCovered;
    }

    /**
     * @param classLinesCovered
     *            the classLinesCovered to set
     */
    public void setClassLinesCovered(int classLinesCovered) {
        this.classLinesCovered = classLinesCovered;
    }

    /**
     * @return the classComplexity
     */
    public double getClassComplexity() {
        return classComplexity;
    }

    /**
     * @param classComplexity
     *            the classComplexity to set
     */
    public void setClassComplexity(double classComplexity) {
        this.classComplexity = classComplexity;
    }

    /**
     * @return the className
     */
    public String getClassName() {
        return className;
    }

    /**
     * @param className
     *            the className to set
     */
    public void setClassName(String className) {
        this.className = className;
    }

    /**
     * @return the classLineRate
     */
    public double getClassLineRate() {
        return classLineRate;
    }

    /**
     * @param classLineRate
     *            the classLineRate to set
     */
    public void setClassLineRate(double classLineRate) {
        this.classLineRate = classLineRate;
    }

    /**
     * @return the classBranchRate
     */
    public double getClassBranchRate() {
        return classBranchRate;
    }

    /**
     * @param classBranchRate
     *            the classBranchRate to set
     */
    public void setClassBranchRate(double classBranchRate) {
        this.classBranchRate = classBranchRate;
    }

    /**
     * @return the methodMetrics
     */

    /**
     * @return the classCoverage
     */

    /**
     * @return the classCoveragePercentage
     */
    public double getClassCoveragePercentage() {
        return classCoveragePercentage;
    }

    /**
     * @param classCoveragePercentage
     *            the classCoveragePercentage to set
     */
    public void setClassCoveragePercentage(Double classCoveragePercentage) {
        this.classCoveragePercentage = classCoveragePercentage;
    }

    /**
     * @return the classLineCoverage
     */
    public String getClassLineCoverage() {
        return classLineCoverage;
    }

    /**
     * @param classLineCoverage
     *            the classLineCoverage to set
     */
    public void setClassLineCoverage(String classLineCoverage) {
        this.classLineCoverage = classLineCoverage;
    }

    /**
     * @return the classLineCoveragePercentage
     */
    public double getClassLineCoveragePercentage() {
        return classLineCoveragePercentage;
    }

    /**
     * @param classLineCoveragePercentage
     *            the classLineCoveragePercentage to set
     */
    public void setClassLineCoveragePercentage(double classLineCoveragePercentage) {
        this.classLineCoveragePercentage = classLineCoveragePercentage;
    }

    /**
     * @return the totalMethodsInClass
     */
    public double getTotalMethodsInClass() {
        return totalMethodsInClass;
    }

    /**
     * @param totalMethods
     *            the totalMethodsInClass to set
     */
    public void setTotalMethodsInClass(int totalMethods) {
        this.totalMethodsInClass = totalMethods;
    }

    /**
     * @return the classCoverage
     */
    public String getClassCoverage() {
        return classCoverage;
    }

    /**
     * @param classCoverage
     *            the classCoverage to set
     */
    public void setClassCoverage(String classCoverage) {
        this.classCoverage = classCoverage;
    }

    /**
     * @return the methodsHit
     */
    public double getMethodsHit() {
        return methodsHit;
    }

    /**
     * @param methodsCovered
     *            the methodsHit to set
     */
    public void setMethodsHit(int methodsCovered) {
        this.methodsHit = methodsCovered;
    }

    /**
     * @param classCoveragePercentage
     *            the classCoveragePercentage to set
     */
    public void setClassCoveragePercentage(double classCoveragePercentage) {
        this.classCoveragePercentage = classCoveragePercentage;
    }

    /**
     * @return the methodCoverage
     */
    public String getMethodCoverage() {
        return methodCoverage;
    }

    /**
     * @param methodCoverage
     *            the methodCoverage to set
     */
    public void setMethodCoverage(String methodCoverage) {
        this.methodCoverage = methodCoverage;
    }

    /**
     * @return the mehodCoveragePercentage
     */
    public double getMehodCoveragePercentage() {
        return mehodCoveragePercentage;
    }

    /**
     * @param mehodCoveragePercentage
     *            the mehodCoveragePercentage to set
     */
    public void setMehodCoveragePercentage(double mehodCoveragePercentage) {
        this.mehodCoveragePercentage = mehodCoveragePercentage;
    }

    /**
     * @return the classTotalLines
     */
    public int getClassTotalLines() {
        return classTotalLines;
    }

    /**
     * @param classTotalLines
     *            the classTotalLines to set
     */
    public void setClassTotalLines(int classTotalLines) {
        this.classTotalLines = classTotalLines;
    }
    /**
     * @return the hitLines
     */
    /**
     * @return the classHitLines
     */

}
