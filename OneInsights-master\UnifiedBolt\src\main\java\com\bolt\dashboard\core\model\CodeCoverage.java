package com.bolt.dashboard.core.model;

import java.util.HashSet;
import java.util.Set;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "codecoverage")
public class CodeCoverage extends BaseModel {
	private ObjectId collectorItemId;
	private long timestamp;
	private String fileName;
	private double classLineRate;
	private double classBranchRate;
	private double classComplexity;
	private Set<CodeCoverageMetrics> metrics = new HashSet<>();
	private String classCoverage;
	private String classCoveragePercentage;

	/**
	 * @return the collectorItemId
	 */
	public ObjectId getCollectorItemId() {
		return collectorItemId;
	}

	/**
	 * @param collectorItemId
	 *            the collectorItemId to set
	 */
	public void setCollectorItemId(ObjectId collectorItemId) {
		this.collectorItemId = collectorItemId;
	}

	/**
	 * @return the timestamp
	 */
	public long getTimestamp() {
		return timestamp;
	}

	/**
	 * @param timestamp
	 *            the timestamp to set
	 */
	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	/**
	 * @return the fileName
	 */
	public String getFileName() {
		return fileName;
	}

	/**
	 * @param fileName
	 *            the fileName to set
	 */
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	/**
	 * @return the classLineRate
	 */
	public double getClassLineRate() {
		return classLineRate;
	}

	/**
	 * @param classLineRate
	 *            the classLineRate to set
	 */
	public void setClassLineRate(double classLineRate) {
		this.classLineRate = classLineRate;
	}

	/**
	 * @return the classBranchRate
	 */
	public double getClassBranchRate() {
		return classBranchRate;
	}

	/**
	 * @param classBranchRate
	 *            the classBranchRate to set
	 */
	public void setClassBranchRate(double classBranchRate) {
		this.classBranchRate = classBranchRate;
	}

	/**
	 * @return the classComplexity
	 */
	public double getClassComplexity() {
		return classComplexity;
	}

	/**
	 * @param classComplexity
	 *            the classComplexity to set
	 */
	public void setClassComplexity(double classComplexity) {
		this.classComplexity = classComplexity;
	}

	/**
	 * @return the metrics
	 */
	public Set<CodeCoverageMetrics> getMetrics() {
		return metrics;
	}

	/**
	 * @param metrics
	 *            the metrics to set
	 */
	public void setMetrics(Set<CodeCoverageMetrics> metrics) {
		this.metrics = metrics;
	}

	/**
	 * @return the classCoverage
	 */
	public String getClassCoverage() {
		return classCoverage;
	}

	/**
	 * @param classCoverage
	 *            the classCoverage to set
	 */
	public void setClassCoverage(String classCoverage) {
		this.classCoverage = classCoverage;
	}

	/**
	 * @return the classCoveragePercentage
	 */
	public String getClassCoveragePercentage() {
		return classCoveragePercentage;
	}

	/**
	 * @param classCoveragePercentage
	 *            the classCoveragePercentage to set
	 */
	public void setClassCoveragePercentage(String classCoveragePercentage) {
		this.classCoveragePercentage = classCoveragePercentage;
	}

}