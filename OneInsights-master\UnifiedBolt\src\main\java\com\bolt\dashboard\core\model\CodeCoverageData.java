package com.bolt.dashboard.core.model;

public class CodeCoverageData {

	private int projectLevelTotalLines;
	private int projectLevelLinesCovered;
	private int projectLevelTotalClasses;
	private int projectLevelClassesCovered;

	private int greaterThanEightyCount;
	private int greaterThanFiftyCount;
	private int greaterThanThirtyFiveCount;
	private int betweenThirtyFiveCount;
	private int hundraedPercentaCount;

	public int getProjectLevelTotalLines() {
		return projectLevelTotalLines;
	}

	public void setProjectLevelTotalLines(int projectLevelTotalLines) {
		this.projectLevelTotalLines = projectLevelTotalLines;
	}

	public int getProjectLevelLinesCovered() {
		return projectLevelLinesCovered;
	}

	public void setProjectLevelLinesCovered(int projectLevelLinesCovered) {
		this.projectLevelLinesCovered = projectLevelLinesCovered;
	}

	public int getProjectLevelTotalClasses() {
		return projectLevelTotalClasses;
	}

	public void setProjectLevelTotalClasses(int projectLevelTotalClasses) {
		this.projectLevelTotalClasses = projectLevelTotalClasses;
	}

	public int getProjectLevelClassesCovered() {
		return projectLevelClassesCovered;
	}

	public void setProjectLevelClassesCovered(int projectLevelClassesCovered) {
		this.projectLevelClassesCovered = projectLevelClassesCovered;
	}

	public int getGreaterThanEightyCount() {
		return greaterThanEightyCount;
	}

	public void setGreaterThanEightyCount(int greaterThanEightyCount) {
		this.greaterThanEightyCount = greaterThanEightyCount;
	}

	public int getGreaterThanFiftyCount() {
		return greaterThanFiftyCount;
	}

	public void setGreaterThanFiftyCount(int greaterThanFiftyCount) {
		this.greaterThanFiftyCount = greaterThanFiftyCount;
	}

	public int getGreaterThanThirtyFiveCount() {
		return greaterThanThirtyFiveCount;
	}

	public void setGreaterThanThirtyFiveCount(int greaterThanThirtyFiveCount) {
		this.greaterThanThirtyFiveCount = greaterThanThirtyFiveCount;
	}

	public int getBetweenThirtyFiveCount() {
		return betweenThirtyFiveCount;
	}

	public void setBetweenThirtyFiveCount(int betweenThirtyFiveCount) {
		this.betweenThirtyFiveCount = betweenThirtyFiveCount;
	}

	public int getHundraedPercentaCount() {
		return hundraedPercentaCount;
	}

	public void setHundraedPercentaCount(int hundraedPercentaCount) {
		this.hundraedPercentaCount = hundraedPercentaCount;
	}
}