package com.bolt.dashboard.core.model;

import java.util.HashSet;
import java.util.Set;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "Code_Quality")
public class CodeQuality extends BaseModel {
	private ObjectId collectorItemId;
	private long timestamp;

	private String name;
	private String url;
	private String version;
	private String instance;
	private Set<CodeQualityMetric> metrics = new HashSet<CodeQualityMetric>();
	private Set<CodeQualityComponents> components = new HashSet<CodeQualityComponents>();

	public ObjectId getCollectorItemId() {
		return collectorItemId;
	}

	public void setCollectorItemId(ObjectId collectorItemId) {
		this.collectorItemId = collectorItemId;
	}

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getInstance() {
		return instance;
	}

	public void setInstance(String instance) {
		this.instance = instance;
	}

	public Set<CodeQualityComponents> getComponents() {
		return components;
	}

	public void setComponents(Set<CodeQualityComponents> components) {
		this.components = components;
	}

	public Set<CodeQualityMetric> getMetrics() {
		return metrics;
	}

	public void setMetrics(Set<CodeQualityMetric> metrics) {
		this.metrics = metrics;
	}

}
