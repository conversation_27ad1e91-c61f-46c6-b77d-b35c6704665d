package com.bolt.dashboard.core.model;

import java.util.HashSet;
import java.util.Set;

public class CodeQualityComponents {
    private String componentName;
    private long moduleCreatedDate;
    private Set<CodeQualityComponentsMetrics> componentsMetrices = new HashSet<CodeQualityComponentsMetrics>();

    public String getComponentName() {
        return componentName;
    }

    public void setComponentName(String componentName) {
        this.componentName = componentName;
    }

    public Set<CodeQualityComponentsMetrics> getComponents() {
        return componentsMetrices;
    }

    public void setComponents(Set<CodeQualityComponentsMetrics> components) {
        this.componentsMetrices = components;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        return componentName.equals(((CodeQualityComponents) o).componentName);
    }

    @Override
    public int hashCode() {
        return componentName.hashCode();
    }

    public long getModuleCreatedDate() {
        return moduleCreatedDate;
    }

    public void setModuleCreatedDate(long moduleCreatedDate) {
        this.moduleCreatedDate = moduleCreatedDate;
    }
}
