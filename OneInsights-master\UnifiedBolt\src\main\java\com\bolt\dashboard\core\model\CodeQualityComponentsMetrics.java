package com.bolt.dashboard.core.model;

public class CodeQualityComponentsMetrics {
    private String name;
    private Object value;
    private String formattedValue;
    private String statusMessage;

    public CodeQualityComponentsMetrics(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public String getFormattedValue() {
        return formattedValue;
    }

    public void setFormattedValue(String formattedValue) {
        this.formattedValue = formattedValue;
    }

    public String getStatusMessage() {
        return statusMessage;
    }

    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        return name.equals(((CodeQualityComponentsMetrics) o).name);
    }

    @Override
    public int hashCode() {
        return name.hashCode();
    }
}
