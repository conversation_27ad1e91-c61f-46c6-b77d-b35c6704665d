package com.bolt.dashboard.core.model;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collector;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import com.bolt.dashboard.core.model.BaseModel;

@Document(collection = "collector_items")
public class CollectorItem extends BaseModel {

    private String description;
    private boolean enabled;
    private ObjectId collectorId;
    private Map<String, Object> options = new HashMap<String, Object>();

    private Collector collector;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public ObjectId getCollectorId() {
        return collectorId;
    }

    public void setCollectorId(ObjectId collectorId) {
        this.collectorId = collectorId;
    }

    public Collector getCollector() {
        return collector;
    }

    public void setCollector(Collector collector) {
        this.collector = collector;
    }

    public Map<String, Object> getOptions() {
        return options;
    }

}
