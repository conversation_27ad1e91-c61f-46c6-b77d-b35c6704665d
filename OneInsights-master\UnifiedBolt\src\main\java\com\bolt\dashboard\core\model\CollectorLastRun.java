/**
 *
 */
package com.bolt.dashboard.core.model;

import java.util.Map;

import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 *
 */
@Document(collection = "LastRun")
public class CollectorLastRun extends BaseModel {
    private String projectName;
    private long timeStamp;
    private Map<String, String[]> lastRun;

    public String getProjectName() {
	return projectName;
    }

    public void setProjectName(String projectName) {
	this.projectName = projectName;
    }

    public long getTimeStamp() {
	return timeStamp;
    }

    public void setTimeStamp(long timeStamp) {
	this.timeStamp = timeStamp;
    }

    public Map<String, String[]> getLastRun() {
	return lastRun;
    }

    public void setLastRun(Map<String, String[]> lastRun) {
	this.lastRun = lastRun;
    }
}
