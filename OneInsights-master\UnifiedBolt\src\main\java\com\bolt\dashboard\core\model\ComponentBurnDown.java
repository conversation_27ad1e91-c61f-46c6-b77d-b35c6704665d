package com.bolt.dashboard.core.model;

import java.util.List;

import com.bolt.dashboard.util.BurnDownDataSprint;


public class  ComponentBurnDown{
	
	List<BurnDownDataSprint> burnDownDataSprint;
	String component;

	public String getComponent() {
		return component;
	}
	public void setComponent(String component) {
		this.component = component;
	}
	public List<BurnDownDataSprint> getBurnDownDataSprint() {
		return burnDownDataSprint;
	}
	public void setBurnDownDataSprint(List<BurnDownDataSprint> burnDownDataSprint) {
		this.burnDownDataSprint = burnDownDataSprint;
	}
	
}
