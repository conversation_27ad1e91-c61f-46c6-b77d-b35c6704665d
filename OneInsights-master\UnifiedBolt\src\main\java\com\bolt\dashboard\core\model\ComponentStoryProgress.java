package com.bolt.dashboard.core.model;

import java.util.List;

import com.bolt.dashboard.util.StoryProgressSprintwise;

public class  ComponentStoryProgress{
	public List<StoryProgressSprintwise> getStoryProgressList() {
		return storyProgressList;
	}
	public void setStoryProgressList(List<StoryProgressSprintwise> storyProgressList) {
		this.storyProgressList = storyProgressList;
	}
	public String getComponent() {
		return component;
	}
	public void setComponent(String component) {
		this.component = component;
	}
	List<StoryProgressSprintwise> storyProgressList;
	String component;
}
