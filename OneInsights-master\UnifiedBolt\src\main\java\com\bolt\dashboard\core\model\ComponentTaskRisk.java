package com.bolt.dashboard.core.model;

import java.util.List;

import com.bolt.dashboard.util.StoryProgressSprintwise;
import com.bolt.dashboard.util.TaskRiskSprint;

public class  ComponentTaskRisk{
	
	List<TaskRiskSprint> taskRiskSprintList;
	String component;
	public List<TaskRiskSprint> getTaskRiskSprintList() {
		return taskRiskSprintList;
	}
	public void setTaskRiskSprintList(List<TaskRiskSprint> taskRiskSprintList) {
		this.taskRiskSprintList = taskRiskSprintList;
	}
	public String getComponent() {
		return component;
	}
	public void setComponent(String component) {
		this.component = component;
	}
	
}
