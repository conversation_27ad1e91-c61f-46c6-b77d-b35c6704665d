package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class CycleTimeResponse {
	private List<Map> issueList =new ArrayList<>();
	private Long totalCount;
	private int throughput;
	public List<Map> getIssueList() {
		return issueList;
	}
	public Long getTotalCount() {
		return totalCount;
	}
	public void setTotalCount(Long totalCount) {
		this.totalCount = totalCount;
	}
	public int getThroughput() {
		return throughput;
	}
	public void setThroughput(int throughput) {
		this.throughput = throughput;
	}
	public void setIssueList(List<Map> issueList) {
		this.issueList = issueList;
	}
	
	
	
	
	
}
