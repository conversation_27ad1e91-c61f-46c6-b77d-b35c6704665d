package com.bolt.dashboard.core.model;

import java.util.List;
import java.util.Map;

public class DefectInsightData {
   
	
	private String priority;
	List<Map<String, String>> averageDefectWaitTime;
    private long meanTimeToFix;
    private long meanTimeToResolveDefects;
    private long meanTimeFixCount;
    private long meanTimeResolveCount;
	List<MetricAgeData > metricData;
    public long getMeanTimeToFix() {
		return meanTimeToFix;
	}
	public void setMeanTimeToFix(long meanTimeToFix) {
		this.meanTimeToFix = meanTimeToFix;
	}
	public long getMeanTimeToResolveDefects() {
		return meanTimeToResolveDefects;
	}
	public void setMeanTimeToResolveDefects(long meanTimeToResolveDefects) {
		this.meanTimeToResolveDefects = meanTimeToResolveDefects;
	}
	public long getMeanTimeFixCount() {
		return meanTimeFixCount;
	}
	public void setMeanTimeFixCount(long meanTimeFixCount) {
		this.meanTimeFixCount = meanTimeFixCount;
	}
	public long getMeanTimeResolveCount() {
		return meanTimeResolveCount;
	}
	public void setMeanTimeResolveCount(long meanTimeResolveCount) {
		this.meanTimeResolveCount = meanTimeResolveCount;
	}
	
	
	
	public List<MetricAgeData> getMetricData() {
		return metricData;
	}
	public void setMetricData(List<MetricAgeData> metricData) {
		this.metricData = metricData;
	}
	
	
	
	public String getPriority() {
		return priority;
	}
	public void setPriority(String priority) {
		this.priority = priority;
	}
	public List<Map<String, String>> getAverageDefectWaitTime() {
		return averageDefectWaitTime;
	}
	public void setAverageDefectWaitTime(List<Map<String, String>> averageDefectWaitTime) {
		this.averageDefectWaitTime = averageDefectWaitTime;
	}
	
	
}
