package com.bolt.dashboard.core.model;

import java.util.LinkedHashSet;
import java.util.Set;
import java.util.TreeMap;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "DefectPainpoint")
public class DefectPainpoint {
	@Id
	private String id;
	private long timestamp;
	private String projectName;
	private String[] excludeStates;
	private TreeMap<Long,Double> ppThreshold;
	private boolean absolute;
	private Set<DefectPainpointRules> rules = new LinkedHashSet<>();

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public Set<DefectPainpointRules> getRules() {
		return rules;
	}

	public void setRules(Set<DefectPainpointRules> rules) {
		this.rules = rules;
	}

	public TreeMap<Long,Double> getPpThreshold() {
		return ppThreshold;
	}

	public void setPpThreshold(TreeMap<Long,Double> ppThreshold) {
		this.ppThreshold = ppThreshold;
	}

	public boolean isAbsolute() {
		return absolute;
	}

	public void setAbsolute(boolean absolute) {
		this.absolute = absolute;
	}

	public String[] getExcludeStates() {
		return excludeStates;
	}

	public void setExcludeStates(String[] excludeStates) {
		this.excludeStates = excludeStates;
	}

	

}
