package com.bolt.dashboard.core.model;

import java.util.LinkedHashSet;
import java.util.Set;

public class DefectPainpointRules {
    private String displayName;
    private String ruleName;
    private int weightage;
    private Set<DefectPainpointMetrics> metrics = new LinkedHashSet();

    public String getDisplayName() {
	return displayName;
    }

    public void setDisplayName(String displayName) {
	this.displayName = displayName;
    }

    public String getRuleName() {
	return ruleName;
    }

    public void setRuleName(String ruleName) {
	this.ruleName = ruleName;
    }

    public int getWeightage() {
	return weightage;
    }

    public void setWeightage(int weightage) {
	this.weightage = weightage;
    }

    public Set<DefectPainpointMetrics> getMetrics() {
	return metrics;
    }

    public void setMetrics(Set<DefectPainpointMetrics> metrics) {
	this.metrics = metrics;
    }

    @Override
    public boolean equals(Object o) {
	if (this == o)
	    return true;
	if (o == null || getClass() != o.getClass())
	    return false;

	return ruleName.equals(((DefectPainpointMetrics) o).getName());
    }

    @Override
    public int hashCode() {
	return super.hashCode();
    }
}
