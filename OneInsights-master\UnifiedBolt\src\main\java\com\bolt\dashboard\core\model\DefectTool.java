package com.bolt.dashboard.core.model;

public class DefectTool {
    private String projectId;
    private String defectId;
    private String defectDescription;
    private String defectDetectedPhase;
    private String defectDisposition;
    private String defectEnvsystem;
    private String defectImpact;
    private long defectLoggedOn;
    private String defectName;
    private String defectNumber;
    private String defectPriority;
    private String defectReson;
    private String defectSeverity;
    private String closedBy;
    private String closedInRelease;
    private String closedInSprint;
    private String deferredInRelease;
    private String deferredInSprint;
    private String fixedInRelease;
    private String fixedInSprint;
    private String foundInRelease;
    private String foundInSprint;
    private String openedBy;
    private String reTestedInRelease;
    private String reTestedInSprint;
    private String resolvedBy;
    private String defectState;
    private String defectStepsToReproduce;
    private String applicationId;
    private String assignedTo;

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getDefectId() {
        return defectId;
    }

    public void setDefectId(String defectId) {
        this.defectId = defectId;
    }

    public String getDefectDescription() {
        return defectDescription;
    }

    public void setDefectDescription(String defectDescription) {
        this.defectDescription = defectDescription;
    }

    public String getDefectDetectedPhase() {
        return defectDetectedPhase;
    }

    public void setDefectDetectedPhase(String defectDetectedPhase) {
        this.defectDetectedPhase = defectDetectedPhase;
    }

    public String getDefectDisposition() {
        return defectDisposition;
    }

    public void setDefectDisposition(String defectDisposition) {
        this.defectDisposition = defectDisposition;
    }

    public String getDefectEnvsystem() {
        return defectEnvsystem;
    }

    public void setDefectEnvsystem(String defectEnvsystem) {
        this.defectEnvsystem = defectEnvsystem;
    }


    public String getDefectImpact() {
        return defectImpact;
    }

    public void setDefectImpact(String defectImpact) {
        this.defectImpact = defectImpact;
    }

    public long getDefectLoggedOn() {
        return defectLoggedOn;
    }

    public void setDefectLoggedOn(long defectLoggedOn) {
        this.defectLoggedOn = defectLoggedOn;
    }

    public String getDefectName() {
        return defectName;
    }

    public void setDefectName(String defectName) {
        this.defectName = defectName;
    }

    public String getDefectNumber() {
        return defectNumber;
    }

    public void setDefectNumber(String defectNumber) {
        this.defectNumber = defectNumber;
    }

    public String getDefectPriority() {
        return defectPriority;
    }

    public void setDefectPriority(String defectPriority) {
        this.defectPriority = defectPriority;
    }

    public String getDefectReson() {
        return defectReson;
    }

    public void setDefectReson(String defectReson) {
        this.defectReson = defectReson;
    }

    public String getDefectSeverity() {
        return defectSeverity;
    }

    public void setDefectSeverity(String defectSeverity) {
        this.defectSeverity = defectSeverity;
    }

    public String getDefectStepsToReproduce() {
        return defectStepsToReproduce;
    }

    public void setDefectStepsToReproduce(String defectStepsToReproduce) {
        this.defectStepsToReproduce = defectStepsToReproduce;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getAssignedTo() {
        return assignedTo;
    }

    public void setAssignedTo(String assignedTo) {
        this.assignedTo = assignedTo;
    }

    public String getClosedBy() {
        return closedBy;
    }

    public void setClosedBy(String closedBy) {
        this.closedBy = closedBy;
    }

    public String getClosedInRelease() {
        return closedInRelease;
    }

    public void setClosedInRelease(String closedInRelease) {
        this.closedInRelease = closedInRelease;
    }

    public String getClosedInSprint() {
        return closedInSprint;
    }

    public void setClosedInSprint(String closedInSprint) {
        this.closedInSprint = closedInSprint;
    }

    public String getDeferredInRelease() {
        return deferredInRelease;
    }

    public void setDeferredInRelease(String deferredInRelease) {
        this.deferredInRelease = deferredInRelease;
    }

    public String getDeferredInSprint() {
        return deferredInSprint;
    }

    public void setDeferredInSprint(String deferredInSprint) {
        this.deferredInSprint = deferredInSprint;
    }

    public String getFixedInRelease() {
        return fixedInRelease;
    }

    public void setFixedInRelease(String fixedInRelease) {
        this.fixedInRelease = fixedInRelease;
    }

    public String getFixedInSprint() {
        return fixedInSprint;
    }

    public void setFixedInSprint(String fixedInSprint) {
        this.fixedInSprint = fixedInSprint;
    }

    public String getFoundInRelease() {
        return foundInRelease;
    }

    public void setFoundInRelease(String foundInRelease) {
        this.foundInRelease = foundInRelease;
    }

    public String getFoundInSprint() {
        return foundInSprint;
    }

    public void setFoundInSprint(String foundInSprint) {
        this.foundInSprint = foundInSprint;
    }

    public String getOpenedBy() {
        return openedBy;
    }

    public void setOpenedBy(String openedBy) {
        this.openedBy = openedBy;
    }

    public String getReTestedInRelease() {
        return reTestedInRelease;
    }

    public void setReTestedInRelease(String reTestedInRelease) {
        this.reTestedInRelease = reTestedInRelease;
    }

    public String getReTestedInSprint() {
        return reTestedInSprint;
    }

    public void setReTestedInSprint(String reTestedInSprint) {
        this.reTestedInSprint = reTestedInSprint;
    }

    public String getResolvedBy() {
        return resolvedBy;
    }

    public void setResolvedBy(String resolvedBy) {
        this.resolvedBy = resolvedBy;
    }

    public String getDefectState() {
        return defectState;
    }

    public void setDefectState(String defectState) {
        this.defectState = defectState;
    }

}
