package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "SCM")
public class DeletedFileDetails {

    private String committer;
    private String fileName;
    private long deletedDateTime;

    public String getCommitter() {
        return committer;
    }

    public void setCommitter(String committer) {
        this.committer = committer;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public long getDeletedDateTime() {
        return deletedDateTime;
    }

    public void setDeletedDateTime(long deletedDateTime) {
        this.deletedDateTime = deletedDateTime;
    }

}
