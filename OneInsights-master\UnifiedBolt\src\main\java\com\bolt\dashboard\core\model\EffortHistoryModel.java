package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "EffortHistory")
public class EffortHistoryModel extends BaseModel {
    private long changeEsmt;
    private double initialWork;
    private Long logDate;
    private String loggedBy;
    private String pName;
    private String projKey;

    private double remainingWork;

    private double remainingWorkDec;

    private double remainingWorkInc;
    private String sName;
    private Integer timeSpent;
    private String wId;

    public long getChangeEsmt() {
	return changeEsmt;
    }

    public double getInitialWork() {
	return initialWork;
    }

    public Long getLogDate() {
	return logDate;
    }

    public String getLoggedBy() {
	return loggedBy;
    }

    public String getpName() {
	return pName;
    }

    public double getRemainingWork() {
	return remainingWork;
    }

    public double getRemainingWorkDec() {
	return remainingWorkDec;
    }

    public double getRemainingWorkInc() {
	return remainingWorkInc;
    }

    public String getsName() {
	return sName;
    }

    public Integer getTimeSpent() {
	return timeSpent;
    }

    public String getwId() {
	return wId;
    }

    public void setChangeEsmt(long changeEsmt) {
	this.changeEsmt = changeEsmt;
    }

    public void setInitialWork(double initialWork) {
	this.initialWork = initialWork;
    }

    public void setLogDate(Long logDate) {
	this.logDate = logDate;
    }

    public void setLoggedBy(String loggedBy) {
	this.loggedBy = loggedBy;
    }

    public void setpName(String pName) {
	this.pName = pName;
    }

    public void setRemainingWork(double remainingWork) {
	this.remainingWork = remainingWork;
    }

    public void setRemainingWorkDec(double remainingWorkDec) {
	this.remainingWorkDec = remainingWorkDec;
    }

    public void setRemainingWorkInc(double remainingWorkInc) {
	this.remainingWorkInc = remainingWorkInc;
    }

    public void setsName(String sName) {
	this.sName = sName;
    }

    public void setTimeSpent(Integer timeSpent) {
	this.timeSpent = timeSpent;
    }

    public void setwId(String wId) {
	this.wId = wId;
    }

    public String getProjKey() {
	return projKey;
    }

    public void setProjKey(String projKey) {
	this.projKey = projKey;
    }

}
