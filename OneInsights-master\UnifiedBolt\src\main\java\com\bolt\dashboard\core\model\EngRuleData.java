package com.bolt.dashboard.core.model;

public class EngRuleData {
	
	
	public EngRuleData(String ruleName, String subParameter, String parameter, String weightage) {
		super();
		this.ruleName = ruleName;
		this.subParameter = subParameter;
		this.parameter = parameter;
		this.weightage = weightage;
	}
	public EngRuleData() {
		super();
		// TODO Auto-generated constructor stub
	}
	private String id;
	private String ruleName;
	private String subParameter;
	private String parameter;
	private String weightage;
	private Double score;
	private String value;
	private String comment;
	
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getRuleName() {
		return ruleName;
	}
	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}
	public Double getScore() {
		return score;
	}
	public void setScore(Double score) {
		this.score = score;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public String getSubParameter() {
		return subParameter;
	}
	public void setSubParameter(String subParameter) {
		this.subParameter = subParameter;
	}
	public String getParameter() {
		return parameter;
	}
	public void setParameter(String parameter) {
		this.parameter = parameter;
	}
	public String getWeightage() {
		return weightage;
	}
	public void setWeightage(String weightage) {
		this.weightage = weightage;
	}
	public String getComment() {
		return comment;
	}
	public void setComment(String comment) {
		this.comment = comment;
	}
	
	
	
}
