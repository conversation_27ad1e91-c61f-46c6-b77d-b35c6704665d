package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

public class EngRulesBasedOnMonth implements Comparable<EngRulesBasedOnMonth> {
	String monthName;
    String displayMonth;
    String inference;
    double engScore;
    List<EngRuleData> ruleData =new ArrayList<EngRuleData>();
	List<EngParamData> paramData =new ArrayList<EngParamData>();
    List<EngSubParamData> subParamData=new ArrayList<EngSubParamData>();
    List<EngAreaData> areaData=new ArrayList<EngAreaData>();
	public double getEngScore() {
		return engScore;
	}

	public void setEngScore(double engScore) {
		this.engScore = engScore;
	}

	
	public String getInference() {
		return inference;
	}

	public void setInference(String inference) {
		this.inference = inference;
	}

	public List<EngSubParamData> getSubParamData() {
		return subParamData;
	}

	public void setSubParamData(List<EngSubParamData> subParamData) {
		this.subParamData = subParamData;
	}

	public List<EngAreaData> getAreaData() {
		return areaData;
	}

	public void setAreaData(List<EngAreaData> areaData) {
		this.areaData = areaData;
	}

	public String getDisplayMonth() {
		return displayMonth;
	}

	public void setDisplayMonth(String displayMonth) {
		this.displayMonth = displayMonth;
	}

	public String getMonthName() {
		return monthName;
	}

	public void setMonthName(String monthName) {
		this.monthName = monthName;
		
	}

	public List<EngParamData> getParamData() {
		return paramData;
	}

	public void setParamData(List<EngParamData> paramData) {
		this.paramData = paramData;
	}

	public List<EngRuleData> getRuleData() {
		return ruleData;
	}

	public void setRuleData(List<EngRuleData> ruleData) {
		this.ruleData = ruleData;
	}

	@Override
	public int compareTo(EngRulesBasedOnMonth o) {
		
		double val1=Double.parseDouble(o.getMonthName());
		double val2=Double.parseDouble(this.getMonthName());
		return (int)(val2-val1);
	}
}
