package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.mongodb.core.mapping.Document;
@Document(collection = "EngScorecard")
public class EngScorecard extends BaseModel  {
	private String pName;
	private  List<EngScorecardSprint> engScoreCardSprint = new ArrayList<>();
	public String getpName() {
		return pName;
	}
	public void setpName(String pName) {
		this.pName = pName;
	}
	public List<EngScorecardSprint> getEngScoreCardSprint() {
		return engScoreCardSprint;
	}
	public void setEngScoreCardSprint(List<EngScorecardSprint> engScoreCardSprint) {
		this.engScoreCardSprint = engScoreCardSprint;
	}

	
}
