package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

public class EngScorecardSprint {
	
	private String sprintName;

	private long startDate;
	private long endDate;
	private String state;
	private String unitOfSizing;
	private String releaseIterationNo;
	private String teamSize;
	private List<EngScorecardParamData> engScoreParamData = new ArrayList<>();
	
	public String getTeamSize() {
		return teamSize;
	}
	public void setTeamSize(String teamSize) {
		this.teamSize = teamSize;
	}
	public String getUnitOfSizing() {
		return unitOfSizing;
	}
	public void setUnitOfSizing(String unitOfSizing) {
		this.unitOfSizing = unitOfSizing;
	}
	public String getReleaseIterationNo() {
		return releaseIterationNo;
	}
	public void setReleaseIterationNo(String releaseIterationNo) {
		this.releaseIterationNo = releaseIterationNo;
	}
	
	
	public String getSprintName() {
		return sprintName;
	}
	public void setSprintName(String sprintName) {
		this.sprintName = sprintName;
	}
	public long getStartDate() {
		return startDate;
	}
	public void setStartDate(long startDate) {
		this.startDate = startDate;
	}
	public long getEndDate() {
		return endDate;
	}
	public void setEndDate(long endDate) {
		this.endDate = endDate;
	}
	public List<EngScorecardParamData> getEngScoreParamData() {
		return engScoreParamData;
	}
	public void setEngScoreParamData(List<EngScorecardParamData> engScoreParamData) {
		this.engScoreParamData = engScoreParamData;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	
}
