package com.bolt.dashboard.core.model;

import java.util.Map;

public class EngScorecardSubjectiveSprintData {

	private String sprintName;
	private double teamSize;
	private String retriLink;
	private String unitOfSizing;
	private String releaseIterationNo;
	private String remarks;
	private Map<String,String> backlogGromming;
	private Map<String,String> dailyCheckPoint;
	private Map<String,String> externalDependency;
	private Map<String,String> readinessIndex0;
	private Map<String,String> readinessIndex1;
	private Map<String,String> readinessIndex2;
	private Map<String,String> readinessIndex3;
	private Map<String,String> sprintGoalsMet;
	private Map<String,String> zeroMidSprintChanges;
	private Map<String,String> commitedStoryPoints;
	private Map<String,String> leadTime;
	private Map<String,String> cycleTime;
	private Map<String,String> velocity;
	private Map<String,String> coverage;
	private Map<String,String> criticalIssues;
	private Map<String,String> addedIssues;
	private Map<String,String> closedIssues;
	private Map<String,String> ccrTickets;
	private Map<String,String> complexity;
	private Map<String,String> blockerViolations;
	private Map<String,String> criticalViolations;
	private Map<String,String> technicalDebt;
	private Map<String,String> mttr;
	private Map<String,String> capacity;
	private Map<String,String> capacityAtSprintEnd;
	private Map<String,String> hoursSpent;
	private Map<String,String> codeReviewDefects;
	private Map<String,String> sitDefects;
	private Map<String,String> uatDefects;
	private Map<String,String> customerReportedDefects;
	private Map<String,String> tDeleveredOnTime;
	private Map<String,String> tmoveToProduction;
	private Map<String,String> twithUATDefects;
	private Map<String,String> testAutomation;
	private Map<String,String> sprintDemoPulse;
	private Map<String,String> retrospectionActionOpened;
	private Map<String,String> retrospectionActionClosed;
	private Map<String,String> retrospectionIndex;
	private Map<String,String> securityRating;
	private Map<String,String> deploymentFrequency;
	private Map<String,String> leadTimeForChanges;
	private Map<String,String> changeFailureRate;

	
	public Map<String, String> getReadinessIndex0() {
		return readinessIndex0;
	}
	public void setReadinessIndex0(Map<String, String> readinessIndex0) {
		this.readinessIndex0 = readinessIndex0;
	}
	public Map<String, String> getSprintDemoPulse() {
		return sprintDemoPulse;
	}
	public void setSprintDemoPulse(Map<String, String> sprintDemoPulse) {
		this.sprintDemoPulse = sprintDemoPulse;
	}
	public Map<String, String> getRetrospectionActionOpened() {
		return retrospectionActionOpened;
	}
	public void setRetrospectionActionOpened(Map<String, String> retrospectionActionOpened) {
		this.retrospectionActionOpened = retrospectionActionOpened;
	}
	public Map<String, String> getRetrospectionActionClosed() {
		return retrospectionActionClosed;
	}
	public void setRetrospectionActionClosed(Map<String, String> retrospectionActionClosed) {
		this.retrospectionActionClosed = retrospectionActionClosed;
	}
	public Map<String, String> getRetrospectionIndex() {
		return retrospectionIndex;
	}
	public void setRetrospectionIndex(Map<String, String> retrospectionIndex) {
		this.retrospectionIndex = retrospectionIndex;
	}
	public Map<String, String> getSecurityRating() {
		return securityRating;
	}
	public void setSecurityRating(Map<String, String> securityRating) {
		this.securityRating = securityRating;
	}
	public Map<String, String> getDeploymentFrequency() {
		return deploymentFrequency;
	}
	public void setDeploymentFrequency(Map<String, String> deploymentFrequency) {
		this.deploymentFrequency = deploymentFrequency;
	}
	public Map<String, String> getLeadTimeForChanges() {
		return leadTimeForChanges;
	}
	public void setLeadTimeForChanges(Map<String, String> leadTimeForChanges) {
		this.leadTimeForChanges = leadTimeForChanges;
	}
	public Map<String, String> getChangeFailureRate() {
		return changeFailureRate;
	}
	public void setChangeFailureRate(Map<String, String> changeFailureRate) {
		this.changeFailureRate = changeFailureRate;
	}
	public Map<String, String> getSitDefects() {
		return sitDefects;
	}
	public void setSitDefects(Map<String, String> sitDefects) {
		this.sitDefects = sitDefects;
	}
	public Map<String, String> getUatDefects() {
		return uatDefects;
	}
	public void setUatDefects(Map<String, String> uatDefects) {
		this.uatDefects = uatDefects;
	}
	public String getReleaseIterationNo() {
		return releaseIterationNo;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	public void setReleaseIterationNo(String releaseIterationNo) {
		this.releaseIterationNo = releaseIterationNo;
	}
	public Map<String, String> getTestAutomation() {
		return testAutomation;
	}
	public void setTestAutomation(Map<String, String> testAutomation) {
		this.testAutomation = testAutomation;
	}
	public String getUnitOfSizing() {
		return unitOfSizing;
	}
	public void setUnitOfSizing(String unitOfSizing) {
		this.unitOfSizing = unitOfSizing;
	}
	public Map<String, String> getCapacityAtSprintEnd() {
		return capacityAtSprintEnd;
	}
	public void setCapacityAtSprintEnd(Map<String, String> capacityAtSprintEnd) {
		this.capacityAtSprintEnd = capacityAtSprintEnd;
	}
	public Map<String, String> getHoursSpent() {
		return hoursSpent;
	}
	public void setHoursSpent(Map<String, String> hoursSpent) {
		this.hoursSpent = hoursSpent;
	}
	public Map<String, String> getCodeReviewDefects() {
		return codeReviewDefects;
	}
	public void setCodeReviewDefects(Map<String, String> codeReviewDefects) {
		this.codeReviewDefects = codeReviewDefects;
	}
	public Map<String, String> getCustomerReportedDefects() {
		return customerReportedDefects;
	}
	public void setCustomerReportedDefects(Map<String, String> customerReportedDefects) {
		this.customerReportedDefects = customerReportedDefects;
	}
	public Map<String, String> gettDeleveredOnTime() {
		return tDeleveredOnTime;
	}
	public void settDeleveredOnTime(Map<String, String> tDeleveredOnTime) {
		this.tDeleveredOnTime = tDeleveredOnTime;
	}
	public Map<String, String> getTmoveToProduction() {
		return tmoveToProduction;
	}
	public void setTmoveToProduction(Map<String, String> tmoveToProduction) {
		this.tmoveToProduction = tmoveToProduction;
	}
	public Map<String, String> getTwithUATDefects() {
		return twithUATDefects;
	}
	public void setTwithUATDefects(Map<String, String> twithUATDefects) {
		this.twithUATDefects = twithUATDefects;
	}

	public Map<String, String> getCapacity() {
		return capacity;
	}
	public void setCapacity(Map<String, String> capacity) {
		this.capacity = capacity;
	}
	public Map<String, String> getMttr() {
		return mttr;
	}
	public void setMttr(Map<String, String> mttr) {
		this.mttr = mttr;
	}
	public Map<String, String> getComplexity() {
		return complexity;
	}
	public void setComplexity(Map<String, String> complexity) {
		this.complexity = complexity;
	}
	public Map<String, String> getBlockerViolations() {
		return blockerViolations;
	}
	public void setBlockerViolations(Map<String, String> blockerViolations) {
		this.blockerViolations = blockerViolations;
	}
	public Map<String, String> getCriticalViolations() {
		return criticalViolations;
	}
	public void setCriticalViolations(Map<String, String> criticalViolations) {
		this.criticalViolations = criticalViolations;
	}
	public Map<String, String> getTechnicalDebt() {
		return technicalDebt;
	}
	public void setTechnicalDebt(Map<String, String> technicalDebt) {
		this.technicalDebt = technicalDebt;
	}
	public Map<String, String> getAddedIssues() {
		return addedIssues;
	}
	public Map<String, String> getCcrTickets() {
		return ccrTickets;
	}
	public void setCcrTickets(Map<String, String> ccrTickets) {
		this.ccrTickets = ccrTickets;
	}
	public void setAddedIssues(Map<String, String> addedIssues) {
		this.addedIssues = addedIssues;
	}
	public Map<String, String> getClosedIssues() {
		return closedIssues;
	}
	public void setClosedIssues(Map<String, String> closedIssues) {
		this.closedIssues = closedIssues;
	}
	public Map<String, String> getZeroMidSprintChanges() {
		return zeroMidSprintChanges;
	}
	public void setZeroMidSprintChanges(Map<String, String> zeroMidSprintChanges) {
		this.zeroMidSprintChanges = zeroMidSprintChanges;
	}
	public Map<String, String> getCommitedStoryPoints() {
		return commitedStoryPoints;
	}
	public void setCommitedStoryPoints(Map<String, String> commitedStoryPoints) {
		this.commitedStoryPoints = commitedStoryPoints;
	}
	public Map<String, String> getLeadTime() {
		return leadTime;
	}
	public void setLeadTime(Map<String, String> leadTime) {
		this.leadTime = leadTime;
	}
	public Map<String, String> getCycleTime() {
		return cycleTime;
	}
	public void setCycleTime(Map<String, String> cycleTime) {
		this.cycleTime = cycleTime;
	}
	public Map<String, String> getVelocity() {
		return velocity;
	}
	public void setVelocity(Map<String, String> velocity) {
		this.velocity = velocity;
	}
	public Map<String, String> getCoverage() {
		return coverage;
	}
	public void setCoverage(Map<String, String> coverage) {
		this.coverage = coverage;
	}
	public Map<String, String> getCriticalIssues() {
		return criticalIssues;
	}
	public void setCriticalIssues(Map<String, String> criticalIssues) {
		this.criticalIssues = criticalIssues;
	}
	private long stDate;
	public long getEndDate() {
		return endDate;
	}
	public void setEndDate(long endDate) {
		this.endDate = endDate;
	}
	private long endDate;
	public String getRetriLink() {
		return retriLink;
	}
	public void setRetriLink(String retriLink) {
		this.retriLink = retriLink;
	}
	public String getSprintName() {
		return sprintName;
	}
	public void setSprintName(String sprintName) {
		this.sprintName = sprintName;
	}
	
	public Map<String, String> getBacklogGromming() {
		return backlogGromming;
	}
	public void setBacklogGromming(Map<String, String> backlogGromming) {
		this.backlogGromming = backlogGromming;
	}
	public Map<String, String> getDailyCheckPoint() {
		return dailyCheckPoint;
	}
	public void setDailyCheckPoint(Map<String, String> dailyCheckPoint) {
		this.dailyCheckPoint = dailyCheckPoint;
	}
	public Map<String, String> getExternalDependency() {
		return externalDependency;
	}
	public void setExternalDependency(Map<String, String> externalDependency) {
		this.externalDependency = externalDependency;
	}
	public Map<String, String> getReadinessIndex1() {
		return readinessIndex1;
	}
	public void setReadinessIndex1(Map<String, String> readinessIndex1) {
		this.readinessIndex1 = readinessIndex1;
	}
	public Map<String, String> getReadinessIndex2() {
		return readinessIndex2;
	}
	public void setReadinessIndex2(Map<String, String> readinessIndex2) {
		this.readinessIndex2 = readinessIndex2;
	}
	public Map<String, String> getReadinessIndex3() {
		return readinessIndex3;
	}
	public void setReadinessIndex3(Map<String, String> readinessIndex3) {
		this.readinessIndex3 = readinessIndex3;
	}
	public Map<String, String> getSprintGoalsMet() {
		return sprintGoalsMet;
	}
	public void setSprintGoalsMet(Map<String, String> sprintGoalsMet) {
		this.sprintGoalsMet = sprintGoalsMet;
	}
	public double getTeamSize() {
		return teamSize;
	}
	public void setTeamSize(double teamSize) {
		this.teamSize = teamSize;
	}
	public long getStDate() {
		return stDate;
	}
	public void setStDate(long stDate) {
		this.stDate = stDate;
	}

	
}
