package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

public class Engagement {

	private String[] towers;
	private String[] parArray;
	private String[] subArray;
	private List<EngagementConfigMetrics> engMetrics = new ArrayList<>();
	public String[] getTowers() {
		return towers;
	}
	public void setTowers(String[] towers) {
		this.towers = towers;
	}
	public String[] getParArray() {
		return parArray;
	}
	public void setParArray(String[] parArray) {
		this.parArray = parArray;
	}
	public String[] getSubArray() {
		return subArray;
	}
	public void setSubArray(String[] subArray) {
		this.subArray = subArray;
	}
	public List<EngagementConfigMetrics> getEngMetrics() {
		return engMetrics;
	}
	public void setEngMetrics(List<EngagementConfigMetrics> engMetrics) {
		this.engMetrics = engMetrics;
	}
	
	
	
	
}
