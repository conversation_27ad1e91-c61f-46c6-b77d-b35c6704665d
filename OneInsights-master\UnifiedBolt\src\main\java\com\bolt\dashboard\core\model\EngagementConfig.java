package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "EIConfig")
public class EngagementConfig extends BaseModel {
    private String towerName;
    private String towerType;
    private List<EngagementConfigMetrics> engMetrics = new ArrayList<>();
	public String getTowerName() {
		return towerName;
	}
	public void setTowerName(String towerName) {
		this.towerName = towerName;
	}
	public String getTowerType() {
		return towerType;
	}
	public void setTowerType(String towerType) {
		this.towerType = towerType;
	}
	public List<EngagementConfigMetrics> getEngMetrics() {
		return engMetrics;
	}
	public void setEngMetrics(List<EngagementConfigMetrics> engMetrics) {
		this.engMetrics = engMetrics;
	}
   

}
