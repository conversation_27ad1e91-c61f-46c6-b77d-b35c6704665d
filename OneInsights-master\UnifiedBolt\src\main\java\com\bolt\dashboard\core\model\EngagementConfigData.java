package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

public class EngagementConfigData {
	private String subParameter;
	private String ruleName;
	private String id;
	private int orderId;
	private String description;
	private String guidelines;
	private Boolean enabled;
	private boolean phiCalculatedFlag;
	 private List<EngagementGuides> engGuides = new ArrayList<>();
	public String getSubParameter() {
		return subParameter;
	}
	public void setSubParameter(String subParameter) {
		this.subParameter = subParameter;
	}
	public String getRuleName() {
		return ruleName;
	}
	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getGuidelines() {
		return guidelines;
	}
	public void setGuidelines(String guidelines) {
		this.guidelines = guidelines;
	}
	public List<EngagementGuides> getEngGuides() {
		return engGuides;
	}
	public void setEngGuides(List<EngagementGuides> engGuides) {
		this.engGuides = engGuides;
	}
	public Boolean getEnabled() {
		return enabled;
	}
	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}
	public String getId() {
		return id;
	}
	public void setId(String ruleId) {
		this.id = ruleId;
	}
	public int getOrderId() {
		return orderId;
	}
	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}
	public boolean isPhiCalculatedFlag() {
		return phiCalculatedFlag;
	}
	public void setPhiCalculatedFlag(boolean phiCalculatedFlag) {
		this.phiCalculatedFlag = phiCalculatedFlag;
	}
	
	 
}