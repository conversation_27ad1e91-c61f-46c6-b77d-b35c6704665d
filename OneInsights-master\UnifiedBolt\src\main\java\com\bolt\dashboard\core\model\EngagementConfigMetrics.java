package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

public class EngagementConfigMetrics {
	private String parameter;
	private String weightage;
	private String parType;

	private List<EngagementConfigData> engData = new ArrayList<>();
	public String getParameter() {
		return parameter;
	}
	public void setParameter(String parameter) {
		this.parameter = parameter;
	}
	public String getWeightage() {
		return weightage;
	}
	public void setWeightage(String weightage) {
		this.weightage = weightage;
	}
	public String getParType() {
		return parType;
	}
	public void setParType(String parType) {
		this.parType = parType;
	}
	public List<EngagementConfigData> getEngData() {
		return engData;
	}
	public void setEngData(List<EngagementConfigData> engData) {
		this.engData = engData;
	}
	


}