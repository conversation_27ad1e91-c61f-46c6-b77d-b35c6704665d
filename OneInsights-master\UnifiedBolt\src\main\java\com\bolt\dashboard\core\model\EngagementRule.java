package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "EngRules")
public class EngagementRule extends BaseModel {

	private String towerName;
	private String projectName;
	//private String paramType;
	 private List<EngRulesBasedOnMonth> listOfRulesBasedOnMonths =new ArrayList<EngRulesBasedOnMonth>();

	public String getTowerName() {
		return towerName;
	}

	public List<EngRulesBasedOnMonth> getListOfRulesBasedOnMonths() {
		return listOfRulesBasedOnMonths;
	}

	public void setListOfRulesBasedOnMonths(List<EngRulesBasedOnMonth> listOfRulesBasedOnMonths) {
		this.listOfRulesBasedOnMonths = listOfRulesBasedOnMonths;
	}

	public void setTowerName(String towerName) {
		this.towerName = towerName;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	
}
