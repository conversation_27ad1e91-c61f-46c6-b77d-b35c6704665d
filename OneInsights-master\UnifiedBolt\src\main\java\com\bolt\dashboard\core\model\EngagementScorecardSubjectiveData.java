package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.mongodb.core.mapping.Document;
@Document(collection = "EngagementScorecardSubjectiveData")
public class EngagementScorecardSubjectiveData extends BaseModel {

	private String pName; 
	private List<EngScorecardSubjectiveSprintData> engScorecardSprintData = new ArrayList<>();
	public String getpName() {
		return pName;
	}
	public void setpName(String pName) {
		this.pName = pName;
	}
	public List<EngScorecardSubjectiveSprintData> getEngScorecardSprintData() {
		return engScorecardSprintData;
	}
	public void setEngScorecardSprintData(List<EngScorecardSubjectiveSprintData> engScorecardSprintData) {
		this.engScorecardSprintData = engScorecardSprintData;
	}
	
	
	
	
	
}
