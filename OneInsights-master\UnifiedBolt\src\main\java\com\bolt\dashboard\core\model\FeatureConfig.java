package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "FeatureConfig")
public class FeatureConfig extends BaseModel {
	 private boolean engScorecardEnable;
	 private boolean safeEnable;
	 private boolean portfolioEnable;
	 private boolean engIndexEnable;
	 private String pName;
	 private boolean lpmEnable;
	 public boolean isPhiEnable() {
		return phiEnable;
	}
	public void setPhiEnable(boolean phiEnable) {
		this.phiEnable = phiEnable;
	}
	private boolean phiEnable;
	public boolean isLpmEnable() {
		return lpmEnable;
	}
	public void setLpmEnable(boolean lpmEnable) {
		this.lpmEnable = lpmEnable;
	}
	public boolean isEngIndexEnable() {
		return engIndexEnable;
	}
	public void setEngIndexEnable(boolean engIndexEnable) {
		this.engIndexEnable = engIndexEnable;
	}
	public boolean isEngScorecardEnable() {
		return engScorecardEnable;
	}
	public void setEngScorecardEnable(boolean engScorecardEnable) {
		this.engScorecardEnable = engScorecardEnable;
	}
	public boolean isSafeEnable() {
		return safeEnable;
	}
	public void setSafeEnable(boolean safeEnable) {
		this.safeEnable = safeEnable;
	}
	public boolean isPortfolioEnable() {
		return portfolioEnable;
	}
	public void setPortfolioEnable(boolean portfolioEnable) {
		this.portfolioEnable = portfolioEnable;
	}
	public String getpName() {
		return pName;
	}
	public void setpName(String pName) {
		this.pName = pName;
	}
	

}
