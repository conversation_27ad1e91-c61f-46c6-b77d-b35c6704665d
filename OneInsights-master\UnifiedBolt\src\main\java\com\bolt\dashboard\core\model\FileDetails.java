package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "SCM")
public class FileDetails {
    private int insertioninFile;
    private int deletioninFile;
    private int modificationinFile;
    private String filename;
    private String filePath;
    private int totalChengesInFile;

    /**
     * @return the insertioninFile
     */
    public int getInsertioninFile() {
        return insertioninFile;
    }

    /**
     * @param insertioninFile
     *            the insertioninFile to set
     */
    public void setInsertioninFile(int insertioninFile) {
        this.insertioninFile = insertioninFile;
    }

    /**
     * @return the deletioninFile
     */
    public int getDeletioninFile() {
        return deletioninFile;
    }

    /**
     * @param deletioninFile
     *            the deletioninFile to set
     */
    public void setDeletioninFile(int deletioninFile) {
        this.deletioninFile = deletioninFile;
    }

    /**
     * @return the modificationinFile
     */
    public int getModificationinFile() {
        return modificationinFile;
    }

    /**
     * @param modificationinFile
     *            the modificationinFile to set
     */
    public void setModificationinFile(int modificationinFile) {
        this.modificationinFile = modificationinFile;
    }

    /**
     * @return the filename
     */
    public String getFilename() {
        return filename;
    }

    /**
     * @param filename
     *            the filename to set
     */
    public void setFilename(String filename) {
        this.filename = filename;
    }

    /**
     * @return the totalChengesInFile
     */
    public int getTotalChengesInFile() {
        return totalChengesInFile;
    }

    /**
     * @param totalChengesInFile
     *            the totalChengesInFile to set
     */
    public void setTotalChengesInFile(int totalChengesInFile) {
        this.totalChengesInFile = totalChengesInFile;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

}
