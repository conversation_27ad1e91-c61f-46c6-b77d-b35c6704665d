package com.bolt.dashboard.core.model;

import com.bolt.dashboard.core.model.GoalMetric;

public class GoalMetric {
    private String name;
    private String displayName;
    private String goal;
    private String selected;
    private String operator;

    public GoalMetric(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getGoal() {
        return goal;
    }

    public void setGoal(String goal) {
        this.goal = goal;
    }

    public String getSelected() {
        return selected;
    }

    public void setSelected(String selected) {
        this.selected = selected;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        return name.equals(((GoalMetric) o).name);
    }

    @Override
    public int hashCode() {
        return name.hashCode();
    }
}
