package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "Goal")
public class GoalSetting extends BaseModel {
    private long timestamp;
    private String name;
    private String url;
    private String projectName;
    private Set<GoalMetric> metrics = new HashSet<>();
    private List<ProjectHealthConfig> config = new ArrayList<>();

    
    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
  
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
    
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
    public Set<GoalMetric> getMetrics() {
        return metrics;
    }

	public List<ProjectHealthConfig> getConfig() {
		return config;
	}

	public void setConfig(List<ProjectHealthConfig> config) {
		this.config = config;
	}
    
    
}
