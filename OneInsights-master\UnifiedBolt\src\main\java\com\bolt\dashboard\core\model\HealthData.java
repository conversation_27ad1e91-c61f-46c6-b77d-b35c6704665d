package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "Health")
public class HealthData extends BaseModel {
    private String projectName;
    private String sprintStatus;
    private String sprintName;
    private long timeStamp;
    private List<HealthProjectSprintMetrics> shealthdataMetrics = new ArrayList<>();
    private List<HealthSprintMetrics> sprintHealthMetrics = new ArrayList<>();
    private List<sprintPredictedBugMetrics> sprintPredictedBugMetrics= new ArrayList<>();
    public String getSprintStatus() {
        return sprintStatus;
    }

    public void setSprintStatus(String sprintStatus) {
        this.sprintStatus = sprintStatus;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getSprintName() {
        return sprintName;
    }

    public void setSprintName(String sprintName) {
        this.sprintName = sprintName;
    }

    public List<HealthProjectSprintMetrics> getShealthdataMetrics() {
        return shealthdataMetrics;
    }

    public void setShealthdataMetrics(List<HealthProjectSprintMetrics> shealthdataMetrics) {
        this.shealthdataMetrics = shealthdataMetrics;
    }

	public long getTimeStamp() {
		return timeStamp;
	}

	public void setTimeStamp(long timeStamp) {
		this.timeStamp = timeStamp;
	}

	public List<HealthSprintMetrics> getSprintHealthMetrics() {
		return sprintHealthMetrics;
	}

	public void setSprintHealthMetrics(List<HealthSprintMetrics> sprintHealthMetrics) {
		this.sprintHealthMetrics = sprintHealthMetrics;
	}

	public List<sprintPredictedBugMetrics> getSprintPredictedBugMetrics() {
		return sprintPredictedBugMetrics;
	}

	public void setSprintPredictedBugMetrics(List<sprintPredictedBugMetrics> sprintPredictedBugMetrics) {
		this.sprintPredictedBugMetrics = sprintPredictedBugMetrics;
	}

}
