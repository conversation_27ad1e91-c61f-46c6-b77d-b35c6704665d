package com.bolt.dashboard.core.model;

public class HealthDataMetrics {
	private String ruleName;
	private String operator;
	private int weightage;
	private double value;
	private int sprintTDValue;
	private String goal;
	private String goalValue;
	private String resultColor;
	private int sprintValue;
	private int sprintPoints;
	private String developerState;
	private String sprintValueAsString;
	private double beta;
	private double beta1;

	public String getGoalValue() {
		return goalValue;
	}

	public void setGoalValue(String goalValue) {
		this.goalValue = goalValue;
	}

	public String getSprintValueAsString() {
		return sprintValueAsString;
	}

	public void setSprintValueAsString(String sprintValueAsString) {
		this.sprintValueAsString = sprintValueAsString;
	}

	public int getWeightage() {
		return weightage;
	}

	public void setWeightage(int weightage) {
		this.weightage = weightage;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getGoal() {
		return goal;
	}

	public void setGoal(String goal) {
		this.goal = goal;
	}

	public String getRuleName() {
		return ruleName;
	}

	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}

	public int getSprintTDValue() {
		return sprintTDValue;
	}

	public void setSprintTDValue(int sprintTDValue) {
		this.sprintTDValue = sprintTDValue;
	}

	public int getSprintValue() {
		return sprintValue;
	}

	public void setSprintValue(int sprintValue) {
		this.sprintValue = sprintValue;
	}

	public int getSprintPoints() {
		return sprintPoints;
	}

	public void setSprintPoints(int sprintPoints) {
		this.sprintPoints = sprintPoints;
	}

	public double getValue() {
		return value;
	}

	public void setValue(double value) {
		this.value = value;
	}

	public String getResultColor() {
		return resultColor;
	}

	public void setResultColor(String resultColor) {
		this.resultColor = resultColor;
	}

	public String getDeveloperState() {
		return developerState;
	}

	public void setDeveloperState(String developerState) {
		this.developerState = developerState;
	}

	public double getBeta() {
		return beta;
	}

	public void setBeta(double beta) {
		this.beta = beta;
	}

	public double getBeta1() {
		return beta1;
	}

	public void setBeta1(double beta1) {
		this.beta1 = beta1;
	}

}
