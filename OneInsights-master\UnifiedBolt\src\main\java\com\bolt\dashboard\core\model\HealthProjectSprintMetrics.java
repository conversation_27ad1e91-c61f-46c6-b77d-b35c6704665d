package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

public class HealthProjectSprintMetrics {
	private String sprintName;
	private String phaseStatus;
	private String phaseName;
	private int phaseValue;
	private String iterationName;
	private List<HealthDataMetrics> healthdataMetrics = new ArrayList<>();

	public List<HealthDataMetrics> getHealthdataMetrics() {
		return healthdataMetrics;
	}

	public void setHealthdataMetrics(List<HealthDataMetrics> healthdataMetrics) {
		this.healthdataMetrics = healthdataMetrics;
	}

	public String getSprintName() {
		return sprintName;
	}

	public void setSprintName(String sprintName) {
		this.sprintName = sprintName;
	}

	public String getPhaseName() {
		return phaseName;
	}

	public void setPhaseName(String phaseName) {
		this.phaseName = phaseName;
	}

	public String getPhaseStatus() {
		return phaseStatus;
	}

	public void setPhaseStatus(String phaseStatus) {
		this.phaseStatus = phaseStatus;
	}

	public int getPhaseValue() {
		return phaseValue;
	}

	public void setPhaseValue(int phaseValue) {
		this.phaseValue = phaseValue;
	}

	public String getIterationName() {
		return iterationName;
	}

	public void setIterationName(String iterationName) {
		this.iterationName = iterationName;
	}
}
