/**
 * 
 */
package com.bolt.dashboard.core.model;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 *
 */
@Document(collection="HightLight")
public class HighLightModel extends BaseModel {
    
    private String projectName;
    private String userName;
    private long timestamp;
    private Set<HighLightProjectRuleSet> rulesListOfProject= new HashSet<>();
   
    /**
     * @return the projectName
     */
    public String getProjectName() {
        return projectName;
    }
    /**
     * @param projectName the projectName to set
     */
    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
    /**
     * @return the userName
     */
    public String getUserName() {
        return userName;
    }
    /**
     * @param userName the userName to set
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }
    /**
     * @return the timestamp
     */
    public long getTimestamp() {
        return timestamp;
    }
    /**
     * @param timestamp the timestamp to set
     */
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    /**
     * @return the rulesListOfProject
     */
    public Set<HighLightProjectRuleSet> getRulesListOfProject() {
        return rulesListOfProject;
    }
    /**
     * @param rulesListOfProject the rulesListOfProject to set
     */
   
    public void setRulesListOfProject(Set<HighLightProjectRuleSet> rulesListOfProject) {
        this.rulesListOfProject = rulesListOfProject;
    }

 
}
