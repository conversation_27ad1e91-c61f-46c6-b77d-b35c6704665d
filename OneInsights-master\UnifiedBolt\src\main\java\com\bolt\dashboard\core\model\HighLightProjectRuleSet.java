/**
 * 
 */
package com.bolt.dashboard.core.model;

/**
 * <AUTHOR>
 *
 */
public class HighLightProjectRuleSet {

    private boolean rulePass;
    private String tabName;
    // This is the value set by the User. later to be named as "parameter".
    private int value;
    private String messageSuccess;
    private String messageFailure;
    private String ruleName;
    private String description;
    private String operator;
    private boolean status;
    private int cycleTimeDays;
    private double averageEstimationForStoryPoint1;
    private double averageEstimationForStoryPoint2;
    private double averageEstimationForStoryPoint3;
    private double averageEstimationForStoryPoint5;
    private double averageEstimationForStoryPoint8;
    private double averageEstimationForStoryPoint13;
    private double averageEstimationForStoryPoint21;
    // This value is derived from the logic of calculation.
    private double actualValue;

    public HighLightProjectRuleSet() {

    }

    /**
     * @return the tabName
     */
    public String getTabName() {
        return tabName;
    }

    /**
     * @param tabName
     *            the tabName to set
     */
    public void setTabName(String tabName) {
        this.tabName = tabName;
    }

    /**
     * @return the percentage
     */
    public int getValue() {
        return value;
    }

    /**
     * @param percentage
     *            the percentage to set
     */
    public void setValue(int value) {
        this.value = value;
    }

    /**
     * @return the messageSuccess
     */
    public String getMessageSuccess() {
        return messageSuccess;
    }

    /**
     * @param messageSuccess
     *            the messageSuccess to set
     */
    public void setMessageSuccess(String messageSuccess) {
        this.messageSuccess = messageSuccess;
    }

    /**
     * @return the messageFailure
     */
    public String getMessageFailure() {
        return messageFailure;
    }

    /**
     * @param messageFailure
     *            the messageFailure to set
     */
    public void setMessageFailure(String messageFailure) {
        this.messageFailure = messageFailure;
    }

    /**
     * @return the ruleName
     */
    public String getRuleName() {
        return ruleName;
    }

    /**
     * @param ruleName
     *            the ruleName to set
     */
    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    /**
     * @return the description
     */
    public String getDescription() {
        return description;
    }

    /**
     * @param description
     *            the description to set
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * @return the status
     */
    public boolean isStatus() {
        return status;
    }

    /**
     * @param status
     *            the status to set
     */
    public void setStatus(boolean status) {
        this.status = status;
    }

    /**
     * @return the cycleTimeDays
     */
    public int getCycleTimeDays() {
        return cycleTimeDays;
    }

    /**
     * @param cycleTimeDays
     *            the cycleTimeDays to set
     */
    public void setCycleTimeDays(int cycleTimeDays) {
        this.cycleTimeDays = cycleTimeDays;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * @return the rulePass
     */
    public boolean isRulePass() {
        return rulePass;
    }

    /**
     * @param rulePass
     *            the rulePass to set
     */
    public void setRulePass(boolean rulePass) {
        this.rulePass = rulePass;
    }

    /**
     * @return the averageEstimationForStoryPoint1
     */
    public double getAverageEstimationForStoryPoint1() {
        return averageEstimationForStoryPoint1;
    }

    /**
     * @param averageEstimationForStoryPoint1
     *            the averageEstimationForStoryPoint1 to set
     */
    public void setAverageEstimationForStoryPoint1(double averageEstimationForStoryPoint1) {
        this.averageEstimationForStoryPoint1 = averageEstimationForStoryPoint1;
    }

    /**
     * @return the averageEstimationForStoryPoint2
     */
    public double getAverageEstimationForStoryPoint2() {
        return averageEstimationForStoryPoint2;
    }

    /**
     * @param averageEstimationForStoryPoint2
     *            the averageEstimationForStoryPoint2 to set
     */
    public void setAverageEstimationForStoryPoint2(double averageEstimationForStoryPoint2) {
        this.averageEstimationForStoryPoint2 = averageEstimationForStoryPoint2;
    }

    /**
     * @return the averageEstimationForStoryPoint3
     */
    public double getAverageEstimationForStoryPoint3() {
        return averageEstimationForStoryPoint3;
    }

    /**
     * @param averageEstimationForStoryPoint3
     *            the averageEstimationForStoryPoint3 to set
     */
    public void setAverageEstimationForStoryPoint3(double averageEstimationForStoryPoint3) {
        this.averageEstimationForStoryPoint3 = averageEstimationForStoryPoint3;
    }

    /**
     * @return the averageEstimationForStoryPoint5
     */
    public double getAverageEstimationForStoryPoint5() {
        return averageEstimationForStoryPoint5;
    }

    /**
     * @param averageEstimationForStoryPoint5
     *            the averageEstimationForStoryPoint5 to set
     */
    public void setAverageEstimationForStoryPoint5(double averageEstimationForStoryPoint5) {
        this.averageEstimationForStoryPoint5 = averageEstimationForStoryPoint5;
    }

    /**
     * @return the averageEstimationForStoryPoint8
     */
    public double getAverageEstimationForStoryPoint8() {
        return averageEstimationForStoryPoint8;
    }

    /**
     * @param averageEstimationForStoryPoint8
     *            the averageEstimationForStoryPoint8 to set
     */
    public void setAverageEstimationForStoryPoint8(double averageEstimationForStoryPoint8) {
        this.averageEstimationForStoryPoint8 = averageEstimationForStoryPoint8;
    }

    /**
     * @return the averageEstimationForStoryPoint13
     */
    public double getAverageEstimationForStoryPoint13() {
        return averageEstimationForStoryPoint13;
    }

    /**
     * @param averageEstimationForStoryPoint13
     *            the averageEstimationForStoryPoint13 to set
     */
    public void setAverageEstimationForStoryPoint13(double averageEstimationForStoryPoint13) {
        this.averageEstimationForStoryPoint13 = averageEstimationForStoryPoint13;
    }

    /**
     * @return the averageEstimationForStoryPoint21
     */
    public double getAverageEstimationForStoryPoint21() {
        return averageEstimationForStoryPoint21;
    }

    /**
     * @param averageEstimationForStoryPoint21
     *            the averageEstimationForStoryPoint21 to set
     */
    public void setAverageEstimationForStoryPoint21(double averageEstimationForStoryPoint21) {
        this.averageEstimationForStoryPoint21 = averageEstimationForStoryPoint21;
    }

    /**
     * @return the actualValue
     */
    public double getActualValue() {
        return actualValue;
    }

    /**
     * @param actualValue
     *            the actualValue to set
     */
    public void setActualValue(double actualValue) {
        this.actualValue = actualValue;
    }
}
