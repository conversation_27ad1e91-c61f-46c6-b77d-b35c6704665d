package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

public class IssueHierarchyChild {
	String Name;
	String Summary;
	String State;
	List<IssueList> children;
	 
	public IssueHierarchyChild(){
		children = new ArrayList<IssueList>();
	}

	public String getName() {
		return Name;
	}

	public void setName(String name) {
		Name = name;
	}

	public String getSummary() {
		return Summary;
	}

	public void setSummary(String summary) {
		Summary = summary;
	}

	public String getState() {
		return State;
	}

	public void setState(String state) {
		State = state;
	}

	public List<IssueList> getChildren() {
		return children;
	}

	public void setChildren(List<IssueList> children) {
		this.children = children;
	}
}
