package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;



public class IssueHierarchyLink {
	String Name;
	List<IssueHierarchyChild> children;
	
	public IssueHierarchyLink(){
		children = new ArrayList<IssueHierarchyChild>();
	}

	public String getName() {
		return Name;
	}

	public void setName(String name) {
		Name = name;
	}

	public List<IssueHierarchyChild> getChildren() {
		return children;
	}

	public void setChildren(List<IssueHierarchyChild> children) {
		this.children = children;
	}
}
