package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;



public class IssueHierrarchyNode {	

	String Name;
	String Summary;
	String State;
	int OpenDefect;
	int ClosedDefect;
	HashMap<String,Integer> info;
	String nameALMProperty;
	double originalEstimation;
	double actualEffort;
	List<IssueHierarchyLink> children;
	
	public IssueHierrarchyNode() {
		info = new HashMap<String, Integer>();
		children = new ArrayList<IssueHierarchyLink>();
	}
	
	
	
	public String getName() {
		return Name;
	}

	public void setName(String name) {
		Name = name;
	}

	public String getSummary() {
		return Summary;
	}

	public void setSummary(String summary) {
		Summary = summary;
	}

	public String getState() {
		return State;
	}

	public void setState(String state) {
		State = state;
	}

	public int getOpenDefect() {
		return OpenDefect;
	}

	public void setOpenDefect(int openDefect) {
		OpenDefect = openDefect;
	}

	public int getClosedDefect() {
		return ClosedDefect;
	}

	public void setClosedDefect(int closedDefect) {
		ClosedDefect = closedDefect;
	}

	public HashMap<String, Integer> getInfo() {
		return info;
	}

	public void setInfo(HashMap<String, Integer> info) {
		this.info = info;
	}

	public String getNameALMProperty() {
		return nameALMProperty;
	}

	public void setNameALMProperty(String nameALMProperty) {
		this.nameALMProperty = nameALMProperty;
	}

	public double getOriginalEstimation() {
		return originalEstimation;
	}

	public void setOriginalEstimation(double originalEstimation) {
		this.originalEstimation = originalEstimation;
	}

	public double getActualEffort() {
		return actualEffort;
	}

	public void setActualEffort(double actualEffort) {
		this.actualEffort = actualEffort;
	}

	public List<IssueHierarchyLink> getChildren() {
		return children;
	}

	public void setChildren(List<IssueHierarchyLink> children) {
		this.children = children;
	}
	
}


