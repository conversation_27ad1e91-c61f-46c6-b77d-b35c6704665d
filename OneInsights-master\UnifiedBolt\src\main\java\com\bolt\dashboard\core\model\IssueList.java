package com.bolt.dashboard.core.model;

import java.util.List;
import java.util.Map;

public class IssueList {
	
		public String wId;
		public double storyPoints;
		public String assignee;
		public String state;
		public String type;
		public String sortId;
		private String epicLink;
		private List<String> outWardIssueLink;
		private String summ;
		private String priority;
		private String severity;
		private List<MetricsModel> epicLinks;
	    private List<MetricsModel> links;
	    private List<MetricsModel> subTaskLists; 
	    private List<MetricsModel> epicArray; 
	    private List<String> subtaskList;
	    private String sName;
		private long createdDate;
		private List<TransitionModel> transitions;
		private Map<Long, String> allocatedDate;
		private Long commitedAftertDate;
		
	    public String getsName() {
			return sName;
		}
		public void setsName(String sName) {
			this.sName = sName;
		}
		public long getCreatedDate() {
			return createdDate;
		}
		public void setCreatedDate(long createdDate) {
			this.createdDate = createdDate;
		}
		public Long getCommitedAftertDate() {
			return commitedAftertDate;
		}
		public void setCommitedAftertDate(Long commitedAftertDate) {
			this.commitedAftertDate = commitedAftertDate;
		}
		public List<TransitionModel> getTransitions() {
			return transitions;
		}
		public void setTransitions(List<TransitionModel> transitions) {
			this.transitions = transitions;
		}
		public Map<Long, String> getAllocatedDate() {
			return allocatedDate;
		}
		public void setAllocatedDate(Map<Long, String> allocatedDate) {
			this.allocatedDate = allocatedDate;
		}
		public String getSumm() {
			return summ;
		}
		public void setSumm(String summ) {
			this.summ = summ;
		}
		public List<String> getSubtaskList() {
			return subtaskList;
		}
		public void setSubtaskList(List<String> subtaskList) {
			this.subtaskList = subtaskList;
		}
		public List<String> getOutWardIssueLink() {
			return outWardIssueLink;
		}
		public void setOutWardIssueLink(List<String> outWardIssueLink) {
			this.outWardIssueLink = outWardIssueLink;
		}
		public List<MetricsModel> getEpicLinks() {
			return epicLinks;
		}
		public void setEpicLinks(List<MetricsModel> epicLinks) {
			this.epicLinks = epicLinks;
		}
		public List<MetricsModel> getLinks() {
			return links;
		}
		public void setLinks(List<MetricsModel> links) {
			this.links = links;
		}
		public List<MetricsModel> getSubTaskLists() {
			return subTaskLists;
		}
		public void setSubTaskLists(List<MetricsModel> subTaskLists) {
			this.subTaskLists = subTaskLists;
		}
		public List<MetricsModel> getEpicArray() {
			return epicArray;
		}
		public void setEpicArray(List<MetricsModel> epicArray) {
			this.epicArray = epicArray;
		}
		
		public String getEpicLink() {
			return epicLink;
		}
		public void setEpicLink(String epicLink) {
			this.epicLink = epicLink;
		}
		public String getwId() {
			return wId;
		}
		public void setwId(String wId) {
			this.wId = wId;
		}
		public double getStoryPoints() {
			return storyPoints;
		}
		public void setStoryPoints(double storyPoints) {
			this.storyPoints = storyPoints;
		}
		public String getAssignee() {
			return assignee;
		}
		public void setAssignee(String assignee) {
			this.assignee = assignee;
		}
		public String getState() {
			return state;
		}
		public void setState(String state) {
			this.state = state;
		}
		public String getType() {
			return type;
		}
		public void setType(String type) {
			this.type = type;
		}
		public String getSortId() {
			return sortId;
		}
		public void setSortId(String sortId) {
			this.sortId = sortId;
		}
		public String getPriority() {
			return priority;
		}
		public void setPriority(String priority) {
			this.priority = priority;
		}
		public String getSeverity() {
			return severity;
		}
		public void setSeverity(String severity) {
			this.severity = severity;
		}
	
}
