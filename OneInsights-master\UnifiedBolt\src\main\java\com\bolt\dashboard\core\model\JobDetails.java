package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "JOB")
public class JobDetails extends BaseModel {
    private String projectName;
    
    private List<JobDetailsMetrics> metrics = new ArrayList<>();

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public List<JobDetailsMetrics> getMetrics() {
        return metrics;
    }

    public void setMetrics(List<JobDetailsMetrics> metrics) {
        this.metrics = metrics;
    }

}
