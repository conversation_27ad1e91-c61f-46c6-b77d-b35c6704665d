package com.bolt.dashboard.core.model;

public class LinesCoverageMetrics {
private int linesHit;
private int lineNumbert;
private boolean branch;
private String conditionCoverage;
private String conditionType;
private String coverage;
private double coverageNumber;
private String linesCoverage;
/**
 * @return the linesCoverage
 */
public String getLinesCoverage() {
    return linesCoverage;
}
/**
 * @param linesCoverage the linesCoverage to set
 */
public void setLinesCoverage(String linesCoverage) {
    this.linesCoverage = linesCoverage;
}
/**
 * @return the linesHit
 */
public int getLinesHit() {
    return linesHit;
}
/**
 * @param linesHit the linesHit to set
 */
public void setLinesHit(int linesHit) {
    this.linesHit = linesHit;
}
/**
 * @return the lineNumbert
 */
public int getLineNumbert() {
    return lineNumbert;
}
/**
 * @param lineNumbert the lineNumbert to set
 */
public void setLineNumbert(int lineNumbert) {
    this.lineNumbert = lineNumbert;
}
/**
 * @return the branch
 */
public boolean isBranch() {
    return branch;
}
/**
 * @param branch the branch to set
 */
public void setBranch(boolean branch) {
    this.branch = branch;
}
/**
 * @return the conditionCoverage
 */
public String getConditionCoverage() {
    return conditionCoverage;
}
/**
 * @param conditionCoverage the conditionCoverage to set
 */
public void setConditionCoverage(String conditionCoverage) {
    this.conditionCoverage = conditionCoverage;
}
/**
 * @return the conditionType
 */
public String getConditionType() {
    return conditionType;
}
/**
 * @param conditionType the conditionType to set
 */
public void setConditionType(String conditionType) {
    this.conditionType = conditionType;
}
/**
 * @return the coverage
 */
public String getCoverage() {
    return coverage;
}
/**
 * @param coverage the coverage to set
 */
public void setCoverage(String coverage) {
    this.coverage = coverage;
}
/**
 * @return the coverageNumber
 */
public double getCoverageNumber() {
    return coverageNumber;
}
/**
 * @param coverageNumber the coverageNumber to set
 */
public void setCoverageNumber(double coverageNumber) {
    this.coverageNumber = coverageNumber;
}
}
