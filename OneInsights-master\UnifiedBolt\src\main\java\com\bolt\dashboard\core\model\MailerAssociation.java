package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection="MAILERASSOCIATION")
public class MailerAssociation {
    private String projectName;
    private String access;
    private String userName;
    private String email;
    private String toAdd;
    private String ccAdd;
    private String bccAdd;
    private String msgBody;
    private String subject;
    public String getProjectName() {
        return projectName;
    }
    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
    public String getAccess() {
        return access;
    }
    public void setAccess(String access) {
        this.access = access;
    }
    public String getUserName() {
        return userName;
    }
    public void setUserName(String userName) {
        this.userName = userName;
    }
    public String getEmail() {
        return email;
    }
    public void setEmail(String email) {
        this.email = email;
    }
    public String getToAdd() {
        return toAdd;
    }
    public void setToAdd(String toAdd) {
        this.toAdd = toAdd;
    }
    public String getCcAdd() {
        return ccAdd;
    }
    public void setCcAdd(String ccAdd) {
        this.ccAdd = ccAdd;
    }
    public String getBccAdd() {
        return bccAdd;
    }
    public void setBccAdd(String bccAdd) {
        this.bccAdd = bccAdd;
    }
    public String getMsgBody() {
        return msgBody;
    }
    public void setMsgBody(String msgBody) {
        this.msgBody = msgBody;
    }
    public String getSubject() {
        return subject;
    }
    public void setSubject(String subject) {
        this.subject = subject;
    }
   
   
}
