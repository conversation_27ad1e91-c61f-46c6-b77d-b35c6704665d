package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection="MAILER")
public class MailerModel {
    private String userName;
    private String password;
   
    private String toMailRecipients;
    private String toCCRecipients;
    private String toBCCRecipients;
    private String subject;
    private String mailBody;
    private String salutation;
    public String getToMailRecipients() {
        return toMailRecipients;
    }
    public void setToMailRecipients(String toMailRecipients) {
        this.toMailRecipients = toMailRecipients;
    }
    public String getToCCRecipients() {
        return toCCRecipients;
    }
    public void setToCCRecipients(String toCCRecipients) {
        this.toCCRecipients = toCCRecipients;
    }
    public String getSubject() {
        return subject;
    }
    public void setSubject(String subject) {
        this.subject = subject;
    }
    public String getMailBody() {
        return mailBody;
    }
    public void setMailBody(String mailBody) {
        this.mailBody = mailBody;
    }
    public String getSalutation() {
        return salutation;
    }
    public void setSalutation(String salutation) {
        this.salutation = salutation;
    }
    public String getUserName() {
        return userName;
    }
    public void setUserName(String userName) {
        this.userName = userName;
    }
    public String getPassword() {
        return password;
    }
    public void setPassword(String password) {
        this.password = password;
    }
    public String getToBCCRecipients() {
        return toBCCRecipients;
    }
    public void setToBCCRecipients(String toBCCRecipients) {
        this.toBCCRecipients = toBCCRecipients;
    }
}
