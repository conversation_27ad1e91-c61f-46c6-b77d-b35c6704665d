package com.bolt.dashboard.core.model;

import java.util.List;

public class MethodCoverageMetrics {
private String methodName;
private double methodLineRate;
private double methodBranchRate;
private List<LinesCoverageMetrics>lineMetrics;
private String methodCoverage;
private double methodCoveragePercentage;
private String methodLineCoverage;
private double methodLineCoveragePercentage;
/**
 * @return the methodCoverage
 */
public String getMethodCoverage() {
    return methodCoverage;
}
/**
 * @param methodCoverage the methodCoverage to set
 */
public void setMethodCoverage(String methodCoverage) {
    this.methodCoverage = methodCoverage;
}
/**
 * @return the methodCoveragePercentage
 */
public double getMethodCoveragePercentage() {
    return methodCoveragePercentage;
}
/**
 * @param methodCoveragePercentage the methodCoveragePercentage to set
 */
public void setMethodCoveragePercentage(Double methodCoveragePercentage) {
    this.methodCoveragePercentage = methodCoveragePercentage;
}
/**
 * @return the methodName
 */
public String getMethodName() {
    return methodName;
}
/**
 * @param methodName the methodName to set
 */
public void setMethodName(String methodName) {
    this.methodName = methodName;
}
/**
 * @return the methodLineRate
 */
public double getMethodLineRate() {
    return methodLineRate;
}
/**
 * @param methodLineRate the methodLineRate to set
 */
public void setMethodLineRate(double methodLineRate) {
    this.methodLineRate = methodLineRate;
}
/**
 * @return the methodBranchRate
 */
public double getMethodBranchRate() {
    return methodBranchRate;
}
/**
 * @param methodBranchRate the methodBranchRate to set
 */
public void setMethodBranchRate(double methodBranchRate) {
    this.methodBranchRate = methodBranchRate;
}
/**
 * @return the lineMetrics
 */
public List<LinesCoverageMetrics> getLineMetrics() {
    return lineMetrics;
}
/**
 * @param lineMetrics the lineMetrics to set
 */
public void setLineMetrics(List<LinesCoverageMetrics> lineMetrics) {
    this.lineMetrics = lineMetrics;
}
/**
 * @return the methodLineCoverage
 */
public String getMethodLineCoverage() {
    return methodLineCoverage;
}
/**
 * @param methodLineCoverage the methodLineCoverage to set
 */
public void setMethodLineCoverage(String methodLineCoverage) {
    this.methodLineCoverage = methodLineCoverage;
}
/**
 * @return the methodLineCoveragePercentage
 */
public double getMethodLineCoveragePercentage() {
    return methodLineCoveragePercentage;
}
/**
 * @param methodLineCoveragePercentage the methodLineCoveragePercentage to set
 */
public void setMethodLineCoveragePercentage(double methodLineCoveragePercentage) {
    this.methodLineCoveragePercentage = methodLineCoveragePercentage;
}
/**
 * @param methodCoveragePercentage the methodCoveragePercentage to set
 */
public void setMethodCoveragePercentage(double methodCoveragePercentage) {
    this.methodCoveragePercentage = methodCoveragePercentage;
}
}
