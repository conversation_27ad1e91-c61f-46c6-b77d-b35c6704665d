package com.bolt.dashboard.core.model;

import java.util.List;
import java.util.Map;

public class MetricAgeData {
	
	private int ageInDays;
	private String ageDisplay;
	private long ageInMilis;
	private String id; 
	private String status; 
	private long createDate;
	private String priority;
	private String sName;
	private double sp;
	private List<Map<String, String>> defectWorkFlow;
	
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	
	public int getAgeInDays() {
		return ageInDays;
	}
	public void setAgeInDays(int ageInDays) {
		this.ageInDays = ageInDays;
	}
	public String getAgeDisplay() {
		return ageDisplay;
	}
	public void setAgeDisplay(String ageDisplay) {
		this.ageDisplay = ageDisplay;
	}
	public List<Map<String, String>> getDefectWorkFlow() {
		return defectWorkFlow;
	}
	public void setDefectWorkFlow(List<Map<String, String>> defectWorkFlow) {
		this.defectWorkFlow = defectWorkFlow;
	}
	public long getCreateDate() {
		return createDate;
	}
	public void setCreateDate(long createDate) {
		this.createDate = createDate;
	}
	public String getPriority() {
		return priority;
	}
	public void setPriority(String priority) {
		this.priority = priority;
	}
	public long getAgeInMilis() {
		return ageInMilis;
	}
	public void setAgeInMilis(long ageInMilis) {
		this.ageInMilis = ageInMilis;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getsName() {
		return sName;
	}
	public void setsName(String sName) {
		this.sName = sName;
	}
	public double getSp() {
		return sp;
	}
	public void setSp(double sp) {
		this.sp = sp;
	}
	
}
