package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonIgnore;

public class MonogOutMetrics {
    private Double actEst;
    private List<String> affectedVersions;
    private Map<Long, String> allocatedDate;
    private String assgnTo;
    private String baseline;
    private List<String> components;
    private Long createDate;
    private Double cycleTime;
    private String defectInjector;
    private long doneDate;
    private Double effort;
    private List<EffortHistoryModel> efforts;
    private String epicLink;
    private Number estChange;
    private Double extEffort;
    private Map<Long, String> fixVer;
    private List<String> targetRelease;
    private List<String> inWardIssueLink;
    private Long leadTime;
    private Double orgEst;
    private List<String> outWardIssueLink;
    private List<String> epicIssues;
    private String pAlmType;
    private String pName;
    private String priority;
    private Double remTime;
    private long resDate;
    private String severity;
    private int sId;
    private List<Integer> multiSprints;
    private String sName;
    private String state;
    @JsonIgnore
    private Set<String> stateSet;
    private String statusCategory;
    private Map<Long, Double> storyPoints;
    private List<String> subtaskList;
    private String summ;
    private List<String> taskList;
    private List<TransitionModel> transitions;
    private String type;
    private long updatedDate;
    private Long updDate;
    private String projKey;
    private List<String> label = new ArrayList<String>();
    private Long waitTime;
    private String wId;

    // Below fields are added for MOVE
    private String squads;
    private String category;
    private String whenFound;
    private String whereFound;
    private String howFound;
    //field for verizon
    private String environment;
    private String acFeatureOrCapability;
    private String acFeatureId;
    private String rallyRefURL;
    
    // Below filed is added of LPM
    private List<CustomFields> customFields;
    
    public List<CustomFields> getCustomFields() {
		return customFields;
	}

	public void setCustomFields(List<CustomFields> customFields) {
		this.customFields = customFields;
	}

	public Double getActEst() {
	return actEst;
    }

    public List<String> getAffectedVersions() {
	return affectedVersions;
    }

    public Map<Long, String> getAllocatedDate() {
	return allocatedDate;
    }

    public String getAssgnTo() {
	return assgnTo;
    }

    public String getBaseline() {
	return baseline;
    }

    public List<String> getComponents() {
	return components;
    }

    public Long getCreateDate() {
	return createDate;
    }

    public Double getCycleTime() {
	return cycleTime;
    }

    public String getDefectInjector() {
	return defectInjector;
    }

    public long getDoneDate() {
	return doneDate;
    }

    public Double getEffort() {
	return effort;
    }

    public List<EffortHistoryModel> getEfforts() {
	return efforts;
    }

    public String getEpicLink() {
	return epicLink;
    }

    public Number getEstChange() {
	return estChange;
    }

    public Double getExtEffort() {
	return extEffort;
    }

    public Map<Long, String> getFixVer() {
	return fixVer;
    }

    public List<String> getInWardIssueLink() {
	return inWardIssueLink;
    }

    public Long getLeadTime() {
	return leadTime;
    }

    public Double getOrgEst() {
	return orgEst;
    }

    public List<String> getOutWardIssueLink() {
	return outWardIssueLink;
    }

    public String getpAlmType() {
	return pAlmType;
    }

    public String getpName() {
	return pName;
    }

    public String getPriority() {
	return priority;
    }

    public Double getRemTime() {
	return remTime;
    }

    public long getResDate() {
	return resDate;
    }

    public String getSeverity() {
	return severity;
    }

    public int getsId() {
	return sId;
    }

    public String getsName() {
	return sName;
    }

    public String getState() {
	return state;
    }

    public Set<String> getStateSet() {
	return stateSet;
    }

    public String getStatusCategory() {
	return statusCategory;
    }

    public Map<Long, Double> getStoryPoints() {
	return storyPoints;
    }

    public List<String> getSubtaskList() {
	return subtaskList;
    }

    public String getSumm() {
	return summ;
    }

    public List<String> getTaskList() {
	return taskList;
    }

    public List<TransitionModel> getTransitions() {
	return transitions;
    }

    public String getType() {
	return type;
    }

    public long getUpdatedDate() {
	return updatedDate;
    }

    public Long getUpdDate() {
	return updDate;
    }

    public Long getWaitTime() {
	return waitTime;
    }

    public String getwId() {
	return wId;
    }

    public void setActEst(Double actEst) {
	this.actEst = actEst;
    }

    public void setAffectedVersions(List<String> affectedVersions) {
	this.affectedVersions = affectedVersions;
    }

    public void setAllocatedDate(Map<Long, String> allocatedDate) {
	this.allocatedDate = allocatedDate;
    }

    public void setAssgnTo(String assgnTo) {
	this.assgnTo = assgnTo;
    }

    public void setBaseline(String baseline) {
	this.baseline = baseline;
    }

    public void setComponents(List<String> components) {
	this.components = components;
    }

    public void setCreateDate(Long createDate) {
	this.createDate = createDate;
    }

    public void setCycleTime(Double cycleTime) {
	this.cycleTime = cycleTime;
    }

    public void setDefectInjector(String defectInjector) {
	this.defectInjector = defectInjector;
    }

    public void setDoneDate(long doneDate) {
	this.doneDate = doneDate;
    }

    public void setEffort(Double effort) {
	this.effort = effort;
    }

    public void setEfforts(List<EffortHistoryModel> efforts) {
	this.efforts = efforts;
    }

    public void setEpicLink(String epicLink) {
	this.epicLink = epicLink;
    }

    public void setEstChange(Number estChange) {
	this.estChange = estChange;
    }

    public void setExtEffort(Double extEffort) {
	this.extEffort = extEffort;
    }

    public void setFixVer(Map<Long, String> fixVer) {
	this.fixVer = fixVer;
    }

    public void setInWardIssueLink(List<String> inWardIssueLink) {
	this.inWardIssueLink = inWardIssueLink;
    }

    public void setLeadTime(Long leadTime) {
	this.leadTime = leadTime;
    }

    public void setOrgEst(Double orgEst) {
	this.orgEst = orgEst;
    }

    public void setOutWardIssueLink(List<String> outWardIssueLink) {
	this.outWardIssueLink = outWardIssueLink;
    }

    public void setpAlmType(String pAlmType) {
	this.pAlmType = pAlmType;
    }

    public void setpName(String pName) {
	this.pName = pName;
    }

    public void setPriority(String priority) {
	this.priority = priority;
    }

    public void setRemTime(Double remTime) {
	this.remTime = remTime;
    }

    public void setResDate(long resDate) {
	this.resDate = resDate;
    }

    public void setSeverity(String severity) {
	this.severity = severity;
    }

    public void setsId(int sId) {
	this.sId = sId;
    }

    public void setsName(String sName) {
	this.sName = sName;
    }

    public void setState(String state) {
	this.state = state;
    }

    public void setStateSet(Set<String> stateSet) {
	this.stateSet = stateSet;
    }

    public void setStatusCategory(String statusCategory) {
	this.statusCategory = statusCategory;
    }

    public void setStoryPoints(Map<Long, Double> storyPoints) {
	this.storyPoints = storyPoints;
    }

    public void setSubtaskList(List<String> subtaskList) {
	this.subtaskList = subtaskList;
    }

    public void setSumm(String summ) {
	this.summ = summ;
    }

    public void setTaskList(List<String> taskList) {
	this.taskList = taskList;
    }

    public void setTransitions(List<TransitionModel> transitions) {
	this.transitions = transitions;
    }

    public void setType(String type) {
	this.type = type;
    }

    public void setUpdatedDate(long updatedDate) {
	this.updatedDate = updatedDate;
    }

    public void setUpdDate(Long updDate) {
	this.updDate = updDate;
    }

    public void setWaitTime(Long waitTime) {
	this.waitTime = waitTime;
    }

    public void setwId(String wId) {
	this.wId = wId;
    }

    public List<String> getTargetRelease() {
	return targetRelease;
    }

    public void setTargetRelease(List<String> targetRelease) {
	this.targetRelease = targetRelease;
    }

    public String getSquads() {
	return squads;
    }

    public void setSquads(String squads) {
	this.squads = squads;
    }

    public String getCategory() {
	return category;
    }

    public void setCategory(String category) {
	this.category = category;
    }

    public String getWhenFound() {
	return whenFound;
    }

    public void setWhenFound(String whenFound) {
	this.whenFound = whenFound;
    }

    public String getWhereFound() {
	return whereFound;
    }

    public void setWhereFound(String whereFound) {
	this.whereFound = whereFound;
    }

    public String getHowFound() {
	return howFound;
    }

    public void setHowFound(String howFound) {
	this.howFound = howFound;
    }

    public List<String> getEpicIssues() {
	return epicIssues;
    }

    public void setEpicIssues(List<String> epicIssues) {
	this.epicIssues = epicIssues;
    }

    public String getProjKey() {
	return projKey;
    }

    public void setProjKey(String projKey) {
	this.projKey = projKey;
    }

	public List<Integer> getMultiSprints() {
		return multiSprints;
	}

	public void setMultiSprints(List<Integer> multiSprints) {
		this.multiSprints = multiSprints;
	}

	public String getEnvironment() {
		return environment;
	}

	public void setEnvironment(String environment) {
		this.environment = environment;
	}

	public String getAcFeatureOrCapability() {
		return acFeatureOrCapability;
	}

	public void setAcFeatureOrCapability(String acFeatureOrCapability) {
		this.acFeatureOrCapability = acFeatureOrCapability;
	}

    public String getAcFeatureId() {
		return acFeatureId;
	}

	public void setAcFeatureId(String acFeatureId) {
		this.acFeatureId = acFeatureId;
	}

	public String getRallyRefURL() {
		return rallyRefURL;
	}

	public void setRallyRefURL(String rallyRefURL) {
		this.rallyRefURL = rallyRefURL;
	}

	public List<String> getLabel() {
		return label;
	}

	public void setLabel(List<String> label) {
		this.label = label;
	}

}
