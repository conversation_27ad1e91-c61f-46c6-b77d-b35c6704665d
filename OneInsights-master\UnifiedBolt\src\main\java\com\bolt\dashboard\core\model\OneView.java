package com.bolt.dashboard.core.model;

import java.util.Set;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document()
public class OneView {

	public ObjectId get_id() {
		return _id;
	}

	public void set_id(ObjectId _id) {
		this._id = _id;
	}

	@Id
	private ObjectId _id;
	private String projectName;
	private String user;
	private Set<OneViewChartOptions> graphs;

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getUser() {
		return user;
	}

	public void setUser(String user) {
		this.user = user;
	}

	public Set<OneViewChartOptions> getGraphs() {
		return graphs;
	}

	public void setGraphs(Set<OneViewChartOptions> graphs) {
		this.graphs = graphs;
	}

}
