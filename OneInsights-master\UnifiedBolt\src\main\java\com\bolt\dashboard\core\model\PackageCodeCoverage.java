package com.bolt.dashboard.core.model;

import java.util.Set;

public class PackageCodeCoverage {
    private double packageComplexity;
    private String packageName;
    private String packageLastName;
    private double packageLineRate;
    private double packageBranchRate;
    private long timestamp;
    private Set<ClassCoverageMetrics> classMetrics;
    private String packageCoverage;
    private double packageCoveragePercentage;
    private double packageLevelTotalClasses;
    private double packageLevelclasesCoveed;
    private String packageLevelClassesCoverage;
    private double packageLevelclasesCoveragePercentage;

    private double packageLevelTotalMethods;
    private double packageLevelMethodsCovered;
    private String packageLevelMethodCoverage;
    private double packageLevelMethodsPercentage;

    private double packageLevelTotalLines;
    private double packageLevelLinesCovered;
    private double packageLevelLinesCoveredPercentage;
    private String packageLevelLinesCoverage;

    public String getPackageLastName() {
        return packageLastName;
    }

    public void setPackageLastName(String packageLastName) {
        this.packageLastName = packageLastName;
    }

    /**
     * @return the classMetrics
     */
    public Set<ClassCoverageMetrics> getClassMetrics() {
        return classMetrics;
    }

    /**
     * @param classMetrics
     *            the classMetrics to set
     */
    public void setClassMetrics(Set<ClassCoverageMetrics> classMetrics) {
        this.classMetrics = classMetrics;
    }

    /**
     * @return the packageComplexity
     */
    public double getPackageComplexity() {
        return packageComplexity;
    }

    /**
     * @param packageComplexity
     *            the packageComplexity to set
     */
    public void setPackageComplexity(double packageComplexity) {
        this.packageComplexity = packageComplexity;
    }

    /**
     * @return the packageName
     */
    public String getPackageName() {
        return packageName;
    }

    /**
     * @param packageName
     *            the packageName to set
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * @return the packageLineRate
     */
    public double getPackageLineRate() {
        return packageLineRate;
    }

    /**
     * @param packageLineRate
     *            the packageLineRate to set
     */
    public void setPackageLineRate(double packageLineRate) {
        this.packageLineRate = packageLineRate;
    }

    /**
     * @return the packageBranchRate
     */
    public double getPackageBranchRate() {
        return packageBranchRate;
    }

    /**
     * @param packageBranchRate
     *            the packageBranchRate to set
     */
    public void setPackageBranchRate(double packageBranchRate) {
        this.packageBranchRate = packageBranchRate;
    }

    /**
     * @return the timestamp
     */
    public long getTimestamp() {
        return timestamp;
    }

    /**
     * @param timestamp
     *            the timestamp to set
     */
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    /**
     * @return the packageCoverage
     */
    public String getPackageCoverage() {
        return packageCoverage;
    }

    /**
     * @param packageCoverage
     *            the packageCoverage to set
     */
    public void setPackageCoverage(String packageCoverage) {
        this.packageCoverage = packageCoverage;
    }

    /**
     * @return the packageCoveragePercentage
     */
    public double getPackageCoveragePercentage() {
        return packageCoveragePercentage;
    }

    /**
     * @param packageCoveragePercentage
     *            the packageCoveragePercentage to set
     */
    public void setPackageCoveragePercentage(Double packageCoveragePercentage) {
        this.packageCoveragePercentage = packageCoveragePercentage;
    }

    /**
     * @return the packageLevelTotalClasses
     */
    public double getPackageLevelTotalClasses() {
        return packageLevelTotalClasses;
    }

    /**
     * @param packageLevelTotalClasses
     *            the packageLevelTotalClasses to set
     */
    public void setPackageLevelTotalClasses(double packageLevelTotalClasses) {
        this.packageLevelTotalClasses = packageLevelTotalClasses;
    }

    /**
     * @return the packageLevelclasesCoveed
     */
    public double getPackageLevelclasesCoveed() {
        return packageLevelclasesCoveed;
    }

    /**
     * @param packageLevelclasesCoveed
     *            the packageLevelclasesCoveed to set
     */
    public void setPackageLevelclasesCoveed(double packageLevelclasesCoveed) {
        this.packageLevelclasesCoveed = packageLevelclasesCoveed;
    }

    /**
     * @param packageCoveragePercentage
     *            the packageCoveragePercentage to set
     */
    public void setPackageCoveragePercentage(double packageCoveragePercentage) {
        this.packageCoveragePercentage = packageCoveragePercentage;
    }

    /**
     * @return the packageLevelTotalMethods
     */
    public double getPackageLevelTotalMethods() {
        return packageLevelTotalMethods;
    }

    /**
     * @return the packageLevelClassesCoverage
     */
    public String getPackageLevelClassesCoverage() {
        return packageLevelClassesCoverage;
    }

    /**
     * @param packageLevelClassesCoverage
     *            the packageLevelClassesCoverage to set
     */
    public void setPackageLevelClassesCoverage(String packageLevelClassesCoverage) {
        this.packageLevelClassesCoverage = packageLevelClassesCoverage;
    }

    /**
     * @return the packageLevelclasesCoveragePercentage
     */
    public double getPackageLevelclasesCoveragePercentage() {
        return packageLevelclasesCoveragePercentage;
    }

    /**
     * @param packageLevelclasesCoveragePercentage
     *            the packageLevelclasesCoveragePercentage to set
     */
    public void setPackageLevelclasesCoveragePercentage(double packageLevelclasesCoveragePercentage) {
        this.packageLevelclasesCoveragePercentage = packageLevelclasesCoveragePercentage;
    }

    /**
     * @param packageLevelTotalMethods
     *            the packageLevelTotalMethods to set
     */
    public void setPackageLevelTotalMethods(double packageLevelTotalMethods) {
        this.packageLevelTotalMethods = packageLevelTotalMethods;
    }

    /**
     * @return the packageLevelMethodsCovered
     */
    public double getPackageLevelMethodsCovered() {
        return packageLevelMethodsCovered;
    }

    /**
     * @param packageLevelMethodsCovered
     *            the packageLevelMethodsCovered to set
     */
    public void setPackageLevelMethodsCovered(double packageLevelMethodsCovered) {
        this.packageLevelMethodsCovered = packageLevelMethodsCovered;
    }

    /**
     * @return the packageLevelLinesCoverage
     */
    public String getPackageLevelLinesCoverage() {
        return packageLevelLinesCoverage;
    }

    /**
     * @param packageLevelLinesCoverage
     *            the packageLevelLinesCoverage to set
     */
    public void setPackageLevelLinesCoverage(String packageLevelLinesCoverage) {
        this.packageLevelLinesCoverage = packageLevelLinesCoverage;
    }

    /**
     * @return the packageLevelMethodCoverage
     */
    public String getPackageLevelMethodCoverage() {
        return packageLevelMethodCoverage;
    }

    /**
     * @param packageLevelMethodCoverage
     *            the packageLevelMethodCoverage to set
     */
    public void setPackageLevelMethodCoverage(String packageLevelMethodCoverage) {
        this.packageLevelMethodCoverage = packageLevelMethodCoverage;
    }

    /**
     * @return the packageLevelMethodsPercentage
     */
    public double getPackageLevelMethodsPercentage() {
        return packageLevelMethodsPercentage;
    }

    /**
     * @param packageLevelMethodsPercentage
     *            the packageLevelMethodsPercentage to set
     */
    public void setPackageLevelMethodsPercentage(double packageLevelMethodsPercentage) {
        this.packageLevelMethodsPercentage = packageLevelMethodsPercentage;
    }

    /**
     * @return the packageLevelTotalLines
     */
    public double getPackageLevelTotalLines() {
        return packageLevelTotalLines;
    }

    /**
     * @param packageLevelTotalLines
     *            the packageLevelTotalLines to set
     */
    public void setPackageLevelTotalLines(double packageLevelTotalLines) {
        this.packageLevelTotalLines = packageLevelTotalLines;
    }

    /**
     * @return the packageLevelLinesCovered
     */
    public double getPackageLevelLinesCovered() {
        return packageLevelLinesCovered;
    }

    /**
     * @param packageLevelLinesCovered
     *            the packageLevelLinesCovered to set
     */
    public void setPackageLevelLinesCovered(double packageLevelLinesCovered) {
        this.packageLevelLinesCovered = packageLevelLinesCovered;
    }

    /**
     * @return the packageLevelLinesCoveredPercentage
     */
    public double getPackageLevelLinesCoveredPercentage() {
        return packageLevelLinesCoveredPercentage;
    }

    /**
     * @param packageLevelLinesCoveredPercentage
     *            the packageLevelLinesCoveredPercentage to set
     */
    public void setPackageLevelLinesCoveredPercentage(double packageLevelLinesCoveredPercentage) {
        this.packageLevelLinesCoveredPercentage = packageLevelLinesCoveredPercentage;
    }

}
