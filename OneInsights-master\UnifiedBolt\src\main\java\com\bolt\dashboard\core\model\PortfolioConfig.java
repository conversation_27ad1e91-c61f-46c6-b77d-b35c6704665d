package com.bolt.dashboard.core.model;

import java.util.SortedMap;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "PortfolioConfig")
public class PortfolioConfig {

	private String id;
	private String projectName;
	private String userName;
	private long timestamp;
	private String orgName;
	private String orgId;
	private String businessUnit;
	private String businessUnitId;
	private String portfolioName;
	private String towerName;

	private String portfolioID;
	private String productOwner;
	private String scrumMaster;
	private String startDate;
	private String endDate;
	private String cronExpression;
	private Boolean schedulerEnabled;
	private String bdepId;
	private SortedMap<String,Double> engScores; 
	
	private String region;
	private String[] practice;
	private String vertical;
	private String account;
	private String execPractice;
	private String clientPartner;
	private String practiceHead;
	private String[] teamLocation;
	private String deliveryModel;
	private String deliveryManager;
	
	
	
	public String getAccount() {
		return account;
	}

	public void setAccount(String account) {
		this.account = account;
	}

	public String getExecPractice() {
		return execPractice;
	}

	public void setExecPractice(String execPractice) {
		this.execPractice = execPractice;
	}

	public String getClientPartner() {
		return clientPartner;
	}

	public void setClientPartner(String clientPartner) {
		this.clientPartner = clientPartner;
	}

	public String getPracticeHead() {
		return practiceHead;
	}

	public void setPracticeHead(String practiceHead) {
		this.practiceHead = practiceHead;
	}

	public String[] getTeamLocation() {
		return teamLocation;
	}

	public void setTeamLocation(String[] teamLocation) {
		this.teamLocation = teamLocation;
	}

	public String getDeliveryModel() {
		return deliveryModel;
	}

	public void setDeliveryModel(String deliveryModel) {
		this.deliveryModel = deliveryModel;
	}

	public String getDeliveryManager() {
		return deliveryManager;
	}

	public void setDeliveryManager(String deliveryManager) {
		this.deliveryManager = deliveryManager;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public String[] getPractice() {
		return practice;
	}

	public void setPractice(String[] practice) {
		this.practice = practice;
	}

	public String getVertical() {
		return vertical;
	}

	public void setVertical(String vertical) {
		this.vertical = vertical;
	}

	public Boolean getSchedulerEnabled() {
		return schedulerEnabled;
	}

	public void setSchedulerEnabled(Boolean schedulerEnabled) {
		this.schedulerEnabled = schedulerEnabled;
	}

	public String getCronExpression() {
		return cronExpression;
	}

	public void setCronExpression(String cronExpression) {
		this.cronExpression = cronExpression;
	}

	/**
	 * @return the projectName
	 */
	public String getProjectName() {
		return projectName;
	}

	/**
	 * @param projectName
	 *            the projectName to set
	 */
	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	/**
	 * @return the userName
	 */
	public String getUserName() {
		return userName;
	}

	/**
	 * @param userName
	 *            the userName to set
	 */
	public void setUserName(String userName) {
		this.userName = userName;
	}

	/**
	 * @return the timestamp
	 */
	public long getTimestamp() {
		return timestamp;
	}

	/**
	 * @param timestamp
	 *            the timestamp to set
	 */
	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public String getBusinessUnit() {
		return businessUnit;
	}

	public void setBusinessUnit(String businessUnit) {
		this.businessUnit = businessUnit;
	}

	public String getBusinessUnitId() {
		return businessUnitId;
	}

	public void setBusinessUnitId(String businessUnitId) {
		this.businessUnitId = businessUnitId;
	}

	public String getPortfolioName() {
		return portfolioName;
	}

	public void setPortfolioName(String portfolioName) {
		this.portfolioName = portfolioName;
	}

	public String getPortfolioID() {
		return portfolioID;
	}

	public void setPortfolioID(String portfolioID) {
		this.portfolioID = portfolioID;
	}

	public String getProductOwner() {
		return productOwner;
	}

	public void setProductOwner(String productOwner) {
		this.productOwner = productOwner;
	}

	public String getScrumMaster() {
		return scrumMaster;
	}

	public void setScrumMaster(String scrumMaster) {
		this.scrumMaster = scrumMaster;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getBdepId() {
		return bdepId;
	}

	public void setBdepId(String bdepId) {
		this.bdepId = bdepId;
	}

	public String getTowerName() {
		return towerName;
	}

	public void setTowerName(String towerName) {
		this.towerName = towerName;
	}

	public SortedMap<String,Double> getEngScores() {
		return engScores;
	}

	public void setEngScores(SortedMap<String,Double> engScores) {
		this.engScores = engScores;
	}

}
