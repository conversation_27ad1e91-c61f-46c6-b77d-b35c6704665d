package com.bolt.dashboard.core.model;

import java.util.SortedMap;

public class PortfolioOutModel {
	private String id;
    private String projectName;
    private String userName;
    private long timestamp;
    private String orgName;
    private String orgId;
    private String businessUnit;
    private String businessUnitId;
    private String portfolioName;
    private String portfolioID;
    private String productOwner;
    private String scrumMaster;
    private String startDate;
    private String endDate;
    private String cronExpression;
    private Boolean schedulerEnabled;
    private String bdepId;
    private String projectAccess;
    private String userImage;
    private String email;
    private SortedMap<String,Double> engScores; 
    
    private HealthData health;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public String getBusinessUnit() {
		return businessUnit;
	}

	public void setBusinessUnit(String businessUnit) {
		this.businessUnit = businessUnit;
	}

	public String getBusinessUnitId() {
		return businessUnitId;
	}

	public void setBusinessUnitId(String businessUnitId) {
		this.businessUnitId = businessUnitId;
	}

	public String getPortfolioName() {
		return portfolioName;
	}

	public void setPortfolioName(String portfolioName) {
		this.portfolioName = portfolioName;
	}

	public String getPortfolioID() {
		return portfolioID;
	}

	public void setPortfolioID(String portfolioID) {
		this.portfolioID = portfolioID;
	}

	public String getProductOwner() {
		return productOwner;
	}

	public void setProductOwner(String productOwner) {
		this.productOwner = productOwner;
	}

	public String getScrumMaster() {
		return scrumMaster;
	}

	public void setScrumMaster(String scrumMaster) {
		this.scrumMaster = scrumMaster;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getCronExpression() {
		return cronExpression;
	}

	public void setCronExpression(String cronExpression) {
		this.cronExpression = cronExpression;
	}

	public Boolean getSchedulerEnabled() {
		return schedulerEnabled;
	}

	public void setSchedulerEnabled(Boolean schedulerEnabled) {
		this.schedulerEnabled = schedulerEnabled;
	}

	public String getBdepId() {
		return bdepId;
	}

	public void setBdepId(String bdepId) {
		this.bdepId = bdepId;
	}

	public HealthData getHealth() {
		return health;
	}

	public void setHealth(HealthData health) {
		this.health = health;
	}

	public String getProjectAccess() {
		return projectAccess;
	}

	public void setProjectAccess(String projectAccess) {
		this.projectAccess = projectAccess;
	}

	public String getUserImage() {
		return userImage;
	}

	public void setUserImage(String userImage) {
		this.userImage = userImage;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public SortedMap<String,Double> getEngScores() {
		return engScores;
	}

	public void setEngScores(SortedMap<String,Double> engScores) {
		this.engScores = engScores;
	}

}
