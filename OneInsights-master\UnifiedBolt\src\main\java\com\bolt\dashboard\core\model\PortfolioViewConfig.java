package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "PortfolioViewConfig")
public class PortfolioViewConfig extends BaseModel{
	private String projectName;
	private String rteName;
	private String piName;
	private String rallyUserName;
	private String rallyPassword;
	private String environment;
	private List<ProjectHealthConfig> config = new ArrayList<>();
	private List<Map> teams;
	
	public String getProjectName() {
		return projectName;
	}
	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}
	public List<ProjectHealthConfig> getConfig() {
		return config;
	}
	public void setConfig(List<ProjectHealthConfig> config) {
		this.config = config;
	}
	public List<Map> getTeams() {
		return teams;
	}
	public void setTeams(List<Map> teams) {
		this.teams = teams;
	}
	public String getRteName() {
		return rteName;
	}
	public void setRteName(String rteName) {
		this.rteName = rteName;
	}
	public String getPiName() {
		return piName;
	}
	public void setPiName(String piName) {
		this.piName = piName;
	}
	public String getRallyUserName() {
		return rallyUserName;
	}
	public void setRallyUserName(String rallyUserName) {
		this.rallyUserName = rallyUserName;
	}
	public String getRallyPassword() {
		return rallyPassword;
	}
	public void setRallyPassword(String rallyPassword) {
		this.rallyPassword = rallyPassword;
	}
	public String getEnvironment() {
		return environment;
	}
	public void setEnvironment(String environment) {
		this.environment = environment;
	}
	
	
}
