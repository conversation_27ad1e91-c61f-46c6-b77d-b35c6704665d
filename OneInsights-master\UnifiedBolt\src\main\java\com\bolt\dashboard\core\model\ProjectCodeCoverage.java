package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "CodeCoverage")
public class ProjectCodeCoverage {
	// @Id
	private String id;
	private ObjectId collectorItemId;
	private long timestamp;
	private double projectLineRate;
	private double pojectBranchRate;
	private double projectComplexity;
	private int projectLinesValid;
	private int projectBranchesCovered;
	private List<PackageCodeCoverage> packageCoverageMetrics = new ArrayList<PackageCodeCoverage>();
	private int projectLinesCovered;
	private int projectBranchValid;
	private String version;
	private Object versionObject;
	private String projectCoverage;
	private String projectCoveragePercentage;
	private String projectName;
	private String coverageType;
	private int projectLevelTotalPackages;
	private int projectLevelPackagesCovered;
	private String projectLevelPackagesCoverage;
	private double projectLevelPackagesCoveragePercentage;
	private int projectLevelTotalClasses;
	private int projectLevelClassesCovered;
	private String projectLevelClassCoverage;
	private double projectLevelClassCoveragePercentage;
	private int projectLevelTotalMethods;
	private int projectLevelMethodsCovered;
	private String projectLevelMethodCoverage;
	private double projectLevelMethodCoveragePercentage;
	private int projectLevelTotalLines;
	private int projectLevelLinesCovered;
	private String projectLevelLinesCoverage;
	private double projectLevelLinessCoveragePercentage;
	private int greaterThanEightyCount;
	private int greaterThanFiftyCount;
	private int greaterThanThirtyFiveCount;
	private int betweenThirtyFiveCount;
	private int hundraedPercentaCount;
	private List<String> classCoverageGreaterThanEighty = new ArrayList<String>();
	private List<String> classCoverageGreaterThanFifty = new ArrayList<>();
	private List<String> classCoverageGreaterThanThirtyFive = new ArrayList<>();
	private List<String> classCoverageBetwenThirtyfive = new ArrayList<>();
	private List<String> classCoverageHundread = new ArrayList<>();
	private Map<String, Double> fileCoverage = new HashMap<>();

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getProjectName() {
		return projectName;
	}

	public List<PackageCodeCoverage> getPackageCoverageMetrics() {
		return packageCoverageMetrics;
	}

	public void setPackageCoverageMetrics(List<PackageCodeCoverage> packageCoverageMetrics) {
		this.packageCoverageMetrics = packageCoverageMetrics;
	}

	/**
	 * @return the projectLinesCovered
	 */
	public int getProjectLinesCovered() {
		return projectLinesCovered;
	}

	/**
	 * @param projectLinesCovered
	 *            the projectLinesCovered to set
	 */
	public void setProjectLinesCovered(int projectLinesCovered) {
		this.projectLinesCovered = projectLinesCovered;
	}

	/**
	 * @return the projectBranchValid
	 */
	public int getProjectBranchValid() {
		return projectBranchValid;
	}

	/**
	 * @param projectBranchValid
	 *            the projectBranchValid to set
	 */
	public void setProjectBranchValid(int projectBranchValid) {
		this.projectBranchValid = projectBranchValid;
	}

	/**
	 * @return the version
	 */
	public String getVersion() {
		return version;
	}

	/**
	 * @param version
	 *            the version to set
	 */
	public void setVersion(String version) {
		this.version = version;
	}

	public ObjectId getCollectorItemId() {
		return collectorItemId;
	}

	/**
	 * @param collectorItemId
	 *            the collectorItemId to set
	 */
	public void setCollectorItemId(ObjectId collectorItemId) {
		this.collectorItemId = collectorItemId;
	}

	/**
	 * @return the timestamp
	 */
	public long getTimestamp() {
		return timestamp;
	}

	/**
	 * @param timestamp
	 *            the timestamp to set
	 */
	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	/**
	 * @return the projectLineRate
	 */
	public double getProjectLineRate() {
		return projectLineRate;
	}

	/**
	 * @param projectLineRate
	 *            the projectLineRate to set
	 */
	public void setProjectLineRate(double projectLineRate) {
		this.projectLineRate = projectLineRate;
	}

	/**
	 * @return the pojectBranchRate
	 */
	public double getPojectBranchRate() {
		return pojectBranchRate;
	}

	/**
	 * @param pojectBranchRate
	 *            the pojectBranchRate to set
	 */
	public void setPojectBranchRate(double pojectBranchRate) {
		this.pojectBranchRate = pojectBranchRate;
	}

	/**
	 * @return the projectComplexity
	 */
	public double getProjectComplexity() {
		return projectComplexity;
	}

	/**
	 * @param projectComplexity
	 *            the projectComplexity to set
	 */
	public void setProjectComplexity(double projectComplexity) {
		this.projectComplexity = projectComplexity;
	}

	/**
	 * @return the projectLinesValid
	 */
	public int getProjectLinesValid() {
		return projectLinesValid;
	}

	/**
	 * @param projectLinesValid
	 *            the projectLinesValid to set
	 */
	public void setProjectLinesValid(int projectLinesValid) {
		this.projectLinesValid = projectLinesValid;
	}

	/**
	 * @return the projectBranchesCovered
	 */
	public int getProjectBranchesCovered() {
		return projectBranchesCovered;
	}

	/**
	 * @param projectBranchesCovered
	 *            the projectBranchesCovered to set
	 */
	public void setProjectBranchesCovered(int projectBranchesCovered) {
		this.projectBranchesCovered = projectBranchesCovered;
	}

	/**
	 * @return the projectCoverage
	 */
	public String getProjectCoverage() {
		return projectCoverage;
	}

	/**
	 * @param projectCoverage
	 *            the projectCoverage to set
	 */
	public void setProjectCoverage(String projectCoverage) {
		this.projectCoverage = projectCoverage;
	}

	/**
	 * @return the projectCoveragePercentage
	 */
	public String getProjectCoveragePercentage() {
		return projectCoveragePercentage;
	}

	/**
	 * @param projectCoveragePercentage
	 *            the projectCoveragePercentage to set
	 */
	public void setProjectCoveragePercentage(String projectCoveragePercentage) {
		this.projectCoveragePercentage = projectCoveragePercentage;
	}

	/**
	 * @return the projectLevelTotalPackages
	 */
	public int getProjectLevelTotalPackages() {
		return projectLevelTotalPackages;
	}

	/**
	 * @param projectLevelTotalPackages
	 *            the projectLevelTotalPackages to set
	 */
	public void setProjectLevelTotalPackages(int projectLevelTotalPackages) {
		this.projectLevelTotalPackages = projectLevelTotalPackages;
	}

	/**
	 * @return the projectLevelPackagesCovered
	 */
	public int getProjectLevelPackagesCovered() {
		return projectLevelPackagesCovered;
	}

	/**
	 * @return the greaterThanEightyCount
	 */
	public int getGreaterThanEightyCount() {
		return greaterThanEightyCount;
	}

	/**
	 * @param greaterThanEightyCount
	 *            the greaterThanEightyCount to set
	 */
	public void setGreaterThanEightyCount(int greaterThanEightyCount) {
		this.greaterThanEightyCount = greaterThanEightyCount;
	}

	/**
	 * @return the greaterThanFiftyCount
	 */
	public int getGreaterThanFiftyCount() {
		return greaterThanFiftyCount;
	}

	/**
	 * @param greaterThanFiftyCount
	 *            the greaterThanFiftyCount to set
	 */
	public void setGreaterThanFiftyCount(int greaterThanFiftyCount) {
		this.greaterThanFiftyCount = greaterThanFiftyCount;
	}

	/**
	 * @return the greaterThanThirtyFiveCount
	 */
	public int getGreaterThanThirtyFiveCount() {
		return greaterThanThirtyFiveCount;
	}

	/**
	 * @param greaterThanThirtyFiveCount
	 *            the greaterThanThirtyFiveCount to set
	 */
	public void setGreaterThanThirtyFiveCount(int greaterThanThirtyFiveCount) {
		this.greaterThanThirtyFiveCount = greaterThanThirtyFiveCount;
	}

	/**
	 * @return the betweenThirtyFiveCount
	 */
	public int getBetweenThirtyFiveCount() {
		return betweenThirtyFiveCount;
	}

	/**
	 * @param betweenThirtyFiveCount
	 *            the betweenThirtyFiveCount to set
	 */
	public void setBetweenThirtyFiveCount(int betweenThirtyFiveCount) {
		this.betweenThirtyFiveCount = betweenThirtyFiveCount;
	}

	public int getHundraedPercentaCount() {
		return hundraedPercentaCount;
	}

	/**
	 * @param hundraedPercentaCount
	 *            the hundraedPercentaCount to set
	 */
	public void setHundraedPercentaCount(int hundraedPercentaCount) {
		this.hundraedPercentaCount = hundraedPercentaCount;
	}

	/**
	 * @return the classCoverageGreaterThanEighty
	 */
	public List<String> getClassCoverageGreaterThanEighty() {
		return classCoverageGreaterThanEighty;
	}

	/**
	 * @param classCoverageGreaterThanEighty
	 *            the classCoverageGreaterThanEighty to set
	 */
	public void setClassCoverageGreaterThanEighty(List<String> classCoverageGreaterThanEighty) {
		this.classCoverageGreaterThanEighty = classCoverageGreaterThanEighty;
	}

	/**
	 * @return the classCoverageGreaterThanFifty
	 */
	public List<String> getClassCoverageGreaterThanFifty() {
		return classCoverageGreaterThanFifty;
	}

	/**
	 * @param classCoverageGreaterThanFifty
	 *            the classCoverageGreaterThanFifty to set
	 */
	public void setClassCoverageGreaterThanFifty(List<String> classCoverageGreaterThanFifty) {
		this.classCoverageGreaterThanFifty = classCoverageGreaterThanFifty;
	}

	/**
	 * @return the classCoverageGreaterThanThirtyFive
	 */
	public List<String> getClassCoverageGreaterThanThirtyFive() {
		return classCoverageGreaterThanThirtyFive;
	}

	/**
	 * @param classCoverageGreaterThanThirtyFive
	 *            the classCoverageGreaterThanThirtyFive to set
	 */
	public void setClassCoverageGreaterThanThirtyFive(List<String> classCoverageGreaterThanThirtyFive) {
		this.classCoverageGreaterThanThirtyFive = classCoverageGreaterThanThirtyFive;
	}

	/**
	 * @return the classCoverageBetwenThirtyfive
	 */
	public List<String> getClassCoverageBetwenThirtyfive() {
		return classCoverageBetwenThirtyfive;
	}

	/**
	 * @param classCoverageBetwenThirtyfive
	 *            the classCoverageBetwenThirtyfive to set
	 */
	public void setClassCoverageBetwenThirtyfive(List<String> classCoverageBetwenThirtyfive) {
		this.classCoverageBetwenThirtyfive = classCoverageBetwenThirtyfive;
	}

	/**
	 * @return the classCoverageHundread
	 */
	public List<String> getClassCoverageHundread() {
		return classCoverageHundread;
	}

	/**
	 * @param classCoverageHundread
	 *            the classCoverageHundread to set
	 */
	public void setClassCoverageHundread(List<String> classCoverageHundread) {
		this.classCoverageHundread = classCoverageHundread;
	}

	/**
	 * @param projectLevelPackagesCovered
	 *            the projectLevelPackagesCovered to set
	 */
	public void setProjectLevelPackagesCovered(int projectLevelPackagesCovered) {
		this.projectLevelPackagesCovered = projectLevelPackagesCovered;
	}

	/**
	 * @return the projectLevelPackagesCoverage
	 */
	public String getProjectLevelPackagesCoverage() {
		return projectLevelPackagesCoverage;
	}

	/**
	 * @param projectLevelPackagesCoverage
	 *            the projectLevelPackagesCoverage to set
	 */
	public void setProjectLevelPackagesCoverage(String projectLevelPackagesCoverage) {
		this.projectLevelPackagesCoverage = projectLevelPackagesCoverage;
	}

	/**
	 * @return the projectLevelPackagesCoveragePercentage
	 */
	public double getProjectLevelPackagesCoveragePercentage() {
		return projectLevelPackagesCoveragePercentage;
	}

	/**
	 * @param projectLevelPackagesCoveragePercentage
	 *            the projectLevelPackagesCoveragePercentage to set
	 */
	public void setProjectLevelPackagesCoveragePercentage(double projectLevelPackagesCoveragePercentage) {
		this.projectLevelPackagesCoveragePercentage = projectLevelPackagesCoveragePercentage;
	}

	/**
	 * @return the projectLevelTotalClasses
	 */
	public int getProjectLevelTotalClasses() {
		return projectLevelTotalClasses;
	}

	/**
	 * @param projectLevelTotalClasses
	 *            the projectLevelTotalClasses to set
	 */
	public void setProjectLevelTotalClasses(int projectLevelTotalClasses) {
		this.projectLevelTotalClasses = projectLevelTotalClasses;
	}

	/**
	 * @return the projectLevelClassesCovered
	 */
	public int getProjectLevelClassesCovered() {
		return projectLevelClassesCovered;
	}

	/**
	 * @param projectLevelClassesCovered
	 *            the projectLevelClassesCovered to set
	 */
	public void setProjectLevelClassesCovered(int projectLevelClassesCovered) {
		this.projectLevelClassesCovered = projectLevelClassesCovered;
	}

	/**
	 * @return the projectLevelClassCoverage
	 */
	public String getProjectLevelClassCoverage() {
		return projectLevelClassCoverage;
	}

	/**
	 * @param projectLevelClassCoverage
	 *            the projectLevelClassCoverage to set
	 */
	public void setProjectLevelClassCoverage(String projectLevelClassCoverage) {
		this.projectLevelClassCoverage = projectLevelClassCoverage;
	}

	/**
	 * @return the projectLevelClassCoveragePercentage
	 */
	public double getProjectLevelClassCoveragePercentage() {
		return projectLevelClassCoveragePercentage;
	}

	/**
	 * @param projectLevelClassCoveragePercentage
	 *            the projectLevelClassCoveragePercentage to set
	 */
	public void setProjectLevelClassCoveragePercentage(double projectLevelClassCoveragePercentage) {
		this.projectLevelClassCoveragePercentage = projectLevelClassCoveragePercentage;
	}

	/**
	 * @return the projectLevelTotalMethods
	 */
	public int getProjectLevelTotalMethods() {
		return projectLevelTotalMethods;
	}

	/**
	 * @param projectLevelTotalMethods
	 *            the projectLevelTotalMethods to set
	 */
	public void setProjectLevelTotalMethods(int projectLevelTotalMethods) {
		this.projectLevelTotalMethods = projectLevelTotalMethods;
	}

	/**
	 * @return the projectLevelMethodsCovered
	 */
	public int getProjectLevelMethodsCovered() {
		return projectLevelMethodsCovered;
	}

	/**
	 * @param projectLevelMethodsCovered
	 *            the projectLevelMethodsCovered to set
	 */
	public void setProjectLevelMethodsCovered(int projectLevelMethodsCovered) {
		this.projectLevelMethodsCovered = projectLevelMethodsCovered;
	}

	/**
	 * @return the projectLevelMethodCoverage
	 */
	public String getProjectLevelMethodCoverage() {
		return projectLevelMethodCoverage;
	}

	/**
	 * @param projectLevelMethodCoverage
	 *            the projectLevelMethodCoverage to set
	 */
	public void setProjectLevelMethodCoverage(String projectLevelMethodCoverage) {
		this.projectLevelMethodCoverage = projectLevelMethodCoverage;
	}

	/**
	 * @return the projectLevelMethodCoveragePercentage
	 */
	public double getProjectLevelMethodCoveragePercentage() {
		return projectLevelMethodCoveragePercentage;
	}

	/**
	 * @param projectLevelMethodCoveragePercentage
	 *            the projectLevelMethodCoveragePercentage to set
	 */
	public void setProjectLevelMethodCoveragePercentage(double projectLevelMethodCoveragePercentage) {
		this.projectLevelMethodCoveragePercentage = projectLevelMethodCoveragePercentage;
	}

	/**
	 * @return the projectLevelTotalLines
	 */
	public int getProjectLevelTotalLines() {
		return projectLevelTotalLines;
	}

	/**
	 * @param projectLevelTotalLines
	 *            the projectLevelTotalLines to set
	 */
	public void setProjectLevelTotalLines(int projectLevelTotalLines) {
		this.projectLevelTotalLines = projectLevelTotalLines;
	}

	/**
	 * @return the projectLevelLinesCovered
	 */
	public int getProjectLevelLinesCovered() {
		return projectLevelLinesCovered;
	}

	/**
	 * @param projectLevelLinesCovered
	 *            the projectLevelLinesCovered to set
	 */
	public void setProjectLevelLinesCovered(int projectLevelLinesCovered) {
		this.projectLevelLinesCovered = projectLevelLinesCovered;
	}

	/**
	 * @return the projectLevelLinesCoverage
	 */
	public String getProjectLevelLinesCoverage() {
		return projectLevelLinesCoverage;
	}

	/**
	 * @param projectLevelLinesCoverage
	 *            the projectLevelLinesCoverage to set
	 */
	public void setProjectLevelLinesCoverage(String projectLevelLinesCoverage) {
		this.projectLevelLinesCoverage = projectLevelLinesCoverage;
	}

	/**
	 * @return the projectLevelLinessCoveragePercentage
	 */
	public double getProjectLevelLinessCoveragePercentage() {
		return projectLevelLinessCoveragePercentage;
	}

	/**
	 * @param projectLevelLinessCoveragePercentage
	 *            the projectLevelLinessCoveragePercentage to set
	 */
	public void setProjectLevelLinessCoveragePercentage(double projectLevelLinessCoveragePercentage) {
		this.projectLevelLinessCoveragePercentage = projectLevelLinessCoveragePercentage;
	}

	/*
	 * @Override public int compareTo(final ProjectCodeCoverage o) { return
	 * Long.compare(this.timestamp, o.timestamp);
	 *
	 * }
	 */

	public Map<String, Double> getFileCoverage() {
		return fileCoverage;
	}

	public void setFileCoverage(Map<String, Double> fileCoverage) {
		this.fileCoverage = fileCoverage;
	}

	public Object getVersionObject() {
		return versionObject;
	}

	public void setVersionObject(Object versionObject) {
		this.versionObject = versionObject;
	}

	public String getCoverageType() {
		return coverageType;
	}

	public void setCoverageType(String coverageType) {
		this.coverageType = coverageType;
	}

}
