package com.bolt.dashboard.core.model;

import java.util.List;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "projectComparison")
public class ProjectComparison extends BaseModel {
    private String projectName;
    private long timeStamp;
    private List<ProjectComparisonMetrics> metrics;

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public List<ProjectComparisonMetrics> getMetrics() {
        return metrics;
    }

    public void setMetrics(List<ProjectComparisonMetrics> metrics) {
        this.metrics = metrics;
    }

}
