package com.bolt.dashboard.core.model;

public class ProjectComparisonMetrics {

    private String projectName;
    private double technicalDebt;
    private double velocity;
    private double efficiency;
    private double defects;

    private double buildFailure;

    public double getTechnicalDebt() {
        return technicalDebt;
    }

    public void setTechnicalDebt(Double technicalDebt) {
        this.technicalDebt = technicalDebt;
    }

    public double getVelocity() {
        return velocity;
    }

    public void setVelocity(Double velocity) {
        this.velocity = velocity;
    }

    public double getEfficiency() {
        return efficiency;
    }

    public void setEfficiency(double efficiency) {
        this.efficiency = efficiency;
    }

    public double getDefects() {
        return defects;
    }

    public void setDefects(Double defects) {
        this.defects = defects;
    }

    public double getBuildFailure() {
        return buildFailure;
    }

    public void setBuildFailure(Double buildFailure) {
        this.buildFailure = buildFailure;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

}
