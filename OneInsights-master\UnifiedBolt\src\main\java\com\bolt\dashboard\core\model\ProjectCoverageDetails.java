package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.mongodb.core.mapping.Document;
@Document(collection = "ProjectCodeCoverage")
public class ProjectCoverageDetails extends BaseModel {
	
	private long created_timestamp;
	private long started_timestamp;
	private String pName;
	

	private int total_statements;
	private int total_branches;
	private int total_functions;
	private int total_lines;
	private int covered_statements;
	private int covered_lines;
	private int covered_functions;
	private int covered_branches;
	private double covered_branches_percentage;
	private double covered_function_percentage;
	private double covered_lines_percentage;
	private double covered_statements_percentage;
	private double covered_class_percentage;
	private double covered_instruction_percentage;
	private int test_cases_passed;
	private int test_cases_failed;
	private int total_test_cases;
	private String commiter;
	private double overall_coverage;
	private String repoName;
	private String branchName;
	private String groupName;
	private int jobId;
	private List<FilesCoverageDetails> files =new ArrayList<>();

	
	public long getCreated_timestamp() {
		return created_timestamp;
	}

	public void setCreated_timestamp(long created_timestamp) {
		this.created_timestamp = created_timestamp;
	}

	public long getStarted_timestamp() {
		return started_timestamp;
	}

	public void setStarted_timestamp(long started_timestamp) {
		this.started_timestamp = started_timestamp;
	}
	public int getJobId() {
		return jobId;
	}

	public void setJobId(int jobId) {
		this.jobId = jobId;
	}

	public String getBranchName() {
		return branchName;
	}

	public void setBranchName(String branchName) {
		this.branchName = branchName;
	}

	public String getRepoName() {
		return repoName;
	}

	public void setRepoName(String repoName) {
		this.repoName = repoName;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public String getpName() {
		return pName;
	}

	public void setpName(String pName) {
		this.pName = pName;
	}

	public int getTotal_statements() {
		return total_statements;
	}

	public void setTotal_statements(int total_statements) {
		this.total_statements = total_statements;
	}

	public int getTotal_branches() {
		return total_branches;
	}

	public void setTotal_branches(int total_branches) {
		this.total_branches = total_branches;
	}

	public int getTotal_functions() {
		return total_functions;
	}

	public void setTotal_functions(int total_functions) {
		this.total_functions = total_functions;
	}

	public int getTotal_lines() {
		return total_lines;
	}

	public void setTotal_lines(int total_lines) {
		this.total_lines = total_lines;
	}

	public int getCovered_statements() {
		return covered_statements;
	}

	public void setCovered_statements(int covered_statements) {
		this.covered_statements = covered_statements;
	}

	public int getCovered_lines() {
		return covered_lines;
	}

	public void setCovered_lines(int covered_lines) {
		this.covered_lines = covered_lines;
	}

	public int getCovered_functions() {
		return covered_functions;
	}

	public void setCovered_functions(int covered_functions) {
		this.covered_functions = covered_functions;
	}

	public int getCovered_branches() {
		return covered_branches;
	}

	public void setCovered_branches(int covered_branches) {
		this.covered_branches = covered_branches;
	}

	public double getCovered_branches_percentage() {
		return covered_branches_percentage;
	}

	public void setCovered_branches_percentage(double covered_branches_percentage) {
		this.covered_branches_percentage = covered_branches_percentage;
	}

	public double getCovered_function_percentage() {
		return covered_function_percentage;
	}

	public void setCovered_function_percentage(double covered_function_percentage) {
		this.covered_function_percentage = covered_function_percentage;
	}

	public double getCovered_lines_percentage() {
		return covered_lines_percentage;
	}

	public void setCovered_lines_percentage(double covered_lines_percentage) {
		this.covered_lines_percentage = covered_lines_percentage;
	}

	public double getCovered_statements_percentage() {
		return covered_statements_percentage;
	}

	public void setCovered_statements_percentage(double covered_statements_percentage) {
		this.covered_statements_percentage = covered_statements_percentage;
	}

	public int getTest_cases_passed() {
		return test_cases_passed;
	}

	public void setTest_cases_passed(int test_cases_passed) {
		this.test_cases_passed = test_cases_passed;
	}

	public int getTest_cases_failed() {
		return test_cases_failed;
	}

	public void setTest_cases_failed(int test_cases_failed) {
		this.test_cases_failed = test_cases_failed;
	}

	public int getTotal_test_cases() {
		return total_test_cases;
	}

	public void setTotal_test_cases(int total_test_cases) {
		this.total_test_cases = total_test_cases;
	}

	public String getCommiter() {
		return commiter;
	}

	public void setCommiter(String commiter) {
		this.commiter = commiter;
	}

	public double getOverall_coverage() {
		return overall_coverage;
	}

	public void setOverall_coverage(double overall_coverage) {
		this.overall_coverage = overall_coverage;
	}

	public List<FilesCoverageDetails> getFiles() {
		return files;
	}

	public void setFiles(List<FilesCoverageDetails> files) {
		this.files = files;
	}

	public double getCovered_class_percentage() {
		return covered_class_percentage;
	}

	public void setCovered_class_percentage(double covered_class_percentage) {
		this.covered_class_percentage = covered_class_percentage;
	}

	public double getCovered_instruction_percentage() {
		return covered_instruction_percentage;
	}

	public void setCovered_instruction_percentage(double covered_instruction_percentage) {
		this.covered_instruction_percentage = covered_instruction_percentage;
	}

	
	
	
	
	
	
	

}
