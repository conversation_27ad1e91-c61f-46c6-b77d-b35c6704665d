package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "Project")
public class ProjectHealth {
    @Id
    private String id;
    private long timestamp;
    private String projectName;
    private String sprintName;
    private List<ProjectHealthApplicationPhase> applicationPhaseList = new ArrayList<>();
    private List<ProjectHealthConfig> config = new ArrayList<>();

    public String getSprintName() {
        return sprintName;
    }

    public void setSprintName(String sprintName) {
        this.sprintName = sprintName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<ProjectHealthApplicationPhase> getApplicationPhaseList() {
        return applicationPhaseList;
    }

    public void setApplicationPhaseList(List<ProjectHealthApplicationPhase> applicationPhaseList) {
        this.applicationPhaseList = applicationPhaseList;
    }

    public List<ProjectHealthConfig> getConfig() {
        return config;
    }

    public void setConfig(List<ProjectHealthConfig> config) {
        this.config = config;
    }

}
