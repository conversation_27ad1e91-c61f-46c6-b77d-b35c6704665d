package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

public class ProjectHealthApplicationPhase {
	private String applicationPhaseName;
	private int applicationPhaseWeightage;
	private List<ProjectHealthRuleSet> ruleSet = new ArrayList<>();

	public String getApplicationPhaseName() {
		return applicationPhaseName;
	}

	public void setApplicationPhaseName(String applicationPhaseName) {
		this.applicationPhaseName = applicationPhaseName;
	}

	public List<ProjectHealthRuleSet> getRuleSet() {
		return ruleSet;
	}

	public void setRuleSet(List<ProjectHealthRuleSet> ruleSet) {
		this.ruleSet = ruleSet;
	}

	public int getApplicationPhaseWeightage() {
		return applicationPhaseWeightage;
	}

	public void setApplicationPhaseWeightage(int applicationPhaseWeightage) {
		this.applicationPhaseWeightage = applicationPhaseWeightage;
	}
}
