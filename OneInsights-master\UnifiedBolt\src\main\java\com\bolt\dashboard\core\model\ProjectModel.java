package com.bolt.dashboard.core.model;

import java.util.List;
import java.util.SortedMap;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "ALMProject")
public class ProjectModel extends BaseModel {
    private String almType;
    private String projectName;
    private Long timeStamp;
    private Number sCount;
    private Number blStCount;
    private Long cRuns;
    private String projKey;
    private List<defectCount> defectCount;
    private SortedMap<String,Double> engScores;
    public String getAlmType() {
	return almType;
    }

    public void setAlmType(String almType) {
	this.almType = almType;
    }

    public Long getTimeStamp() {
	return timeStamp;
    }

    public void setTimeStamp(Long timeStamp) {
	this.timeStamp = timeStamp;
    }

    public Number getsCount() {
	return sCount;
    }

    public void setsCount(Number sCount) {
	this.sCount = sCount;
    }

    public Number getBlStCount() {
	return blStCount;
    }

    public void setBlStCount(Number blStCount) {
	this.blStCount = blStCount;
    }

    public Long getcRuns() {
	return cRuns;
    }

    public void setcRuns(Long cRuns) {
	this.cRuns = cRuns;
    }

    public String getProjectName() {
	return projectName;
    }

    public void setProjectName(String projectName) {
	this.projectName = projectName;
    }

    public List<defectCount> getDefectCount() {
	return defectCount;
    }

    public void setDefectCount(List<defectCount> defectCount) {
	this.defectCount = defectCount;
    }

    public String getProjKey() {
	return projKey;
    }

    public void setProjKey(String projKey) {
	this.projKey = projKey;
    }

	public SortedMap<String,Double> getEngScores() {
		return engScores;
	}

	public void setEngScores(SortedMap<String,Double> engScores) {
		this.engScores = engScores;
	}

}
