package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "RepoCodeCoverageStatus")
public class RepoCodeCoverageStatus extends BaseModel {
	private String repoName;
	private String groupName;
	private boolean coverageStatus;
	private String causeDescription;
	private String pName;
	public String getpName() {
		return pName;
	}
	public void setpName(String pName) {
		this.pName = pName;
	}
	public String getRepoName() {
		return repoName;
	}
	public void setRepoName(String repoName) {
		this.repoName = repoName;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public boolean isCoverageStatus() {
		return coverageStatus;
	}
	public void setCoverageStatus(boolean coverageStatus) {
		this.coverageStatus = coverageStatus;
	}
	public String getCauseDescription() {
		return causeDescription;
	}
	public void setCauseDescription(String causeDescription) {
		this.causeDescription = causeDescription;
	}
	
	
	

}
