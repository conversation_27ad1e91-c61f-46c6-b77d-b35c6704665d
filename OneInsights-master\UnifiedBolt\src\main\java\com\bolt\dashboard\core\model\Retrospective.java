package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "RetrospectiveDetails")
public class Retrospective extends BaseModel {
	private int uniqueID;
	private String userName;
	private String projectName;
	private String sprintName;
	private String wentWell;
	private String wentBad;
	private String toContinue;
	private String happyMood;

	public int getUniqueID() {
		return uniqueID;
	}

	public void setUniqueID(int uniqueID) {
		this.uniqueID = uniqueID;
	}

	private String $$hashKey;

	public String getWentWell() {
		return wentWell;
	}

	public void setWentWell(String wentWell) {
		this.wentWell = wentWell;
	}

	public String getWentBad() {
		return wentBad;
	}

	public void setWentBad(String wentBad) {
		this.wentBad = wentBad;
	}

	public String getToContinue() {
		return toContinue;
	}

	public void setToContinue(String toContinue) {
		this.toContinue = toContinue;
	}

	public String get$$hashKey() {
		return $$hashKey;
	}

	public void set$$hashKey(String $$hashKey) {
		this.$$hashKey = $$hashKey;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getSprintName() {
		return sprintName;
	}

	public void setSprintName(String sprintName) {
		this.sprintName = sprintName;
	}

	public String getHappyMood() {
		return happyMood;
	}

	public void setHappyMood(String happyMood) {
		this.happyMood = happyMood;
	}

}