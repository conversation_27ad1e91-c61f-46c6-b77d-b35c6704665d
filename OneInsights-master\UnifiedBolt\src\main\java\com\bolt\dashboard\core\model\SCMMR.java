package com.bolt.dashboard.core.model;


import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "SCMMerge")
public class SCMMR extends BaseModel implements Comparable<SCMMR> {
	protected int mrId;
    protected String title;
    protected String scType;
    protected long createdAt;
    protected long updatedAt;
    protected String description;
    protected String targetBranch;
    protected String sourceBranch;
    protected String state;
    protected String projectName;
    protected String repoName;
    protected String groupName;
    @Override
    public int compareTo(final SCMMR o) {
        return Long.compare(this.createdAt, o.createdAt);
    }
	public String getTitle() {
		return title;
	}
	
	public void setTitle(String title) {
		this.title = title;
	}
	
	public int getMrId() {
		return mrId;
	}
	public void setMrId(int mrId) {
		this.mrId = mrId;
	}
	public String getScType() {
		return scType;
	}
	public void setScType(String scType) {
		this.scType = scType;
	}
	public long getCreatedAt() {
		return createdAt;
	}
	public void setCreatedAt(long createdAt) {
		this.createdAt = createdAt;
	}
	public long getUpdatedAt() {
		return updatedAt;
	}
	public void setUpdatedAt(long updatedAt) {
		this.updatedAt = updatedAt;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getTargetBranch() {
		return targetBranch;
	}
	public void setTargetBranch(String targetBranch) {
		this.targetBranch = targetBranch;
	}
	public String getSourceBranch() {
		return sourceBranch;
	}
	public void setSourceBranch(String sourceBranch) {
		this.sourceBranch = sourceBranch;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getProjectName() {
		return projectName;
	}
	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}
	public String getRepoName() {
		return repoName;
	}
	public void setRepoName(String repoName) {
		this.repoName = repoName;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
    
}