package com.bolt.dashboard.core.model;

import java.util.List;

import org.springframework.data.mongodb.core.mapping.Document;

import com.fasterxml.jackson.annotation.JsonIgnore;

@Document(collection = "SCM")
public class SCMTool extends BaseModel implements Comparable<SCMTool> {
    protected String scType;
    protected long timestamp;
    protected String url;
    protected String branch;
    protected String revisionNo;
    protected String commitLog;
    protected long commitTS;
    protected int noOfChanges;
    protected int addition;
    protected int deletion;
    protected int modification;
    private String commiter;
    protected List<String> fileName;
    private List<FileDetails> fileDetails;
    private List<DeletedFileDetails> deletedFileDetails;
    private String insertionInCommit;
    private String deletioninfileInCommit;
    private String projectName;
    private String repoName;
    private String branchName;
    
    private String commitTsFormatted;
    public String getCommitTsFormatted() {
		return commitTsFormatted;
	}

	public void setCommitTsFormatted(String commitTsFormatted) {
		this.commitTsFormatted = commitTsFormatted;
	}

	public String getCommitId() {
		return commitId;
	}

	public void setCommitId(String commitId) {
		this.commitId = commitId;
	}

	public String getRevisionNo() {
		return revisionNo;
	}


	private String groupName;
    private String commitId;
    public String getScType() {
        return scType;
    }

    public void setScType(String scType) {
        this.scType = scType;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String scmUrl) {
        this.url = scmUrl;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public String getRevisioNo() {
        return revisionNo;
    }

    public void setRevisionNo(String revisionNo) {
        this.revisionNo = revisionNo;
    }

    public String getCommitLog() {
        return commitLog;
    }

    public void setCommitLog(String commitLog) {
        this.commitLog = commitLog;
    }

    public long getCommitTS() {
        return commitTS;
    }

    public void setCommitTS(long commitTS) {
        this.commitTS = commitTS;
    }

   
   
    public int getNoOfChanges() {
		return noOfChanges;
	}

	public void setNoOfChanges(int noOfChanges) {
		this.noOfChanges = noOfChanges;
	}

	/**
     * @return the additionInCommit
     */
    public int getAddition() {
        return addition;
    }

    /**
     * @param additionInCommit
     *            the additionInCommit to set
     */
    public void setAddition(int addition) {
        this.addition = addition;
    }

    /**
     * @return the deletionInCommit
     */
    public int getDeletion() {
        return deletion;
    }

    /**
     * @param deletionInCommit
     *            the deletionInCommit to set
     */
    public void setDeletion(int deletion) {
        this.deletion = deletion;
    }

    /**
     * @return the modificationsInCommit
     */
    public int getModification() {
        return modification;
    }

    /**
     * @param modificationsInCommit
     *            the modificationsInCommit to set
     */
    public void setModification(int modification) {
        this.modification = modification;
    }

    /**
     * @return the scmFileName
     */
    public List<String> getfileName() {
        return fileName;
    }

    /**
     * @param scmFileName
     *            the scmFileName to set
     */
    public void setFileName(List<String> fileName) {
        this.fileName = fileName;
    }

    public String getCommiter() {
        return commiter;
    }

    public void setCommiter(String commiter) {
        this.commiter = commiter;
    }

    public List<FileDetails> getFileDetails() {
        return fileDetails;
    }

    public void setFileDetails(List<FileDetails> fileDetails) {
        this.fileDetails = fileDetails;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getInsertionInCommit() {
        return insertionInCommit;
    }

    public void setInsertionInCommit(String insertionInCommit) {
        this.insertionInCommit = insertionInCommit;
    }

    public String getDeletioninfileInCommit() {
        return deletioninfileInCommit;
    }

    public void setDeletioninfileInCommit(String deletioninfileInCommit) {
        this.deletioninfileInCommit = deletioninfileInCommit;
    }

    public List<DeletedFileDetails> getDeletedFileDetails() {
        return deletedFileDetails;
    }

    public void setDeletedFileDetails(List<DeletedFileDetails> deletedFileDetails) {
        this.deletedFileDetails = deletedFileDetails;
    }

    @Override
    public int compareTo(final SCMTool o) {
        return Long.compare(this.commitTS, o.commitTS);
    }

	public String getRepoName() {
		return repoName;
	}

	public void setRepoName(String repoName) {
		this.repoName = repoName;
	}

	public String getBranchName() {
		return branchName;
	}

	public void setBranchName(String branchName) {
		this.branchName = branchName;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

}
