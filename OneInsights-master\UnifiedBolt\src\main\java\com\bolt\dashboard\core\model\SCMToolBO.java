package com.bolt.dashboard.core.model;

import java.util.List;

public class SCMToolBO {

    protected String scType;
    protected long commitTS;
    private String commiter;
    protected long noOfChanges;
    protected int addition;
    protected int deletion;
    protected int modification;
    private String projectName;
    protected List<DeletedFileDetails> deletedFileDetails;
    protected List<FileDetails> fileDetails;
    private String repoName;
    private String groupName;
    public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getScType() {
        return scType;
    }

    public void setScType(String scType) {
        this.scType = scType;
    }

    public long getCommitTS() {
        return commitTS;
    }

    public void setCommitTS(long commitTS) {
        this.commitTS = commitTS;
    }

    public String getCommiter() {
        return commiter;
    }

    public void setCommiter(String commiter) {
        this.commiter = commiter;
    }

    public long getNoOfChanges() {
        return noOfChanges;
    }

    public void setNoOfChanges(long noOfChanges) {
        this.noOfChanges = noOfChanges;
    }

    public int getAddition() {
        return addition;
    }

    public void setAddition(int addition) {
        this.addition = addition;
    }

    public int getDeletion() {
        return deletion;
    }

    public void setDeletion(int deletion) {
        this.deletion = deletion;
    }

    public int getModification() {
        return modification;
    }

    public void setModification(int modification) {
        this.modification = modification;
    }

    public List<DeletedFileDetails> getDeletedFileDetails() {
        return deletedFileDetails;
    }

    public void setDeletedFileDetails(List<DeletedFileDetails> deletedFileDetails) {
        this.deletedFileDetails = deletedFileDetails;
    }

    public List<FileDetails> getFileDetails() {
        return fileDetails;
    }

    public void setFileDetails(List<FileDetails> fileDetails) {
        this.fileDetails = fileDetails;
    }

	public String getRepoName() {
		return repoName;
	}

	public void setRepoName(String repoName) {
		this.repoName = repoName;
	}

}
