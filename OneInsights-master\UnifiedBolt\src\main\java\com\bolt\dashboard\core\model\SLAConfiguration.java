package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "SLAConfiguration")
public class SLAConfiguration extends BaseModel {
	private String tier;
	private String projectName;
	private String respTime;
	private String resoTime;
	private double storyPoint;
	private String[] responseState;
	private String[] resolveState;
	private String[] sevArray;

	public String getTier() {
		return tier;
	}

	public String getRespTime() {
		return respTime;
	}

	public void setRespTime(String respTime) {
		this.respTime = respTime;
	}

	public String getResoTime() {
		return resoTime;
	}

	public void setResoTime(String resoTime) {
		this.resoTime = resoTime;
	}

	public void setTier(String tier) {
		this.tier = tier;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String[] getResponseState() {
		return responseState;
	}

	public void setResponseState(String[] responseState) {
		this.responseState = responseState;
	}

	public String[] getResolveState() {
		return resolveState;
	}

	public void setResolveState(String[] resolveState) {
		this.resolveState = resolveState;
	}

	public String[] getSevArray() {
		return sevArray;
	}

	public void setSevArray(String[] sevArray) {
		this.sevArray = sevArray;
	}

	public double getStoryPoint() {
		return storyPoint;
	}

	public void setStoryPoint(double storyPoint) {
		this.storyPoint = storyPoint;
	}

}
