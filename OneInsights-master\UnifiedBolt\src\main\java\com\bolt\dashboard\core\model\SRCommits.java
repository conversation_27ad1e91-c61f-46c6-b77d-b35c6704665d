package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "stackrankings")
public class SRCommits extends BaseModel {
    private long timpStamp;
    private String author;
    private String dailyCommitPoint;

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getDailyCommitPoint() {
        return dailyCommitPoint;
    }

    public void setDailyCommitPoint(String dailyCommitPoint) {
        this.dailyCommitPoint = dailyCommitPoint;
    }

    public long getTimpStamp() {
        return timpStamp;
    }

    public void setTimpStamp(long timpStamp) {
        this.timpStamp = timpStamp;
    }
}
