package com.bolt.dashboard.core.model;


import java.util.ArrayList;
import java.util.List;

public class ScoreCardSprintData {
	private String sprintName;
	private int sprintId;
	private double commitedSp;
	private double completedSp;
	private double commitedAfterSp;
	private double refinedSp;
	private double removedSp;
	private long startDate;
	private long endDate;
	private double effort;
	private double defects;
	private double capacity;
	private double committedStories;
	private double completedStories;
	private double refinedDefects;
	private double completedDefects;
	private String state;
	private List<IssueList> issuesCommited= new ArrayList<>();
	private List<IssueList> issuesCommitedAfter= new ArrayList<>();
	private List<IssueList> issuesComplted= new ArrayList<>();
	private List<IssueList> issuesRemoved= new ArrayList<>();
	private List<IssueList> issuesRefined= new ArrayList<>();	
	
	public double getCommittedStories() {
		return committedStories;
	}
	public void setCommittedStories(double committedStories) {
		this.committedStories = committedStories;
	}
	public double getCompletedStories() {
		return completedStories;
	}
	public void setCompletedStories(double completedStories) {
		this.completedStories = completedStories;
	}
	public double getEffort() {
		return effort;
	}
	public void setEffort(double effort) {
		this.effort = effort;
	}
	public double getDefects() {
		return defects;
	}
	public void setDefects(double defects) {
		this.defects = defects;
	}
	public double getCapacity() {
		return capacity;
	}
	public void setCapacity(double capacity) {
		this.capacity = capacity;
	}
	public double getCompletedDefects() {
		return completedDefects;
	}
	public void setCompletedDefects(double completedDefects) {
		this.completedDefects = completedDefects;
	}

	
	public long getStartDate() {
		return startDate;
	}
	public void setStartDate(long startDate) {
		this.startDate = startDate;
	}
	public long getEndDate() {
		return endDate;
	}
	public void setEndDate(long endDate) {
		this.endDate = endDate;
	}
	public double getCommitedSp() {
		return commitedSp;
	}
	public void setCommitedSp(double commitedSp) {
		this.commitedSp = commitedSp;
	}
	public double getCompletedSp() {
		return completedSp;
	}
	public void setCompletedSp(double completedSp) {
		this.completedSp = completedSp;
	}
	public double getCommitedAfterSp() {
		return commitedAfterSp;
	}
	public void setCommitedAfterSp(double commitedAfterSp) {
		this.commitedAfterSp = commitedAfterSp;
	}
	public double getRefinedSp() {
		return refinedSp;
	}
	public void setRefinedSp(double refinedSp) {
		this.refinedSp = refinedSp;
	}
	public double getRemovedSp() {
		return removedSp;
	}
	public void setRemovedSp(double removedSp) {
		this.removedSp = removedSp;
	}

	
	public String getSprintName() {
		return sprintName;
	}
	public List<IssueList> getIssuesCommited() {
		return issuesCommited;
	}
	public void setIssuesCommited(List<IssueList> issuesCommited) {
		this.issuesCommited = issuesCommited;
	}
	public List<IssueList> getIssuesCommitedAfter() {
		return issuesCommitedAfter;
	}
	public void setIssuesCommitedAfter(List<IssueList> issuesCommitedAfter) {
		this.issuesCommitedAfter = issuesCommitedAfter;
	}
	public List<IssueList> getIssuesComplted() {
		return issuesComplted;
	}
	public void setIssuesComplted(List<IssueList> issuesComplted) {
		this.issuesComplted = issuesComplted;
	}
	public List<IssueList> getIssuesRemoved() {
		return issuesRemoved;
	}
	public void setIssuesRemoved(List<IssueList> issuesRemoved) {
		this.issuesRemoved = issuesRemoved;
	}
	public List<IssueList> getIssuesRefined() {
		return issuesRefined;
	}
	public void setIssuesRefined(List<IssueList> issuesRefined) {
		this.issuesRefined = issuesRefined;
	}
	public void setSprintName(String sprintName) {
		this.sprintName = sprintName;
	}
	
	public int getSprintId() {
		return sprintId;
	}
	public void setSprintId(int sprintId) {
		this.sprintId = sprintId;
	}
	public double getRefinedDefects() {
		return refinedDefects;
	}
	public void setRefinedDefects(double refinedDefects) {
		this.refinedDefects = refinedDefects;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}

	
}

