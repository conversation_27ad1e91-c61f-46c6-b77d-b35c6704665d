package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "Incidents")
public class ServiceNowInc   extends BaseModel{

	private String sys_updated_on;
    private String number;
    private String state ;
    private String sys_created_by;
    private String impact;
    private String active;
    private String priority ;
    private String short_description;
    private String sys_class_name ;
    private String assigned_to;
    private String sys_updated_by;
    private String sys_created_on;
    private String u_sla_duration;
    private String closed_at;
    private String opened_at;
    private String reopened_time;
    private String resolved_at;
    private String subcategory;
    private String close_code;
    private String contact_type;
    private String incident_state;
    private String urgency;
    private String category;
    private Long openedAt;
    private Long closedAt;
    private String assignment_group;
    private String projectName;
    
    
    
	public String getProjectName() {
		return projectName;
	}
	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}
	public String getAssignment_group() {
		return assignment_group;
	}
	public void setAssignment_group(String assignment_group) {
		this.assignment_group = assignment_group;
	}
	public Long getOpenedAt() {
		return openedAt;
	}
	public void setOpenedAt(Long openedAt) {
		this.openedAt = openedAt;
	}
	public Long getClosedAt() {
		return closedAt;
	}
	public void setClosedAt(Long closedAt) {
		this.closedAt = closedAt;
	}
	public String getSys_updated_on() {
		return sys_updated_on;
	}
	public void setSys_updated_on(String sys_updated_on) {
		this.sys_updated_on = sys_updated_on;
	}
	public String getNumber() {
		return number;
	}
	public void setNumber(String number) {
		this.number = number;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getSys_created_by() {
		return sys_created_by;
	}
	public void setSys_created_by(String sys_created_by) {
		this.sys_created_by = sys_created_by;
	}
	public String getImpact() {
		return impact;
	}
	public void setImpact(String impact) {
		this.impact = impact;
	}
	public String getActive() {
		return active;
	}
	public void setActive(String active) {
		this.active = active;
	}
	public String getPriority() {
		return priority;
	}
	public void setPriority(String priority) {
		this.priority = priority;
	}
	public String getShort_description() {
		return short_description;
	}
	public void setShort_description(String short_description) {
		this.short_description = short_description;
	}
	public String getSys_class_name() {
		return sys_class_name;
	}
	public void setSys_class_name(String sys_class_name) {
		this.sys_class_name = sys_class_name;
	}
	public String getAssigned_to() {
		return assigned_to;
	}
	public void setAssigned_to(String assigned_to) {
		this.assigned_to = assigned_to;
	}
	public String getSys_updated_by() {
		return sys_updated_by;
	}
	public void setSys_updated_by(String sys_updated_by) {
		this.sys_updated_by = sys_updated_by;
	}
	public String getSys_created_on() {
		return sys_created_on;
	}
	public void setSys_created_on(String sys_created_on) {
		this.sys_created_on = sys_created_on;
	}
	public String getU_sla_duration() {
		return u_sla_duration;
	}
	public void setU_sla_duration(String u_sla_duration) {
		this.u_sla_duration = u_sla_duration;
	}
	public String getClosed_at() {
		return closed_at;
	}
	public void setClosed_at(String closed_at) {
		this.closed_at = closed_at;
	}
	public String getOpened_at() {
		return opened_at;
	}
	public void setOpened_at(String opened_at) {
		this.opened_at = opened_at;
	}
	public String getReopened_time() {
		return reopened_time;
	}
	public void setReopened_time(String reopened_time) {
		this.reopened_time = reopened_time;
	}
	public String getResolved_at() {
		return resolved_at;
	}
	public void setResolved_at(String resolved_at) {
		this.resolved_at = resolved_at;
	}
	public String getSubcategory() {
		return subcategory;
	}
	public void setSubcategory(String subcategory) {
		this.subcategory = subcategory;
	}
	public String getClose_code() {
		return close_code;
	}
	public void setClose_code(String close_code) {
		this.close_code = close_code;
	}
	public String getContact_type() {
		return contact_type;
	}
	public void setContact_type(String contact_type) {
		this.contact_type = contact_type;
	}
	public String getIncident_state() {
		return incident_state;
	}
	public void setIncident_state(String incident_state) {
		this.incident_state = incident_state;
	}
	public String getUrgency() {
		return urgency;
	}
	public void setUrgency(String urgency) {
		this.urgency = urgency;
	}
	public String getCategory() {
		return category;
	}
	public void setCategory(String category) {
		this.category = category;
	}
    
    
}
