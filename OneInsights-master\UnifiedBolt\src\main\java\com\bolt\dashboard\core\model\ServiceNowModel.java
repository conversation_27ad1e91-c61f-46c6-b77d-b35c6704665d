package com.bolt.dashboard.core.model;



import org.springframework.data.mongodb.core.mapping.Document;

 @Document(collection = "ServiceNowIncident")
public class ServiceNowModel extends BaseModel {

    private Long timeStamp;
    private String cRuns;

	 private Long slaLastTimeStamp;
	 private String slaLastRuns;
	 
	 private String inflowTrendLastLastRuns;

	 

	public String getInflowTrendLastLastRuns() {
		return inflowTrendLastLastRuns;
	}

	public void setInflowTrendLastLastRuns(String inflowTrendLastLastRuns) {
		this.inflowTrendLastLastRuns = inflowTrendLastLastRuns;
	}

	public Long getSlaLastTimeStamp() {
		 return slaLastTimeStamp;
	 }

	 public void setSlaLastTimeStamp(Long slaLastTimeStamp) {
		 this.slaLastTimeStamp = slaLastTimeStamp;
	 }

	 public String getSlaLastRuns() {
		 return slaLastRuns;
	 }

	 public void setSlaLastRuns(String slaLastRuns) {
		 this.slaLastRuns = slaLastRuns;
	 }

	 public Long getTimeStamp() {
		return timeStamp;
	}
	public void setTimeStamp(Long timeStamp) {
		this.timeStamp = timeStamp;
	}
	public String getcRuns() {
		return cRuns;
	}
	public void setcRuns(String cRuns) {
		this.cRuns = cRuns;
	}
    
}
