package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "InflowTrend")
public class ServiceNowSCReqItem extends BaseModel{

	private String parent, made_sla, watch_list, sc_catalog, sn_esign_document, upon_reject, requested_for,
			sys_updated_on, task_effective_number, approval_history, skills, number, sys_updated_by, user_input, price,
			sys_created_on, recurring_frequency, state, task_for, route_reason, sys_created_by, knowledge, order,
			closed_at, cmdb_ci, backordered, contract, impact, active, work_notes_list, business_service, priority,
			sys_domain_path, time_worked, expected_start, opened_at, business_duration, group_list, configuration_item,
			work_end, approval_set, work_notes, order_guide, universal_request, short_description, correlation_display,
			work_start, assignment_group, additional_assignee_list, description, calendar_duration, close_notes,
			service_offering, sys_class_name, closed_by, follow_up, sys_id, contact_type,
			sn_esign_esignature_configuration, urgency, company, reassignment_count, activity_due, assigned_to,
			comments, quantity, approval, sla_due, comments_and_work_notes, due_date, sys_mod_count, recurring_price,
			sys_tags, billable, stage, escalation, upon_approval, correlation_id, location, estimated_delivery,
			opened_by, sys_domain, request, cat_item;

	public ServiceNowSCReqItem(String parent, String made_sla, String watch_list, String sc_catalog,
			String sn_esign_document, String upon_reject, String requested_for, String sys_updated_on,
			String task_effective_number, String approval_history, String skills, String number, String sys_updated_by,
			String user_input, String price, String sys_created_on, String recurring_frequency, String state,
			String task_for, String route_reason, String sys_created_by, String knowledge, String order,
			String closed_at, String cmdb_ci, String backordered, String contract, String impact, String active,
			String work_notes_list, String business_service, String priority, String sys_domain_path,
			String time_worked, String expected_start, String opened_at, String business_duration, String group_list,
			String configuration_item, String work_end, String approval_set, String work_notes, String order_guide,
			String universal_request, String short_description, String correlation_display, String work_start,
			String assignment_group, String additional_assignee_list, String description, String calendar_duration,
			String close_notes, String service_offering, String sys_class_name, String closed_by, String follow_up,
			String sys_id, String contact_type, String sn_esign_esignature_configuration, String urgency,
			String company, String reassignment_count, String activity_due, String assigned_to, String comments,
			String quantity, String approval, String sla_due, String comments_and_work_notes, String due_date,
			String sys_mod_count, String recurring_price, String sys_tags, String billable, String stage,
			String escalation, String upon_approval, String correlation_id, String location, String estimated_delivery,
			String opened_by, String sys_domain, String request, String cat_item) {
		super();
		this.parent = parent;
		this.made_sla = made_sla;
		this.watch_list = watch_list;
		this.sc_catalog = sc_catalog;
		this.sn_esign_document = sn_esign_document;
		this.upon_reject = upon_reject;
		this.requested_for = requested_for;
		this.sys_updated_on = sys_updated_on;
		this.task_effective_number = task_effective_number;
		this.approval_history = approval_history;
		this.skills = skills;
		this.number = number;
		this.sys_updated_by = sys_updated_by;
		this.user_input = user_input;
		this.price = price;
		this.sys_created_on = sys_created_on;
		this.recurring_frequency = recurring_frequency;
		this.state = state;
		this.task_for = task_for;
		this.route_reason = route_reason;
		this.sys_created_by = sys_created_by;
		this.knowledge = knowledge;
		this.order = order;
		this.closed_at = closed_at;
		this.cmdb_ci = cmdb_ci;
		this.backordered = backordered;
		this.contract = contract;
		this.impact = impact;
		this.active = active;
		this.work_notes_list = work_notes_list;
		this.business_service = business_service;
		this.priority = priority;
		this.sys_domain_path = sys_domain_path;
		this.time_worked = time_worked;
		this.expected_start = expected_start;
		this.opened_at = opened_at;
		this.business_duration = business_duration;
		this.group_list = group_list;
		this.configuration_item = configuration_item;
		this.work_end = work_end;
		this.approval_set = approval_set;
		this.work_notes = work_notes;
		this.order_guide = order_guide;
		this.universal_request = universal_request;
		this.short_description = short_description;
		this.correlation_display = correlation_display;
		this.work_start = work_start;
		this.assignment_group = assignment_group;
		this.additional_assignee_list = additional_assignee_list;
		this.description = description;
		this.calendar_duration = calendar_duration;
		this.close_notes = close_notes;
		this.service_offering = service_offering;
		this.sys_class_name = sys_class_name;
		this.closed_by = closed_by;
		this.follow_up = follow_up;
		this.sys_id = sys_id;
		this.contact_type = contact_type;
		this.sn_esign_esignature_configuration = sn_esign_esignature_configuration;
		this.urgency = urgency;
		this.company = company;
		this.reassignment_count = reassignment_count;
		this.activity_due = activity_due;
		this.assigned_to = assigned_to;
		this.comments = comments;
		this.quantity = quantity;
		this.approval = approval;
		this.sla_due = sla_due;
		this.comments_and_work_notes = comments_and_work_notes;
		this.due_date = due_date;
		this.sys_mod_count = sys_mod_count;
		this.recurring_price = recurring_price;
		this.sys_tags = sys_tags;
		this.billable = billable;
		this.stage = stage;
		this.escalation = escalation;
		this.upon_approval = upon_approval;
		this.correlation_id = correlation_id;
		this.location = location;
		this.estimated_delivery = estimated_delivery;
		this.opened_by = opened_by;
		this.sys_domain = sys_domain;
		this.request = request;
		this.cat_item = cat_item;
	}

	public String getParent() {
		return parent;
	}

	public void setParent(String parent) {
		this.parent = parent;
	}

	public String getMade_sla() {
		return made_sla;
	}

	public void setMade_sla(String made_sla) {
		this.made_sla = made_sla;
	}

	public String getWatch_list() {
		return watch_list;
	}

	public void setWatch_list(String watch_list) {
		this.watch_list = watch_list;
	}

	public String getSc_catalog() {
		return sc_catalog;
	}

	public void setSc_catalog(String sc_catalog) {
		this.sc_catalog = sc_catalog;
	}

	public String getSn_esign_document() {
		return sn_esign_document;
	}

	public void setSn_esign_document(String sn_esign_document) {
		this.sn_esign_document = sn_esign_document;
	}

	public String getUpon_reject() {
		return upon_reject;
	}

	public void setUpon_reject(String upon_reject) {
		this.upon_reject = upon_reject;
	}

	public String getRequested_for() {
		return requested_for;
	}

	public void setRequested_for(String requested_for) {
		this.requested_for = requested_for;
	}

	public String getSys_updated_on() {
		return sys_updated_on;
	}

	public void setSys_updated_on(String sys_updated_on) {
		this.sys_updated_on = sys_updated_on;
	}

	public String getTask_effective_number() {
		return task_effective_number;
	}

	public void setTask_effective_number(String task_effective_number) {
		this.task_effective_number = task_effective_number;
	}

	public String getApproval_history() {
		return approval_history;
	}

	public void setApproval_history(String approval_history) {
		this.approval_history = approval_history;
	}

	public String getSkills() {
		return skills;
	}

	public void setSkills(String skills) {
		this.skills = skills;
	}

	public String getNumber() {
		return number;
	}

	public void setNumber(String number) {
		this.number = number;
	}

	public String getSys_updated_by() {
		return sys_updated_by;
	}

	public void setSys_updated_by(String sys_updated_by) {
		this.sys_updated_by = sys_updated_by;
	}

	public String getUser_input() {
		return user_input;
	}

	public void setUser_input(String user_input) {
		this.user_input = user_input;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getSys_created_on() {
		return sys_created_on;
	}

	public void setSys_created_on(String sys_created_on) {
		this.sys_created_on = sys_created_on;
	}

	public String getRecurring_frequency() {
		return recurring_frequency;
	}

	public void setRecurring_frequency(String recurring_frequency) {
		this.recurring_frequency = recurring_frequency;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getTask_for() {
		return task_for;
	}

	public void setTask_for(String task_for) {
		this.task_for = task_for;
	}

	public String getRoute_reason() {
		return route_reason;
	}

	public void setRoute_reason(String route_reason) {
		this.route_reason = route_reason;
	}

	public String getSys_created_by() {
		return sys_created_by;
	}

	public void setSys_created_by(String sys_created_by) {
		this.sys_created_by = sys_created_by;
	}

	public String getKnowledge() {
		return knowledge;
	}

	public void setKnowledge(String knowledge) {
		this.knowledge = knowledge;
	}

	public String getOrder() {
		return order;
	}

	public void setOrder(String order) {
		this.order = order;
	}

	public String getClosed_at() {
		return closed_at;
	}

	public void setClosed_at(String closed_at) {
		this.closed_at = closed_at;
	}

	public String getCmdb_ci() {
		return cmdb_ci;
	}

	public void setCmdb_ci(String cmdb_ci) {
		this.cmdb_ci = cmdb_ci;
	}

	public String getBackordered() {
		return backordered;
	}

	public void setBackordered(String backordered) {
		this.backordered = backordered;
	}

	public String getContract() {
		return contract;
	}

	public void setContract(String contract) {
		this.contract = contract;
	}

	public String getImpact() {
		return impact;
	}

	public void setImpact(String impact) {
		this.impact = impact;
	}

	public String getActive() {
		return active;
	}

	public void setActive(String active) {
		this.active = active;
	}

	public String getWork_notes_list() {
		return work_notes_list;
	}

	public void setWork_notes_list(String work_notes_list) {
		this.work_notes_list = work_notes_list;
	}

	public String getBusiness_service() {
		return business_service;
	}

	public void setBusiness_service(String business_service) {
		this.business_service = business_service;
	}

	public String getPriority() {
		return priority;
	}

	public void setPriority(String priority) {
		this.priority = priority;
	}

	public String getSys_domain_path() {
		return sys_domain_path;
	}

	public void setSys_domain_path(String sys_domain_path) {
		this.sys_domain_path = sys_domain_path;
	}

	public String getTime_worked() {
		return time_worked;
	}

	public void setTime_worked(String time_worked) {
		this.time_worked = time_worked;
	}

	public String getExpected_start() {
		return expected_start;
	}

	public void setExpected_start(String expected_start) {
		this.expected_start = expected_start;
	}

	public String getOpened_at() {
		return opened_at;
	}

	public void setOpened_at(String opened_at) {
		this.opened_at = opened_at;
	}

	public String getBusiness_duration() {
		return business_duration;
	}

	public void setBusiness_duration(String business_duration) {
		this.business_duration = business_duration;
	}

	public String getGroup_list() {
		return group_list;
	}

	public void setGroup_list(String group_list) {
		this.group_list = group_list;
	}

	public String getConfiguration_item() {
		return configuration_item;
	}

	public void setConfiguration_item(String configuration_item) {
		this.configuration_item = configuration_item;
	}

	public String getWork_end() {
		return work_end;
	}

	public void setWork_end(String work_end) {
		this.work_end = work_end;
	}

	public String getApproval_set() {
		return approval_set;
	}

	public void setApproval_set(String approval_set) {
		this.approval_set = approval_set;
	}

	public String getWork_notes() {
		return work_notes;
	}

	public void setWork_notes(String work_notes) {
		this.work_notes = work_notes;
	}

	public String getOrder_guide() {
		return order_guide;
	}

	public void setOrder_guide(String order_guide) {
		this.order_guide = order_guide;
	}

	public String getUniversal_request() {
		return universal_request;
	}

	public void setUniversal_request(String universal_request) {
		this.universal_request = universal_request;
	}

	public String getShort_description() {
		return short_description;
	}

	public void setShort_description(String short_description) {
		this.short_description = short_description;
	}

	public String getCorrelation_display() {
		return correlation_display;
	}

	public void setCorrelation_display(String correlation_display) {
		this.correlation_display = correlation_display;
	}

	public String getWork_start() {
		return work_start;
	}

	public void setWork_start(String work_start) {
		this.work_start = work_start;
	}

	public String getAssignment_group() {
		return assignment_group;
	}

	public void setAssignment_group(String assignment_group) {
		this.assignment_group = assignment_group;
	}

	public String getAdditional_assignee_list() {
		return additional_assignee_list;
	}

	public void setAdditional_assignee_list(String additional_assignee_list) {
		this.additional_assignee_list = additional_assignee_list;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getCalendar_duration() {
		return calendar_duration;
	}

	public void setCalendar_duration(String calendar_duration) {
		this.calendar_duration = calendar_duration;
	}

	public String getClose_notes() {
		return close_notes;
	}

	public void setClose_notes(String close_notes) {
		this.close_notes = close_notes;
	}

	public String getService_offering() {
		return service_offering;
	}

	public void setService_offering(String service_offering) {
		this.service_offering = service_offering;
	}

	public String getSys_class_name() {
		return sys_class_name;
	}

	public void setSys_class_name(String sys_class_name) {
		this.sys_class_name = sys_class_name;
	}

	public String getClosed_by() {
		return closed_by;
	}

	public void setClosed_by(String closed_by) {
		this.closed_by = closed_by;
	}

	public String getFollow_up() {
		return follow_up;
	}

	public void setFollow_up(String follow_up) {
		this.follow_up = follow_up;
	}

	public String getSys_id() {
		return sys_id;
	}

	public void setSys_id(String sys_id) {
		this.sys_id = sys_id;
	}

	public String getContact_type() {
		return contact_type;
	}

	public void setContact_type(String contact_type) {
		this.contact_type = contact_type;
	}

	public String getSn_esign_esignature_configuration() {
		return sn_esign_esignature_configuration;
	}

	public void setSn_esign_esignature_configuration(String sn_esign_esignature_configuration) {
		this.sn_esign_esignature_configuration = sn_esign_esignature_configuration;
	}

	public String getUrgency() {
		return urgency;
	}

	public void setUrgency(String urgency) {
		this.urgency = urgency;
	}

	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getReassignment_count() {
		return reassignment_count;
	}

	public void setReassignment_count(String reassignment_count) {
		this.reassignment_count = reassignment_count;
	}

	public String getActivity_due() {
		return activity_due;
	}

	public void setActivity_due(String activity_due) {
		this.activity_due = activity_due;
	}

	public String getAssigned_to() {
		return assigned_to;
	}

	public void setAssigned_to(String assigned_to) {
		this.assigned_to = assigned_to;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getQuantity() {
		return quantity;
	}

	public void setQuantity(String quantity) {
		this.quantity = quantity;
	}

	public String getApproval() {
		return approval;
	}

	public void setApproval(String approval) {
		this.approval = approval;
	}

	public String getSla_due() {
		return sla_due;
	}

	public void setSla_due(String sla_due) {
		this.sla_due = sla_due;
	}

	public String getComments_and_work_notes() {
		return comments_and_work_notes;
	}

	public void setComments_and_work_notes(String comments_and_work_notes) {
		this.comments_and_work_notes = comments_and_work_notes;
	}

	public String getDue_date() {
		return due_date;
	}

	public void setDue_date(String due_date) {
		this.due_date = due_date;
	}

	public String getSys_mod_count() {
		return sys_mod_count;
	}

	public void setSys_mod_count(String sys_mod_count) {
		this.sys_mod_count = sys_mod_count;
	}

	public String getRecurring_price() {
		return recurring_price;
	}

	public void setRecurring_price(String recurring_price) {
		this.recurring_price = recurring_price;
	}

	public String getSys_tags() {
		return sys_tags;
	}

	public void setSys_tags(String sys_tags) {
		this.sys_tags = sys_tags;
	}

	public String getBillable() {
		return billable;
	}

	public void setBillable(String billable) {
		this.billable = billable;
	}

	public String getStage() {
		return stage;
	}

	public void setStage(String stage) {
		this.stage = stage;
	}

	public String getEscalation() {
		return escalation;
	}

	public void setEscalation(String escalation) {
		this.escalation = escalation;
	}

	public String getUpon_approval() {
		return upon_approval;
	}

	public void setUpon_approval(String upon_approval) {
		this.upon_approval = upon_approval;
	}

	public String getCorrelation_id() {
		return correlation_id;
	}

	public void setCorrelation_id(String correlation_id) {
		this.correlation_id = correlation_id;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public String getEstimated_delivery() {
		return estimated_delivery;
	}

	public void setEstimated_delivery(String estimated_delivery) {
		this.estimated_delivery = estimated_delivery;
	}

	public String getOpened_by() {
		return opened_by;
	}

	public void setOpened_by(String opened_by) {
		this.opened_by = opened_by;
	}

	public String getSys_domain() {
		return sys_domain;
	}

	public void setSys_domain(String sys_domain) {
		this.sys_domain = sys_domain;
	}

	public String getRequest() {
		return request;
	}

	public void setRequest(String request) {
		this.request = request;
	}

	public String getCat_item() {
		return cat_item;
	}

	public void setCat_item(String cat_item) {
		this.cat_item = cat_item;
	}

}
