package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "SLA")
public class ServiceNowSLA extends BaseModel{
    private String pause_duration;
    private String pause_time;
    private String timezone;
    private String sys_updated_on;
    private String business_time_left;
    private String duration;
    private String sys_id;
    private String time_left, sys_updated_by, sys_created_on, percentage, original_breach_time,
            sys_created_by, business_percentage, end_time, sys_mod_count, active, business_pause_duration,
            start_time, business_duration, stage, planned_end_time, has_breached, number, priority;

    public ServiceNowSLA(String pause_duration, String pause_time, String timezone, String sys_updated_on, String business_time_left, String duration, String sys_id, String time_left, String sys_updated_by, String sys_created_on, String percentage, String original_breach_time, String sys_created_by, String business_percentage, String end_time, String sys_mod_count, String active, String business_pause_duration, String start_time, String business_duration, String stage, String planned_end_time, String has_breached, String number, String priority) {
        this.pause_duration = pause_duration;
        this.pause_time = pause_time;
        this.timezone = timezone;
        this.sys_updated_on = sys_updated_on;
        this.business_time_left = business_time_left;
        this.duration = duration;
        this.sys_id = sys_id;
        this.time_left = time_left;
        this.sys_updated_by = sys_updated_by;
        this.sys_created_on = sys_created_on;
        this.percentage = percentage;
        this.original_breach_time = original_breach_time;
        this.sys_created_by = sys_created_by;
        this.business_percentage = business_percentage;
        this.end_time = end_time;
        this.sys_mod_count = sys_mod_count;
        this.active = active;
        this.business_pause_duration = business_pause_duration;
        this.start_time = start_time;
        this.business_duration = business_duration;
        this.stage = stage;
        this.planned_end_time = planned_end_time;
        this.has_breached = has_breached;
        this.number = number;
        this.priority = priority;
    }

    public String getPause_duration() {
        return pause_duration;
    }

    public void setPause_duration(String pause_duration) {
        this.pause_duration = pause_duration;
    }

    public String getPause_time() {
        return pause_time;
    }

    public void setPause_time(String pause_time) {
        this.pause_time = pause_time;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public String getSys_updated_on() {
        return sys_updated_on;
    }

    public void setSys_updated_on(String sys_updated_on) {
        this.sys_updated_on = sys_updated_on;
    }

    public String getBusiness_time_left() {
        return business_time_left;
    }

    public void setBusiness_time_left(String business_time_left) {
        this.business_time_left = business_time_left;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public String getSys_id() {
        return sys_id;
    }

    public void setSys_id(String sys_id) {
        this.sys_id = sys_id;
    }

    public String getTime_left() {
        return time_left;
    }

    public void setTime_left(String time_left) {
        this.time_left = time_left;
    }

    public String getSys_updated_by() {
        return sys_updated_by;
    }

    public void setSys_updated_by(String sys_updated_by) {
        this.sys_updated_by = sys_updated_by;
    }

    public String getSys_created_on() {
        return sys_created_on;
    }

    public void setSys_created_on(String sys_created_on) {
        this.sys_created_on = sys_created_on;
    }

    public String getPercentage() {
        return percentage;
    }

    public void setPercentage(String percentage) {
        this.percentage = percentage;
    }

    public String getOriginal_breach_time() {
        return original_breach_time;
    }

    public void setOriginal_breach_time(String original_breach_time) {
        this.original_breach_time = original_breach_time;
    }

    public String getSys_created_by() {
        return sys_created_by;
    }

    public void setSys_created_by(String sys_created_by) {
        this.sys_created_by = sys_created_by;
    }

    public String getBusiness_percentage() {
        return business_percentage;
    }

    public void setBusiness_percentage(String business_percentage) {
        this.business_percentage = business_percentage;
    }

    public String getEnd_time() {
        return end_time;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public String getSys_mod_count() {
        return sys_mod_count;
    }

    public void setSys_mod_count(String sys_mod_count) {
        this.sys_mod_count = sys_mod_count;
    }

    public String getActive() {
        return active;
    }

    public void setActive(String active) {
        this.active = active;
    }

    public String getBusiness_pause_duration() {
        return business_pause_duration;
    }

    public void setBusiness_pause_duration(String business_pause_duration) {
        this.business_pause_duration = business_pause_duration;
    }

    public String getStart_time() {
        return start_time;
    }

    public void setStart_time(String start_time) {
        this.start_time = start_time;
    }

    public String getBusiness_duration() {
        return business_duration;
    }

    public void setBusiness_duration(String business_duration) {
        this.business_duration = business_duration;
    }

    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }

    public String getPlanned_end_time() {
        return planned_end_time;
    }

    public void setPlanned_end_time(String planned_end_time) {
        this.planned_end_time = planned_end_time;
    }

    public String getHas_breached() {
        return has_breached;
    }

    public void setHas_breached(String has_breached) {
        this.has_breached = has_breached;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }
}

