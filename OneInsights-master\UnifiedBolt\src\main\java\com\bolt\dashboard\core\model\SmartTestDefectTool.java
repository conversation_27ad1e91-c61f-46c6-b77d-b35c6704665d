package com.bolt.dashboard.core.model;

import java.util.List;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "DefectManagement")
public class SmartTestDefectTool {

    private List<DefectTool> toolList;
    private String projectName;
    private String defectType;

    public List<DefectTool> getToolList() {
        return toolList;
    }

    public void setToolList(List<DefectTool> toolList) {
        this.toolList = toolList;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getDefectType() {
        return defectType;
    }

    public void setDefectType(String defectType) {
        this.defectType = defectType;
    }

}
