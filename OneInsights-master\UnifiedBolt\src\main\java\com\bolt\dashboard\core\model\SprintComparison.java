package com.bolt.dashboard.core.model;

import java.util.List;
import java.util.Set;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "sprintComparison")
public class SprintComparison extends BaseModel {
    private String projectName;
    private long timeStamp;
    private Set<SprintComparisonMetrics> metrics;

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public Set<SprintComparisonMetrics> getMetrics() {
        return metrics;
    }

    public void setMetrics(Set<SprintComparisonMetrics> metrics) {
        this.metrics = metrics;
    }

}
