package com.bolt.dashboard.core.model;

public class SprintComparisonMetrics {
    private String iterationName;
    private long itaretionStartDate;
    private long iterationEndDate;
    private String technicalDebt;
    private int velocity;
    private double teamCapacity;
    private double efficiency;
    private int defects;
    private int filesAdded;
    private int filesModified;
    private int filesDeleted;
    private int buildFailure;
    private double successRate;

    public String getIterationName() {
        return iterationName;
    }

    public void setIterationName(String iterationName) {
        this.iterationName = iterationName;
    }

    public long getItaretionStartDate() {
        return itaretionStartDate;
    }

    public void setItaretionStartDate(long itaretionStartDate) {
        this.itaretionStartDate = itaretionStartDate;
    }

    public long getIterationEndDate() {
        return iterationEndDate;
    }

    public void setIterationEndDate(long iterationEndDate) {
        this.iterationEndDate = iterationEndDate;
    }

    public String getTechnicalDebt() {
        return technicalDebt;
    }

    public void setTechnicalDebt(String technicalDebt) {
        this.technicalDebt = technicalDebt;
    }

    public int getVelocity() {
        return velocity;
    }

    public void setVelocity(int velocity) {
        this.velocity = velocity;
    }

    public double getTeamCapacity() {
        return teamCapacity;
    }

    public void setTeamCapacity(Double teamCapacity) {
        this.teamCapacity = teamCapacity;
    }

    public double getEfficiency() {
        return efficiency;
    }

    public void setEfficiency(double efficiency) {
        this.efficiency = efficiency;
    }

    public int getDefects() {
        return defects;
    }

    public void setDefects(int defects) {
        this.defects = defects;
    }

    public int getFilesAdded() {
        return filesAdded;
    }

    public void setFilesAdded(int filesAdded) {
        this.filesAdded = filesAdded;
    }

    public int getFilesModified() {
        return filesModified;
    }

    public void setFilesModified(int filesModified) {
        this.filesModified = filesModified;
    }

    public int getFilesDeleted() {
        return filesDeleted;
    }

    public void setFilesDeleted(int filesDeleted) {
        this.filesDeleted = filesDeleted;
    }

    public int getBuildFailure() {
        return buildFailure;
    }

    public void setBuildFailure(int buildFailure) {
        this.buildFailure = buildFailure;
    }

	public double getSuccessRate() {
		return successRate;
	}

	public void setSuccessRate(double successRate) {
		this.successRate = successRate;
	}

}
