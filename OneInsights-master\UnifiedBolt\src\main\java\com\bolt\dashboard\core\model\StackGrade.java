package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "gradeConfiguration")
public class StackGrade extends BaseModel {

    private String role;
    private String projectName;
    private List<StackRuleName> rulemetrics = new ArrayList<>();

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public List<StackRuleName> getRulemetrics() {
        return rulemetrics;
    }

    public void setRulemetrics(List<StackRuleName> rulemetrics) {
        this.rulemetrics = rulemetrics;
    }

    public void addRuleMetric(String ruleName, List<StackGradeMetrics> stackGradeRanking) {
        StackRuleName ruleMetrics = new StackRuleName();
        ruleMetrics.setRuleName(ruleName);
        ruleMetrics.setGrademetrics(stackGradeRanking);

        this.rulemetrics.add(ruleMetrics);
    }

}
