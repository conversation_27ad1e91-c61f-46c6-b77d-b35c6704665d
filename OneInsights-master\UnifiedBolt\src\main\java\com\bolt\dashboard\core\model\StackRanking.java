package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "rankingConfiguration")
public class StackRanking {

    @Id
    private String id;
    private long timestamp;
    private String projectName;
    private List<StackRankingMetrics> metrics = new ArrayList<>();

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public List<StackRankingMetrics> getStackRankingMetrics() {
        return metrics;
    }

    public void setStackRankingMetrics(List<StackRankingMetrics> metrics) {
        this.metrics = metrics;
    }

}
