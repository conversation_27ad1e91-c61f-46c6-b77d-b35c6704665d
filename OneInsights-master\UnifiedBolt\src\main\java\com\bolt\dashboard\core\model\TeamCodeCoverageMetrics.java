package com.bolt.dashboard.core.model;

import java.util.Map;

public class TeamCodeCoverageMetrics {

	private double teamScores;
	private double teamCodeCoveragePercentage;
	Map<String,Double> repos;
	public double getTeamScores() {
		return teamScores;
	}
	public void setTeamScores(double teamScores) {
		this.teamScores = teamScores;
	}
	public double getTeamCodeCoveragePercentage() {
		return teamCodeCoveragePercentage;
	}
	public void setTeamCodeCoveragePercentage(double teamCodeCoveragePercentage) {
		this.teamCodeCoveragePercentage = teamCodeCoveragePercentage;
	}
	public Map<String, Double> getRepos() {
		return repos;
	}
	public void setRepos(Map<String, Double> repos) {
		this.repos = repos;
	}
}
