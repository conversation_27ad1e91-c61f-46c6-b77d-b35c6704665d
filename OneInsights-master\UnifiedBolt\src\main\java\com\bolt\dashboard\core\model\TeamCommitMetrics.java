package com.bolt.dashboard.core.model;

import java.util.Map;

public class TeamCommitMetrics {
	private double teamScore;
	private Map<String,Integer> commits;
	private double teamCommitFrequency;
	public double getTeamScore() {
		return teamScore;
	}
	public void setTeamScore(double teamScore) {
		this.teamScore = teamScore;
	}
	public Map<String, Integer> getCommits() {
		return commits;
	}
	public void setCommits(Map<String, Integer> commits) {
		this.commits = commits;
	}
	public double getTeamCommitFrequency() {
		return teamCommitFrequency;
	}
	public void setTeamCommitFrequency(double teamCommitFrequency) {
		this.teamCommitFrequency = teamCommitFrequency;
	}
	
	
}
