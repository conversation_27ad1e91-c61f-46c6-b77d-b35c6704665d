package com.bolt.dashboard.core.model;

public class TeamDefectsClosedMetrics {
	private double teamScores;
	private int critical;
	private int high;
	private int medium;
	private int low;
	
	public double getTeamScores() {
		return teamScores;
	}
	public void setTeamScores(double teamScores) {
		this.teamScores = teamScores;
	}
	public int getCritical() {
		return critical;
	}
	public void setCritical(int critical) {
		this.critical = critical;
	}
	public int getHigh() {
		return high;
	}
	public void setHigh(int high) {
		this.high = high;
	}
	public int getMedium() {
		return medium;
	}
	public void setMedium(int medium) {
		this.medium = medium;
	}
	public int getLow() {
		return low;
	}
	public void setLow(int low) {
		this.low = low;
	}
	
}
