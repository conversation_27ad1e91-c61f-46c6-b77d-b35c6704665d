package com.bolt.dashboard.core.model;

import java.util.Map;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "TeamIndexConfig")
public class TeamIndexConfiguration extends BaseModel {
	private String pName;
	private long timestamp;
	Map<String,TeamIndexQuestion> teamIndexQuestions;
	public String getpName() {
		return pName;
	}
	public void setpName(String pName) {
		this.pName = pName;
	}
	public Map<String, TeamIndexQuestion> getTeamIndexQuestions() {
		return teamIndexQuestions;
	}
	public void setTeamIndexQuestions(Map<String, TeamIndexQuestion> teamIndexQuestions) {
		this.teamIndexQuestions = teamIndexQuestions;
	}
	public long getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}
	

}
