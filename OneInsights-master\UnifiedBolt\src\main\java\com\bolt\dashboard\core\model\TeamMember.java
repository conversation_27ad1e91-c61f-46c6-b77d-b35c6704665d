package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;


@Document(collection = "TeamMember")
public class TeamMember extends BaseModel {
	private String name;
	private String role;
	private String email;
	private String almId;
	private String repoId;
	private String testId;
	private String buildId;
	private String projectName;
	private String component;
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getRole() {
		return role;
	}
	public void setRole(String role) {
		this.role = role;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getAlmId() {
		return almId;
	}
	public void setAlmId(String almId) {
		this.almId = almId;
	}
	public String getRepoId() {
		return repoId;
	}
	public void setRepoId(String repoId) {
		this.repoId = repoId;
	}
	public String getBuildId() {
		return buildId;
	}
	public void setBuildId(String buildId) {
		this.buildId = buildId;
	}
	public String getTestId() {
		return testId;
	}
	public String getComponent() {
		return component;
	}
	public void setComponent(String component) {
		this.component = component;
	}
	public void setTestId(String testId) {
		this.testId = testId;
	}
	public String getProjectName() {
		return projectName;
	}
	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	

}
