package com.bolt.dashboard.core.model;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "TeamQuality")
public class TeamQuality extends BaseModel {
	
	private Set<TeamCodeCoverageMetrics> coverageMetrics = new HashSet<>();
	private Set<TeamCommitMetrics> commitMetrics = new HashSet<>();
	private Set<TeamDefectsClosedMetrics> defectsClosedMetrics = new HashSet();
	
	private String projectName;
	private String sprintName;
	private long sprintStart;
	private long sprintEnd;
	private long timeStamp;
	private String componentName;
	private String environment;
	public Set<TeamCodeCoverageMetrics> getCoverageMetrics() {
		return coverageMetrics;
	}
	public void setCoverageMetrics(Set<TeamCodeCoverageMetrics> coverageMetrics) {
		this.coverageMetrics = coverageMetrics;
	}
	public String getProjectName() {
		return projectName;
	}
	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}
	public String getSprintName() {
		return sprintName;
	}
	public void setSprintName(String sprintName) {
		this.sprintName = sprintName;
	}
	public long getTimeStamp() {
		return timeStamp;
	}
	public void setTimeStamp(long timeStamp) {
		this.timeStamp = timeStamp;
	}
	public String getComponentName() {
		return componentName;
	}
	public void setComponentName(String componentName) {
		this.componentName = componentName;
	}
	public long getSprintStart() {
		return sprintStart;
	}
	public void setSprintStart(long sprintStart) {
		this.sprintStart = sprintStart;
	}
	public long getSprintEnd() {
		return sprintEnd;
	}
	public void setSprintEnd(long sprintEnd) {
		this.sprintEnd = sprintEnd;
	}
	public Set<TeamDefectsClosedMetrics> getDefectsClosedMetrics() {
		return defectsClosedMetrics;
	}
	public void setDefectsClosedMetrics(Set<TeamDefectsClosedMetrics> defectsClosedMetrics) {
		this.defectsClosedMetrics = defectsClosedMetrics;
	}
	public Set<TeamCommitMetrics> getCommitMetrics() {
		return commitMetrics;
	}
	public void setCommitMetrics(Set<TeamCommitMetrics> commitMetrics) {
		this.commitMetrics = commitMetrics;
	}
	public String getEnvironment() {
		return environment;
	}
	public void setEnvironment(String environment) {
		this.environment = environment;
	}
	
	
}