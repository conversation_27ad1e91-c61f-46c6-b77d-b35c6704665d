package com.bolt.dashboard.core.model;

public class TestCaseModel {
    private String testCaseId;
    private String owner;
    private String actualTester;
    private String execStatus;
    private long execDate;
    private String testCaseName;
    private long plannedExecDate;
    private String testCaseType;

    public String getTestCaseId() {
        return testCaseId;
    }

    public void setTestCaseId(String testCaseId) {
        this.testCaseId = testCaseId;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getActualTester() {
        return actualTester;
    }

    public void setActualTester(String actualTester) {
        this.actualTester = actualTester;
    }

    public String getExecStatus() {
        return execStatus;
    }

    public void setExecStatus(String execStatus) {
        this.execStatus = execStatus;
    }

    public long getExecDate() {
        return execDate;
    }

    public void setExecDate(long execDate) {
        this.execDate = execDate;
    }

    public String getTestCaseName() {
        return testCaseName;
    }

    public void setTestCaseName(String testCaseName) {
        this.testCaseName = testCaseName;
    }

    public long getPlannedExecDate() {
        return plannedExecDate;
    }

    public void setPlannedExecDate(long plannedExecDate) {
        this.plannedExecDate = plannedExecDate;
    }

    public String getTestCaseType() {
        return testCaseType;
    }

    public void setTestCaseType(String testCaseType) {
        this.testCaseType = testCaseType;
    }

}
