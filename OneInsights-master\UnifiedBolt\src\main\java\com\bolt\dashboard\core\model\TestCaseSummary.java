package com.bolt.dashboard.core.model;

public class TestCaseSummary {

    private String totalTimeTaken;
    private String start;
    private String passPercentage;
    private int totalTests;
    private String end;
    private String testCasesPassed;
    private int testCasesFailed;

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getPassPercentage() {
        return passPercentage;
    }

    public void setPassPercentage(String passPercentage) {
        this.passPercentage = passPercentage;
    }

    public int getTotalTests() {
        return totalTests;
    }

    public void setTotalTests(int totalTests) {
        this.totalTests = totalTests;
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }

    public String getTestCasesPassed() {
        return testCasesPassed;
    }

    public void setTestCasesPassed(String testCasesPassed) {
        this.testCasesPassed = testCasesPassed;
    }

    public int getTestCasesFailed() {
        return testCasesFailed;
    }

    public void setTestCasesFailed(int testCasesFailed) {
        this.testCasesFailed = testCasesFailed;
    }

    public String getTotalTimeTaken() {
        return totalTimeTaken;
    }

    public void setTotalTimeTaken(String totalTimeTaken) {
        this.totalTimeTaken = totalTimeTaken;
    }
}
