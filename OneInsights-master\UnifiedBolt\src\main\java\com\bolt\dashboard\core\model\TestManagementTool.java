package com.bolt.dashboard.core.model;

import java.util.List;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "TestManagement")
public class TestManagementTool {
    private String projectName;
    private String testType;
    private long timeStamp;
    private List<TestSetModel> testSetList;

    public List<TestSetModel> getTestSetList() {
        return testSetList;
    }

    public void setTestSetList(List<TestSetModel> testSetList) {
        this.testSetList = testSetList;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getTestType() {
        return testType;
    }

    public void setTestType(String testType) {
        this.testType = testType;
    }

    public long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(long timeStamp) {
        this.timeStamp = timeStamp;
    }

}
