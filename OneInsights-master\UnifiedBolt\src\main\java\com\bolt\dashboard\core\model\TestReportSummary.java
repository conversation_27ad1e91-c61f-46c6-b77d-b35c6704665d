package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "Tesst Report")
public class TestReportSummary extends BaseModel {
    private String name;
    private TestConfiguration testConfiguration;
    private TestCaseSummary testCaseSummary;
    private TestStepsSummary testStepsSummary;

    public TestCaseSummary getTestCaseSummary() {
        return testCaseSummary;
    }

    public void setTestCaseSummary(TestCaseSummary testCaseSummary) {
        this.testCaseSummary = testCaseSummary;
    }

    public TestConfiguration getTestConfiguration() {
        return testConfiguration;
    }

    public void setTestConfiguration(TestConfiguration testConfiguration) {
        this.testConfiguration = testConfiguration;
    }

    public TestStepsSummary getTestStepsSummary() {
        return testStepsSummary;
    }

    public void setTestStepsSummary(TestStepsSummary testStepsSummary) {
        this.testStepsSummary = testStepsSummary;
    }

    public String getProjectName() {
        return name;
    }

    public void setProjectName(String projectName) {
        this.name = projectName;
    }
}
