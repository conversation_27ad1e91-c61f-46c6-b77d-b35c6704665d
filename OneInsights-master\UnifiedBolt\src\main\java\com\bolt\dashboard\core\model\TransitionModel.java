package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "Transition")
public class TransitionModel extends BaseModel {
    private String wId;
    private String crState;
    private String frmState;
    private Long mdfDate;
    private Long waitTime;
    private long preStateWaitTime;
    private long CreateTime;
    private long effort;
    private long leadTime;
    private String pName;
    private String projKey;
    
   
    private String sName;

    public String getwId() {
	return wId;
    }

    public void setwId(String wId) {
	this.wId = wId;
    }

    public long getPreStateWaitTime() {
	return preStateWaitTime;
    }

    public void setPreStateWaitTime(long preStateWaitTime) {
	this.preStateWaitTime = preStateWaitTime;
    }

    public long getCreateTime() {
	return CreateTime;
    }

    public void setCreateTime(long createTime) {
	CreateTime = createTime;
    }

    public long getEffort() {
	return effort;
    }

    public void setEffort(long effort) {
	this.effort = effort;
    }

    public long getLeadTime() {
	return leadTime;
    }

    public void setLeadTime(long leadTime) {
	this.leadTime = leadTime;
    }

    public String getCrState() {
	return crState;
    }

    public void setCrState(String crState) {
	this.crState = crState;
    }

    public String getFrmState() {
	return frmState;
    }

    public void setFrmState(String frmState) {
	this.frmState = frmState;
    }

    public Long getMdfDate() {
	return mdfDate;
    }

    public void setMdfDate(Long mdfDate) {
	this.mdfDate = mdfDate;
    }

    public Long getWaitTime() {
	return waitTime;
    }

    public void setWaitTime(Long waitTime) {
	this.waitTime = waitTime;
    }

    public String getpName() {
	return pName;
    }

    public void setpName(String pName) {
	this.pName = pName;
    }

    public String getsName() {
	return sName;
    }

    public void setsName(String sName) {
	this.sName = sName;
    }

	public String getProjKey() {
		return projKey;
	}

	public void setProjKey(String projKey) {
		this.projKey = projKey;
	}

   

}
