package com.bolt.dashboard.core.model;

import java.util.HashSet;
import java.util.Set;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "UserAssociation")
public class UserAssociation extends BaseModel {
	private String pName;
	private String almType;
	private Set<AssociatedUsers> users = new HashSet<>();

	public String getAlmType() {
		return almType;
	}

	public void setAlmType(String almType) {
		this.almType = almType;
	}

	public String getpName() {
		return pName;
	}

	public void setpName(String pName) {
		this.pName = pName;
	}

	public Set<AssociatedUsers> getUsers() {
		return users;
	}

	public void setUsers(Set<AssociatedUsers> users) {
		this.users = users;
	}

}
