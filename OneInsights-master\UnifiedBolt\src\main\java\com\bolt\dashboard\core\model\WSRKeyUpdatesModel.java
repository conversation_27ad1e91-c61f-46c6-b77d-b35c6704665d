package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "wsr")
public class WSRKeyUpdatesModel {
	private String ta;
	private String opp;
	private String tp;
	private String accmp;	
	public String getTa() {
		return ta;
	}
	public void setTa(String ta) {
		this.ta = ta;
	}
	public String getOpp() {
		return opp;
	}
	public void setOpp(String opp) {
		this.opp = opp;
	}
	public String getTp() {
		return tp;
	}
	public void setTp(String tp) {
		this.tp = tp;
	}
	public String getAccmp() {
		return accmp;
	}
	public void setAccmp(String accmp) {
		this.accmp = accmp;
	}
  
}
