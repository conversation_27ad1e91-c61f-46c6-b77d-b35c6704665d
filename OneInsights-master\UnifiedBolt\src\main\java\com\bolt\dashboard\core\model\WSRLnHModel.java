package com.bolt.dashboard.core.model;

import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "wsr")
public class WSRLnHModel {
    private String name;
    private String frm;
    private String to;	
	public String getFrm() {
		return frm;
	}
	public void setFrm(String frm) {
		this.frm = frm;
	}
	public String getTo() {
		return to;
	}
	public void setTo(String to) {
		this.to = to;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
}
