package com.bolt.dashboard.core.model;

import java.util.List;

import org.springframework.data.mongodb.core.mapping.Document;


@Document(collection = "wsr")
public class WSRModel extends BaseModel {
	private String pName;
	private String itr;
	private String stDate;
	private String endDate;	
	private List<WSRLnHModel> lnh;
	private List<WSRActionItemsModel> act;
	private List<WSRRisksModel> risks;
	private List<WSRKeyUpdatesModel> upd;
	private List<WSRProjectStatusModel> sts;
	private String kCmt;
	private String rCmt;
	private String pCmt; 
    private String lCmt;
    private String aCmt;
	public String getkCmt() {
		return kCmt;
	}
	public void setkCmt(String kCmt) {
		this.kCmt = kCmt;
	}
	public String getrCmt() {
		return rCmt;
	}
	public void setrCmt(String rCmt) {
		this.rCmt = rCmt;
	}
	public String getpCmt() {
		return pCmt;
	}
	public void setpCmt(String pCmt) {
		this.pCmt = pCmt;
	}
	public String getlCmt() {
		return lCmt;
	}
	public void setlCmt(String lCmt) {
		this.lCmt = lCmt;
	}
	public String getaCmt() {
		return aCmt;
	}
	public void setaCmt(String aCmt) {
		this.aCmt = aCmt;
	}
	public String getpName() {
		return pName;
	}
	public void setpName(String pName) {
		this.pName = pName;
	}	
	public List<WSRLnHModel> getLnh() {
		return lnh;
	}
	public void setLnh(List<WSRLnHModel> lnh) {
		this.lnh = lnh;
	}
	public List<WSRActionItemsModel> getAct() {
		return act;
	}
	public void setAct(List<WSRActionItemsModel> act) {
		this.act = act;
	}
	public List<WSRRisksModel> getRisks() {
		return risks;
	}
	public void setRisks(List<WSRRisksModel> risks) {
		this.risks = risks;
	}
	public List<WSRProjectStatusModel> getSts() {
		return sts;
	}
	public void setSts(List<WSRProjectStatusModel> sts) {
		this.sts = sts;
	}
	public String getItr() {
		return itr;
	}
	public void setItr(String itr) {
		this.itr = itr;
	}
	public List<WSRKeyUpdatesModel> getUpd() {
		return upd;
	}
	public void setUpd(List<WSRKeyUpdatesModel> upd) {
		this.upd = upd;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public String getStDate() {
		return stDate;
	}
	public void setStDate(String stDate) {
		this.stDate = stDate;
	}

}
