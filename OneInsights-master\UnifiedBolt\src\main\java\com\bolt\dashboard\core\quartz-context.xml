<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
        					http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd">

	<context:component-scan base-package="com.bolt.dashboard" />

<bean id="TaskCollector" class="com.bolt.dashboard.Scheduler.TaskCollector">  </bean>
	<!-- For times when you just need to invoke a method on a specific object -->
	<bean id="simpleJobDetail" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
		<property name="targetObject" ref="TaskCollector" />
		<property name="targetMethod" value="PrintMessage" />
	</bean>



	<!-- Run the job every 2 seconds with initial delay of 1 second -->
	<bean id="simpleTrigger"  class="org.springframework.scheduling.quartz.SimpleTriggerFactoryBean">
		<property name="jobDetails" ref="simpleJobDetail" /> 
		<property name="cronExpression" value="0 0 17 ? * *" />
		<!-- <property name="startDelay" value="1000" />
		<property name="repeatInterval" value="2000" /> -->
	</bean>


	<!-- Scheduler factory bean to glue together jobDetails and triggers to Configure Quartz Scheduler -->
	<bean  class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
		<property name="jobDetails" ref="simpleJobDetail"/>
			
		<property name="triggers" ref="simpleTrigger"/>
			
	</bean>

</beans>