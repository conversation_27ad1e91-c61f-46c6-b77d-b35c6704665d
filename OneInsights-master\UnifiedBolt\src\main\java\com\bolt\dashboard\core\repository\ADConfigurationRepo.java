package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.ActiveDirectoryConfiguration;

public interface ADConfigurationRepo extends CrudRepository<ActiveDirectoryConfiguration, ObjectId> {

    ActiveDirectoryConfiguration findByOrganisationName(String projectName);

    List<ActiveDirectoryConfiguration> findAll();

    int deleteByOrganisationName(String projectName);

}
