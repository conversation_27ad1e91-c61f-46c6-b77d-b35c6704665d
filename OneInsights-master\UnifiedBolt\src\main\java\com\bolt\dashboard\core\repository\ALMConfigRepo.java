package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.ALMConfiguration;

public interface ALMConfigRepo extends CrudRepository<ALMConfiguration, ObjectId> {
	List<ALMConfiguration> findByProjectName(String projectName);
	int deleteByProjectName(String projectName);

}
