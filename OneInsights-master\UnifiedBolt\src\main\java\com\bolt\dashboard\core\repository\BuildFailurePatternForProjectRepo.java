/**
 * 
 */
package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.BuildFailurePatternForProjectInJenkinsModel;

/**
 * <AUTHOR>
 *
 */
public interface BuildFailurePatternForProjectRepo
        extends CrudRepository<BuildFailurePatternForProjectInJenkinsModel, ObjectId> {
    List<BuildFailurePatternForProjectInJenkinsModel> findByProjectName(String projectName);

    List<BuildFailurePatternForProjectInJenkinsModel> findAll();
}
