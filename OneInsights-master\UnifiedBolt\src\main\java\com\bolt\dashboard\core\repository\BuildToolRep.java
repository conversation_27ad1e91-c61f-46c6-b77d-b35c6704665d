/**
 * 
 */
package com.bolt.dashboard.core.repository;

import java.util.List;
import java.util.Set;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.BuildTool;

/**
 * <AUTHOR>
 *
 */
public interface BuildToolRep extends CrudRepository<BuildTool, ObjectId> {

    Set<BuildTool> findBybuildType(String buildType);

    long countByBuildType(String buildType);

    Set<BuildTool> findByBuildTypeAndName(String buildType, String projectName);
    Set<BuildTool> findByNameAndJobName(String name,String jobName);

    Set<BuildTool> getIdByBuildType(String buildType);
    @Query(fields="{jobList:0}")
    Set<BuildTool> findByBuildTypeAndNameAndTimestampBetween(String buildType, String projectName, long startDate,
            long endDate);
    List<BuildTool> findByNameAndTimestampBetween(String projectName, long startDate, long endDate);
    @Query(fields="{jobList:0}")
    Set<BuildTool> findByName(String projectName);
    BuildTool findOneByName(String projectName);

	BuildTool findOneByNameOrderByBuildIDDesc(String projectName);
	List<BuildTool> findByBuildTypeAndRepoNameAndGroupNameOrderByBuildIDDesc(String buildType, String repoName, String groupName);


	//List<BuildTool> findByNameAndToolNameAndTimestampBetween(String projectName, long sDate, long eDate);

	Iterable<BuildTool> findByNameAndBuildTypeIgnoreCase(String projectName, String toolName);

	Set<BuildTool> findByNameAndRepoNameAndBranchName(String projectName, String repoName, String branchName);

	List<BuildTool> findByNameAndDefinitionId(String projectName, String definitionId);

	//List<BuildTool> findByNameAndDefinitionId(String projectName, String definitionId);
}
