package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.ChangeHistoryModel;


public interface ChangeHisortyRepo extends CrudRepository<ChangeHistoryModel, ObjectId>{
	List<ChangeHistoryModel> findByWId(String wId);

	List<ChangeHistoryModel> findByPName(String projName);

}
