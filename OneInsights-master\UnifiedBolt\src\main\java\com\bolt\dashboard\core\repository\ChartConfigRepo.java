package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.ChartConfiguration;

public interface ChartConfigRepo extends CrudRepository<ChartConfiguration, ObjectId> {
	List<ChartConfiguration> findByPName(String projectName);	
	ChartConfiguration findByPNameAndAlmType(String pName, String almType);
}
