package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.ProjectCodeCoverage;

public interface CodeCoverageRepository extends CrudRepository<ProjectCodeCoverage, ObjectId> {

	ProjectCodeCoverage findByCollectorItemIdAndTimestamp(ObjectId collectorItemId, long timestamp);

	List<ProjectCodeCoverage> findByProjectName(String projectName);

	List<ProjectCodeCoverage> findByProjectNameAndCoverageType(String projectName, String coverageType);
}