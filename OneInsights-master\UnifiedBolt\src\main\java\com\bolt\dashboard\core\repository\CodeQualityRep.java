package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;
import com.bolt.dashboard.core.model.CodeQuality;

public interface CodeQualityRep extends CrudRepository<CodeQuality, ObjectId> {

    CodeQuality findByCollectorItemIdAndTimestamp(ObjectId collectorItemId, long timestamp);

    Iterable<CodeQuality> findByName(String projectName);

    Iterable<CodeQuality> findByNameAndTimestampBetween(String projectName, long startDate, long endDate);
    
    Iterable<CodeQuality> findByNameAndVersion(String projectName, String version);

	CodeQuality findOneByNameAndVersionOrderByTimestamp(String projectName, String projectCode);

	List<CodeQuality> findByNameAndTimestampBetweenOrderByTimestampAsc(String projName, long startDate, long endDate);

	CodeQuality findOneByNameAndTimestampBetweenOrderByTimestampAsc(String projName, long startDate, long endDate);

	CodeQuality findFirstByNameAndTimestampBetweenOrderByTimestampDesc(String projName, long startDate, long endDate);

	CodeQuality findFirstByNameOrderByTimestampDesc(String projName);

	List<CodeQuality> findByNameOrderByTimestampDesc(String projName);
}
