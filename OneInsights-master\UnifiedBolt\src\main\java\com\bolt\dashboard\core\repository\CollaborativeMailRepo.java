package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.CollaborativeMailModel;

public interface CollaborativeMailRepo extends CrudRepository<CollaborativeMailModel, ObjectId> {
	List<CollaborativeMailModel> findAll();

	List<CollaborativeMailModel> findByProjectName(String projectName);

	int deleteByProjectNameAndActionIdAndChartName(String projectName, String actionId, String chartName);

	CollaborativeMailModel findByProjectNameAndActionIdAndChartName(String projectName, String actionId,
			String chartName);
	int deleteByProjectNameAndActionId(String projectName, String actionId);
	CollaborativeMailModel findByProjectNameAndActionId(String projectName, String actionId
		);
}
