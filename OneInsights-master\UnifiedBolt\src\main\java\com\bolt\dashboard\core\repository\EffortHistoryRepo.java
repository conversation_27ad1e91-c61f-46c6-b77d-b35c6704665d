package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.EffortHistoryModel;

public interface EffortHistoryRepo extends CrudRepository<EffortHistoryModel, ObjectId> {

	List<EffortHistoryModel> findByWId(String wId);
	List<EffortHistoryModel> findByPNameAndSNameAndWId(String pName,String sName,String wId);
	List<EffortHistoryModel> findByPName(String projName);
	
}
