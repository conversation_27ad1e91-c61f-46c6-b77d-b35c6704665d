package com.bolt.dashboard.core.repository;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.EngagementScorecardSubjectiveData;

public interface EngScorecardSubjectiveDataRepo extends CrudRepository<EngagementScorecardSubjectiveData, ObjectId> {
	
	public EngagementScorecardSubjectiveData findByPName(String pName);
	
  }
