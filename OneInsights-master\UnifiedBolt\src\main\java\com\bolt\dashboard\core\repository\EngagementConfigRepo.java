package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.EngagementConfig;
import com.bolt.dashboard.core.model.HealthData;

public interface EngagementConfigRepo extends CrudRepository<EngagementConfig, ObjectId> {

	List<EngagementConfig> findAll();
	List<EngagementConfig> findByTowerName(String towerName);

	
}
