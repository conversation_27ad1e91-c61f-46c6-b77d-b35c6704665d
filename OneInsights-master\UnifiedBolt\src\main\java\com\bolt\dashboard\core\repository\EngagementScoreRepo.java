package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.EngagementRule;

public interface EngagementScoreRepo extends CrudRepository<EngagementRule, ObjectId> {

	List<EngagementRule> findByProjectName(String projectName);

	Iterable<EngagementRule> findByTowerName(String towerName);
	Iterable<EngagementRule> findAll();

}
