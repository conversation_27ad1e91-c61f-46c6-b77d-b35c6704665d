package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.GeneralDirectoryConfiguration;

public interface GDConfigurationRepo extends CrudRepository<GeneralDirectoryConfiguration, ObjectId> {
    GeneralDirectoryConfiguration findByName(String name);

    List<GeneralDirectoryConfiguration> findAll();

    int deleteByName(String name);

}
