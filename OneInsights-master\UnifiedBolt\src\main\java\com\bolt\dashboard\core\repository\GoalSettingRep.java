package com.bolt.dashboard.core.repository;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.GoalSetting;

public interface GoalSettingRep extends CrudRepository<GoalSetting, ObjectId> {

    Iterable<GoalSetting> findByProjectName(String name);

    Iterable<GoalSetting> findByProjectNameAndTimestampBetween(String name, long startDate, long endDate);
    
    Iterable<GoalSetting> findByProjectNameAndName(String projectName,String name);

	GoalSetting findOneByProjectNameOrderByTimestampAsc(String projName);

	GoalSetting findFirstByProjectNameOrderByTimestampDesc(String projName);

	GoalSetting findFirstByProjectNameAndNameOrderByTimestampDesc(String projName, String string);

}
