package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.HealthData;

public interface HealthDataRepo extends CrudRepository<HealthData, ObjectId> {

	List<HealthData> findByProjectName(String projectName);

	HealthData findLastByProjectName(String projectName);

}
