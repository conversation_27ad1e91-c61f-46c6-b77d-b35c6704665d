package com.bolt.dashboard.core.repository;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.HealthIndicator;

public interface HealthIndicatorRep extends CrudRepository<HealthIndicator, ObjectId> {

    int deleteByRuleName(String ruleName);

    int deleteById(String id);

    int deleteByWeightage(int i);

    HealthIndicator findByProjectName(String ruleName);
}
