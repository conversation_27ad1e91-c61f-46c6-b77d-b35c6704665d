/**
 *
 */
package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.HighLightReelModel;

/**
 * <AUTHOR>
 *
 */
public interface HighLightReelRepository extends CrudRepository<HighLightReelModel, ObjectId> {
    List<HighLightReelModel> findAll();

    HighLightReelModel findByProjectName(String projectName);

}
