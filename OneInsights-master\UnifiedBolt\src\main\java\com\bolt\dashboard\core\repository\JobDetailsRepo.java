/**
 * 
 */
package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.JobDetails;

/**
 * <AUTHOR>
 *
 */
public interface JobDetailsRepo extends CrudRepository<JobDetails, ObjectId> {
    List<JobDetails> findAll();

    JobDetails findByProjectName(String projectName);

    int deleteByProjectName(String projectName);
}
