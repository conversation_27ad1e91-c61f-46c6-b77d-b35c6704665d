package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.MailerAssociation;

public interface MailerAssociationRepo extends CrudRepository<MailerAssociation, ObjectId>  {

    List<MailerAssociation> findAll();
    List<MailerAssociation> findByProjectNameAndAccess(String projectName,String access);
}
