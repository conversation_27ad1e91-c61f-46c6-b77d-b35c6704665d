package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.MetricsModel;

public interface MetricRepo extends CrudRepository<MetricsModel, ObjectId> {
    List<MetricsModel> findByPNameAndSName(String pName, String sName);

    List<MetricsModel> findByPNameAndType(String pName, String Type);

    MetricsModel findByWIdAndPNameAndSName(String wId, String pName, String sName);

    MetricsModel findByWId(String wId);

    List<MetricsModel> findByPNameAndSId(String pName, int sid);

    List<MetricsModel> findByPNameAndTypeAndPAlmType(String pName, String type, String almType);

    List<MetricsModel> findByPNameAndSNameAndPAlmType(String pName, String sprintName, String almType);

    List<MetricsModel> findByPNameAndSIdAndPAlmType(String pName, int sprintId, String almType);

    List<MetricsModel> findByPNameAndSNameAndType(String pName, String sprintName, String type);

    List<MetricsModel> findByPName(String pName);

    List<MetricsModel> findByPNameAndBaselineAndType(String pName, String baseLine, String type);

	MetricsModel findByPNameAndWId(String projectName, String getwId);

	List<MetricsModel> findByPNameAndPAlmType(String projectName, String almType);

	MetricsModel findByWIdAndPName(String workItemId, String pName);
}
