package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import com.bolt.dashboard.core.model.OneView;

@Repository
public interface OneViewRepositary extends CrudRepository<OneView, ObjectId> {

	List<OneView> findByProjectName(String projectName);

	OneView findByProjectNameAndUser(String projectName, String user);
}
