package com.bolt.dashboard.core.repository;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.SCMTool;

public interface PUMABitBucketRepository extends CrudRepository<SCMTool, ObjectId> {

    Iterable<SCMTool> findByScType(String scType);

    Iterable<SCMTool> findByScTypeAndProjectName(String scType, String projectName);

    long countByScType(String scType);

    Iterable<SCMTool> findByScTypeAndProjectNameAndCommitTSBetween(String scType, String projectName,
            long startDate, long endDate);
}
