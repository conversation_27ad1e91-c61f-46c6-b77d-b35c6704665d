package com.bolt.dashboard.core.repository;

import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;
import com.bolt.dashboard.core.model.PortfolioConfig;


public interface PortfolioConfigRepo extends CrudRepository<PortfolioConfig, ObjectId> {
    List<PortfolioConfig>findAll();
    List<PortfolioConfig> findByProjectName(String projectName);
    int deleteByProjectName(String projectName);
}
