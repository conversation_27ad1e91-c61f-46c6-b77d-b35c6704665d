package com.bolt.dashboard.core.repository;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;


import com.bolt.dashboard.core.model.PortfolioViewConfig;

public interface PortfolioViewConfigRepo extends CrudRepository<PortfolioViewConfig, ObjectId> {
	
	PortfolioViewConfig findByProjectName(String projectName);
	void deleteByProjectName(String projectName);
}
