package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.ProjectComparison;
import com.bolt.dashboard.core.model.SprintComparison;

public interface ProjectComparisonRepo extends CrudRepository<ProjectComparison, ObjectId> {

    List<ProjectComparison> findByProjectName(String projectName);
}
