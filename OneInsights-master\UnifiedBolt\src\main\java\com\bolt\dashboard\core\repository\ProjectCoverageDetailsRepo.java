package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.ProjectCoverageDetails;

public interface ProjectCoverageDetailsRepo extends CrudRepository<ProjectCoverageDetails, ObjectId> {




	List<ProjectCoverageDetails> findByRepoNameAndGroupNameOrderByJobIdDesc(String string, String repoName);

	List<ProjectCoverageDetails> findByPName(String pName);

	ProjectCoverageDetails findOneByRepoNameAndGroupName(String repoName, String groupName);

	List<ProjectCoverageDetails> findByRepoNameAndGroupName(String repoName, String groupName);

}
