package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.ProjectHealth;

public interface ProjectHealthRep extends CrudRepository<ProjectHealth, ObjectId> {

	List<ProjectHealth> findByProjectName(String projectName);

	List<ProjectHealth> findByProjectNameAndSprintName(String projectName, String sprintName);

	List<ProjectHealth> findLastByProjectName(String projectName);
}