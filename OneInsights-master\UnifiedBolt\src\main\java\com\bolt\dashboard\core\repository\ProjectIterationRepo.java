package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.IterationOutModel;

public interface ProjectIterationRepo extends CrudRepository<IterationOutModel, ObjectId> {
	List<IterationOutModel> findByPName(String pName);
	List<IterationOutModel> findByPNameAndPAlmType(String pName,String almType);
	IterationOutModel findByPNameAndSNameAndPAlmType(String pName, String itrName, String pAlmType);
	List<IterationOutModel> findByPNameAndPAlmTypeAndState(String pName,String almType,String state);
	IterationOutModel findOneByPNameAndPAlmTypeOrderByEndDateDesc(String pName,String almType);
	List<IterationOutModel> findByPNameAndState(String projectName2, String string);
}
