package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.ProjectModel;

public interface ProjectRepo extends CrudRepository<ProjectModel, ObjectId> {
	//ProjectModel findByProjectName(String name);
	List<ProjectModel> findByProjectName(String projName);
	ProjectModel findByProjectNameAndAlmType(String name, String almType);

}
