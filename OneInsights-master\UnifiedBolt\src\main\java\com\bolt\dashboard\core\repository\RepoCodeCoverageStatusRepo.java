package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.RepoCodeCoverageStatus;



public interface RepoCodeCoverageStatusRepo extends CrudRepository<RepoCodeCoverageStatus, ObjectId> {

	List<RepoCodeCoverageStatus> findByPName(String pName);

	RepoCodeCoverageStatus findByRepoNameAndGroupName(String repoName, String groupName);

	
}
