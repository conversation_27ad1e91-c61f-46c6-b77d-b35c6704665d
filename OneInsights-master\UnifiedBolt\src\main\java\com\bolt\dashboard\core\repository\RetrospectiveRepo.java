package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.Retrospective;;

public interface RetrospectiveRepo extends CrudRepository<Retrospective, ObjectId> {
	Retrospective findByUniqueIDAndSprintNameAndProjectName(int uniqueID, String sprintName, String projectName);

	List<Retrospective> findAll();
	List<Retrospective> findByProjectName(String projectName);

	int deleteByUniqueIDAndSprintNameAndProjectName(int uniqueID, String sprintName, String projectName);

}
