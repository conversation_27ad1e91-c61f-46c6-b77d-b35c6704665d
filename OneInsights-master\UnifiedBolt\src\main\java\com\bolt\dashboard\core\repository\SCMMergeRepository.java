package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.SCMMR;

public interface SCMMergeRepository extends CrudRepository<SCMMR, ObjectId> {

	List<SCMMR> findByScTypeAndProjectName(String scType, String projectName);

	List<SCMMR> findByScTypeAndProjectNameAndRepoName(String scType, String projectName, String repoName);

	List<SCMMR> findByProjectName(String projectName);

	List<SCMMR> findByProjectNameAndRepoName(String projectName, String repoName);

	List<SCMMR> findByScTypeAndProjectNameIgnoreCase(String scType, String pName);

	SCMMR findByMrId(int mrID);
}
