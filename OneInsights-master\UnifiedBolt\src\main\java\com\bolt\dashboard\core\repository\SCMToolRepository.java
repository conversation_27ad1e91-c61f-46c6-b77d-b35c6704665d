package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.model.SCMToolBO;

public interface SCMToolRepository extends CrudRepository<SCMTool, ObjectId> {

    List<SCMTool> findByScTypeAndProjectName(String scType, String projectName);
    List<SCMTool> findByScTypeAndProjectNameAndRepoName(String scType, String projectName,String repoName);
    List<SCMToolBO> findByScTypeAndProjectNameIgnoreCase(String scType, String projectName);
    long countByScType(String scType);

    Iterable<SCMTool> findByScTypeAndProjectNameAndCommitTSBetween(String scType, String projectName,
            long startDate, long endDate);
	List<SCMTool> findByProjectName(String projectName);
}
