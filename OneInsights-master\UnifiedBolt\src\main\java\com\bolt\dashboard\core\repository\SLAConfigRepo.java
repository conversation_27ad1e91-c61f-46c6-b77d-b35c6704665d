package com.bolt.dashboard.core.repository;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.SLAConfiguration;

public interface SLAConfigRepo extends CrudRepository<SLAConfiguration, ObjectId> {
	SLAConfiguration findByProjectName(String projectName);
	int deleteByProjectName(String projectName);

}
