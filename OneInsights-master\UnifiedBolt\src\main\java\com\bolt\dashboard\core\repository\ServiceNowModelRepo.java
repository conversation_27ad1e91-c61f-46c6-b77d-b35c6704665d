package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.ServiceNowInc;
import com.bolt.dashboard.core.model.ServiceNowModel;
import com.bolt.dashboard.core.model.TeamQuality;

public interface ServiceNowModelRepo extends CrudRepository<ServiceNowModel, ObjectId> {

	List<ServiceNowModel> findAll();

	

	

}
