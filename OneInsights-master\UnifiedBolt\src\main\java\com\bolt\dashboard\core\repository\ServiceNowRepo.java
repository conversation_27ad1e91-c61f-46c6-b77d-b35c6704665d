package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.ServiceNowInc;
import com.bolt.dashboard.core.model.TeamQuality;

public interface ServiceNowRepo extends CrudRepository<ServiceNowInc, ObjectId> {

	List<ServiceNowInc> findAll();

	ServiceNowInc findByNumber(String number);
	

	

}
