package com.bolt.dashboard.core.repository;

import com.bolt.dashboard.core.model.ServiceNowSLA;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.repository.CrudRepository;

public interface ServiceNowSLARepo extends CrudRepository<ServiceNowSLA, ObjectId> {

//    @Query("sys_id: ?0")
//    public ServiceNowSLA findBySys_id(String id);

    @Query("{ 'sys_id' : ?0 }")
    public ServiceNowSLA findBySysId(String name);
}
