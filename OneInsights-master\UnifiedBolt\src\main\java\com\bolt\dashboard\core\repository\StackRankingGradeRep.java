package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.StackGrade;

public interface StackRankingGradeRep extends CrudRepository<StackGrade, ObjectId> {
    int deleteByProjectName(String projectName);

    List<StackGrade> findByProjectName(String projectName);

    List<StackGrade> findByProjectNameAndRole(String projectName, String role);

    List<StackGrade> findByRulemetricsRuleName(String ruleName);

    List<StackGrade> findByProjectNameAndRoleAndRulemetricsRuleName(String projectName, String role, String ruleName);

}
