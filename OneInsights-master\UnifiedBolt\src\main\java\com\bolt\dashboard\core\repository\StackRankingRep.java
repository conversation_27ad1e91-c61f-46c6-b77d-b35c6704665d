package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.StackRanking;
import com.bolt.dashboard.core.model.StackRankingMetrics;

public interface StackRankingRep extends CrudRepository<StackRanking, ObjectId> {
    int deleteByProjectName(String projectName);

    StackRanking findByProjectName(String projectName);
    
   
}
