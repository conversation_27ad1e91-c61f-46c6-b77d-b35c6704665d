package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.TeamIndexConfiguration;


public interface TeamIndexConfigRepo extends CrudRepository<TeamIndexConfiguration,ObjectId> {

	List<TeamIndexConfiguration> findByPName(String pName);
	List<TeamIndexConfiguration> findByPNameOrderByTimestamp(String pName);
	
	

}
