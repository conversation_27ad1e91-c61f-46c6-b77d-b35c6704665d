package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.TeamMember;

public interface TeamMemberRepo extends CrudRepository<TeamMember, ObjectId> {
	TeamMember findByEmailAndProjectName(String email, String projectName);

	List<TeamMember> findByProjectName(String projectName);
}
