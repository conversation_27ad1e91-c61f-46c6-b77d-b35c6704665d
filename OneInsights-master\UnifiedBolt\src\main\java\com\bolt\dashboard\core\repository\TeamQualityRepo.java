package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.TeamQuality;

public interface TeamQualityRepo extends CrudRepository<TeamQuality, ObjectId> {

	List<TeamQuality> findByProjectName(String projectName);
	List<TeamQuality> findByProjectNameAndEnvironment(String projectName,String environment);
	void deleteByProjectName(String projectName);
	TeamQuality findBySprintNameAndComponentName(String sprintName,String componentName);

	

}
