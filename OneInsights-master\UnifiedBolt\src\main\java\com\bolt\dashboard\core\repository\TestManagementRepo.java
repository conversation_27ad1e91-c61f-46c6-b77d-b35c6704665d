package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.TestManagementTool;

public interface TestManagementRepo extends CrudRepository<TestManagementTool, ObjectId> {
    List<TestManagementTool> findByProjectName(String projectName);

    List<TestManagementTool> findByTestTypeAndProjectName(String testType, String projectName);

    List<TestManagementTool> findByProjectNameAndTimeStampBetween(String projectName, long startDate, long endDate);
}
