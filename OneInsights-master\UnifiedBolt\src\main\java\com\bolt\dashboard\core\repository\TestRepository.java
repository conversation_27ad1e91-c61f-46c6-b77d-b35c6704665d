package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.TestTool;

public interface TestRepository extends CrudRepository<TestTool, ObjectId> {

    TestTool findByCollectorItemIdAndTimestamp(ObjectId collectorItemId, long timestamp);

    Iterable<TestTool> findByName(String projectName);

    List<TestTool> findByNameAndTimestampBetween(String projectName, long startDate, long endDate);
}
