package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.TransitionModel;

public interface TransitionRepo extends CrudRepository<TransitionModel, ObjectId> {
	List<TransitionModel> findByPNameAndSName(String pName, String sName);
	
	List<TransitionModel> findByWId(String wId);

	List<TransitionModel> findByPName(String projName);

}
