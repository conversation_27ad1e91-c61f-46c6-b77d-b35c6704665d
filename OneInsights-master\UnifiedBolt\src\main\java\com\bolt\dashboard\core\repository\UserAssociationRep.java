package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.UserAssociation;


public interface UserAssociationRep extends CrudRepository<UserAssociation, ObjectId> {
	UserAssociation findByPNameAndAlmType(String pName, String almType);

	List<UserAssociation> findByPName(String projectName);

	/*
	 * Iterable<UserAssociation> findByProject(String projectName);
	 * 
	 * Iterable<UserAssociation> findAll();
	 * 
	 * int deleteByProject(String project);
	 * 
	 * List<UserAssociation> findByProjectAndMetricEmail(String projectName,
	 * String email);
	 */
}