package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.ManageUser;

public interface UserRepo extends CrudRepository<ManageUser, ObjectId> {
   /* Iterable<ManageUser> findByprojectName(String projectName);

    int deleteByMetric(Iterable<ManageUser> metric);

    @Query(value = "{'metric.email' : ?0 }")
    Iterable<ManageUser> findByEmail(String email);

    int deleteById(ObjectId objectId);*/
	
	List<ManageUser> findByUserName(String userName);
	
	ManageUser findByUserNameAndEmail(String userName, String email);
	
	//ManageUser findByUserName(String userName);
	
	@Query(value = "{'metric.email' : ?0 }")
    Iterable<ManageUser> findByEmail(String email);

    @Query("{ 'email' : ?0 }")
	ManageUser findUsersByEmail(String email);
}
