package com.bolt.dashboard.core.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.repository.CrudRepository;

import com.bolt.dashboard.core.model.WSRModel;

public interface WSRRepo extends CrudRepository<WSRModel, ObjectId> {
	List<WSRModel>  findByPName(String pName);
	WSRModel findByPNameAndItrAndStDateAndEndDate(String pName, String itr,String stDate, String endDate);
}
