/**
 * 
 */
package com.bolt.dashboard.core.scheduler;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *
 */

@Component
public class FirstJobDetail extends QuartzJobBean {

    private ISchedulerService schedulerService;
    private String projectName;
    private static final Logger LOG = LogManager.getLogger(FirstJobDetail.class);
    private JobExecutionContext jobExecutionContext;

    public FirstJobDetail() {
    }

   

    @Override
    protected void executeInternal(JobExecutionContext jobExecContext) {
        // JobExecutionContext is being set...
        setJobExecutionContext(jobExecContext);

        // First Task is being executing...
        getSchedulerService().executeFirstTask(projectName);

        // Check for whther value could be transferred to Quartz job.
        LOG.debug(
                "Value assigned in XML is : " + jobExecContext.getJobDetail().getJobDataMap().getString("projectName"));
        LOG.debug("Value assigned in XML Directly to property is  : " + projectName);
        LOG.debug("Value assigned in XML Directly to property is through Class in Springfarmework First JOB details : "
                + getSchedulerService().getParameter());

    }

    /**
     * @return the projectName
     */
    public String getProjectName() {
        return this.projectName;
    }

    /**
     * @param projectName
     *            the projectName to set
     */
    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return the schedulerService
     */
    public ISchedulerService getSchedulerService() {
        return schedulerService;
    }

    /**
     * @param schedulerService
     *            the schedulerService to set
     */
    public void setSchedulerService(ISchedulerService schedulerService) {
        this.schedulerService = schedulerService;
    }

    /**
     * @return the jobExecutionContext
     */
    public JobExecutionContext getJobExecutionContext() {
        return jobExecutionContext;
    }

    /**
     * @param jobExecutionContext
     *            the jobExecutionContext to set
     */
    public void setJobExecutionContext(JobExecutionContext jobExecutionContext) {
        this.jobExecutionContext = jobExecutionContext;
    }

}
