
package com.bolt.dashboard.core.scheduler;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.bolt.dashboard.core.ProjectCollector;

/**
 * <AUTHOR>
 *
 */
public class FirstTaskCollector {

	private String projectName;

	private static final Logger LOG = LogManager.getLogger(FirstTaskCollector.class);

	public FirstTaskCollector() {
	}

	public void execute(String projectName) {
		LOG.debug("First Task ran successfully for :" + projectName);
		new ProjectCollector().multiTaskThread(projectName);
		LOG.debug("Value assigned in XML Directly to property is through Class in Springfarmework  : "
				+ getProjectName());
	}

	/**
	 * @return the projectName
	 */
	public String getProjectName() {
		return this.projectName;
	}

	/**
	 * @param projectName
	 *            the projectName to set
	 */
	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

}
