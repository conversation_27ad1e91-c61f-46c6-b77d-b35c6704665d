

/**
 * 
 *//*
package com.bolt.dashboard.core.scheduler;

import org.apache.logging.log4j.LogManager;

import com.bolt.dashboard.highlight.HighlightMain;

*//**
 * <AUTHOR>
 *
 *//*
public class HighLightTask {

    private static final Logger LOG = LogManager.getLogger(SecondTaskCollector.class);
    public HighLightTask() {
    }

   

    
     * Execute this task
     

    public void execute(String highlight) {
        LOG.debug("HighLight Task ran successfully" + highlight);
        new HighlightMain().highlightMain();
    }

}
*/