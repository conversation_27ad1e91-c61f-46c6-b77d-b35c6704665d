/**
 * 
 */
package com.bolt.dashboard.core.scheduler;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;

/**
 * <AUTHOR>
 *
 */
public class HighlightSchedulerJobBean extends QuartzJobBean {

    private ISchedulerService schedulerService;
    private String highlightRule;
    private JobExecutionContext jobExecutionContext;
    private static final Logger LOG = LogManager.getLogger(HighlightSchedulerJobBean.class);
    /**
     * 
     */
    public HighlightSchedulerJobBean() {
        
    }
    
    
    @Override
    protected void executeInternal(JobExecutionContext jobExecContext) {
        // JobExecutionContext is being set...
        setJobExecutionContext(jobExecContext);

        // First Task is being executing...
        LOG.info("highLightRule :"+ highlightRule);
        if(getSchedulerService()==null)
            LOG.info("Service is null");
        getSchedulerService().executeHighLightTask(highlightRule);

        // Check for whther value could be transferred to Quartz job.
        LOG.debug(
                "Value assigned in XML is : " + jobExecContext.getJobDetail().getJobDataMap().getString("highlight"));
        LOG.debug("Value assigned in XML Directly to property is  : " + highlightRule);
        LOG.debug("Value assigned in XML Directly to property is through Class in Springfarmework First JOB details : "
                + getSchedulerService().getParameter());

    }
    /**
     * @return the schedulerService
     */
    public ISchedulerService getSchedulerService() {
        return schedulerService;
    }
    /**
     * @param schedulerService the schedulerService to set
     */
    public void setSchedulerService(ISchedulerService schedulerService) {
        this.schedulerService = schedulerService;
    }
    /**
     * @return the jobExecutionContext
     */
    public JobExecutionContext getJobExecutionContext() {
        return jobExecutionContext;
    }
    /**
     * @param jobExecutionContext the jobExecutionContext to set
     */
    public void setJobExecutionContext(JobExecutionContext jobExecutionContext) {
        this.jobExecutionContext = jobExecutionContext;
    }
    /**
     * @return the highlightRule
     */
    public String getHighlightRule() {
        return highlightRule;
    }
    /**
     * @param highlightRule the highlightRule to set
     */
    public void setHighlightRule(String highlightRule) {
        this.highlightRule = highlightRule;
    }

}
