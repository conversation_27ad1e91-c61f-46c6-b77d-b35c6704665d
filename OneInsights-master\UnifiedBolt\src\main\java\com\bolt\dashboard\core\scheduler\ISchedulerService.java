/**
 * 
 */
package com.bolt.dashboard.core.scheduler;

/**
 * <AUTHOR>
 *
 */
public interface ISchedulerService {

    /**
     * Execute First Task
     * 
     * @param  
     * @throws 
     * @return 
     */
    public void executeFirstTask(String projectName);
     
    /**
     * Execute Second Task
     * 
     * @param  
     * @throws 
     * @return 
     */
    public void executeSecondTask();
    
    /**
     * It executes the HighLight Rule task
     * 
     * @param highlightRule
     */
    public void executeHighLightTask(String highlightRule);
    public void executeProjectTask(String projectRule);
    
    public String getParameter();
}
