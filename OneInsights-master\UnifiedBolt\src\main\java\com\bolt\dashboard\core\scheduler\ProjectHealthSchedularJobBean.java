package com.bolt.dashboard.core.scheduler;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.JobExecutionContext;
import org.springframework.scheduling.quartz.QuartzJobBean;

public class ProjectHealthSchedularJobBean extends QuartzJobBean {
    private ISchedulerService schedulerService;
    private String projectHealthRule;
    private JobExecutionContext jobExecutionContext;
    private static final Logger LOG = LogManager.getLogger(ProjectHealthSchedularJobBean.class);

    /**
     * 
     */
    public ProjectHealthSchedularJobBean() {
    }

    public String getProjectHealthRule() {
        return projectHealthRule;
    }

    public void setProjectHealthRule(String projectHealthRule) {
        this.projectHealthRule = projectHealthRule;
    }

    @Override
    protected void executeInternal(JobExecutionContext jobExecContext) {
        // JobExecutionContext is being set...
        setJobExecutionContext(jobExecContext);

        // First Task is being executing...
        LOG.info("projectHealthRule :" + projectHealthRule);
        if (getSchedulerService() == null)
            LOG.info("Service is null");
        getSchedulerService().executeProjectTask(projectHealthRule);

    }

    /**
     * @return the schedulerService
     */
    public ISchedulerService getSchedulerService() {
        return schedulerService;
    }

    /**
     * @param schedulerService
     *            the schedulerService to set
     */
    public void setSchedulerService(ISchedulerService schedulerService) {
        this.schedulerService = schedulerService;
    }

    /**
     * @return the jobExecutionContext
     */
    public JobExecutionContext getJobExecutionContext() {
        return jobExecutionContext;
    }

    /**
     * @param jobExecutionContext
     *            the jobExecutionContext to set
     */
    public void setJobExecutionContext(JobExecutionContext jobExecutionContext) {
        this.jobExecutionContext = jobExecutionContext;
    }
    /**
     * @return the highlightRule
     */

}
