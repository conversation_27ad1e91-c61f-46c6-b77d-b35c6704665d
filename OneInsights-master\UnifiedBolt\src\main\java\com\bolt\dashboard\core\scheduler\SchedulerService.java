

/**
 * 
 *//*
package com.bolt.dashboard.core.scheduler;

*//**
 * <AUTHOR>
 *
 *//*
public class SchedulerService implements ISchedulerService {

    *//**
     * 
     *//*
    private String projectName;

    private FirstTaskCollector firstTask;
    private SecondTaskCollector secondTask;
    private HighLightTask highLightTask;
    private ProjectHealthTask projectHealthTask;
     private static final Logger LOG = LogManager.getLogger(SchedulerService.class);

    public SchedulerService() {
    }

    
     * (non-Javadoc)
     * 
     * @see
     * com.bolt.dashboard.core.scheduler.ISchedulerService#executeFirstTask()
     
    @Override
    public void executeFirstTask(String projectName) {
        try {
            getFirstTask().execute(projectName);
        } catch (Exception e) {
            LOG.info(e);           
        }

    }

    
     * (non-Javadoc)
     * 
     * @see
     * com.bolt.dashboard.core.scheduler.ISchedulerService#executeSecondTask()
     
    @Override
    public void executeSecondTask() {
        getSecondTask().execute();

    }

    @Override
    public void executeHighLightTask(String highlight) {
        getHighLightTask().execute(highlight);

    }

    @Override
    public void executeProjectTask(String project) {
        getProjectHealthTask().execute(project);

    }

    public String getParameter() {
        return this.getProjectName();
    }

    *//**
     * @return the projectName
     *//*
    public String getProjectName() {
        return this.projectName;
    }

    *//**
     * @param projectName
     *            the projectName to set
     *//*
    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    *//**
     * @return the firstTask
     *//*
    public FirstTaskCollector getFirstTask() {
        return firstTask;
    }

    *//**
     * @param firstTask
     *            the firstTask to set
     *//*
    public void setFirstTask(FirstTaskCollector firstTask) {
        this.firstTask = firstTask;
    }

    *//**
     * @return the secondTask
     *//*
    public SecondTaskCollector getSecondTask() {
        return secondTask;
    }

    *//**
     * @param secondTask
     *            the secondTask to set
     *//*
    public void setSecondTask(SecondTaskCollector secondTask) {
        this.secondTask = secondTask;
    }

    *//**
     * @return the highLightTask
     *//*
    public HighLightTask getHighLightTask() {
        return highLightTask;
    }

    *//**
     * @param highLightTask
     *            the highLightTask to set
     *//*
    public void setHighLightTask(HighLightTask highLightTask) {
        this.highLightTask = highLightTask;
    }

    public ProjectHealthTask getProjectHealthTask() {
        return projectHealthTask;
    }

    public void setProjectHealthTask(ProjectHealthTask projectHealthTask) {
        this.projectHealthTask = projectHealthTask;
    }

}
*/