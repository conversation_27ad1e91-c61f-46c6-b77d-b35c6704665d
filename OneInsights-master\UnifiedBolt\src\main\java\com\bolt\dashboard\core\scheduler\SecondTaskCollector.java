/**
 * 
 */
package com.bolt.dashboard.core.scheduler;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR>
 *
 */
public class SecondTaskCollector {

    private static final Logger LOG = LogManager.getLogger(SecondTaskCollector.class);
    public SecondTaskCollector() {
    }

   

    /*
     * Execute this task
     */

    public void execute() {
        LOG.debug("Second Task ran successfully");
    }

}
