package com.bolt.dashboard.core.scheduler;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.bolt.dashboard.core.scheduler.TaskCollector;

public abstract class TaskScheduler extends QuartzJobBean {

    private TaskCollector task;

    @Override
    protected void executeInternal(JobExecutionContext arg0) throws JobExecutionException {
        task.printMessage();
    }

}
