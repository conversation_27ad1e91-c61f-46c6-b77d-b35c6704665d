package com.bolt.dashboard.engagementScorecard;

import java.util.List;

public class EngScoreCardQuarterData {
	private  List<EngScorecardQuarterSeries> velocitySeries;
	private List<EngScorecardQuarterSeries> compeltionSeries;
	private List<EngScorecardQuarterSeries> cycleTimeSeries;
    
	private List<String> categories;
	private int readinessIndex1;
	private int readinessIndex2;
	private int readinessIndex3;
	private int avgCompletion;
	private int avgVelocity;
	private int changeFailureRate;
	private String leadTime;
	private String cycleTime;
	private String MTTR;
    private boolean completionFlag;
    private boolean velocityFlag;
    private boolean changeFailureRateFlag;
    private boolean leadTimeFlag;
    private boolean cycleTimeFlag;
    private boolean mttrFlag;
    //Depends on Subjective Data and Team-Size for now
	private boolean readinessIndexFlag;
	
	
	public boolean isCompletionFlag() {
		return completionFlag;
	}
	public void setCompletionFlag(boolean completionFlag) {
		this.completionFlag = completionFlag;
	}
	public boolean isVelocityFlag() {
		return velocityFlag;
	}
	public void setVelocityFlag(boolean velocityFlag) {
		this.velocityFlag = velocityFlag;
	}
	public boolean isChangeFailureRateFlag() {
		return changeFailureRateFlag;
	}
	public void setChangeFailureRateFlag(boolean changeFailureRateFlag) {
		this.changeFailureRateFlag = changeFailureRateFlag;
	}
	public boolean isLeadTimeFlag() {
		return leadTimeFlag;
	}
	public void setLeadTimeFlag(boolean leadTimeFlag) {
		this.leadTimeFlag = leadTimeFlag;
	}
	public boolean isCycleTimeFlag() {
		return cycleTimeFlag;
	}
	public void setCycleTimeFlag(boolean cycleTimeFlag) {
		this.cycleTimeFlag = cycleTimeFlag;
	}
	public boolean isMttrFlag() {
		return mttrFlag;
	}
	public void setMttrFlag(boolean mttrFlag) {
		this.mttrFlag = mttrFlag;
	}
	public List<String> getCategories() {
		return categories;
	}
	public void setCategories(List<String> categories) {
		this.categories = categories;
	}
	public int getReadinessIndex1() {
		return readinessIndex1;
	}
	public void setReadinessIndex1(int readinessIndex1) {
		this.readinessIndex1 = readinessIndex1;
	}
	public int getReadinessIndex2() {
		return readinessIndex2;
	}
	public void setReadinessIndex2(int readinessIndex2) {
		this.readinessIndex2 = readinessIndex2;
	}
	public int getReadinessIndex3() {
		return readinessIndex3;
	}
	public void setReadinessIndex3(int readinessIndex3) {
		this.readinessIndex3 = readinessIndex3;
	}
	public int getAvgCompletion() {
		return avgCompletion;
	}
	public void setAvgCompletion(int avgCompletion) {
		this.avgCompletion = avgCompletion;
	}
	public int getAvgVelocity() {
		return avgVelocity;
	}
	public void setAvgVelocity(int avgVelocity) {
		this.avgVelocity = avgVelocity;
	}
	public int getChangeFailureRate() {
		return changeFailureRate;
	}
	public void setChangeFailureRate(int changeFailureRate) {
		this.changeFailureRate = changeFailureRate;
	}
	public String getLeadTime() {
		return leadTime;
	}
	public void setLeadTime(String leadTime) {
		this.leadTime = leadTime;
	}
	public String getCycleTime() {
		return cycleTime;
	}
	public void setCycleTime(String cycleTime) {
		this.cycleTime = cycleTime;
	}
	public String getMTTR() {
		return MTTR;
	}
	public void setMTTR(String mTTR) {
		MTTR = mTTR;
	}
	public List<EngScorecardQuarterSeries> getCycleTimeSeries() {
		return cycleTimeSeries;
	}
	public void setCycleTimeSeries(List<EngScorecardQuarterSeries> cycleTimeSeries) {
		this.cycleTimeSeries = cycleTimeSeries;
	}
	public List<EngScorecardQuarterSeries> getVelocitySeries() {
		return velocitySeries;
	}
	public void setVelocitySeries(List<EngScorecardQuarterSeries> velocitySeries) {
		this.velocitySeries = velocitySeries;
	}
	public List<EngScorecardQuarterSeries> getCompeltionSeries() {
		return compeltionSeries;
	}
	public void setCompeltionSeries(List<EngScorecardQuarterSeries> compeltionSeries) {
		this.compeltionSeries = compeltionSeries;
	}
	public boolean isReadinessIndexFlag() {
		return readinessIndexFlag;
	}
	public void setReadinessIndexFlag(boolean readinessIndexFlag) {
		this.readinessIndexFlag = readinessIndexFlag;
	}


}
