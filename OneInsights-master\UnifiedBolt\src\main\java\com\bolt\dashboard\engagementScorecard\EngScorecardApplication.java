package com.bolt.dashboard.engagementScorecard;

import java.util.Date;
import java.util.Iterator;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;


public class EngScorecardApplication {


	private static final Logger LOGGER = LogManager.getLogger(EngScorecardApplication.class.getName());
	  
		AnnotationConfigApplicationContext ctx;
	  private EngScoreCard engScorecard= new EngScorecardImplementation(); 
	  private EngScorecardKanbanImplementation engScorecardKanban = new EngScorecardKanbanImplementation();
	  
//  public static void main(String[] args) {
//	new EngScorecardApplication().engScorecardMain("Basecamp", true);
//	
//}
//	  
//	  
//	  public static void main(String[] args) {
//		  new EngScorecardKanbanImplementation().calculations("Dashboard Offshore", "JIRA-KANBAN",true);
//		  LOGGER.info("Engagement Scorecard Collector ended for" + "Dashboard Offshore");
//	}
	  
	  
	  
	   public void engScorecardMain(String projectName, boolean subjective) {
		   
		   LOGGER.info("Engagement Scorecard Collector started for" + projectName);
		   
		   try {
			   
				ConfigurationSetting config = DataConfig.getContext().getBean(ConfigurationSettingRep.class)
						.findByProjectName(projectName).get(0);
				String pType= config.getProjectType();
				if(pType.equalsIgnoreCase("Sprint Wise")) {
					engScorecard.calculateEngScores(projectName, subjective);
				}
				else if(pType.equalsIgnoreCase("Kanban")) {
					Set<ConfigurationToolInfoMetric> metric = config.getMetrics();
					String almType="";
					Iterator iter = metric.iterator();
					while (iter.hasNext()) {
						Object configuration1 = iter.next();
						ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;

						if("ALM".equals(metric1.getToolType())) {
							almType = metric1.getToolName().toUpperCase();
						}
					}
					engScorecardKanban.calculations(projectName, almType,subjective);
					
				}
			   
			   
				ConstantVariable.getLastRun(projectName, "Engagement Scorecard", new Date().getTime(), "SUCCESS");
				LOGGER.info("Engagement Scorecard Collector ended for " + projectName);
			   
		   }catch(Exception ex) {
			   LOGGER.error("Error in Engagement Scorecard "+ex);
			   LOGGER.info("Engagement Scorecard Error Msg "+ ex.getMessage());
			   ConstantVariable.getLastRun(projectName, "Engagement Scorecard", new Date().getTime(), "FAIL");
			   LOGGER.info("Engagement Scorecard " + " Collector failed for " + projectName);
		   }

		   
	   }
	
	   
	
   }
