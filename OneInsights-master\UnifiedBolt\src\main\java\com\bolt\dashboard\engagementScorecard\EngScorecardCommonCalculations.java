package com.bolt.dashboard.engagementScorecard;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.http.ResponseEntity;

import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.core.model.CodeQualityMetric;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.EngScorecardParamData;
import com.bolt.dashboard.core.model.EngScorecardSprint;
import com.bolt.dashboard.core.model.TransitionModel;
import com.bolt.dashboard.util.RestClient;

public class EngScorecardCommonCalculations {
	private static final Logger LOGGER = LogManager.getLogger(EngScorecardCommonCalculations.class);
	
	RestClient restClient = new RestClient();
	Integer[] quater1 = { 7, 8, 9 };
	Integer[] quater4 = { 4, 5, 6 };
	Integer[] quater3 = { 1, 2, 3 };
	Integer[] quater2 = { 10, 11, 12 };
	public long stateTimeTransitionCalculation(List<TransitionModel> transitions2, List<String> states, ALMConfiguration almConfig) {

		long time = 0;
		long startTimeVal = 0;
		long endTimeVal = 0;
		String crState = "";
		for (int index1 = 0; index1 < transitions2.size(); index1++) {
			if (startTimeVal > 0 && (!(transitions2.get(index1).getCrState().toLowerCase().equals(crState)))) {
				endTimeVal = transitions2.get(index1).getMdfDate();
				time = time + (endTimeVal - startTimeVal);
				startTimeVal = 0;
			}
			if (states.indexOf(transitions2.get(index1).getCrState().toLowerCase()) > -1) {
				startTimeVal = transitions2.get(index1).getMdfDate();
				crState = transitions2.get(index1).getCrState();
				if (index1 == (transitions2.size() - 1)) {
					DateTimeZone zoneUTC = DateTimeZone.forID(almConfig.getTimeZone());
					endTimeVal = new DateTime(zoneUTC).getMillis();
					time = time + (endTimeVal - startTimeVal);
				}

			}

		}
		return time;
	}
	
	
	
	
	public void setCoverageScorecard(ScoreCardSprintData scoreCardSprint, Iterable<CodeQuality> codeQualityData,
			EngScorecardSprint engScoreSprint) {
		int codeCoverage = getCodeQualityMetricValue(codeQualityData, scoreCardSprint.getStartDate(), scoreCardSprint.getEndDate(),"coverage");
		setRule(scoreCardSprint.getSprintName(), "Quality", "Unit/Integration Automation Coverage",
				String.valueOf(codeCoverage), engScoreSprint.getEngScoreParamData(), 0);
	}




	public int getCodeQualityMetricValue(
			Iterable<CodeQuality> codeQualityData, long startDate, long endDate, String metricName) {
		
		int metricValue=0;
		
		if (StreamSupport.stream(codeQualityData.spliterator(), false).count() > 0) {
		
		List<CodeQuality> codeQuality = StreamSupport.stream(codeQualityData.spliterator(), false)
				.collect(Collectors.toList());

		codeQuality.sort(Comparator.comparing(CodeQuality::getTimestamp));

		List<CodeQuality> filteredCodeQuality = codeQuality.stream()
				.filter(codeQualityOne -> codeQualityOne.getTimestamp() > startDate
						&& codeQualityOne.getTimestamp() < endDate) // Multiple conditions
				.collect(Collectors.toList());
		
		if (filteredCodeQuality.size() > 0) {
			Optional<CodeQualityMetric> codeQualityMetric = filteredCodeQuality.get(filteredCodeQuality.size() - 1)
					.getMetrics().stream().filter(o -> o.getName().equals(metricName)).findFirst();
			if (codeQualityMetric.isPresent() && (double) codeQualityMetric.get().getValue() > 0) {
				metricValue = (int) Math.round((double) codeQualityMetric.get().getValue());

			}

		}
		}
		return metricValue ;
	}
	
	void setRule(String sprintName, String paramaterName, String subParamaterName, String value,
			List<EngScorecardParamData> list, long timestamp) {
		
		Optional<EngScorecardParamData> conatinParam = list.stream()
				.filter(o -> o.getSubParamaterName().equals(subParamaterName)).findFirst();
		EngScorecardParamData engScoreParam = new EngScorecardParamData();
		if (conatinParam.isPresent()) {

			engScoreParam = conatinParam.get();
		} else {
			list.add(engScoreParam);
		}

		engScoreParam.setParamaterName(paramaterName);
		engScoreParam.setTimestamp(timestamp);
		engScoreParam.setSubParamaterName(subParamaterName);
		engScoreParam.setValue(value);

	}
	
	void setSubjectiveRule(String sprintName, String subParamaterName, String paramaterName, String value,
			EngScorecardParamData param) {

		param.setParamaterName(paramaterName);
		param.setSubParamaterName(subParamaterName);
		param.setValue(value);

	}
	
	
	
	public String convertToDisplayValues(long time, int noOfDays) {
		
		String result = "";
		long seconds = time / 1000;
		long minutes = seconds / 60;
		long hours = minutes / 60;
		// double leftMinutes=minutes%60;
		long days = hours / noOfDays;
		long leftHours = hours % 24;
		long week = days / 7;
		long leftDays = days % 7;

		if (week != 0) {
			result = String.valueOf(week) + "w ";

		}
		if (leftDays != 0) {
			result += String.valueOf(leftDays) + "d ";
		}

		if (leftHours != 0) {
			result += String.valueOf(leftHours) + "h";
		}

		if (result.equals("")) {
			return "0h";
		}

		return result;

	}
	
	public int checkQuarter(int currentMonth) {
		
		List<Integer> quarterList = Arrays.asList(quater4);

		if (quarterList.contains(currentMonth)) {
			return 4;
		}
		quarterList = Arrays.asList(quater3);
		if (quarterList.contains(currentMonth)) {
			return 3;
		}
		quarterList = Arrays.asList(quater2);
		if (quarterList.contains(currentMonth)) {
			return 2;
		}
		return 1;

	}
	public int getQuarterStartMonth(int checkCurrentQuarter) {
		
		if (checkCurrentQuarter == 1) {
			return 7;
		} else if (checkCurrentQuarter == 3) {
			return 1;
		} else if (checkCurrentQuarter == 2) {
			return 10;
		}
		return 4;
	}
	
	public String convertCycleTimeToDays(long cycleTime) {
		
		String result = "";
		long seconds = cycleTime / 1000;
		long minutes = seconds / 60;
		long hours = minutes / 60;
		// double leftMinutes=minutes%60;
		long days = hours / 24;
		result += String.valueOf(days);
		return result;

	}
	
	void calculateExternalDependency(ScoreCardSprintData scoreCardSprint, List<EngScorecardParamData> list,
			ALMConfiguration almConfig, ConfigurationSetting config) {
		
		String url = "";
		String userName = "";
		String pwd = "";
		List<String> linkTypes = new ArrayList<String>();
		String[] tempIssueLinks = almConfig.getIssueLinkTypes();
		if (tempIssueLinks != null) {
			linkTypes.addAll(Arrays.asList(tempIssueLinks));
		}
		for (ConfigurationToolInfoMetric metric1 : config.getMetrics()) {
			if (metric1.getToolType().equalsIgnoreCase("ALM")) {
				url = metric1.getUrl();
				userName = metric1.getUserName();
				pwd = metric1.getPassword();
			}
		}
		String result = "100";
		int count = 0;
		boolean firstCheckFalg = false;
		List<String> closedStates = Arrays.asList(almConfig.getCloseState());
		for (IssueList issue : scoreCardSprint.getIssuesRefined()) {
			ResponseEntity<String> response = restClient.makeGetRestCall(url + "/rest/api/latest/issue/" + issue.getwId(), userName, pwd);
			if (response != null) {
				JSONObject resp = parseAsNewObject(response);
				JSONObject fieldsObj = resp.getJSONObject("fields");
				JSONArray issueLinks = fieldsObj.getJSONArray("issuelinks");

				for (int i = 0; i < issueLinks.length(); i++) {
					try {
						JSONObject linkObj = issueLinks.getJSONObject(i);
						JSONObject outwardIssue = null;
						JSONObject inwardIssue = null;
						if (linkObj.has("outwardIssue")) {
							outwardIssue = linkObj.getJSONObject("outwardIssue");
						}
						if (linkObj.has("inwardIssue")) {
							inwardIssue = linkObj.getJSONObject("inwardIssue");
						}
						JSONObject linkType = (JSONObject) linkObj.getJSONObject("type");
						JSONObject commonIssue = null;
						String type = "";

						if (outwardIssue != null && outwardIssue.get("key") != null) {
							type = linkType.getString("outward");
							commonIssue = outwardIssue;
						}

						if (inwardIssue != null && inwardIssue.get("key") != null) {
							type = linkType.getString("inward");
							commonIssue = inwardIssue;
						}
							if (linkTypes.contains(type) &&commonIssue != null) {
								JSONObject statusObj = commonIssue.getJSONObject("fields").getJSONObject("status");
								String status = statusObj.getString("name").toLowerCase();
								boolean closeStateFlag = checkCloseSatate(closedStates, status);
								if (!closeStateFlag) {
								// Check If it the current time in sprint is Before mid or after mid
								count++;
								long midValueSprint = (scoreCardSprint.getEndDate() + scoreCardSprint.getStartDate())
										/ 2;
								DateTimeZone zoneUTC = DateTimeZone.forID(almConfig.getTimeZone());
								DateTime currentTime = new DateTime(zoneUTC);
								if (!firstCheckFalg) {
									if (currentTime.getMillis() < midValueSprint) {
										result = "75";
									} else {
										result = "0";
									}
								}
							}
						}
					} catch (Exception e) {
						LOGGER.error("Engagement Scorecard Error ", e);
					}
				}
			}
			
		}
		setRule(scoreCardSprint.getSprintName(), "Communication", "External Dependencies",
				result, list, count);
	}
	private JSONObject parseAsNewObject(ResponseEntity<String> response) {
		return (JSONObject) new JSONTokener(response.getBody()).nextValue();
	}
	
	private boolean checkCloseSatate(List<String> closedStates, String status) {
		

		for (String state : closedStates) {
			if (state.equalsIgnoreCase(status)) {
				return true;
			}
		}
		return false;
	}

	
	public List<EngScorecardSprint> getQuarterScoreCardData(List<EngScorecardSprint> engScoreSprintList2,
			DateTime startDate, DateTime endDate) {
		

		List<EngScorecardSprint> filteredScorecard = engScoreSprintList2.stream().filter(
				score -> score.getStartDate() > startDate.getMillis() && score.getStartDate() < endDate.getMillis())
				.collect(Collectors.toList());

		return filteredScorecard;
	}

	
	

}
