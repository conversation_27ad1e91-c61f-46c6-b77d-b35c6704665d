package com.bolt.dashboard.engagementScorecard;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.Days;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.EngScorecard;
import com.bolt.dashboard.core.model.EngScorecardParamData;
import com.bolt.dashboard.core.model.EngScorecardSprint;
import com.bolt.dashboard.core.model.EngScorecardSubjectiveSprintData;
import com.bolt.dashboard.core.model.EngagementScorecardSubjectiveData;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.TransitionModel;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.CodeQualityRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.EngScorecardRepo;
import com.bolt.dashboard.core.repository.EngScorecardSubjectiveDataRepo;
import com.bolt.dashboard.core.repository.ProjectIterationRepo;
import com.bolt.dashboard.core.repository.TransitionRepo;
import com.bolt.dashboard.util.RestClient;

public class EngScorecardImplementation implements EngScoreCard {
	private static final Logger LOGGER = LogManager.getLogger(EngScorecardImplementation.class.getName());
	AnnotationConfigApplicationContext ctx = DataConfig.getContext();

	ProjectIterationRepo iterationRepo = null;
	List<IterationOutModel> iterations = null;
	VelocityCalculations calculate = new VelocityCalculations();
	EngScorecardRepo engRepo = null;
	EngScorecard scoreCard = null;
	List<EngScorecardSprint> engScoreSprintList = null;
	EngScorecardSprint engScoreSprint = null;
	ALMConfigRepo almConfigRepo = null;
	ALMConfiguration almConfig = null;
	TransitionRepo transRepo = null;
	List<TransitionModel> transitions = null;
	BuildToolRep buildRepo = null;
	List<BuildTool> builds = null;
	String projectName = "";
	EngScorecardSubjectiveDataRepo engScoreCardSubjetiveRepo = null;
	EngagementScorecardSubjectiveData engScorecardSubjetiveData = null;
	CodeQualityRep codeQualityRepo = null;
	Iterable<CodeQuality> codeQualityData = null;
	ConfigurationSettingRep configRep = null;
	ConfigurationSetting config = null;
	RestClient restClient = new RestClient();
	EngScorecardCommonCalculations engScoreCommonCalc = new EngScorecardCommonCalculations();

	@Override
	public void calculateEngScores(String pName, boolean subjetive) {
		projectName = pName;
		
			getIntialData(pName);
			List<ScoreCardSprintData> sprintData = calculate.calcVelocity(iterations, almConfig);
			List<String> removeSprint = new ArrayList<String>();
			String[] tempfilter = null;
			if (almConfig.getFilteredSprints() != null) {
				tempfilter = almConfig.getFilteredSprints();
			}
			if (tempfilter != null) {
				removeSprint.addAll(Arrays.asList());
			}

			List<ScoreCardSprintData> filteredSprint = sprintData.stream()
					.filter(sprint -> !removeSprint.contains(sprint.getSprintName())).collect(Collectors.toList());

			calculateAllRules(filteredSprint, subjetive);
			engRepo.save(scoreCard);
		

	}
	


	private List<EngScorecardSprint> calculateAllRules(List<ScoreCardSprintData> sprintData, boolean subjetive) {
		List<EngScorecardSprint> engScoreSprintListNew = engScoreSprintList;
		// List<String> closeState = Arrays.asList(almConfig.getCloseState());
		for (ScoreCardSprintData scoreCardSprint : sprintData) {
			Optional<EngScorecardSprint> contain = engScoreSprintList.stream()
					.filter(o -> o.getSprintName().equals(scoreCardSprint.getSprintName())).findFirst();
			if (!contain.isPresent()) {
				engScoreSprint = new EngScorecardSprint();
				engScoreSprintList.add(engScoreSprint);
			} else {
				engScoreSprint = contain.get();
			}
			List<IssueList> refinedList = scoreCardSprint.getIssuesRefined();
			long cycleTime = 0;
			long leadTime = 0;
			List<String> cycleTimeFields = new ArrayList<String>();
			if (almConfig.getCycleTimeStates() != null) {
				cycleTimeFields = Arrays.asList(almConfig.getCycleTimeStates());
			}
			for (IssueList issue : refinedList) {
				transitions = issue.getTransitions();
				if (transitions != null) {
					transitions.sort(Comparator.comparing(TransitionModel::getMdfDate));
					cycleTime = cycleTime + engScoreCommonCalc.stateTimeTransitionCalculation(transitions,
							cycleTimeFields, almConfig);
				}
				if (transitions == null) {
					transitions = new ArrayList<TransitionModel>();
				}
				Optional<TransitionModel> transitionOptional = transitions.stream()
						.filter(o -> o.getMdfDate().equals(issue.getCreatedDate())).findFirst();
				if (!transitionOptional.isPresent()) {
					TransitionModel trans = new TransitionModel();
					trans.setCrState("created");
					trans.setMdfDate(issue.getCreatedDate());
					transitions.add(0, trans);
				}
				List<String> leadTimeFields = new ArrayList<String>();
				leadTimeFields.addAll(cycleTimeFields);
				leadTimeFields.add("created");
				leadTime = leadTime
						+ engScoreCommonCalc.stateTimeTransitionCalculation(transitions, leadTimeFields, almConfig);
			}
			if (refinedList.size() > 0) {
				cycleTime = cycleTime / refinedList.size();
				leadTime = leadTime / refinedList.size();
			}
			setJiraRelatedRulesSet(scoreCardSprint, cycleTime, leadTime, engScoreSprintListNew);

			builds = buildRepo.findByNameAndTimestampBetween(projectName, scoreCardSprint.getStartDate(),
					scoreCardSprint.getEndDate());
			List<Object> buildValues = calculateMTTRAndChangeFailureRate(builds);
			engScoreCommonCalc.setRule(scoreCardSprint.getSprintName(), "Builds", "MTTR",
					engScoreCommonCalc.convertToDisplayValues((long) buildValues.get(0), 24),
					engScoreSprint.getEngScoreParamData(), (long) buildValues.get(0));
			engScoreCommonCalc.setRule(scoreCardSprint.getSprintName(), "Builds", "Change Failure Rate",
					String.valueOf(buildValues.get(1)), engScoreSprint.getEngScoreParamData(), 0);

			engScoreCommonCalc.setCoverageScorecard(scoreCardSprint, codeQualityData, engScoreSprint);
			if (subjetive) {
				calculateSubjective(projectName, scoreCardSprint, contain, engScoreSprintListNew);
			}
		}
		return engScoreSprintListNew;
	}

	private void calculateSubjective(String projectName2, ScoreCardSprintData scoreCardSprint,
			Optional<EngScorecardSprint> c, List<EngScorecardSprint> engScoreSprintListNew) {
		if (c.isPresent()) {
			engScoreSprint = c.get();
		}
		if (engScorecardSubjetiveData != null) {
			Optional<EngScorecardSubjectiveSprintData> sprintSubjetive = engScorecardSubjetiveData
					.getEngScorecardSprintData().stream()
					.filter(o -> o.getSprintName().equals(scoreCardSprint.getSprintName())).findFirst();
			if (sprintSubjetive.isPresent()) {
				// Sprint Planning
				List<EngScorecardParamData> paramData = engScoreSprint.getEngScoreParamData();
				Optional<EngScorecardParamData> containParam = paramData.stream()
						.filter(o -> o.getSubParamaterName().equals("Sprint Planning")).findFirst();
				EngScorecardParamData param = new EngScorecardParamData();
				if (containParam.isPresent()) {
					param = containParam.get();

				} else {
					paramData.add(param);
				}

				String sprintPlanning = "0";

				if (scoreCardSprint.getCommitedSp() > 0 && sprintSubjetive.get().getTeamSize() > 0) {
					double commited = (int) scoreCardSprint.getCommitedSp();
					double capacity = sprintSubjetive.get().getTeamSize() * 8.0;
					double val = capacity - commited;
					val = Math.abs(val);
					sprintPlanning = String.valueOf(100 - ((int) (val * 100 / capacity)));
				}
				
				engScoreCommonCalc.setSubjectiveRule(scoreCardSprint.getSprintName(), "Sprint Planning",
						"Communication", sprintPlanning, param);

				// Daily Check Point
				containParam = paramData.stream().filter(o -> o.getSubParamaterName().equals("Daily Check Point"))
						.findFirst();
				EngScorecardParamData dailyCheckPoint = new EngScorecardParamData();
				if (containParam.isPresent()) {
					dailyCheckPoint = containParam.get();

				} else {
					paramData.add(dailyCheckPoint);
				}

				String dailyCheckDone = getSubjetiveValue(sprintSubjetive.get().getDailyCheckPoint());

				engScoreCommonCalc.setSubjectiveRule(scoreCardSprint.getSprintName(), "Daily Check Point",
						"Communication", dailyCheckDone, dailyCheckPoint);
				// Readiness index N+1
				containParam = paramData.stream().filter(o -> o.getSubParamaterName().equals("N+1")).findFirst();
				EngScorecardParamData readinessIndex1 = new EngScorecardParamData();
				if (containParam.isPresent()) {
					readinessIndex1 = containParam.get();

				} else {
					paramData.add(readinessIndex1);
				}

				String readinessIndex1Val = String.valueOf(sprintSubjetive.get().getReadinessIndex1().get("score"));

				engScoreCommonCalc.setSubjectiveRule(scoreCardSprint.getSprintName(), "N+1", "Readiness Index",
						readinessIndex1Val, readinessIndex1);

				// Readiness index N+2
				containParam = paramData.stream().filter(o -> o.getSubParamaterName().equals("N+2")).findFirst();
				EngScorecardParamData readinessIndex2 = new EngScorecardParamData();
				if (containParam.isPresent()) {
					readinessIndex2 = containParam.get();

				} else {
					paramData.add(readinessIndex2);
				}

				String readinessIndex2Val = String.valueOf(sprintSubjetive.get().getReadinessIndex2().get("score"));

				engScoreCommonCalc.setSubjectiveRule(scoreCardSprint.getSprintName(), "N+2", "Readiness Index",
						readinessIndex2Val, readinessIndex2);

				// Readiness index N+6
				containParam = paramData.stream().filter(o -> o.getSubParamaterName().equals("N+6")).findFirst();
				EngScorecardParamData readinessIndex3 = new EngScorecardParamData();
				if (containParam.isPresent()) {
					readinessIndex3 = containParam.get();

				} else {
					paramData.add(readinessIndex3);
				}

				String readinessIndex3Val = String.valueOf(sprintSubjetive.get().getReadinessIndex3().get("score"));

				engScoreCommonCalc.setSubjectiveRule(scoreCardSprint.getSprintName(), "N+6", "Readiness Index",
						readinessIndex3Val, readinessIndex3);
				// Sprint Goals Met
				containParam = paramData.stream().filter(o -> o.getSubParamaterName().equals("Sprint Goals Met"))
						.findFirst();
				EngScorecardParamData sprintGoals = new EngScorecardParamData();
				if (containParam.isPresent()) {
					sprintGoals = containParam.get();
				} else {
					paramData.add(sprintGoals);
				}

				String sprintGoalsVal = getSubjetiveValue(sprintSubjetive.get().getDailyCheckPoint());

				engScoreCommonCalc.setSubjectiveRule(scoreCardSprint.getSprintName(), "Sprint Goals Met", "Quality",
						sprintGoalsVal, sprintGoals);

			}

		}
	}

	String getSubjetiveValue(Map<String, String> subjectiveMap) {
		if (subjectiveMap.get("score").equalsIgnoreCase("green")) {
			return "100";
		} else if (subjectiveMap.get("score").equalsIgnoreCase("amber")) {
			return "75";
		}

		return "0";
	}

	private List<Object> calculateMTTRAndChangeFailureRate(List<BuildTool> builds2) {
		Collections.reverse(builds);
		builds.sort(Comparator.comparing(BuildTool::getTimestamp));
		boolean failed = false;
		long failureTimeStamp = 0;
		long successTimeStamp = 0;
		// long differneceTimeStamp=0;
		long mttr = 0;
		int mttrCount = 0;
		int failedCount = 0;
		int totalCount = 0;
		for (BuildTool build : builds) {
			long duration = 0;
			String result = "";
			totalCount++;
			for (BuildToolMetric metric : build.getMetrics()) {

				if (metric.getName().equalsIgnoreCase("duration")) {
					duration = Long.parseLong(metric.getValue().toString());
				}
				if (metric.getName().equalsIgnoreCase("result")) {
					result = metric.getValue().toString();
				}
			}

			if (duration != 0) {

				if (result.equalsIgnoreCase("failure")) {
					failedCount++;
					if (!failed) {
						failureTimeStamp = build.getTimestamp();
						failed = true;
					}
				} else if (failed && result.equalsIgnoreCase("success")) {

					failed = false;
					successTimeStamp = build.getTimestamp();
					mttr = mttr + (successTimeStamp - failureTimeStamp);
					mttrCount++;

				}

			}

		}
		String percentage = "0";
		List<Object> values = new ArrayList<>();
		if (failedCount != 0) {
			percentage = String.valueOf((failedCount * 100) / totalCount);
		}
		if (mttr > 0) {
			mttr = mttr / mttrCount;
		}
		values.add(mttr);
		values.add(percentage);
		return values;
	}

	private void setJiraRelatedRulesSet(ScoreCardSprintData scoreCardSprint, long cycleTime, long leadTime,
			List<EngScorecardSprint> engScoreSprintListNew) {
		engScoreSprint.setEndDate(scoreCardSprint.getEndDate());
		engScoreSprint.setStartDate(scoreCardSprint.getStartDate());
		engScoreSprint.setSprintName(scoreCardSprint.getSprintName());
		engScoreSprint.setState(scoreCardSprint.getState());
		if (scoreCardSprint.getState().equalsIgnoreCase("active")) {
			engScoreCommonCalc.setRule(scoreCardSprint.getSprintName(), "Committed Vs Completed", "Committed",
					String.valueOf((int) scoreCardSprint.getCommitedSp()), engScoreSprint.getEngScoreParamData(), 0);

			// sendNotification(scoreCardSprint);

		}
		// Velocity Rule
		engScoreCommonCalc.setRule(scoreCardSprint.getSprintName(), "Committed Vs Completed", "Velocity",
				String.valueOf((int) scoreCardSprint.getCompletedSp()), engScoreSprint.getEngScoreParamData(), 0);
		// Estimation Accuracy
		String estimationAccuracy = "";
		String compeletion = "";
		if (scoreCardSprint.getCommitedSp() > 0 && scoreCardSprint.getCompletedSp() > 0) {
			int estval = (int) (scoreCardSprint.getCommitedSp() * 100);
			estval = (int) (estval / scoreCardSprint.getCompletedSp());
			estimationAccuracy = String.valueOf(estval);
			estval = (int) (scoreCardSprint.getCompletedSp() * 100);
			estval = (int) (estval / scoreCardSprint.getCommitedSp());
			compeletion = String.valueOf(estval);
		} else if (scoreCardSprint.getCompletedSp() == 0 || scoreCardSprint.getCommitedSp() == 0) {
			estimationAccuracy = "0";
			compeletion = "0";
		}
		engScoreCommonCalc.setRule(scoreCardSprint.getSprintName(), "Committed Vs Completed", "Completion", compeletion,
				engScoreSprint.getEngScoreParamData(), 0);
		engScoreCommonCalc.setRule(scoreCardSprint.getSprintName(), "Committed Vs Completed", "Estimation Accuracy",
				estimationAccuracy, engScoreSprint.getEngScoreParamData(), 0);
		// Cycle Time
		engScoreCommonCalc.setRule(scoreCardSprint.getSprintName(), "Committed Vs Completed", "Cycle Time",
				engScoreCommonCalc.convertToDisplayValues(cycleTime, 24), engScoreSprint.getEngScoreParamData(),
				cycleTime);

		// Lead Time
		engScoreCommonCalc.setRule(scoreCardSprint.getSprintName(), "Committed Vs Completed", "Lead Time",
				engScoreCommonCalc.convertToDisplayValues(leadTime, 24), engScoreSprint.getEngScoreParamData(),
				leadTime);
		// Zero Midsprint changes
		String midSprintChanges = "0";

		// String.valueOf(scoreCardSprint.getIssuesCommitedAfter().size());
		double totalSpChanged = (scoreCardSprint.getCommitedAfterSp() + scoreCardSprint.getRemovedSp());
		if (scoreCardSprint.getCommitedSp() != 0) {
			int val = (int) (100 - ((totalSpChanged * 100) / scoreCardSprint.getCommitedSp()));
			if (val < 0) {
				val = 0;
			}
			midSprintChanges = String.valueOf(val);
		} else {
			midSprintChanges = "100";
		}

		engScoreCommonCalc.setRule(scoreCardSprint.getSprintName(), "Communication", "Zero Mid Sprint Changes",
				midSprintChanges, engScoreSprint.getEngScoreParamData(), 0);

		 if (engScoreSprint.getState().equalsIgnoreCase("active")) {

		try {
			engScoreCommonCalc.calculateExternalDependency(scoreCardSprint, engScoreSprint.getEngScoreParamData(),
					almConfig, config);
		} catch (Exception e) {
			LOGGER.info("Problem In External Dependency Engagement Score Card Calculation");
			LOGGER.error(e.getLocalizedMessage());
		}
		 }

	}

	private void sendNotification(ScoreCardSprintData scoreCardSprint) {

		// DateTime
		DateTimeZone timeZone = DateTimeZone.forID(almConfig.getTimeZone());
		DateTime start = new DateTime(timeZone);
		DateTime end = new DateTime(scoreCardSprint.getEndDate(), timeZone);

		int days = Days.daysBetween(end.withTimeAtStartOfDay(), start.withTimeAtStartOfDay()).getDays();

		if (days < 2) {
			// if()
			Optional<EngScorecardSubjectiveSprintData> sprintSubjetive = Optional.empty();
			if (engScorecardSubjetiveData != null) {
				sprintSubjetive = engScorecardSubjetiveData.getEngScorecardSprintData().stream()
						.filter(o -> o.getSprintName().equals(scoreCardSprint.getSprintName())).findFirst();
			}
			LOGGER.info(sprintSubjetive);

//			if (sprintSubjetive == null || !sprintSubjetive.isPresent() || sprintSubjetive.get().getTeamSize() > 0.0) {
//
//				CollaborativeMailModel mail = new CollaborativeMailModel();
//				mail.setToAddress("<EMAIL>");
//				mail.setFileName("");
//				mail.setComment("Hi Team,<br /> <br /> Please fill the Engagement Scorecard Subjective data for "
//						+ " <b>" + projectName + "</b>. <br /> <br /> <b> Thanks & Regards, <br /> Team BrillioOne.ai insights team </b>");
//				mail.setSubject("Reminder for Scorecard Subjective Data BOLT");
//				EmailNotification email = new EmailNotification();
//				email.sendMail(mail);
//
//			}

		}
	}

	private void getIntialData(String pName) {

		iterationRepo = ctx.getBean(ProjectIterationRepo.class);
		engRepo = ctx.getBean(EngScorecardRepo.class);
		scoreCard = engRepo.findByPName(pName);
		if (scoreCard != null) {
			engScoreSprintList = scoreCard.getEngScoreCardSprint();

		} else {
			scoreCard = new EngScorecard();
			scoreCard.setpName(pName);

		}
		engScoreSprintList = scoreCard.getEngScoreCardSprint();

		iterations = iterationRepo.findByPName(pName);

		almConfigRepo = ctx.getBean(ALMConfigRepo.class);
		almConfig = almConfigRepo.findByProjectName(pName).get(0);
		transRepo = ctx.getBean(TransitionRepo.class);

		buildRepo = ctx.getBean(BuildToolRep.class);
		engScoreCardSubjetiveRepo = ctx.getBean(EngScorecardSubjectiveDataRepo.class);
		engScorecardSubjetiveData = engScoreCardSubjetiveRepo.findByPName(pName);

		codeQualityRepo = ctx.getBean(CodeQualityRep.class);
		codeQualityData = codeQualityRepo.findByName(pName);
		configRep = ctx.getBean(ConfigurationSettingRep.class);
		config = configRep.findByProjectName(projectName).get(0);

	}

}