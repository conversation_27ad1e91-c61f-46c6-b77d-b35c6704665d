package com.bolt.dashboard.engagementScorecard;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.EngScorecard;
import com.bolt.dashboard.core.model.EngScorecardParamData;
import com.bolt.dashboard.core.model.EngScorecardSprint;
import com.bolt.dashboard.core.model.EngScorecardSubjectiveSprintData;
import com.bolt.dashboard.core.model.EngagementScorecardSubjectiveData;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.TransitionModel;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.CodeQualityRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.EngScorecardRepo;
import com.bolt.dashboard.core.repository.EngScorecardSubjectiveDataRepo;
import com.bolt.dashboard.core.repository.ProjectIterationRepo;

public class EngScorecardKanbanImplementation {
	private static final Logger LOGGER = LogManager.getLogger(EngScorecardKanbanImplementation.class.getName());
	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
	List<IterationOutModel> iterations = null;
	ALMConfiguration almConfig = null;
	ALMConfigRepo almConfigRepo = null;
	ProjectIterationRepo authorRepo = null;
	EngScorecardRepo engRepo = null;
	EngScorecard scoreCard;
	List<EngScorecardSprint> weekList = null;
	EngScorecardSprint engScoreWeek = null;
	ConfigurationSetting config;
	ConfigurationSettingRep configRep;
	String projectName;
	EngagementScorecardSubjectiveData engScorecardSubjetiveData = null;
	EngScorecardSubjectiveDataRepo engScoreCardSubjetiveRepo = null;
	List<TransitionModel> transitions = null;

	EngScorecardCommonCalculations engScoreCommonCalc = new EngScorecardCommonCalculations();
	CodeQualityRep codeQualityRepo = null;
	Iterable<CodeQuality> codeQualityData = null;

	void calculateAllRules(List<ScoreCardSprintData> weeksData, boolean subjetive) {
		List<EngScorecardSprint> engScoreweekListNew = weekList;
		for (ScoreCardSprintData scoreCardweek : weeksData) {
			Optional<EngScorecardSprint> contain = weekList.stream()
					.filter(o -> o.getSprintName().equals(scoreCardweek.getSprintName())).findFirst();
			if (!contain.isPresent()) {
				engScoreWeek = new EngScorecardSprint();
				weekList.add(engScoreWeek);
			} else {
				engScoreWeek = contain.get();
			}

			setJiraRelatedRulesSet(scoreCardweek, engScoreweekListNew);

			// Code Coverage
			engScoreCommonCalc.setCoverageScorecard(scoreCardweek, codeQualityData, engScoreWeek);

			// Violations

			int violations = engScoreCommonCalc.getCodeQualityMetricValue(codeQualityData, scoreCardweek.getStartDate(),
					scoreCardweek.getEndDate(), "violations");
			engScoreCommonCalc.setRule(scoreCardweek.getSprintName(), "Quality", "Violations",
					String.valueOf(violations), engScoreWeek.getEngScoreParamData(), 0);
			
			//Critical Violations
		violations = engScoreCommonCalc.getCodeQualityMetricValue(codeQualityData, scoreCardweek.getStartDate(),
					scoreCardweek.getEndDate(), "critical_violations");
			engScoreCommonCalc.setRule(scoreCardweek.getSprintName(), "Quality", "Critical Violations",
					String.valueOf(violations), engScoreWeek.getEngScoreParamData(), 0);
			//Blocker Violations
			 violations = engScoreCommonCalc.getCodeQualityMetricValue(codeQualityData, scoreCardweek.getStartDate(),
					scoreCardweek.getEndDate(), "blocker_violations");
			engScoreCommonCalc.setRule(scoreCardweek.getSprintName(), "Quality", "Blocker Violations",
					String.valueOf(violations), engScoreWeek.getEngScoreParamData(), 0);
			
			// Technical Debt
			int technicalDebt = engScoreCommonCalc.getCodeQualityMetricValue(codeQualityData,
					scoreCardweek.getStartDate(), scoreCardweek.getEndDate(), "sqale_index");
			engScoreCommonCalc.setRule(scoreCardweek.getSprintName(), "Quality", "Technical Debt",
					engScoreCommonCalc.convertToDisplayValues(technicalDebt,24), engScoreWeek.getEngScoreParamData(), technicalDebt);

			// Complexity
			int complexity = engScoreCommonCalc.getCodeQualityMetricValue(codeQualityData, scoreCardweek.getStartDate(),
					scoreCardweek.getEndDate(), "complexity");
			engScoreCommonCalc.setRule(scoreCardweek.getSprintName(), "Quality", "Complexity",
					String.valueOf(complexity), engScoreWeek.getEngScoreParamData(), 0);

			if (subjetive) {
				calculateSubjective(projectName, scoreCardweek, contain, engScoreweekListNew);
			}
		}
	}

	private void calculateSubjective(String projectName2, ScoreCardSprintData scoreCardweek,
			Optional<EngScorecardSprint> contain, List<EngScorecardSprint> engScoreweekListNew) {
		if (contain.isPresent()) {
			engScoreWeek = contain.get();
		}
		if (engScorecardSubjetiveData != null) {
			Optional<EngScorecardSubjectiveSprintData> weekSubjetive = engScorecardSubjetiveData
					.getEngScorecardSprintData().stream()
					.filter(o -> o.getSprintName().equals(scoreCardweek.getSprintName())).findFirst();
			if (weekSubjetive.isPresent()) {
				List<EngScorecardParamData> paramData = engScoreWeek.getEngScoreParamData();
				// Backlog Grooming
				Optional<EngScorecardParamData> containParam = paramData.stream()
						.filter(o -> o.getSubParamaterName().equals("Backlog Grooming")).findFirst();
				EngScorecardParamData param = new EngScorecardParamData();
				if (containParam.isPresent()) {
					param = containParam.get();

				} else {
					paramData.add(param);
				}
				String backlogGrooming = new EngScorecardImplementation()
						.getSubjetiveValue(weekSubjetive.get().getBacklogGromming());
				engScoreCommonCalc.setSubjectiveRule(scoreCardweek.getSprintName(), "Backlog Grooming", "Communication",
						backlogGrooming, param);

				// Daily checkpoint
				containParam = paramData.stream().filter(o -> o.getSubParamaterName().equals("Daily Check Point"))
						.findFirst();
				EngScorecardParamData dailyCheckPoint = new EngScorecardParamData();
				if (containParam.isPresent()) {
					dailyCheckPoint = containParam.get();

				} else {
					paramData.add(dailyCheckPoint);
				}

				String dailyCheckDone = new EngScorecardImplementation()
						.getSubjetiveValue(weekSubjetive.get().getDailyCheckPoint());
				engScoreCommonCalc.setSubjectiveRule(scoreCardweek.getSprintName(), "Daily Check Point",
						"Communication", dailyCheckDone, dailyCheckPoint);

				containParam = paramData.stream().filter(o -> o.getSubParamaterName().equals("N+1")).findFirst();
				EngScorecardParamData readinessIndex1 = new EngScorecardParamData();
				if (containParam.isPresent()) {
					readinessIndex1 = containParam.get();

				} else {
					paramData.add(readinessIndex1);
				}

				String readinessIndex1Val = String.valueOf(weekSubjetive.get().getReadinessIndex1().get("score"));

				engScoreCommonCalc.setSubjectiveRule(scoreCardweek.getSprintName(), "N+1", "Readiness Index",
						readinessIndex1Val, readinessIndex1);

				// Readiness index N+2
				containParam = paramData.stream().filter(o -> o.getSubParamaterName().equals("N+2")).findFirst();
				EngScorecardParamData readinessIndex2 = new EngScorecardParamData();
				if (containParam.isPresent()) {
					readinessIndex2 = containParam.get();

				} else {
					paramData.add(readinessIndex2);
				}

				String readinessIndex2Val = String.valueOf(weekSubjetive.get().getReadinessIndex2().get("score"));

				engScoreCommonCalc.setSubjectiveRule(scoreCardweek.getSprintName(), "N+2", "Readiness Index",
						readinessIndex2Val, readinessIndex2);

				// Readiness index N+6
				containParam = paramData.stream().filter(o -> o.getSubParamaterName().equals("N+6")).findFirst();
				EngScorecardParamData readinessIndex3 = new EngScorecardParamData();
				if (containParam.isPresent()) {
					readinessIndex3 = containParam.get();

				} else {
					paramData.add(readinessIndex3);
				}

				String readinessIndex3Val = String.valueOf(weekSubjetive.get().getReadinessIndex3().get("score"));

				engScoreCommonCalc.setSubjectiveRule(scoreCardweek.getSprintName(), "N+6", "Readiness Index",
						readinessIndex3Val, readinessIndex3);

			}
		}

	}

	private void setJiraRelatedRulesSet(ScoreCardSprintData scoreCardSprint,
			List<EngScorecardSprint> engScoreSprintListNew) {
		engScoreWeek.setEndDate(scoreCardSprint.getEndDate());
		engScoreWeek.setStartDate(scoreCardSprint.getStartDate());
		engScoreWeek.setSprintName(scoreCardSprint.getSprintName());
		engScoreWeek.setState(scoreCardSprint.getState());

		// External Dependency
		if (engScoreWeek.getState().equalsIgnoreCase("active")) {
			try {
				engScoreCommonCalc.calculateExternalDependency(scoreCardSprint, engScoreWeek.getEngScoreParamData(),
						almConfig, config);
			} catch (Exception e) {
				LOGGER.info("Problem In External Dependency Engagement Score Card Calculation");
				LOGGER.error(e.getLocalizedMessage());
			}
		}

		// CycleTime
		List<String> cycleTimeStates = new ArrayList<String>();
		if (almConfig.getCycleTimeStates() != null) {
			cycleTimeStates = Arrays.asList(almConfig.getCycleTimeStates());
		}
		long cycleTime = 0;
		long leadTime = 0;
		List<IssueList> closedIssues = scoreCardSprint.getIssuesComplted();

		for (IssueList issue : closedIssues) {
			transitions = issue.getTransitions();
			if (transitions != null) {
				transitions.sort(Comparator.comparing(TransitionModel::getMdfDate));
				cycleTime = cycleTime
						+ engScoreCommonCalc.stateTimeTransitionCalculation(transitions, cycleTimeStates, almConfig);
			}

			if (transitions == null) {
				transitions = new ArrayList<TransitionModel>();
			}
			Optional<TransitionModel> transitionOptional = transitions.stream()
					.filter(o -> o.getMdfDate().equals(issue.getCreatedDate())).findFirst();
			if (!transitionOptional.isPresent()) {
				TransitionModel trans = new TransitionModel();
				trans.setCrState("created");
				trans.setMdfDate(issue.getCreatedDate());
				transitions.add(0, trans);
			}
			List<String> leadTimeFields = new ArrayList<String>();
			leadTimeFields.addAll(cycleTimeStates);
			leadTimeFields.add("created");
			leadTime = leadTime
					+ engScoreCommonCalc.stateTimeTransitionCalculation(transitions, leadTimeFields, almConfig);

		}
		if (closedIssues.size() > 0) {
			cycleTime = cycleTime / closedIssues.size();
			leadTime = leadTime / closedIssues.size();
		}
		engScoreCommonCalc.setRule(scoreCardSprint.getSprintName(), "Committed Vs Completed", "Cycle Time",
				engScoreCommonCalc.convertToDisplayValues(cycleTime, 24), engScoreWeek.getEngScoreParamData(), cycleTime);

		engScoreCommonCalc.setRule(scoreCardSprint.getSprintName(), "Committed Vs Completed", "Lead Time",
				engScoreCommonCalc.convertToDisplayValues(leadTime, 24), engScoreWeek.getEngScoreParamData(), leadTime);
		int closedIssuesNo = closedIssues.size();

		engScoreCommonCalc.setRule(scoreCardSprint.getSprintName(), "Committed Vs Completed", "Closed Issues",
				String.valueOf(closedIssuesNo), engScoreWeek.getEngScoreParamData(), 0);
		int createdIssues = scoreCardSprint.getIssuesCommited().size();

		//Added Issues 
		engScoreCommonCalc.setRule(scoreCardSprint.getSprintName(), "Committed Vs Completed", "Added Issues",
				String.valueOf(createdIssues), engScoreWeek.getEngScoreParamData(), 0);
		
		// completion
		String completion = "0";

		if (createdIssues > 0 && closedIssuesNo > 0) {
			int estval = closedIssuesNo * 100;
			estval = (int) (estval / createdIssues);
			completion = String.valueOf(estval);

		}
		engScoreCommonCalc.setRule(scoreCardSprint.getSprintName(), "Committed Vs Completed", "Completion", completion,
				engScoreWeek.getEngScoreParamData(), 0);

		// CCR tickets
		int noOfCCRTickets = 0;
		for (IssueList iss : scoreCardSprint.getIssuesCommited()) {

			List<String> labels = iss.getLabel();
			if (labels.contains("CCR")) {
				noOfCCRTickets++;
			}

		}

		engScoreCommonCalc.setRule(scoreCardSprint.getSprintName(), "Committed Vs Completed", "CCRTickets",
				String.valueOf(noOfCCRTickets), engScoreWeek.getEngScoreParamData(), 0);

	}

	void calculations(String projectName, String almType, boolean subjective) {
		getInititalData(projectName, almType);
		if (iterations != null && iterations.size() > 0) {
			List<MonogOutMetrics> m = new ArrayList<MonogOutMetrics>();
			
			for (IterationOutModel itr : iterations) {
				m.addAll(itr.getMetrics());
				
			}
			List<ScoreCardSprintData> weeksData = new WeeklyAddedClosedCalculations()
					.calculateAddedClosed(m, almConfig);
			System.out.println(weeksData);

			calculateAllRules(weeksData, subjective);
			engRepo.save(scoreCard);
		}

	}

	void getInititalData(String projectName, String almType) {
		authorRepo = ctx.getBean(ProjectIterationRepo.class);
		almConfigRepo = ctx.getBean(ALMConfigRepo.class);
		iterations = authorRepo.findByPNameAndPAlmType(projectName, almType);
		almConfig = almConfigRepo.findByProjectName(projectName).get(0);
		engRepo = ctx.getBean(EngScorecardRepo.class);
		scoreCard = engRepo.findByPName(projectName);
		if (scoreCard != null) {
			weekList = scoreCard.getEngScoreCardSprint();

		} else {
			scoreCard = new EngScorecard();
			scoreCard.setpName(projectName);

		}
		weekList = scoreCard.getEngScoreCardSprint();
		configRep = ctx.getBean(ConfigurationSettingRep.class);
		config = configRep.findByProjectName(projectName).get(0);
		engScoreCardSubjetiveRepo = ctx.getBean(EngScorecardSubjectiveDataRepo.class);
		engScorecardSubjetiveData = engScoreCardSubjetiveRepo.findByPName(projectName);
		codeQualityRepo = ctx.getBean(CodeQualityRep.class);
		codeQualityData = codeQualityRepo.findByName(projectName);
	}

}
