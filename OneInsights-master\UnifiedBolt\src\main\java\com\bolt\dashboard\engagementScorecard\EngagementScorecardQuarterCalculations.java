package com.bolt.dashboard.engagementScorecard;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.EngScorecard;
import com.bolt.dashboard.core.model.EngScorecardParamData;
import com.bolt.dashboard.core.model.EngScorecardSprint;
import com.bolt.dashboard.core.model.EngScorecardSubjectiveSprintData;
import com.bolt.dashboard.core.model.EngagementScorecardSubjectiveData;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.EngScorecardRepo;
import com.bolt.dashboard.core.repository.EngScorecardSubjectiveDataRepo;

public class EngagementScorecardQuarterCalculations {

	
    EngScorecardCommonCalculations engScoreCommon= new EngScorecardCommonCalculations();
	EngScoreCardQuarterData engScorecardQuarter = new EngScoreCardQuarterData();
	ConfigurationSettingRep configRep = null;
	ConfigurationSetting config = null;
	List<String> quarterCats = new ArrayList<String>();
	List<EngScorecardQuarterSeries> velocitySeries = new ArrayList<>();
	List<EngScorecardQuarterSeries> completionSeries = new ArrayList<>();
	List<EngScorecardQuarterSeries> cycleTimeSeries = new ArrayList<>();
	String colors[] = { "#7197EF", "#D68F2D", "#AAA", "#70BE34" };
	EngScorecardQuarterSeries velocity = new EngScorecardQuarterSeries();
	EngScorecardQuarterSeries completion = new EngScorecardQuarterSeries();
	EngScorecardQuarterSeries changeFaulure = new EngScorecardQuarterSeries();
	EngScorecardQuarterSeries cycleTime = new EngScorecardQuarterSeries();
	EngScorecardQuarterSeries leadTime = new EngScorecardQuarterSeries();
	EngScorecardQuarterSeries addedIssues = new EngScorecardQuarterSeries();
	EngScorecardQuarterSeries closedIssues = new EngScorecardQuarterSeries();
	EngScorecardQuarterSeries mttr = new EngScorecardQuarterSeries();
	EngScorecardSubjectiveDataRepo engSubjectiveRepo = null;
	// List<EngagementScorecardSubjectiveData> engSubjectiveData=null;
	List<EngScorecardSubjectiveSprintData> engSubjectiveDataSprint = null;
	private static final Logger LOGGER = LogManager.getLogger(EngagementScorecardQuarterCalculations.class);
	AnnotationConfigApplicationContext ctx = DataConfig.getContext();

	EngScorecardRepo engRepo = null;
	EngScorecard scoreCard = null;
	List<EngScorecardSprint> engScoreSprintList = null;
	ALMConfigRepo almConfigRepo = null;
	ALMConfiguration almConfig = null;

	String projectName = "";
	EngScorecardSubjectiveDataRepo engScoreCardSubjetiveRepo = null;
	EngagementScorecardSubjectiveData engScorecardSubjetiveData = null;

//	public static void main(String[] args) {
//		new EngagementScorecardQuarterCalculations().calculateQuarters("Recommendations");
//	}
	public EngScoreCardQuarterData calculateQuarters(String pName) {
		
		getIntialData(pName);
		DateTime currentTime = new DateTime();
		int currentMonth = currentTime.getMonthOfYear();

		// int index=quater1.
		int checkCurrentQuarter = engScoreCommon.checkQuarter(currentMonth);
		int currentQuarterMonth = engScoreCommon.getQuarterStartMonth(checkCurrentQuarter);

		DateTime currentQuarterStartDate = new DateTime(currentTime.getYear(), currentQuarterMonth, 1, 0, 0);
		List<EngScorecardSprint> currentQuarterData = engScoreCommon.getQuarterScoreCardData(engScoreSprintList,
				currentQuarterStartDate, currentTime);
		boolean dataFlag = false;
		DateTime lastQuarter1EndDate = null;
		DateTime lastQuarter1StartDate = currentQuarterStartDate;

		while (!dataFlag) {

			if (currentQuarterData.size() <= 0) {
				lastQuarter1EndDate = currentQuarterStartDate;
				lastQuarter1StartDate = currentQuarterStartDate.minusMonths(3);
				currentQuarterStartDate= lastQuarter1StartDate;
				currentQuarterData = engScoreCommon.getQuarterScoreCardData(engScoreSprintList, lastQuarter1StartDate,
						lastQuarter1EndDate);
				checkCurrentQuarter = checkCurrentQuarter - 1;
			}
			if (currentQuarterData.size() > 0) {

				processRulesForQuarter(currentQuarterData, checkCurrentQuarter, true, false,
						lastQuarter1StartDate.getYear());
				dataFlag = true;
			}
		}

		lastQuarter1EndDate = lastQuarter1StartDate;
		lastQuarter1StartDate = lastQuarter1StartDate.minusMonths(3);
		currentQuarterData = engScoreCommon.getQuarterScoreCardData(engScoreSprintList, lastQuarter1StartDate, lastQuarter1EndDate);
		if (currentQuarterData.size() > 0)
			processRulesForQuarter(currentQuarterData, checkCurrentQuarter - 1, false, true,
					lastQuarter1StartDate.getYear());
		DateTime lastQuarter2EndDate = lastQuarter1StartDate;
		DateTime lastQuarter2StartDate = lastQuarter1StartDate.minusMonths(3);
		currentQuarterData = engScoreCommon.getQuarterScoreCardData(engScoreSprintList, lastQuarter2StartDate, lastQuarter2EndDate);
		if (currentQuarterData.size() > 0)
			processRulesForQuarter(currentQuarterData, checkCurrentQuarter - 2, false, false,
					lastQuarter2StartDate.getYear());

		DateTime lastQuarter3EndDate = lastQuarter2StartDate;
		DateTime lastQuarter3StartDate = lastQuarter2StartDate.minusMonths(3);
		currentQuarterData = engScoreCommon.getQuarterScoreCardData(engScoreSprintList, lastQuarter3StartDate, lastQuarter3EndDate);

		if (currentQuarterData.size() > 0)
			processRulesForQuarter(currentQuarterData, checkCurrentQuarter - 3, false, false,
					lastQuarter3StartDate.getYear());
		engScorecardQuarter.setCategories(quarterCats);
		engScorecardQuarter.setVelocitySeries(velocitySeries);
		engScorecardQuarter.setCycleTimeSeries(cycleTimeSeries);
		engScorecardQuarter.setCompeltionSeries(this.completionSeries);
		reverseDataForQuarter();
		return engScorecardQuarter;

	}

	private void reverseDataForQuarter() {
		

		Collections.reverse(engScorecardQuarter.getCategories());
		for (EngScorecardQuarterSeries engSeries : engScorecardQuarter.getCycleTimeSeries()) {
			Collections.reverse(engSeries.getData());
		}
		for (EngScorecardQuarterSeries engSeries : engScorecardQuarter.getVelocitySeries()) {
			Collections.reverse(engSeries.getData());
		}
		for (EngScorecardQuarterSeries engSeries : engScorecardQuarter.getCompeltionSeries()) {
			Collections.reverse(engSeries.getData());
		}

	}

	private void processRulesForQuarter(List<EngScorecardSprint> currentQuarterData, int checkCurrentQuarter,
			boolean currentQuarterFlag, boolean prevQuarterFlag, int financialYear) {
		
		pushQuarterCat(checkCurrentQuarter, financialYear);
		int velocity = 0;
		int completion = 0;
		long leadTime = 0;
		long cycleTime = 0;
		int changeFailureRate = 0;
		long mttf = 0;
		int readinessindex1 = 0;
		int readinessindex2 = 0;
		int readinessindex3 = 0;
		int addedIssues = 0;
		int closedIssues = 0;
		for (EngScorecardSprint engScorecardSprint : currentQuarterData) {
			if (currentQuarterFlag) {
				Optional<EngScorecardSubjectiveSprintData> sprintSubjective = Optional.empty();
				if (engSubjectiveDataSprint != null) {
					sprintSubjective = engSubjectiveDataSprint.stream()
							.filter(o -> o.getSprintName().equals(engScorecardSprint.getSprintName())).findFirst();
				}
				if (!sprintSubjective.isPresent()
						|| (sprintSubjective.get().getTeamSize() == 0.0
								&& Integer.parseInt(sprintSubjective.get().getReadinessIndex1().get("score")) == 0)) {

					engScorecardQuarter.setReadinessIndexFlag(true);

				}
			}
LOGGER.info("Sprint Name "+engScorecardSprint.getSprintName() );
			
			for (EngScorecardParamData engParam : engScorecardSprint.getEngScoreParamData()) {

				if (engParam.getSubParamaterName().equalsIgnoreCase("velocity") && engParam.getValue()!=null) {
					velocity = velocity + Integer.valueOf(engParam.getValue());
				}
				if (engParam.getSubParamaterName().equalsIgnoreCase("added issues") && engParam.getValue()!=null) {
					addedIssues = addedIssues + Integer.valueOf(engParam.getValue());
				}
				if (engParam.getSubParamaterName().equalsIgnoreCase("closed issues") && engParam.getValue()!=null) {
					closedIssues = closedIssues + Integer.valueOf(engParam.getValue());
				}
				if (engParam.getSubParamaterName().equalsIgnoreCase("completion") && engParam.getValue()!=null) {
					completion = completion + Integer.parseInt(engParam.getValue());
					
				}
				if (engParam.getSubParamaterName().equalsIgnoreCase("change failure rate") && engParam.getValue()!=null) {
					changeFailureRate = changeFailureRate + Integer.parseInt(engParam.getValue());
				}
				if (engParam.getSubParamaterName().equalsIgnoreCase("lead time") && engParam.getValue()!=null) {
					leadTime = leadTime + engParam.getTimestamp();
				}
				if (engParam.getSubParamaterName().equalsIgnoreCase("cycle time") && engParam.getValue()!=null) {
					cycleTime = cycleTime + engParam.getTimestamp();
				}
				if (engParam.getSubParamaterName().equalsIgnoreCase("MTTR") && engParam.getValue()!=null) {
					mttf = mttf + engParam.getTimestamp();
				}
				if (currentQuarterFlag) {

					if (engParam.getSubParamaterName().equalsIgnoreCase("N+6") && engParam.getValue()!=null) {
						readinessindex3 = readinessindex3 + Integer.parseInt(engParam.getValue());
					}
					if (engParam.getSubParamaterName().equalsIgnoreCase("N+2") && engParam.getValue()!=null) {
						readinessindex2 = readinessindex2 + Integer.parseInt(engParam.getValue());
					}
					if (engParam.getSubParamaterName().equalsIgnoreCase("N+1") && engParam.getValue()!=null) {
						readinessindex1 = readinessindex1 + Integer.parseInt(engParam.getValue());
					}

				}

			}

		}

		// Avg Completion
		setQuartervalues(this.completion, completion, currentQuarterData.size(), currentQuarterFlag, "Avg Completion",
				colors[0], "spline", prevQuarterFlag);
		// Avg Velocity
		setQuartervalues(this.velocity, velocity, currentQuarterData.size(), currentQuarterFlag, "Avg Velocity",
				colors[1], "spline", prevQuarterFlag);

		// Added Issues
		setQuartervalues(this.addedIssues, addedIssues, 1, currentQuarterFlag, "Added", colors[0], "column",
				prevQuarterFlag);
		// Closed Issues
		setQuartervalues(this.closedIssues, closedIssues, 1, currentQuarterFlag, "Closed", colors[1], "column",
				prevQuarterFlag);

		// Change Failure Rate

		// setQuartervalues(this.changeFaulure,changeFailureRate,
		// currentQuarterData.size(),currentQuarterFlag,"Change Failure
		// Rate",colors[3],"spline", prevQuarterFlag);

		int hour = 0;
		double days = 0;
		// Lead Time
		if (leadTime > 0) {
			long seconds = leadTime / 1000;
			long minutes = seconds / 60;
			hour = (int) (minutes / 60);
			//days = hour / 24;

		}
		setQuartervalues(this.leadTime, hour, currentQuarterData.size(), currentQuarterFlag, "Lead Time", colors[0],
				"column", prevQuarterFlag);

		// Cycle Time
		hour = 0;

		if (cycleTime > 0) {

			long seconds = cycleTime / 1000;
			long minutes = seconds / 60;
			hour = (int) (minutes / 60);
			//days = hour / 24;
		}
		setQuartervalues(this.cycleTime, hour, currentQuarterData.size(), currentQuarterFlag, "Cycle Time", colors[1],
				"column", prevQuarterFlag);
		hour = 0;
		// MTTR
		if (mttf > 0) {
			long seconds = mttf / 1000;
			long minutes = seconds / 60;
			hour = (int) (minutes / 60);
			//days = hour / 24;
		}

		setQuartervalues(this.mttr, hour, currentQuarterData.size(), currentQuarterFlag, "MTTR", colors[2], "spline",
				prevQuarterFlag);

		if (currentQuarterFlag) {

			setReadinessIndex(readinessindex1, readinessindex2, readinessindex3, currentQuarterData.size());
		}

	}

	private void setReadinessIndex(int readinessindex1, int readinessindex2, int readinessindex3, int size) {
		
		if (readinessindex1 > 0) {
			readinessindex1 = readinessindex1 / size;
		}
		if (readinessindex2 > 0) {
			readinessindex2 = readinessindex2 / size;
		}
		if (readinessindex3 > 0) {
			readinessindex3 = readinessindex3 / size;
		}

		this.engScorecardQuarter.setReadinessIndex1(readinessindex1);
		this.engScorecardQuarter.setReadinessIndex2(readinessindex2);
		this.engScorecardQuarter.setReadinessIndex3(readinessindex3);

	}

	private void setQuartervalues(EngScorecardQuarterSeries quaterObj, double value, int size,
			boolean currentQuarterFlag, String name, String color, String type, boolean prevQuarter) {
		// TODO Auto-generated method stub0
        
		
		
		
		if (currentQuarterFlag) {

			quaterObj.setName(name);
			quaterObj.setColor(color);
			quaterObj.setType(type);
			

			
			
			
			setValuesForCurrentQuarter(name, value, size);

		}
		
		
		if (value > 0) {
			value = value / size;
		}
		
		if (name.equals("Cycle Time") || name.equals("MTTR") || name.equals("Lead Time")) {
		       value = value/24;
				        
		       DecimalFormat df = new DecimalFormat("#.00");
		       value = Double.valueOf(df.format(value));
				}

		if (prevQuarter) {

			setFlags(name, (int) value, size);

		}

		quaterObj.getData().add(value);

	}

	private void setFlags(String name, int value, int size) {
		
		int cuurentval = 0;
		if (name.equals("Avg Velocity")) {
			if (this.velocitySeries.get(0).getData().size() > 0) {
				cuurentval = this.velocitySeries.get(0).getData().get(0).intValue();
			}
			engScorecardQuarter.setVelocityFlag(cuurentval > value);
		} else if (name.equals("Avg Completion")) {
			if (this.completionSeries.get(0).getData().size() > 0) {
				cuurentval = this.completionSeries.get(0).getData().get(0).intValue();
			}
			engScorecardQuarter.setCompletionFlag(cuurentval > value);
		} else if (name.equals("Cycle Time")) {
			if (this.cycleTimeSeries.get(1).getData().size() > 0) {
				cuurentval = this.cycleTimeSeries.get(1).getData().get(0).intValue();
			}
			engScorecardQuarter.setCycleTimeFlag(cuurentval > value);
		} else if (name.equals("Lead Time")) {
			if (this.cycleTimeSeries.get(0).getData().size() > 0) {
				cuurentval = this.cycleTimeSeries.get(0).getData().get(0).intValue();
			}
			engScorecardQuarter.setLeadTimeFlag(cuurentval > value);
		} else if (name.equals("MTTR")) {
			if (this.cycleTimeSeries.get(2).getData().size() > 0) {
				cuurentval = this.cycleTimeSeries.get(2).getData().get(0).intValue();
			}
			engScorecardQuarter.setMttrFlag(cuurentval > value);
		}

	}

	private void setValuesForCurrentQuarter(String name, double value, int size) {
		
		String result = "";
		if (value > 0) {
			value = value / size;
		}

		if (name.equals("Avg Velocity")) {
			engScorecardQuarter.setAvgVelocity((int) value);
		} else if (name.equals("Avg Completion")) {
			engScorecardQuarter.setAvgCompletion((int) value);
		} else if (name.equals("Change Failure Rate")) {
			engScorecardQuarter.setChangeFailureRate((int) value);
		} else if (name.equals("Cycle Time") || name.equals("MTTR") || name.equals("Lead Time")) {
			if (value > 0) {
				long days = (int) value / 24;
				long leftHours = (int) value % 24;
				long week = days / 7;
				long leftDays = days % 7;

				if (week != 0) {
					result = String.valueOf(week) + "w ";

				}
				if (leftDays != 0) {
					result += String.valueOf(leftDays) + "d ";
				}

				if (leftHours != 0) {
					result += String.valueOf(leftHours) + "h";
				}

			}
			if (result.equals("")) {
				result = "0h";
			}
			if (name.equals("Cycle Time")) {
				engScorecardQuarter.setCycleTime(result);
			}
			if (name.equals("Lead Time")) {
				engScorecardQuarter.setLeadTime(result);
			}
			if (name.equals("MTTR")) {
				engScorecardQuarter.setMTTR(result);
			}
		}

	}

	private void pushQuarterCat(int checkCurrentQuarter, int financialYear) {
		LOGGER.info(financialYear % 100);
		financialYear = financialYear % 100;

		if (checkCurrentQuarter == 1 || checkCurrentQuarter == 2) {
			financialYear = financialYear + 1;
		}

		if (checkCurrentQuarter == -2) {
			financialYear = financialYear + 1;
		}

		if (checkCurrentQuarter > 0) {

			quarterCats.add("FY" + financialYear + " Q" + checkCurrentQuarter);

		} else if (checkCurrentQuarter == 0) {
			quarterCats.add("FY" + financialYear + " Q4");
		} else if (checkCurrentQuarter < 0) {
			quarterCats.add("FY" + financialYear + " Q" + (4 + checkCurrentQuarter));
		}
	}

	

	

	private void getIntialData(String pName) {
		

		engRepo = ctx.getBean(EngScorecardRepo.class);
		almConfigRepo = ctx.getBean(ALMConfigRepo.class);
		almConfig = almConfigRepo.findByProjectName(pName).get(0);
		scoreCard = engRepo.findByPName(pName);
		configRep = ctx.getBean(ConfigurationSettingRep.class);
		config = configRep.findByProjectName(pName).get(0);
		if (scoreCard != null) {
			engScoreSprintList = scoreCard.getEngScoreCardSprint();

		} else {
			scoreCard = new EngScorecard();
			scoreCard.setpName(pName);

		}
		engScoreSprintList = scoreCard.getEngScoreCardSprint();
		engScoreSprintList = engScoreSprintList.stream()
				.filter(score -> score.getState() != null && !(score.getState().equalsIgnoreCase("active")))
				.collect(Collectors.toList());
		engScorecardQuarter.setReadinessIndexFlag(false);
		this.completionSeries.add(this.completion);
		if (config.getProjectType().equalsIgnoreCase("sprint wise")) {
			this.velocitySeries.add(this.velocity);
		} else {
			this.velocitySeries.add(this.addedIssues);
			this.velocitySeries.add(this.closedIssues);
		}

		this.cycleTimeSeries.add(this.leadTime);
		this.cycleTimeSeries.add(this.cycleTime);
		this.cycleTimeSeries.add(this.mttr);
		// this.cycleTimeSeries.add(this.changeFaulure);
		List<String> sprints = new ArrayList<String>();
		String[] tempSprints = almConfig.getFilteredSprints();
		if (tempSprints != null) {
			sprints.addAll(Arrays.asList(tempSprints));
		}
		engScoreSprintList = engScoreSprintList.stream().filter(score -> !(sprints.contains(score.getSprintName())))
				.collect(Collectors.toList());
		engSubjectiveRepo = ctx.getBean(EngScorecardSubjectiveDataRepo.class);
		EngagementScorecardSubjectiveData subjective = engSubjectiveRepo.findByPName(pName);

		if (subjective != null) {
			engSubjectiveDataSprint = subjective.getEngScorecardSprintData();

		}

	}

}
