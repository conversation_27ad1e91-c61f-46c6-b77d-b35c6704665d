package com.bolt.dashboard.engagementScorecard;

import java.util.List;
import java.util.Map;

import com.bolt.dashboard.core.model.TransitionModel;

public class IssueList {
	
		private String wId;
		private double storyPoints;
		private String assignee;
		private String state;
		private String type;
		private long createdDate;
		private List<TransitionModel> transitions;
		  private Map<Long, String> allocatedDate;
		  private Long commitedAftertDate;
		  private Long removedtDate;
		  private List<String> label=null;
		public String getwId() {
			return wId;
		}
		public void setwId(String wId) {
			this.wId = wId;
		}
		public double getStoryPoints() {
			return storyPoints;
		}
		public void setStoryPoints(double storyPoints) {
			this.storyPoints = storyPoints;
		}
		public String getAssignee() {
			return assignee;
		}
		public void setAssignee(String assignee) {
			this.assignee = assignee;
		}
		public String getState() {
			return state;
		}
		public void setState(String state) {
			this.state = state;
		}
		public String getType() {
			return type;
		}
		public void setType(String type) {
			this.type = type;
		}
		public List<TransitionModel> getTransitions() {
			return transitions;
		}
		public void setTransitions(List<TransitionModel> transitions) {
			this.transitions = transitions;
		}
		public Map<Long, String> getAllocatedDate() {
			return allocatedDate;
		}
		public void setAllocatedDate(Map<Long, String> allocatedDate) {
			this.allocatedDate = allocatedDate;
		}
		public Long getCommitedAftertDate() {
			return commitedAftertDate;
		}
		public void setCommitedAftertDate(Long commitedAftertDate) {
			this.commitedAftertDate = commitedAftertDate;
		}
		public Long getRemovedtDate() {
			return removedtDate;
		}
		public void setRemovedtDate(Long removedtDate) {
			this.removedtDate = removedtDate;
		}
		public long getCreatedDate() {
			return createdDate;
		}
		public void setCreatedDate(long createdDate) {
			this.createdDate = createdDate;
		}
		public List<String> getLabel() {
			return label;
		}
		public void setLabel(List<String> label) {
			this.label = label;
		}
	
	
}
