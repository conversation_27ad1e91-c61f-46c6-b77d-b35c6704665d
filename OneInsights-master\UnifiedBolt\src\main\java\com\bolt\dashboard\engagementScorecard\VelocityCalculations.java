package com.bolt.dashboard.engagementScorecard;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.TransitionModel;

public class VelocityCalculations {

	private ArrayList<IterationOutModel> workingBacklog;
	private ArrayList<IterationOutModel> workingSprints;
	private List<String> velocityFields;
	private List<String> closestates = null;
	private List<String> taskNames;
	private int tempSpRefined;
	private int tempSpRemoved;
	private int finalStoriesCommited;
	private int storiesCompleted;
	private int defetcsCompleted;
	private List<IssueList> issueList = null;
	private List<String> workItemArr2 = null;
	private ALMConfiguration almConfig = null;
	private ArrayList<String> wIdArr;
	private List<ScoreCardSprintData> scoreCardSprintList = new ArrayList<>();
	private List<IssueList> refinedIssuList = null;
	private List<IssueList> removedIssuList = null;
	private long enDateSprint = 0;

	public List<ScoreCardSprintData> calcVelocity(List<IterationOutModel> iterations, ALMConfiguration temp) {
		almConfig = temp;
		// vlist = new VelocityList();
		workingBacklog = new ArrayList<IterationOutModel>();
		workingSprints = new ArrayList<IterationOutModel>();
		velocityFields = Arrays.asList(almConfig.getVelocityFields());
		closestates = Arrays.asList(almConfig.getCloseState());
		taskNames = Arrays.asList(almConfig.getTaskName());

		for (IterationOutModel auth : iterations) {

//			if (null == auth.getState() ||  (null != auth.getState() && !auth.getState().toLowerCase().contains("future"))) {
//				workingBacklog.add(auth);
//			}

			if (null == auth.getState() || (null != auth.getState())) {
				workingBacklog.add(auth);
			}
		}

		for (IterationOutModel auth : iterations) {
			if (null != auth.getState() && auth.getStDate() != 0 && !auth.getState().toLowerCase().contains("future")
					&& !auth.getState().toLowerCase().contains("backlog") && auth.getsId() != 0) {
				workingSprints.add(auth);
				workingSprints.sort(Comparator.comparing(IterationOutModel::getStDate));

			}
		}
		
		for (int i = workingSprints.size()- 30 >= 0 ? workingSprints.size()-30:0; i < workingSprints.size() ; i++) {
			double totalStoryPoints = 0, totalCompletedStoryPoints = 0, totalSPAfter = 0, totalSpRefined = 0,
					totalSPremoved = 0, currentSprintBugs = 0, capacityPerSprint = 0, actualEffort = 0, tempSp = 0;
			tempSpRefined = 0;
			tempSpRemoved = 0;
			finalStoriesCommited = 0;

			storiesCompleted = 0;
			defetcsCompleted = 0;

			workItemArr2 = new ArrayList<String>();
			
			if(workingSprints.get(i).getsName().equals("Sprint 98 Feb 04 - Feb 17")) {
				System.out.println("Stop");
			}

			if (workingSprints.get(i).getMetrics() != null) {
				for (MonogOutMetrics m : workingSprints.get(i).getMetrics()) {
					if (taskNames.contains(m.getType())) {
						if (null != m.getEffort()) {
							actualEffort += m.getEffort();
						} else {
							actualEffort += 0;
						}

					}
				}
			}
			refinedIssuList = new ArrayList<>();
			removedIssuList = new ArrayList<>();
			currentSprintBugs = workingSprints.get(i).getTotClosedDefects();
			if (workingSprints.get(i).getCompletedDate() == 0) {

				enDateSprint = workingSprints.get(i).getEndDate();
			} else {
				enDateSprint = workingSprints.get(i).getCompletedDate();
			}
			totalStoryPoints = callSP("start", workingSprints.get(i));

			ScoreCardSprintData scoreCardSprint = new ScoreCardSprintData();
			scoreCardSprint.setCommitedSp(totalStoryPoints);
			scoreCardSprint.setSprintName(workingSprints.get(i).getsName());
			scoreCardSprint.setSprintId(workingSprints.get(i).getsId());
			scoreCardSprint.setStartDate(workingSprints.get(i).getStDate());

			scoreCardSprint.setEndDate(enDateSprint);
			scoreCardSprint.setIssuesCommited(issueList);
			totalSpRefined = tempSpRefined;
			totalSPremoved = tempSpRemoved;
			scoreCardSprint.setState(workingSprints.get(i).getState());

			totalSPAfter = callSP("end", workingSprints.get(i));
			scoreCardSprint.setCommitedAfterSp(totalSPAfter);
			scoreCardSprint.setIssuesCommitedAfter(issueList);
			totalSpRefined += tempSpRefined;
			totalSPremoved += tempSpRemoved;

			totalCompletedStoryPoints = calcClosedSP(workingSprints.get(i));
			scoreCardSprint.setCompletedSp(totalCompletedStoryPoints);
			scoreCardSprint.setIssuesComplted(issueList);

			capacityPerSprint = 10 * 6.5 * (int) workingSprints.get(i).getTeamSize();
			scoreCardSprint.setRefinedSp(totalSpRefined);
			scoreCardSprint.setRemovedSp(totalSPremoved);
			scoreCardSprint.setIssuesRefined(refinedIssuList);

			scoreCardSprint.setIssuesRemoved(removedIssuList);
			scoreCardSprint.setDefects(workingSprints.get(i).getTotClosedDefects());

			scoreCardSprint.setCapacity(capacityPerSprint);
			scoreCardSprint.setEffort(Math.floor(actualEffort / 3600));
			scoreCardSprint.setCompletedDefects(defetcsCompleted);
			List<IssueList> refinedDefects = new ArrayList<IssueList>();
			refinedIssuList.forEach(issue -> {

				if (issue.getType().equals(almConfig.getDefectName())) {
					refinedDefects.add(issue);
				}
			});
			scoreCardSprint.setRefinedDefects(refinedDefects.size());
			scoreCardSprint.setDefects(currentSprintBugs);
			// vlist.finalCommitedStories.add((double)finaRefinedStories.size());
			// vlist.finalCommitedDefetcs.add((double)finaRefinedDefects.size());
			// vlist.completedDefects.add(defetcsCompleted);
			scoreCardSprintList.add(scoreCardSprint);
		}

		return scoreCardSprintList;

	}

	public double callSP(String flag, IterationOutModel sp) {
		double tempSp = 0;
		tempSpRefined = 0;
		tempSpRemoved = 0;
		issueList = new ArrayList<IssueList>();

		long date;
		if (flag.equals("start")) {
			date = sp.getStDate();
		} else {
			if (sp.getState().toLowerCase().contains("active")) {
				date = new Date().getTime();
			} else {

				date = sp.getEndDate();

			}

		}

		for (IterationOutModel it : workingBacklog) {

			ArrayList<String> keys, dateMapValues;
			ArrayList<Long> dateMapKeys=new ArrayList<Long>();
			ArrayList<Long> storyPointMapKeys=new ArrayList<Long>();
			Map storyPointMap;
			Map<Long, String> dateMap;
			if (it.getMetrics() != null) {

				for (MonogOutMetrics ele : it.getMetrics()) {
					
					if(ele.getwId().equals("IRC-1785")) {
						System.out.println("Issue Comes");
					}
					
					Collections.sort(dateMapKeys);
					if (ele.getAllocatedDate() != null && (velocityFields.indexOf(ele.getType()) > -1)
							&& ele.getStoryPoints() != null && (workItemArr2.indexOf(ele.getwId()) < 0)) {
						dateMap = ele.getAllocatedDate();
						dateMapKeys = new ArrayList<Long>(ele.getAllocatedDate().keySet());
						storyPointMapKeys = new ArrayList<Long>(ele.getStoryPoints().keySet());
						storyPointMap = ele.getStoryPoints();
						Collections.sort(storyPointMapKeys);
						for (int i = 0; i < dateMapKeys.size(); i++) {
							List<String> dateValues = Arrays.asList(dateMap.get(dateMapKeys.get(i)).split(","));
							if (i == dateMapKeys.size() - 1 && dateMapKeys.get(i) < date && (dateValues.contains(String.valueOf(sp.getsId())))) {

								tempSp = tempSp + storyLoop(storyPointMapKeys, sp, storyPointMap, issueList, ele, date,
										false, dateMapKeys.get(i));
								storyLoopRefined(true, storyPointMapKeys, sp, storyPointMap, issueList, ele, date,
										dateMapKeys.get(i), flag);
							} else if ((dateMapKeys.get(i) < date) && (dateValues.contains(String.valueOf(sp.getsId())))
									&& (dateMapKeys.get(i + 1) > date)) {
								tempSp = tempSp + storyLoop(storyPointMapKeys, sp, storyPointMap, issueList, ele, date,
										false, dateMapKeys.get(i));
								storyLoopRefined(false, storyPointMapKeys, sp, storyPointMap, issueList, ele, date,
										dateMapKeys.get(i), flag);
							}

						}
					}

				}
			}

		}
		return tempSp;
	}

	public double calcClosedSP(IterationOutModel sp) {
		double tempSp = 0;
		issueList = new ArrayList<IssueList>();

		long date;

		if (sp.getState().toLowerCase().contains("active")) {
			date = new Date().getTime();
		} else {
			if (sp.getCompletedDate() != 0) {
				date = sp.getCompletedDate();
			} else {
				date = sp.getEndDate();
			}
		}

		wIdArr = new ArrayList<String>();
		for (IterationOutModel it : workingBacklog) {

			ArrayList<String> keys;
			ArrayList<Long> dateMapKeys, storyPointMapKeys;
			Map storyPointMap;
			Map<Long, String> dateMap;

			if (it.getMetrics() != null) {

				for (MonogOutMetrics ele : it.getMetrics()) {
					if(ele.getwId().equals("PRO-3546")) {
						System.out.println("Issue Comes");
					}
					
					if (ele.getsId() != 0) {
						if (ele.getAllocatedDate() != null && (velocityFields.indexOf(ele.getType()) > -1)
								&& ele.getStoryPoints() != null) {
							dateMap = ele.getAllocatedDate();
							dateMapKeys = new ArrayList<Long>(ele.getAllocatedDate().keySet());
							Collections.sort(dateMapKeys);

							storyPointMapKeys = new ArrayList<Long>(ele.getStoryPoints().keySet());
							storyPointMap = ele.getStoryPoints();

							Collections.sort(storyPointMapKeys);

							for (int i = 0; i < dateMapKeys.size(); i++) {
								List<String> dateValues = Arrays.asList(dateMap.get(dateMapKeys.get(i)).split(","));
								if (i == dateMapKeys.size() - 1 && dateMapKeys.get(i) < date
										&& dateValues.contains(String.valueOf(sp.getsId()))) {
									String wid = ele.getwId();
									TransitionModel trans = filterTrans(ele.getTransitions(), date);
									if (closestates.indexOf(trans.getCrState()) > -1 && trans.getMdfDate() < date
											&& trans.getMdfDate() > sp.getStDate()
											&& trans.getMdfDate() > dateMapKeys.get(i)
											&& wIdArr.indexOf(ele.getwId()) <= -1) {
										wIdArr.add(ele.getwId());
										long alloc = 0;
										tempSp = tempSp + storyLoop(storyPointMapKeys, sp, storyPointMap, issueList,
												ele, date, true, alloc);

									}

								} else if (dateMapKeys.get(i) < date && dateValues.indexOf(sp.getsId() + "") > -1
										&& dateMapKeys.get(i + 1) > date) {
									String wid = ele.getwId();
									TransitionModel trans = filterTrans(ele.getTransitions(), date);
									if (closestates.indexOf(trans.getCrState()) > -1 && trans.getMdfDate() < date
											&& trans.getMdfDate() > sp.getStDate()
											&& trans.getMdfDate() > dateMapKeys.get(i)
											&& wIdArr.indexOf(ele.getwId()) <= -1) {

										wIdArr.add(ele.getwId());
										long alloc = 0;
										tempSp = tempSp + storyLoop(storyPointMapKeys, sp, storyPointMap, issueList,
												ele, date, true, alloc);

									}

								}

							}
						}
					}
				}
			}
		}
		return tempSp;
	}

	double storyLoop(List<Long> keys, IterationOutModel it, Map story, List<IssueList> issueList, MonogOutMetrics m,
			long date, boolean colsedflag, Long allocDate) {
		double temp = 0;
		for (int i = 0; i < keys.size(); i++) {
			if (keys.get(i) <= date && i == keys.size() - 1) {
				temp = temp + (double) story.get(keys.get(i));
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getStoryName()) && colsedflag) {
					storiesCompleted++;
				}
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getDefectName()) && colsedflag) {
					defetcsCompleted++;
				}
				IssueList iss = new IssueList();
				iss.setStoryPoints((double) story.get(keys.get(i)));
				iss.setwId(m.getwId());
				iss.setAssignee(m.getAssgnTo());
				iss.setState(m.getState());
				iss.setType(m.getType());
				iss.setAllocatedDate(m.getAllocatedDate());
				iss.setTransitions(m.getTransitions());
				iss.setCommitedAftertDate(allocDate);
				iss.setCreatedDate(m.getCreateDate());
				issueList.add(iss);
				workItemArr2.add(m.getwId());
			} else if (keys.get(i) <= date && keys.get(i + 1) > date) {
				temp = temp + (double) story.get(keys.get(i));
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getStoryName()) && colsedflag) {
					storiesCompleted++;
				}
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getDefectName()) && colsedflag) {
					defetcsCompleted++;
				}
				IssueList iss = new IssueList();
				iss.setStoryPoints((double) story.get(keys.get(i)));
				iss.setwId(m.getwId());
				iss.setAssignee(m.getAssgnTo());
				iss.setState(m.getState());
				iss.setType(m.getType());
				iss.setAllocatedDate(m.getAllocatedDate());
				iss.setTransitions(m.getTransitions());
				iss.setCommitedAftertDate(allocDate);
				iss.setCreatedDate(m.getCreateDate());
				issueList.add(iss);
				workItemArr2.add(m.getwId());
			}
		}
		return temp;
	}

	TransitionModel filterTrans(List<TransitionModel> tr, long date) {
		List<TransitionModel> newTr = new ArrayList<TransitionModel>();
		if (null != tr) {
			for (int i = 0; i < tr.size(); i++) {
				if (tr.get(i).getMdfDate() < date) {
					newTr.add(tr.get(i));
				}
			}
		}

		if (newTr.size() > 0) {
			return newTr.get(newTr.size() - 1);
		} else {
			return new TransitionModel();
		}
	}

	void storyLoopRefined(boolean isRefined, List<Long> keys, IterationOutModel it, Map story,
			List<IssueList> issueList, MonogOutMetrics m, long date, Long allocateDate, String flag) {
		long endDate = it.getEndDate();
		List<TransitionModel> tr = m.getTransitions();
		if (tr != null) {
			tr.sort(Comparator.comparing(TransitionModel::getMdfDate));
		}
		boolean checkRemoved = false;
	//	if (flag.equalsIgnoreCase("start")) {
			if (it.getState().toLowerCase().contains("active")) {
				endDate = new Date().getTime();
			} else {
				if (it.getCompletedDate() != 0) {
					endDate = it.getCompletedDate();
				} else {
					endDate = it.getEndDate();
				}
			}
//		}
		List<String> checkRemoveResult = null;
		if (!isRefined) {

			checkRemoveResult = checkRemoved(it, m, allocateDate, endDate);
			checkRemoved = checkRemoveResult.get(0).equals("false") ? false : true;
		}

		if (checkRemoved) {
			endDate = Long.parseLong(checkRemoveResult.get(1));
		}
		IssueList issue = new IssueList();

		issue.setwId(m.getwId());
		issue.setAssignee(m.getAssgnTo());
		issue.setState(m.getState());
		issue.setType(m.getType());
		issue.setAllocatedDate(m.getAllocatedDate());
		issue.setTransitions(m.getTransitions());
		issue.setCreatedDate(m.getCreateDate());

		for (int i = 0; i < keys.size(); i++) {
			issue.setStoryPoints((double) story.get(keys.get(i)));
			if (keys.get(i) <= endDate && i == keys.size() - 1) {

				if (checkRemoved) {
					removedIssuList.add(issue);
					tempSpRemoved += (double) story.get(keys.get(i));
					workItemArr2.add(m.getwId());
					break;
				} else {
					refinedIssuList.add(issue);
					workItemArr2.add(m.getwId());
					tempSpRefined+=(double) story.get(keys.get(i));
					break;
				}
			} else if (keys.get(i) < endDate && !checkRemoved && keys.get(i + 1) > endDate) {
				refinedIssuList.add(issue);
				workItemArr2.add(m.getwId());
				tempSpRefined+=(double) story.get(keys.get(i));
				break;
			} else if (checkRemoved && keys.get(i) < endDate && keys.get(i + 1) > endDate) {
				removedIssuList.add(issue);
				tempSpRemoved += (double) story.get(keys.get(i));
				workItemArr2.add(m.getwId());
				break;
			}
		}

	}

	private List<String> checkRemoved(IterationOutModel it, MonogOutMetrics m, Long allocateDate, long endDate) {
		
		Map<Long, String> dateMap = m.getAllocatedDate();
		ArrayList<Long> dateMapKeys = new ArrayList<Long>(m.getAllocatedDate().keySet());
		List<String> result = new ArrayList<String>();
		result.add("false");

		Collections.sort(dateMapKeys);
		int index = dateMapKeys.indexOf(allocateDate);

		for (int i = index + 1; i < dateMapKeys.size(); i++) {
			// String Sprints[]=dateMap.get(dateMapKeys.get(i))
			List<String> dateValues = Arrays.asList(dateMap.get(dateMapKeys.get(i)).split(","));
			if (dateMapKeys.get(i) < endDate && !(dateValues.contains(String.valueOf(it.getsId())))) {
				result.add(0, "true");
				result.add(1, String.valueOf(dateMapKeys.get(i)));
				return result;
			}

		}
		return result;
	}

}
