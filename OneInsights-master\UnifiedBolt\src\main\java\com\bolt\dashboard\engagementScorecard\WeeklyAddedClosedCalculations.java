package com.bolt.dashboard.engagementScorecard;

import java.util.*;

import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.TransitionModel;
import com.fasterxml.jackson.databind.util.BeanUtil;


import java.time.*;
import java.text.*;

public class WeeklyAddedClosedCalculations {
	
    Calendar c = Calendar.getInstance();
    List<IterationOutModel> iterations = null;
    AnnotationConfigApplicationContext ctx = DataConfig.getContext();
    List<String> closed =new ArrayList<String>();
    List<String> added =new ArrayList<String>();
    List<ScoreCardSprintData> weeks;
    List<String> closedStates;
    
    
    long getWeekEnd() {
      c.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
      
      c.set(Calendar.HOUR_OF_DAY, 23);
      c.set(Calendar.MINUTE, 59);
      c.set(Calendar.SECOND, 59);
      c.set(Calendar.MILLISECOND, 999);
      return c.getTimeInMillis();
    }
    
    
    long getWeekStart() {
    	 c.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        
         c.set(Calendar.HOUR_OF_DAY, 0);
         c.set(Calendar.MINUTE, 0);
         c.set(Calendar.SECOND, 0);
         c.set(Calendar.MILLISECOND, 0);
         return c.getTimeInMillis();
    }
    
    
    void calculateWeeks(List<MonogOutMetrics> metrics) {
 
    	metrics.sort(Comparator.comparing(MonogOutMetrics::getCreateDate));
    	long cuttOffTime = metrics.get(0).getCreateDate();
    	
    	boolean active=true;
    	weeks = new ArrayList<ScoreCardSprintData>(); 
    	for(int i=0;i<60 && c.getTimeInMillis() > cuttOffTime;i++) {
    		long WeekEnd = getWeekEnd();
    		DateFormat df = new SimpleDateFormat("dd MMM");
    		String end = df.format(c.getTime());
        	long weekStart = getWeekStart();
        	String start = df.format(c.getTime());
        	ScoreCardSprintData week = new ScoreCardSprintData();
        	week.setSprintName(start+"-"+end);
        	week.setStartDate(weekStart);
        	week.setEndDate(WeekEnd);
        	if(active) {
        		week.setState("active");
        		active = false;
        	}
        	else {
        		week.setState("closed");
        	}
        	weeks.add(week);
        	c.add(Calendar.DATE, -6);
    	}
    	
    }
    
    public List<ScoreCardSprintData> getWeeks(String pName,String pAlmType,List<MonogOutMetrics> metrics){
    	calculateWeeks(metrics);
    	return weeks;
    }
    
    
    List<ScoreCardSprintData> calculateAddedClosed(List<MonogOutMetrics> metrics,ALMConfiguration almConfig) {
    	calculateWeeks(metrics);
    	Iterator<ScoreCardSprintData> itr = weeks.iterator();
    	closedStates = Arrays.asList(almConfig.getCloseState());
    	while(itr.hasNext()) {
    		ScoreCardSprintData week= itr.next();
    		int add=0,close=0;
    		List<IssueList> addedIssues = new ArrayList<IssueList>();
    		List<IssueList> closedIssues = new ArrayList<IssueList>();
    		
    		Iterator<MonogOutMetrics> metricIterator = metrics.iterator();
    		while(metricIterator.hasNext()) {
    			MonogOutMetrics metric = metricIterator.next();
    			IssueList iss= new IssueList();
				iss.setwId(metric.getwId());
				iss.setAssignee(metric.getAssgnTo());
				iss.setState(metric.getState());
				iss.setType(metric.getType());
				iss.setAllocatedDate(metric.getAllocatedDate());
				iss.setTransitions(metric.getTransitions());
				iss.setCreatedDate(metric.getCreateDate());
    			iss.setLabel(metric.getLabel());
    			
				if(!closed.contains(metric.getwId())) {
					List<TransitionModel> trans = metric.getTransitions();
					if(trans != null) {
						trans.sort(Comparator.comparing(TransitionModel::getMdfDate));
						TransitionModel lastTrans = trans.get(trans.size()-1);
						if(closedStates.contains(lastTrans.getCrState())) {
							if(lastTrans.getMdfDate() >= week.getStartDate()) {
								close++;
								closed.add(metric.getwId());
								closedIssues.add(iss);
								
							}
						}
					}
				}
				
				if(!added.contains(metric.getwId()) && metric.getCreateDate() >=  week.getStartDate()) {
					add++;
					added.add(metric.getwId());
					addedIssues.add(iss);
				}
			}
    		week.setIssuesCommited(addedIssues);
    		week.setIssuesRefined(addedIssues);
    		week.setIssuesComplted(closedIssues);
    		
    	}
		return weeks;
    }
    
}
