package com.bolt.dashboard.extentReport;

import java.io.File;
import java.util.List;

import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.TestTool;
import com.bolt.dashboard.core.repository.TestRepository;

public class TestExtentApplication {
	 private static final Logger LOGGER = LogManager.getLogger(TestExtentApplication.class);
	AnnotationConfigApplicationContext ctx = null;

	public TestExtentApplication() {
	}

	public void main() {
		ctx = DataConfig.getContext();
		TestRepository repo = ctx.getBean(TestRepository.class);
		String fileLocation;
		String projectName = "Sample Project";
		TestExtentClientImplementation testExtentClientImplementation = new TestExtentClientImplementation();
		try {
			File fXmlFile = new File("src/main/java/com/bolt/dashboard/extentReport/testExtentmetrics.xml");
			DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
	         dbFactory.setAttribute(XMLConstants.ACCESS_EXTERNAL_DTD, "");
	         dbFactory.setAttribute(XMLConstants.ACCESS_EXTERNAL_SCHEMA, "");
			
			DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
			Document doc = dBuilder.parse(fXmlFile);
			doc.getDocumentElement().normalize();
			NodeList nList = doc.getElementsByTagName("Tool");
			for (int temp = 0; temp < nList.getLength(); temp++) {
				Node nNode = nList.item(temp);
				if (nNode.getNodeType() == Node.ELEMENT_NODE) {
					Element eElement = (Element) nNode;
					fileLocation = eElement.getElementsByTagName("fileLocation").item(0).getTextContent();
					LOGGER.info("This    is file Location " + fileLocation);

					List<TestTool> reportSummary = testExtentClientImplementation.getTestExtentReport(fileLocation,
							repo, projectName);
					repo.save(reportSummary);
				}
			}

			////// catx.close();
		} catch (Exception e) {
			LOGGER.error(e.getMessage());
			LOGGER.info(e.getStackTrace());
			LOGGER.info(e);

		} finally {
			repo = null;
			ctx = null;
		}

	}

}
