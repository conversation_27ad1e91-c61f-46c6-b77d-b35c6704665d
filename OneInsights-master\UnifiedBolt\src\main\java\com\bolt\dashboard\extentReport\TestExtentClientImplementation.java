package com.bolt.dashboard.extentReport;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.json.XML;

import com.bolt.dashboard.codecoverage.CodeCoverageExceptions;
import com.bolt.dashboard.core.model.TestCaseSummary;
import com.bolt.dashboard.core.model.TestConfiguration;
import com.bolt.dashboard.core.model.TestReportSummary;
import com.bolt.dashboard.core.model.TestStepsSummary;
import com.bolt.dashboard.core.model.TestTool;
import com.bolt.dashboard.core.repository.TestRepository;
import com.bolt.dashboard.testcollector.TestExceptions;

public class TestExtentClientImplementation implements TextExtentClient {
    private static final Logger LOGGER = LogManager.getLogger(TestExtentClientImplementation.class);
    TestReportSummary reportSummary;
    List<TestTool> toolList = new ArrayList<>();

    @Override
    public List<TestTool> getTestExtentReport(String fileLocation, TestRepository repo, String projectName)
            throws TestExceptions {
        reportSummary = new TestReportSummary();
        TestTool testTool = new TestTool();
        try {
            reportSummary.setProjectName(projectName);
            JSONObject json = convertXMLToJson(fileLocation);
            JSONObject reportJsonObject = json.getJSONObject("ReportSummary");
            JSONObject configurationJsonObject = reportJsonObject.getJSONObject("Configuration");
            //TestConfiguration testConfiguration = new TestConfiguration();
            LOGGER.info("CONFIGURATION      ");
            testTool.setName(configurationJsonObject.getString("CurrentApplication"));
            testTool.setUrl(configurationJsonObject.getString("CurrentURL"));
            testTool.setBrowser(configurationJsonObject.getString("CurrentBrowser"));
            // testConfiguration.setCurrentApplication(configurationJsonObject.getString("CurrentApplication"));
            // testConfiguration.setCurrentEnvironment(configurationJsonObject.getString("CurrentEnvironment"));
            // testConfiguration.setCurrentBrowser(configurationJsonObject.getString("CurrentBrowser"));
            // testConfiguration.setCurrentURL();
            // reportSummary.setTestConfiguration(testConfiguration);
            JSONObject testCaseSummaryJsonObject = reportJsonObject.getJSONObject("TestCaseSummary");
           // TestCaseSummary testCaseSummary = new TestCaseSummary();

            LOGGER.info("TestCaseSummary      ");
            Timestamp startTimestamp = Timestamp.valueOf(testCaseSummaryJsonObject.getString("Start"));
            long start = startTimestamp.getTime();
            testTool.setStartTime(start);
            Timestamp endTimestamp = Timestamp.valueOf(testCaseSummaryJsonObject.getString("End"));
            long end = endTimestamp.getTime();
            testTool.setEndTime(end);
            long duration = end - start;
            testTool.setDuration(duration);

            // testCaseSummary.setTotalTimeTaken(testCaseSummaryJsonObject.getString("TotalTimeTaken"));
            // testCaseSummary.setStart(testCaseSummaryJsonObject.getString("Start"));
            // testCaseSummary.setPassPercentage();
            testTool.setPassPercentage(testCaseSummaryJsonObject.getString("PassPercentage"));
            testTool.setTotalcount(testCaseSummaryJsonObject.getInt("TotalTests"));
            String pass = testCaseSummaryJsonObject.getString("TestCasesPassed");
            String[] projectSeparationString = null;
            if (pass.contains(" ")) {
                projectSeparationString = pass.split(Pattern.quote(" "));
                int passTest = Integer.parseInt(projectSeparationString[0]);
                testTool.setPassCount(passTest);

            }

            testTool.setFailCount(testCaseSummaryJsonObject.getInt("TestCasesFailed"));
            // testCaseSummary.setTotalTests();
            // testCaseSummary.setEnd(testCaseSummaryJsonObject.getString("End"));
            // testCaseSummary.setTestCasesPassed(testCaseSummaryJsonObject.getString("TestCasesPassed"));
            // testCaseSummary.setTestCasesFailed();
            // reportSummary.setTestCaseSummary(testCaseSummary);

            JSONObject testStepsSummaryJsonObject = reportJsonObject.getJSONObject("TestStepsSummary");
          //  TestStepsSummary testStepsSummary = new TestStepsSummary();
            LOGGER.info("TestStepsSummary      ");
            testTool.setTestStepsFailed(testStepsSummaryJsonObject.getInt("TestStepsFailed"));
            testTool.setTestStepsPassed(testStepsSummaryJsonObject.getInt("TestStepsPassed"));
            testTool.setTotalSteps(testStepsSummaryJsonObject.getInt("TestStepsPassed"));
            // reportSummary.setTestStepsSummary(testStepsSummary);
            toolList.add(testTool);
            // repo.save(toolList);
        } catch (CodeCoverageExceptions  e) {
        	LOGGER.error(e.getMessage());
        } catch(IOException e) {
        	LOGGER.error(e.getMessage());
        }
        return toolList;
    }

    public JSONObject convertXMLToJson(String xmlFileLocation) throws CodeCoverageExceptions, IOException {
        JSONObject jsonObject = null;
        InputStream inputStream = null;
        try {
            File file = new File(xmlFileLocation);
            inputStream = new FileInputStream(file);
            StringBuilder builder = new StringBuilder();
            int ptr = 0;
            while ((ptr = inputStream.read()) != -1) {
                builder.append((char) ptr);
            }

            String xml = builder.toString();
            jsonObject = XML.toJSONObject(xml);
            LOGGER.info("json " + jsonObject);
            inputStream.close();
        } catch (Exception e) {
            throw new CodeCoverageExceptions(e);
        } finally {
        	if(inputStream != null) {
        		inputStream.close();
        	}
        	
        }

        return jsonObject;

    }
}
