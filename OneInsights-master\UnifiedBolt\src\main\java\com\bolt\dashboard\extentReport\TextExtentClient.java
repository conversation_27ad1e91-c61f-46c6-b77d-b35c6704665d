package com.bolt.dashboard.extentReport;

import java.util.List;

import com.bolt.dashboard.core.model.TestCaseSummary;
import com.bolt.dashboard.core.model.TestReportSummary;
import com.bolt.dashboard.core.model.TestTool;
import com.bolt.dashboard.core.repository.TestExtentRepo;
import com.bolt.dashboard.core.repository.TestRepository;
import com.bolt.dashboard.testcollector.TestExceptions;

public interface TextExtentClient {

    public List<TestTool> getTestExtentReport(String fileLocation, TestRepository repo,String projectName) throws TestExceptions;
}
