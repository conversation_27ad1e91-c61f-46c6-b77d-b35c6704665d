package com.bolt.dashboard.github;

import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.TimeZone;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.json.simple.parser.JSONParser;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.SCMToolRepository;

/**
 * <AUTHOR>
 *
 */
@Component
public class GitClientImplementation implements GitClient {

	private static final String SEGMENT_API = "/api/v3/repos";
	private static final String PUBLIC_GITHUB_REPO_HOST = "api.github.com";
	private static final String PUBLIC_GITHUB_HOST_NAME = "github.move.com";
	private static final Logger LOG = LogManager.getLogger(GitClientImplementation.class);
	private RestOperations rest;
	private String statsURL;
	private static long time = 0;
	private static long timestamp;
	private static long newscmCommitTimestamp;
	boolean firstRun = true;
	String branch = "master";
	String getFirstRunHistoryDays = "2000";
	int pageLimit = 100;
	SCMToolRepository scmRepo = DataConfig.getContext().getBean(SCMToolRepository.class);

	public GitClientImplementation() {
		this.rest = get();
	}

	private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000);
		requestFactory.setReadTimeout(20000);
		return new RestTemplate(requestFactory);
	}

	@SuppressWarnings("unused")
	private JSONArray parseAsArray(String url) throws RestClientException, org.json.simple.parser.ParseException {
		return (JSONArray) new JSONParser().parse(rest.getForObject(url, String.class));
	}

	public List<SCMTool> getCommits(String baseUrl, SCMToolRepository repo, String user, String pass, String apiToken,
			String projectName, String repoName) throws GitExceptions {

		List<SCMTool> scmtool = new ArrayList<>();
		boolean lastPage = false;
		String apiUrl = baseUrl;
		LOG.info("API  URL IS:" + apiUrl);

		if (apiUrl.endsWith(".git")) {
			apiUrl = apiUrl.substring(0, apiUrl.lastIndexOf(".git"));
		}
		URL url = null;
		String hostName = "";
		String protocol = "";
		int port;
		try {
			url = new URL(apiUrl);
			hostName = url.getHost();
			protocol = url.getProtocol();
			port = url.getPort();
		} catch (MalformedURLException e) {
			LOG.error(e.getMessage());
			throw new GitExceptions();
		}
		String hostUrl = protocol + "://" + hostName + "/";

		if (hostName.startsWith(PUBLIC_GITHUB_HOST_NAME)) {
			apiUrl = protocol + "://" + hostName + "/api/v3" + "/repos/"
					+ apiUrl.substring(hostUrl.length(), apiUrl.length());
		} else {
			/*
			 * hostUrl = protocol + "://" + hostName + ":" + port; apiUrl = hostUrl +
			 * SEGMENT_API + apiUrl.substring(hostUrl.length(), apiUrl.length());
			 */

			apiUrl = protocol + "://" + PUBLIC_GITHUB_REPO_HOST + "/repos/"
					+ apiUrl.substring(hostUrl.length(), apiUrl.length());

			LOG.debug("API  URL:" + apiUrl);
		}
		statsURL = apiUrl + "/commits/";
		LOG.info("statsUrl  " + statsURL);
		SCMTool scmToolData;
		List<SCMTool> scmToolList = scmRepo.findByScTypeAndProjectNameAndRepoName("GITHUB", projectName, repoName);
		long lastCommitTimeStamp = 0;
		int dataSize = scmToolList.size();
		if (!scmToolList.isEmpty()) {
			scmToolData = scmToolList.get(scmToolList.size() - 1);
			lastCommitTimeStamp = scmToolData.getCommitTS();
			firstRun = false;
		}

		Date dt = null;
		if (firstRun) {
			int firstRunDaysHistory = Integer.parseInt(getFirstRunHistoryDays);
			if (firstRunDaysHistory > 0) {
				dt = getDate(new Date(), -firstRunDaysHistory, 0);
				LOG.info(" Date:" + dt);

			}
		} else {
			dt = new Date(lastCommitTimeStamp);

		}
		Calendar calendar = new GregorianCalendar();
		TimeZone timeZone = calendar.getTimeZone();
		Calendar cal = Calendar.getInstance(timeZone);
		cal.setTime(dt);
		String thisMoment = String.format("%tFT%<tRZ", cal, cal);

		String queryUrl = apiUrl.concat("/commits");
	//	if (!(apiToken.equals("")))
		//	queryUrl = queryUrl + "?access_token=" + apiToken;

		LOG.info("queryUrl   :" + queryUrl);

		String queryUrlPage = queryUrl;

		try {
			ResponseEntity<String> response = makeRestCall(queryUrlPage, user, pass);
			LOG.info("Responce  " + response);

			// Pagination logic to get 100 commits per page .Going till last page
			int page = 1;
			String totPage = "";
			int totalPages;
			if (response != null) {
				List<String> pages = response.getHeaders().get("Link");
				String[] links = pages.get(0).split(",");
				LOG.info("page size:" + pages.size());
				String link = links[1];
				if (link.contains("last")) {
					totPage = link.split("page=")[1].split(">;")[0];
					LOG.info("totPage:" + totPage);
				}
				totalPages = Integer.parseInt(totPage);

				// Pagination ends
				while (page <= totalPages) {
					LOG.info("page:" + page);
					ResponseEntity<String> response1 = makeRestCall(queryUrl + "?page=" + page, user, pass);
					JSONArray jsonArray = parseAsArray(response1);
					if (jsonArray.length() != 0) {
						JSONObject obj = (JSONObject) jsonArray.get(0);
						if (dataSize > 0)
							newscmCommitTimestamp = getNextChangeSetID(obj);
					}

					if (newscmCommitTimestamp < lastCommitTimeStamp) {

						LOG.info("No ChangeSet to be stored    ");
					} else {

						for (int i = 0; i < jsonArray.length(); i++) {
							JSONObject jsonObject = (JSONObject) jsonArray.get(i);
							String sha = jsonObject.getString("sha");
							String urlStats = statsURL + sha;


						
							ResponseEntity<String> responseStats = makeRestCall(urlStats, user, pass);
							JSONObject statsJSONValue = parseAsObject(responseStats);
							JSONObject commitObject = (JSONObject) jsonObject.get("commit");
							JSONObject authorObject = (JSONObject) commitObject.get("author");
							String message = commitObject.getString("message");
							String author = authorObject.getString("name");
							timestamp = new DateTime(authorObject.get("date")).getMillis();
							if (time < timestamp) {
								JSONObject valueForStats = (JSONObject) statsJSONValue.get("stats");
								SCMTool commit = new SCMTool();
								int totalChange = Integer.parseInt(valueForStats.get("total").toString());
								int additionInCommit = Integer.parseInt(valueForStats.get("additions").toString());
								int deletionInCommit = Integer.parseInt(valueForStats.get("deletions").toString());

								commit.setScType("GITHUB");
								commit.setRepoName(repoName);
								commit.setTimestamp(System.currentTimeMillis());
								commit.setRevisionNo(sha);
								commit.setCommiter(author);
								commit.setCommitLog(message);
								commit.setCommitTS(timestamp);
								commit.setNoOfChanges(totalChange);
								commit.setAddition(additionInCommit);
								commit.setDeletion(deletionInCommit);
								commit.setProjectName(projectName);
								scmtool.add(commit);
							} else {
								break;
							}
						}
					}
					page++;
				}
			}
		} catch (Exception re) {
			LOG.info(re);
			lastPage = true;
			repo.save(scmtool);
			throw new GitExceptions();

		}
		Collections.sort(scmtool);
		return scmtool;
	}

	@SuppressWarnings({})
	public long getNextChangeSetID(JSONObject json) {
		JSONObject commitObject = (JSONObject) json.get("commit");
		JSONObject authorObject = (JSONObject) commitObject.get("author");
		newscmCommitTimestamp = new DateTime(authorObject.get("date")).getMillis();
		return newscmCommitTimestamp;

	}

	private Date getDate(Date dateInstance, int offsetDays, int offsetMinutes) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dateInstance);
		cal.add(Calendar.DATE, offsetDays);
		cal.add(Calendar.MINUTE, offsetMinutes);
		return cal.getTime();
	}

	private ResponseEntity<String> makeRestCall(String url, String userId, String password) {
		try {
			// Basic Auth only.
			if (!"".equals(userId) && !"".equals(password)) {
				return get().exchange(url, HttpMethod.GET, new HttpEntity<>(createHeaders(userId, password)),
						String.class);
			} else {
				return get().exchange(url, HttpMethod.GET, null, String.class);
			}
		} catch (Exception ex) {
			LOG.info(ex);
			return null;

		}
	}

	private JSONArray parseAsArray(ResponseEntity<String> response) {
		return (JSONArray) new JSONTokener(response.getBody()).nextValue();
	}

	private JSONObject parseAsObject(ResponseEntity<String> response) {

		return (JSONObject) new JSONTokener(response.getBody()).nextValue();

	}

	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		// String authHeader = "Token token=" +
		// "aa2c006cb59b0eaf4cee94b112c8c543cfd2c47b";
		String authHeader = "Basic " + new String(encodedAuth);
		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}
}