package com.bolt.dashboard.github;

import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import java.util.regex.Pattern;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.SCMToolRepository;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class GitHubApplication {

	private static final Logger LOGGER = LogManager.getLogger(GitHubApplication.class.getName());
	AnnotationConfigApplicationContext ctx = null;
	SCMToolRepository repo = null;
	GitClientImplementation scmToolMetricsimpl = null;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configuration = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	Iterator iter = null;
	ConfigurationToolInfoMetric metric1 = null;
	String result = "SUCCESS";
	String buildType = "GITHUB";

	/**
	 * Private Constructor
	 */
	public GitHubApplication() {

	}
	/*public static void main(String[] args) {
		new GitHubApplication().gitMain("FIND");
	}*/
	public void gitMain(String projectName) {
		LOGGER.info("Git Hub Collector started for " + projectName);
		ctx = DataConfig.getContext();
		repo = ctx.getBean(SCMToolRepository.class);
		String instanceURL = "";
		String apiToken = null;
		String username = null, password = null;
		scmToolMetricsimpl = new GitClientImplementation();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configuration = configurationRepo.findByProjectName(projectName).get(0);
		metric = configuration.getMetrics();
		iter = metric.iterator();
		LOGGER.info("Project name  " + configuration.getProjectName());
		List<SCMTool> scmTool;
		String repoName = null;
		String[] splitUrl = null;
		String[] splitRepoName = null;
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOGGER.info("Tool name  " + metric1.getToolName());
			if ("GITHUB".equals(metric1.getToolName())) {
				LOGGER.info("URL  " + metric1.getUrl());
				instanceURL = metric1.getUrl();
				username = metric1.getUserName();
				
					password=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
					
				apiToken = metric1.getProjectCode();
				repoName = metric1.getRepoName();
				break;
			}

		}

		if (repoName == null)
			repoName = projectName;
		if (instanceURL.contains(",")) {
			splitUrl = instanceURL.split(Pattern.quote(","));
			splitRepoName = repoName.split(Pattern.quote(","));
		} else {
			splitUrl = new String[1];
			splitUrl[0] = instanceURL;
			splitRepoName = new String[1];
			splitRepoName[0] = repoName;

		}

		for (int i = 0; i < splitUrl.length; i++) {
			try {
				scmTool = scmToolMetricsimpl.getCommits(splitUrl[i], repo, username, password, apiToken, projectName,
						splitRepoName[i]);
				if (!scmTool.isEmpty())
					repo.save(scmTool);

			} catch (GitExceptions e) {
				result="FAIL";
				ConstantVariable.getLastRun(projectName, buildType, new Date().getTime(), result);
				LOGGER.info(e.getMessage());
			}
		}
		ConstantVariable.getLastRun(projectName, buildType, new Date().getTime(), result);

		LOGGER.info("Git Hub Collector ended for " + projectName);
		cleanObject();

	}

	public void cleanObject() {
		repo = null;
		scmToolMetricsimpl = null;
		configurationRepo = null;
		configuration = null;
		metric = null;
		iter = null;
		metric1 = null;

	}
}