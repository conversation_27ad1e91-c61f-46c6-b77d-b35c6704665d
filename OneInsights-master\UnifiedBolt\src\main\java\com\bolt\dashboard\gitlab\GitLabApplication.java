package com.bolt.dashboard.gitlab;

import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.SCMMergeRepository;
import com.bolt.dashboard.core.repository.SCMToolRepository;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class GitLabApplication {

	private static final Logger LOGGER = LogManager.getLogger(GitLabApplication.class.getName());
	AnnotationConfigApplicationContext ctx = null;
	SCMToolRepository repo = null;
	GitLabClientImplementation scmToolMetricsimpl = null;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configuration = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	ConfigurationToolInfoMetric metric1 = null;
	String buildType = "GITLAB";
	String result = "SUCCESS";
	SCMMergeRepository mrRepo ;

	/**
	 * Private Constructor
	 */
	public GitLabApplication() {

	}

//	public static void main(String[] args) {
//		new GitLabApplication().gitLabMain("Network Personalization ART");
//	}

	public void gitLabMain(String projectName) {
		LOGGER.info("Git Lab Collector started for " + projectName);
		ctx = DataConfig.getContext();
		repo = ctx.getBean(SCMToolRepository.class);
		mrRepo= ctx.getBean(SCMMergeRepository.class);
		String instanceURL = "";
		String branch = "";
		String username = "";
		String projectCode = "";
		String password = null;
		String getFirstRunHistoryDays = null;
		boolean firstRun = false;
		scmToolMetricsimpl = new GitLabClientImplementation();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configuration = configurationRepo.findByProjectName(projectName).get(0);
		metric = configuration.getMetrics();
		@SuppressWarnings("rawtypes")
		Iterator iter = metric.iterator();
		LOGGER.info("Project name  " + configuration.getProjectName());
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOGGER.info("Tool name  " + metric1.getToolName());
			if ("GITLAB".equals(metric1.getToolName())) {
				LOGGER.info("URL  " + metric1.getUrl());
				instanceURL = metric1.getUrl();
				username = metric1.getUserName();
				
					password=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				  // password=metric1.getPassword();
				projectCode = metric1.getProjectCode();
				break;

			}

		}
		try {
			// File fXmlFile = new
			// File("src/main/java/com/bolt/dashboard/github/GitMetrics.xml");
			// DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
			// DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
			// Document doc = dBuilder.parse(fXmlFile);
			// doc.getDocumentElement().normalize();
			// NodeList nList = doc.getElementsByTagName("Tool");
			// for (int temp = 0; temp < nList.getLength(); temp++) {
			// Node nNode = nList.item(temp);
			// if (nNode.getNodeType() == Node.ELEMENT_NODE) {
			// Element eElement = (Element) nNode;
			//
			// LOG.info("Instance URL is as :" + instanceURL);
			// getFirstRunHistoryDays =
			// eElement.getElementsByTagName("getFirstRunHistoryDays").item(0)
			// .getTextContent();
			// branch = eElement.getElementsByTagName("branch").item(0).getTextContent();
			// firstRun = eElement.getElementsByTagName("firstRun").item(0).getTextContent()
			// != null;
			// LOG.info("GetfirstRunHistoryDays value is: " + getFirstRunHistoryDays);
			// LOG.info("Branch values is: " + branch);
			// LOG.info("value of Run is :" + firstRun);
			// }
			// }
			String multiRepo[] = projectCode.split(",");
			
			int total_pages=1;
			for (int i = 0; i < multiRepo.length; i++) {
				List<SCMTool> scmTool = null;		
				ResponseEntity<String> groupResponse = checkGroups(instanceURL, multiRepo[i], password,1);
				if (groupResponse.getStatusCode() == HttpStatus.NOT_FOUND || groupResponse.getBody().length()>0) {
					ResponseEntity<String> projectResponse = checkProjects(instanceURL, multiRepo[i], password,1);
					if (projectResponse.getStatusCode() == HttpStatus.NOT_FOUND) {
						scmTool = scmToolMetricsimpl.getCommits(instanceURL, repo, mrRepo, firstRun, branch, getFirstRunHistoryDays,
								multiRepo[i], password, projectName, "");
						repo.save(scmTool);
					}else {
						int page3=1;
						List<String> pages = projectResponse.getHeaders().get("X-Total-Pages");
						int total_pages3=Integer.parseInt(pages.get(0));
						for(;page3<=total_pages3;page3++) {
							
							if(page3>1)
							{
						 projectResponse = checkProjects(instanceURL, multiRepo[i], password,page3);
							}
						JSONArray response = parseAsArray(projectResponse);
						
						processGroup(projectName, instanceURL, branch, password, getFirstRunHistoryDays, firstRun,
								response);
						}
					}

				}else {
					List<String> pages = groupResponse.getHeaders().get("X-Total-Pages");
					//LOGGER.info(groupResponse.getHeaders());
					total_pages = Integer.parseInt(pages.get(0));
					int page=1;
					for(;page<=total_pages;page++) {
						if(page>1)
						{
					 groupResponse = checkGroups(instanceURL, multiRepo[i], password,page);
							
						}
					JSONArray response = parseAsArray(groupResponse);
					LOGGER.info("Group Length "+response.length());
					for (int k = 0; k < response.length(); k++) {
						
						JSONObject groupObj = (JSONObject) response.get(k);
						ResponseEntity<String> projectResponse = checkProjects(instanceURL, String.valueOf(groupObj.getInt("id")), password,1);
						
					 pages = projectResponse.getHeaders().get("X-Total-Pages");
						int total_pages2=Integer.parseInt(pages.get(0));
						int page2=1;
						for(;page2<=total_pages2;page2++) {
							
							if(page2>1)
							{
						 projectResponse = checkProjects(instanceURL, multiRepo[i], password,page2);
								
							}
//							String groupNames=groupObj.getString("full_name");
//							String group[]=groupNames.split(" / "+groupObj.getString("name"));
//							group=group[0].split(" / ");
//							groupNames=group[0];
						JSONArray projResponse = parseAsArray(projectResponse);
						
						processGroup(projectName, instanceURL, branch, password, getFirstRunHistoryDays, firstRun,/*group[0],*/
								projResponse);
						}
					}
				 }
					
				}
		 }
			cleanObject();
		} catch (Exception e) {
			cleanObject();
			result = "FAILED";
			ConstantVariable.getLastRun(projectName, buildType, new Date().getTime(), result);
			LOGGER.error("Issue in Gitlab Collector" + e.getMessage());
		} finally {
			repo = null;

		}
		ConstantVariable.getLastRun(projectName, buildType, new Date().getTime(), result);
		LOGGER.info("Git Lab Collector ended for " + projectName);
	}

	private void processGroup(String projectName, String instanceURL, String branch, String password,
			String getFirstRunHistoryDays, boolean firstRun,JSONArray response) throws GitLabExceptions {
		List<SCMTool> scmTool;
		LOGGER.info("No Of Projects In group "+response.length());
		for (int j = 0; j < response.length(); j++) {
			scmTool = null;
			JSONObject projObj = (JSONObject) response.get(j);

			JSONObject nameSpace = projObj.getJSONObject("namespace");
			String group = nameSpace.getString("name");
			int projCode = projObj.getInt("id");
			String repoName = projObj.getString("name");
			LOGGER.info("Projects name:  "+repoName);
			scmTool = scmToolMetricsimpl.getCommits(instanceURL, repo, mrRepo, firstRun, branch,
					getFirstRunHistoryDays, String.valueOf(projCode), password, projectName, group);
			repo.save(scmTool);
		}
	}

	private JSONArray parseAsArray(ResponseEntity<String> response) {
		return (JSONArray) new JSONTokener(response.getBody()).nextValue();
	}

	private ResponseEntity<String> checkGroups(String instanceURL, String groupCode, String password, int page) {
		instanceURL = instanceURL + "/api/v4/groups/" + groupCode + "/subgroups?private_token=" + password+"&per_page=50&page="+page;
		ResponseEntity<String> response = makeRestCall(instanceURL);
		return response;
	}
	private ResponseEntity<String> checkProjects(String instanceURL, String groupCode, String password, int page) {
		instanceURL = instanceURL + "/api/v4/groups/" + groupCode + "/projects?private_token=" +  password+"&per_page=50&page="+page;
		ResponseEntity<String> response = makeRestCall(instanceURL);
		return response;
	}
	
	private ResponseEntity<String> makeRestCall(String url) {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000000);
		requestFactory.setReadTimeout(20000000);
		RestTemplate rest = new RestTemplate(requestFactory);

		try {
			ResponseEntity<String> response = rest.exchange(url, HttpMethod.GET, null, String.class);
			return response;
		} catch (RestClientException e) {
			
			LOGGER.error("Exception in calling Gitlap Group API" + e.getMessage());
			return new ResponseEntity<String>(HttpStatus.NOT_FOUND);
		}

	}

	public void cleanObject() {
		repo = null;
		scmToolMetricsimpl = null;
		configurationRepo = null;
		configuration = null;
		metric = null;
		metric1 = null;

	}
}