package com.bolt.dashboard.gitlab;

import java.util.List;

import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.SCMMergeRepository;
import com.bolt.dashboard.core.repository.SCMToolRepository;

public interface GitLabClient {
    public List<SCMTool> getCommits(String url, SCMToolRepository repo, SCMMergeRepository mrRepo, boolean firstRun, String branch,
            String getFirstRunHistoryDays, String username, String password, String projectName,String groupName)
            throws GitLabExceptions;
}