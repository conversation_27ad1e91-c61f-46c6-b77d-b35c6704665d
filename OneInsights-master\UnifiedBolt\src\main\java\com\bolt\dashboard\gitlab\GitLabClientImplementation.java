package com.bolt.dashboard.gitlab;

import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.TimeZone;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.json.simple.parser.JSONParser;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.model.RepoCodeCoverageStatus;
import com.bolt.dashboard.core.model.SCMMR;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.SCMMergeRepository;
import com.bolt.dashboard.core.repository.SCMToolRepository;

/**
 * <AUTHOR>
 *
 */
@Component
public class GitLabClientImplementation implements GitLabClient {

	private static final Logger LOGGER = LogManager.getLogger(GitLabClientImplementation.class);
	private static final String SEGMENT_API = "/api/v4/projects/";
	private static final String PUBLIC_GITHUB_REPO_HOST = "api.github.com/repos/";
	private static final String PUBLIC_GITLAB_MERGE_REQ = "/merge_requests?state=all&private_token=";
	private static final String PUBLIC_GITHUB_HOST_NAME = "github.com";
	private RestOperations rest;
	private String statsURL;
	private static long time = 0;
	private static long timestamp;
	private static long newscmCommitTimestamp;
	int page = 1;
	int per_page = 100;
	int totalPages = 0;
	SCMMergeRepository mrRepo;
	private String mergeRequestURL;

	public GitLabClientImplementation() {
		this.rest = get();
	}

	private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000000);
		requestFactory.setReadTimeout(20000000);
		return new RestTemplate(requestFactory);
	}

	@SuppressWarnings("unused")
	private JSONArray parseAsArray(String url) throws RestClientException, org.json.simple.parser.ParseException {
		return (JSONArray) new JSONParser().parse(rest.getForObject(url, String.class));
	}

	public List<SCMTool> getCommits(String baseUrl, SCMToolRepository repo, SCMMergeRepository mrRepo, boolean firstRun,
			String branch, String getFirstRunHistoryDays, String projectCode, String pass, String projectName,
			String groupName) throws GitLabExceptions {
		this.mrRepo = mrRepo;
		// page=1;
		List<SCMTool> scmtool = new ArrayList<>();
		List<SCMMR> scmMRArr = new ArrayList<>();

		String apiUrl = baseUrl;
		LOGGER.info("API URL IS:" + apiUrl);

		if (apiUrl.endsWith(".git")) {
			apiUrl = apiUrl.substring(0, apiUrl.lastIndexOf(".git"));
		}
		if (apiUrl.endsWith("/")) {
			apiUrl = apiUrl.substring(0, apiUrl.lastIndexOf("/"));
		}
		URL url = null;
		String hostName = "";
		String protocol = "";
		int port;
		try {
			url = new URL(apiUrl);
			hostName = url.getHost();
			protocol = url.getProtocol();
			port = url.getPort();
		} catch (MalformedURLException e) {
			LOGGER.error(e.getMessage());
			throw new GitLabExceptions();
		}
		String hostUrl = protocol + "://" + hostName;

		if (hostName.startsWith(PUBLIC_GITHUB_HOST_NAME)) {
			apiUrl = protocol + "://" + PUBLIC_GITHUB_REPO_HOST + apiUrl.substring(hostUrl.length(), apiUrl.length());
		} else {
			// hostUrl = protocol + "://" + hostName + ":" + port;
			// apiUrl = hostUrl + SEGMENT_API + apiUrl.substring(hostUrl.length(),
			// apiUrl.length());
			apiUrl = hostUrl + SEGMENT_API;
			LOGGER.debug("API  URL IS:" + apiUrl);
		}
		statsURL = apiUrl + projectCode + "/repository/commits/";

		Date dt = null;
		if (firstRun) {
			int firstRunDaysHistory = Integer.parseInt(getFirstRunHistoryDays);
			if (firstRunDaysHistory > 0) {
				dt = getDate(new Date(), -firstRunDaysHistory, 0);

			}
		} else {
			dt = getDate(new Date(), -1, 0);

		}
		Calendar calendar = new GregorianCalendar();
		TimeZone timeZone = calendar.getTimeZone();
		Calendar cal = Calendar.getInstance(timeZone);
		cal.setTime(dt);

		@SuppressWarnings("unused")

		String queryUrl = apiUrl.concat(projectCode + "/repository/commits?private_token=" + pass + "&all=" + true);

		String statisticsUrl = apiUrl.concat(projectCode + "?private_token=" + pass + "&statistics=true");
		LOGGER.info("queryUrl called");
		ResponseEntity<String> staticsticResponse = makeRestCall(statisticsUrl, "", pass);
		if (staticsticResponse != null) {
			JSONObject jsonObjForCommits = parseAsObject(staticsticResponse);
			String repoName = jsonObjForCommits.getString("name");
			if (groupName.equals("")) {
				groupName = repoName;
			}
			jsonObjForCommits = jsonObjForCommits.getJSONObject("statistics");
			int totalCommits = jsonObjForCommits.getInt("commit_count");
			totalPages = (int) Math.ceil((double) totalCommits / 100);
			String queryUrlPage = queryUrl;
			List<SCMTool> collection = repo.findByScTypeAndProjectNameAndRepoName("GITLAB", projectName, repoName);
			if (collection.isEmpty()) {
				page = 1;
				time = 0;
				// per_page=100;
			} else {
				int totalStoredCommits = collection.size();

				page = Math.floorDiv(totalStoredCommits, 100);
				if (page == 0)
					page = 1;
				time = collection.get(collection.size() - 1).getCommitTS();

			}

			ResponseEntity<String> response = makeRestCall(queryUrlPage, "", pass);
			if (response != null && response.getBody().length() > 0) {
				JSONArray jsonArray = parseAsArray(response);
				if (jsonArray.length() > 0) {
					JSONObject obj = (JSONObject) jsonArray.get(0);
					getNextChangeSetID(obj);
					timestamp = newscmCommitTimestamp;
					if (newscmCommitTimestamp <= time) {

						LOGGER.info("No ChangeSet to be stored    ");
					} else {
						while (page <= totalPages) {

							try {

								response = makeRestCall(queryUrl + "&page=" + page + "&per_page=" + per_page, "",
										pass + "&all=" + true);
								if (response != null && response.getBody().length() > 0) {
									jsonArray = parseAsArray(response);
									if (jsonArray.length() > 0) {
										obj = (JSONObject) jsonArray.get(0);
										getNextChangeSetID(obj);
										timestamp = newscmCommitTimestamp;

										for (int i = 0; i < jsonArray.length(); i++) {
											JSONObject jsonObject = (JSONObject) jsonArray.get(i);
											String id = jsonObject.getString("id");
											// String urlStats = statsURL + id;
											// LOG.info("REST API URL " + urlStats);
											String message = jsonObject.getString("title");
											String author = jsonObject.getString("author_name");

											timestamp = new DateTime(jsonObject.get("created_at")).getMillis();
											if (time < timestamp) {
												SCMTool commit = new SCMTool();
												// statsURL=statsURL+id+"/diff?private_token="+pass;
												ResponseEntity<String> diffResponse = makeRestCall(
														statsURL + id + "/diff?private_token=" + pass, "", pass);
												if (diffResponse != null) {
													JSONArray diffJson = parseAsArray(diffResponse);
													// JSONObject statsJson=jsonObject.getJSONObject("stats");
													commit.setTimestamp(System.currentTimeMillis());
													// commit.setAddition(statsJson.getInt("additions"));
													// commit.setDeletion(statsJson.getInt("deletions"));
													// commit.setNoOfChanges(statsJson.getLong("total"));
													int additions = 0;
													int deletion = 0;
													int modification = 0;
													for (int j = 0; j < diffJson.length(); j++) {
														JSONObject fileJson = diffJson.getJSONObject(j);
														if (fileJson.getBoolean("new_file"))
															additions++;
														else if (fileJson.getBoolean("renamed_file")
																|| (!fileJson.getBoolean("new_file")
																		&& !fileJson.getBoolean("deleted_file")))
															modification++;
														else if (fileJson.getBoolean("deleted_file"))
															deletion++;

													}
													int noofChanges = additions + modification + deletion;
													commit.setNoOfChanges(noofChanges);
													commit.setAddition(additions);
													commit.setModification(modification);
													commit.setDeletion(deletion);
													commit.setScType("GITLAB");
													commit.setRevisionNo(id);
													commit.setCommiter(author);
													commit.setCommitLog(message);
													commit.setCommitTS(timestamp);
													commit.setRepoName(repoName);
													commit.setGroupName(groupName);
													commit.setProjectName(projectName);
													scmtool.add(commit);
												} else {
													break;
												}
											}

										}
									}
								}
							} catch (Exception re) {
								LOGGER.info(re);
								throw new GitLabExceptions(re);

							}
							page++;
						}
					}
				} else {
					LOGGER.info("queryUrl response- no data");
				}
			}
			// Fetching the Merge Request Details for Each Repo
			mergeRequestURL = apiUrl + projectCode + PUBLIC_GITLAB_MERGE_REQ;
			List<SCMMR> collectionMR = mrRepo.findByProjectNameAndRepoName(projectName, repoName);
			if (collectionMR.isEmpty()) {
				page = 1;
				time = 0;
				// per_page=100;
			} else {
				int totalStoredCommits = collectionMR.size();

				page = Math.floorDiv(totalStoredCommits, 100);
				if (page == 0)
					page = 1;
				time = collectionMR.get(totalStoredCommits- 1).getCreatedAt();

			}
			LOGGER.info("mergeRequestURL: " + mergeRequestURL);
			ResponseEntity<String> responseMR = makeRestCall(mergeRequestURL + pass, "", "");

			List<String> pagesMr = responseMR.getHeaders().get("X-Total-Pages");
			int total_pagesMR = Integer.parseInt(pagesMr.get(0));
			if (responseMR != null && responseMR.getBody().length() > 0) {
				JSONArray jsonArray = parseAsArray(responseMR);
				if (jsonArray.length() > 0) {
					JSONObject obj = (JSONObject) jsonArray.get(0);
//					getNextChangeSetID(obj);
//					timestamp = newscmCommitTimestamp;
//					if (newscmCommitTimestamp <= time) {
//
//						LOGGER.info("No ChangeSet to be stored  in merge request");
//					} else {
						while (page <= total_pagesMR) {
							try {

								response = makeRestCall(mergeRequestURL +pass+ "&page=" + page + "&per_page=" + per_page, "",
										pass);
								if (response != null && response.getBody().length() > 0) {
									jsonArray = parseAsArray(response);
									if (jsonArray.length() > 0) {
										obj = (JSONObject) jsonArray.get(0);
//										getNextChangeSetID(obj);
//										timestamp = newscmCommitTimestamp;

										for (int i = 0; i < jsonArray.length(); i++) {
											SCMMR scmMR = new SCMMR();
											JSONObject jsonObject = (JSONObject) jsonArray.get(i);

											scmMR.setTitle(jsonObject.getString("title"));

											scmMR.setTargetBranch(jsonObject.getString("target_branch"));
											scmMR.setSourceBranch(jsonObject.getString("source_branch"));

											timestamp = new DateTime(jsonObject.get("created_at")).getMillis();
											scmMR.setCreatedAt(timestamp);
											timestamp = new DateTime(jsonObject.get("updated_at")).getMillis();
											scmMR.setUpdatedAt(timestamp);
											scmMR.setState(jsonObject.getString("state"));
											scmMR.setProjectName(projectName);
											scmMR.setRepoName(repoName);
											scmMR.setGroupName(groupName);
											scmMR.setScType("GITLAB");
											scmMRArr.add(scmMR);
										}
									}
								}
							} catch (Exception re) {
								LOGGER.info(re);
								throw new GitLabExceptions(re);
							}
							page++;

						}
						mrRepo.deleteAll();
						mrRepo.save(scmMRArr);
					}
				
//				}

			}

		}
		Collections.reverse(scmtool);
		return scmtool;
	}

	private JSONObject parseAsObject(ResponseEntity<String> staticsticResponse) {
		
		return (JSONObject) new JSONTokener(staticsticResponse.getBody()).nextValue();
	}

	public void getNextChangeSetID(JSONObject json) {
		newscmCommitTimestamp = new DateTime(json.get("created_at")).getMillis();
	}

	private Date getDate(Date dateInstance, int offsetDays, int offsetMinutes) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dateInstance);
		cal.add(Calendar.DATE, offsetDays);
		cal.add(Calendar.MINUTE, offsetMinutes);
		return cal.getTime();
	}

	private ResponseEntity<String> makeRestCall(String url, String userId, String password) {
		// Basic Auth only.
		// if (!"".equals(userId) && !"".equals(password)) {
		// return get().exchange(url, HttpMethod.GET, new
		// HttpEntity<>(createHeaders(userId, password)), String.class);
		//
		// } else {
		// return get().exchange(url, HttpMethod.GET, null, String.class);
		// }
		return get().exchange(url, HttpMethod.GET, null, String.class);
	}

	private JSONArray parseAsArray(ResponseEntity<String> response) {
		return (JSONArray) new JSONTokener(response.getBody()).nextValue();
	}

	@SuppressWarnings("unused")
	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}
}
