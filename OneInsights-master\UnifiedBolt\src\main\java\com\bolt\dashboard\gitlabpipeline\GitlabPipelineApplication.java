package com.bolt.dashboard.gitlabpipeline;

import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import com.atlassian.jira.rest.client.api.RestClientException;
import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class GitlabPipelineApplication {

	private static final Logger LOGGER = LogManager.getLogger(GitlabPipelineApplication.class.getName());
	AnnotationConfigApplicationContext applicationContext = null;
	BuildToolRep repo = null;
	GitlabPipeline gitlabPipelineMetrics = null;
	String result = "SUCCESS";
	String buildType = "Gitlab Pipeline";
	int pageLimit = 100;
	AnnotationConfigApplicationContext ctx = null;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configuration = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	ConfigurationToolInfoMetric metric1 = null;

//	public static void main(String[] args) {
//		new GitlabPipelineApplication().gitlabPipelineMain("Network Personalization ART");
//	}

	public void gitlabPipelineMain(String projectName) throws RestClientException {
		LOGGER.info("Gitlab Pipeline Collector started for " + projectName);
		ctx = DataConfig.getContext();
		repo = ctx.getBean(BuildToolRep.class);
		String instanceURL = "";
		String branch = "";
		String groupCode = "";
		String password = null;
		boolean firstRun = false;
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configuration = configurationRepo.findByProjectName(projectName).get(0);
		metric = configuration.getMetrics();
		@SuppressWarnings("rawtypes")
		Iterator iter = metric.iterator();
		LOGGER.info("Project name  " + configuration.getProjectName());
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOGGER.info("Tool name  " + metric1.getToolName());
			if ("GITLAB Pipeline".equals(metric1.getToolName())) {
				LOGGER.info("URL  " + metric1.getUrl());
				instanceURL = metric1.getUrl();
				password = EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				groupCode = metric1.getProjectCode();
				break;
			}
		}
		applicationContext = DataConfig.getContext();
		repo = applicationContext.getBean(BuildToolRep.class);

		gitlabPipelineMetrics = new GitlabPipelineImplementation();
		try {

			String multiRepo[] = groupCode.split(",");
			for (int i = 0; i < multiRepo.length; i++) {
				int page = 1;
				int total_pages = 1;
				String groupId = multiRepo[i].replaceAll("\\s+", "");
				ResponseEntity<String> groupResponse = checkGroup(instanceURL, groupId, password, page);
				if (groupResponse.getStatusCode() != HttpStatus.UNAUTHORIZED) {
					if (groupResponse.getStatusCode() == HttpStatus.NOT_FOUND) {

						ResponseEntity<String> projectResponse = checkProject(instanceURL, groupId, password);
						if (projectResponse != null) {

							JSONObject projectObj = new JSONObject(projectResponse.getBody());
							String repoName = projectObj.getString("name");
							int projCode = projectObj.getInt("id");
							gitlabPipelineMetrics.getBuildTool(instanceURL, repo, firstRun, branch,
									String.valueOf(projCode), password, projectName, repoName, repoName);
						}
					} else {
						if (page == 1) {
							List<String> pages = groupResponse.getHeaders().get("X-Total-Pages");
							LOGGER.info(pages.get(0));
							total_pages = Integer.parseInt(pages.get(0));
						}
						for (; page <= total_pages; page++) {
							if (page > 1) {
								groupResponse = checkGroup(instanceURL, groupId, password, page);
							}
							JSONArray response = parseAsArray(groupResponse);
							for (int j = 0; j < response.length(); j++) {
								JSONObject projObj = (JSONObject) response.get(j);
								JSONObject nameSpace = projObj.getJSONObject("namespace");
								String groupName = nameSpace.getString("name");
								String repoName = projObj.getString("name");
								int projCode = projObj.getInt("id");
								gitlabPipelineMetrics.getBuildTool(instanceURL, repo, firstRun, branch,
										String.valueOf(projCode), password, projectName, groupName, repoName);
							}
						}
					}
				} else
					LOGGER.error("UnAuthorized Error, Please update the Access Token");
			}
		} catch (Exception e) {
			result = "FAIL";
			ConstantVariable.getLastRun(projectName, buildType, new Date().getTime(), result);
			cleanObject();
			LOGGER.error("Gitlab Pipeline Exception ", e.fillInStackTrace());
			LOGGER.info("Gitlab Pipeline Collector failed for " + projectName);

		}
		cleanObject();
		ConstantVariable.getLastRun(projectName, buildType, new Date().getTime(), result);
		LOGGER.info("Gitlab Pipeline Collector ended for " + projectName);
	}

	public void cleanObject() {
		repo = null;
		gitlabPipelineMetrics = null;
	}

	private JSONArray parseAsArray(ResponseEntity<String> response) {
		return (JSONArray) new JSONTokener(response.getBody()).nextValue();
	}

	private ResponseEntity<String> checkGroup(String instanceURL, String groupCode, String password, int page) {
		
		instanceURL = instanceURL + "/api/v4/groups/" + groupCode + "/projects?private_token=" + password + "&per_page="
				+ pageLimit + "&page=" + page;
		LOGGER.info(instanceURL);
		ResponseEntity<String> response = makeRestCall(instanceURL);
		if(response!=null) {
			LOGGER.info(response.getHeaders().get("X-Total-Pages"));
		}
		

		return response;
	}

	private ResponseEntity<String> checkProject(String instanceURL, String projectCode, String password) {
		
		instanceURL = instanceURL + "/api/v4/projects/" + projectCode + "?private_token=" + password;
		LOGGER.info(instanceURL);
		ResponseEntity<String> response = makeRestCall(instanceURL);

		return response;
	}

	private ResponseEntity<String> makeRestCall(String url) {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000000);
		requestFactory.setReadTimeout(20000000);
		RestTemplate rest = new RestTemplate(requestFactory);

		try {
			ResponseEntity<String> response = rest.exchange(url, HttpMethod.GET, null, String.class);
			return response;
		} catch (HttpClientErrorException e) {
			
			LOGGER.error("Pipeline Error in Gitlab Group ", e);
			if (e.getMessage().contains("404"))
				return new ResponseEntity<String>(HttpStatus.NOT_FOUND);
			else if (e.getMessage().contains("401"))
				return new ResponseEntity<String>(HttpStatus.UNAUTHORIZED);
		} catch (Exception e) {
			// TODO: handle exception
			
			LOGGER.info(e);
			return new ResponseEntity<String>(HttpStatus.NOT_FOUND);
		}
		return null;

	}
}
