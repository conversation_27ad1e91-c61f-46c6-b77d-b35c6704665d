package com.bolt.dashboard.gitlabpipeline;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import org.apache.logging.log4j.Logger;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.BuildFailurePatternForProjectInJenkinsModel;
import com.bolt.dashboard.core.model.BuildFailurePatternMetrics;
import com.bolt.dashboard.core.model.BuildSteps;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.repository.BuildFailurePatternForProjectRepo;
import com.bolt.dashboard.core.repository.BuildToolRep;

public class GitlabPipelineImplementation implements GitlabPipeline {
	private static final Logger LOGGER = LogManager.getLogger(GitlabPipelineImplementation.class.getName());
	private static final String SEGMENT_API = "api/v4/projects/";
	AnnotationConfigApplicationContext ctx = null;
	String projectName = "";
	String userName = "";
	String password = "";
	private static long time = 0;
	// List<String> jobsList = new ArrayList<>();
	BuildToolRep repo = null;
	List<String> jobCollection = new ArrayList<>();
	String pipelineUrl = null;
	String singlePipelineUrl = null;
	String gitlabUrl = null;
	String jobsUrl = null;
	int size = 0;
	int lastPage = 0;
	long lastBuildid;
	private static long newbuildPipelineTimestamp;
	private static long timestamp;
	int page = 1;
	int per_page = 100;
	long totalPages = 1000000000;
	String pipelineId = null;
	List<BuildTool> builds = new ArrayList<>();
	BuildTool build;
	List<BuildSteps> jobsList;
	BuildFailurePatternForProjectRepo failurePatternRepo = null;
	String repoName;

	public void getBuildTool(String baseUrl, BuildToolRep repo, boolean firstRun, String branch, String projectCode,
			String pass, String projectName, String groupName, String repoName) {
		
		gitlabUrl = baseUrl + SEGMENT_API + projectCode;
		this.projectName = projectName;
		this.repo = repo;
		this.repoName = repoName;
		page = 0;
		ResponseEntity<String> response = makeRestCall(gitlabUrl + "/pipelines?private_token=" + pass);
		if (response != null) {
//			LOGGER.info(gitlabUrl + "/pipelines?private_token=" + pass);
			JSONArray valueArray = new JSONArray(response.getBody());
			if (valueArray.length() > 0) {
				List<String> pages = response.getHeaders().get("X-Total-Pages");
//				LOGGER.info(pages.get(0));
				totalPages = Integer.parseInt(pages.get(0));
				JSONObject obj = (JSONObject) valueArray.get(0);
				newbuildPipelineTimestamp = getTimeInMiliseconds(obj.getString("created_at"));
				;
				timestamp = newbuildPipelineTimestamp;
				List<BuildTool> tool = repo.findByBuildTypeAndRepoNameAndGroupNameOrderByBuildIDDesc("GITLAB", repoName,
						groupName);
				if (tool.isEmpty()) {
					page = 1;
					time = 0;
					// per_page=100;
				} else {
					int totalStoredBuild = tool.size();

					page = Math.floorDiv(totalStoredBuild, 100);
					if (page == 0)
						page = 1;
					time = tool.get(0).getTimestamp();

				}
				if (newbuildPipelineTimestamp <= time) {

					LOGGER.info("No ChangeSet to be stored    ");
				} else {
					while (page <= totalPages) {
						builds = new ArrayList<>();
						pipelineUrl = gitlabUrl + "/pipelines/?private_token=" + pass + "&page=" + page + "&per_page="
								+ per_page;
//						LOGGER.info("countP: " + pipelineUrl);
//						LOGGER.info("countP: " + page + " " + per_page);
						response = makeRestCall(pipelineUrl);
						if (response != null) {
							valueArray = new JSONArray(response.getBody());
							if (valueArray.length() > 0) {
								obj = (JSONObject) valueArray.get(0);
								newbuildPipelineTimestamp = getTimeInMiliseconds(obj.getString("created_at"));
								timestamp = newbuildPipelineTimestamp;

//								LOGGER.info("pipeline length : " + valueArray.length());
								processPipelineData(valueArray, pass, groupName, repoName);

								repo.save(builds);
								LOGGER.info(
										(((page - 1) * 100) + builds.size()) + " builds are saved:" + this.repoName);
								page++;
							} else
								break;

						}

					}
				}
			} else {
				LOGGER.info("pipeline length : " + valueArray.length());
				LOGGER.info("-------------------------- No CICD Setup ------------------ ");
			}
		}
	}

	private void processPipelineData(JSONArray pipelineValues, String pass, String groupName, String repoName) {

		for (int i = 0; i < pipelineValues.length(); i++) {
			JSONObject pipeline_Obj = pipelineValues.getJSONObject(i);

			timestamp = getTimeInMiliseconds(pipeline_Obj.getString("created_at"));
			if (time < timestamp) {
				build = new BuildTool();
				int id = pipeline_Obj.optInt("id");
				build.setBuildID(id);
				build.setGroupName(groupName);
				build.setRepoName(repoName);
				this.pipelineId = "" + id;
				// Single pipeline Details
				singlePipelineUrl = gitlabUrl + "/pipelines/" + id + "?private_token=" + pass;
//				LOGGER.info(singlePipelineUrl);
				ResponseEntity<String> pipelineResponse = makeRestCall(singlePipelineUrl);
				if (pipelineResponse != null) {
					pipeline_Obj = new JSONObject(pipelineResponse.getBody());

					// pipeline jobs Details
					jobsUrl = gitlabUrl + "/pipelines/" + this.pipelineId + "/jobs/";
//					LOGGER.info(jobsUrl);
					ResponseEntity<String> jobResponse = makeRestCall(jobsUrl + "?private_token=" + pass);
					if (jobResponse != null) {
						if (build.getBuildID() <= lastBuildid)
							continue;

						build.setBranchName(pipeline_Obj.optString("ref"));
						build.setBuildType("GITLAB");
						build.setName(projectName);
						String temp_date = null;
						if (!pipeline_Obj.isNull("created_at")) {
							temp_date = pipeline_Obj.optString("created_at");
						}
						long createTime = 0;
						if (temp_date != null) {
							createTime = getTimeInMiliseconds(temp_date);
						}
						build.setTimestamp(createTime);
						temp_date = null;
						if (!pipeline_Obj.isNull("created_at")) {
							temp_date = pipeline_Obj.getString("created_at");
						}
						long completeTime = 0;
						if (temp_date != null) {
							completeTime = getTimeInMiliseconds(temp_date);
						}
						// Building the Metrics Data of Result, duration, timestamp

						BuildToolMetric durationMetric = new BuildToolMetric("duration");
						long duration = 0;
						if (!pipeline_Obj.isNull("duration")) {
							duration = pipeline_Obj.optLong("duration") * 1000;
						}
						durationMetric.setValue(duration);
						BuildToolMetric resultMetric = new BuildToolMetric("result");
						resultMetric.setValue(pipeline_Obj.optString("status"));
						BuildToolMetric timestampMetric = new BuildToolMetric("timestamp");
						timestampMetric.setValue(completeTime);

						build.getMetrics().add(resultMetric);
						build.getMetrics().add(durationMetric);
						build.getMetrics().add(timestampMetric);
						JSONArray jobsArray = new JSONArray(jobResponse.getBody());
						if (jobsArray.length() > 0) {
							JSONObject singleJobsobj = jobsArray.getJSONObject(0);
							JSONObject commitObj = singleJobsobj.getJSONObject("commit");
							build.setCreatedBy(commitObj.optString("author_name"));
							build.setTriggerType(commitObj.optString("author_name"));
							jobsList = new ArrayList<BuildSteps>();
							for (int j = 0; j < jobsArray.length(); j++) {
								BuildSteps tempSteps = new BuildSteps();
								try {
									JSONObject stepsObj = jobsArray.getJSONObject(j);
									// tempJson = stepsObj.getJSONObject("state");
									if (!stepsObj.isNull("duration")) {
										tempSteps.setDuration(stepsObj.optLong("duration") * 1000);
									}
									String completeDate = null;
									if (!stepsObj.isNull("finished_at")) {
										completeDate = stepsObj.getString("finished_at");
									}
									long tempDate = 0;

									if (completeDate != null) {
										tempDate = getTimeInMiliseconds(completeDate);
									}
									tempSteps.setCompletedTime(tempDate);
									completeDate = null;
									if (!stepsObj.isNull("started_at")) {
										completeDate = stepsObj.getString("started_at");
									}
									if (completeDate != null) {
										tempDate = getTimeInMiliseconds(completeDate);
									}
									tempSteps.setStartedTime(tempDate);

									tempSteps.setStepName(stepsObj.optString("name"));
									String result = stepsObj.optString("status");
									if (result.toLowerCase().equals("failed")) {
										// todo Handle Failure case
										processFailure(gitlabUrl, stepsObj.optInt("id"), pass);
									}

									tempSteps.setResult(result);
								} catch (JSONException e) {
									
									LOGGER.error("parsing esception", e.getCause());

								}
								jobsList.add(tempSteps);
							}
						}
						build.setStepsList(jobsList);

						builds.add(build);
					}
				}
			}
		}

	}

	private void processFailure(String jobsUrl2, int id, String token) {
		
		ctx = DataConfig.getContext();
		failurePatternRepo = ctx.getBean(BuildFailurePatternForProjectRepo.class);
		List<BuildFailurePatternForProjectInJenkinsModel> failurePattern = failurePatternRepo
				.findByProjectName(projectName);
		try {
			if (!failurePattern.isEmpty()) {
				BuildFailurePatternForProjectInJenkinsModel tempBuildFailure = failurePattern.get(0);
				List<BuildFailurePatternMetrics> failureMetrics = tempBuildFailure.getPatternMetrics();
				List<BuildFailurePatternMetrics> newList = new ArrayList<BuildFailurePatternMetrics>();
				String url = gitlabUrl + "/jobs/" + id + "/trace?private_token=" + token;
				ResponseEntity<String> logResponseData = makeRestCall(url);
				if (logResponseData != null) {
					String failureLog = logResponseData.getBody();
					for (BuildFailurePatternMetrics temp : failureMetrics) {
						if (null != temp.getRepoName()) {
							if (failureLog.contains(temp.getPatternDefined())) {
								temp.setPatternCount(temp.getPatternCount() + 1);
							}
						} else {
							boolean flag = true;

							for (BuildFailurePatternMetrics n : failureMetrics) {
								if (n.getPatternDefined().equals(temp.getPatternDefined()) && null != n.getRepoName()
										&& n.getRepoName().equals(repoName)) {
									if (failureLog.contains(n.getPatternDefined())) {
										n.setPatternCount(n.getPatternCount() + 1);
									}
									flag = false;
								}
							}

							if (flag) {
								BuildFailurePatternMetrics b = new BuildFailurePatternMetrics();
								b.setPatternCount(0);
								b.setPatternDefined(temp.getPatternDefined());
								b.setPatternDisplayed(temp.getPatternDisplayed());
								b.setRepoName(repoName);
								if (failureLog.contains(b.getPatternDefined())) {
									b.setPatternCount(b.getPatternCount() + 1);
								}
								newList.add(b);
							}

						}
					}

					failureMetrics.addAll(newList);
					// ResponseEntity<String> logResponseData = makeRestCall(url);
					// String failureLog = logResponseData.getBody();
					// for (BuildFailurePatternMetrics temp : failureMetrics) {
					// if (failureLog.contains(temp.getPatternDefined())) {
					// temp.setPatternCount(temp.getPatternCount() + 1);
					// }
					// }
					failurePatternRepo.save(tempBuildFailure);
				}
			}
		} catch (Exception e) {
			LOGGER.error("ProcessFailure Exception/ Empty in calling Gitlab Pipeline ", e.fillInStackTrace());
		
		}
	}

	private long getTimeInMiliseconds(String temp_date) {
		
		String[] splitDate = temp_date.split("T");
		String[] temp = splitDate[1].split("\\.");
		temp = temp[0].split("Z");
		// temp=temp_date.split(".");
		String tempTime = splitDate[0] + " " + temp[0];

		DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
		DateTime createdDate = fmt.parseDateTime(tempTime);
		return createdDate.getMillis();
	}

	private ResponseEntity<String> makeRestCall(String pipelineUrl) {
		
		if (!"".equals(this.userName) && !"".equals(this.password)) {
			try {

				UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(pipelineUrl);
				UriComponents uriComponents = builder.build();
				System.out.println(uriComponents.getHost());
				URI uri = uriComponents.toUri();
				return get().exchange(uri, HttpMethod.GET, new HttpEntity<>(createHeaders(userName, password)),
						String.class);

			} catch (Exception e) {
				LOGGER.error("Exception in calling Gitlab Pipeline API ", e.fillInStackTrace());
				return null;
			}

		} else {
			try {
				return get().exchange(pipelineUrl, HttpMethod.GET, null, String.class);
			} catch (Exception e) {
				LOGGER.error("Exception in calling Gitlab Pipeline API", e.fillInStackTrace());
				return null;
			}

		}
	}

	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}

	private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000);
		requestFactory.setReadTimeout(20000);
		return new RestTemplate(requestFactory);
	}

}
