
package com.bolt.dashboard.highlight;

import java.util.Set;

import com.bolt.dashboard.core.model.HighLightModel;
import com.bolt.dashboard.core.model.HighLightProjectRuleSet;
import com.bolt.dashboard.core.repository.HighLightRepo;

/**
 * <AUTHOR>
 *
 */
public interface HighLightClient {
    /**
     * This is logic for counting Volatility for requirement stories.
     *
     * @param hlmProjectRuleSet
     * @param highLightRepo
     * @param hlmProject
     * @param highLightReelRuleSet
     * @param almProject
     * @throws HighLightException
     */
    public void volatilityCalculation(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
	    HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet) throws HighLightException;

    /**
     * This is logic for counting CYCLE TIME for CRITICAL Priority stories.
     *
     * @param hlmProjectRuleSet
     * @param highLightRepo
     * @param hlmProject
     * @param highLightReelRuleSet
     * @param almProject
     * @throws HighLightException
     */
    public void cycleTimeCriticalCalculation(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
	    HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet) throws HighLightException;

    /**
     * @param hlmProjectRuleSet
     * @param highLightRepo
     * @param hlmProject
     * @param highLightReelRuleSet
     * @param almProject
     * @throws HighLightException
     */
    public void cycleTimeHighCalculation(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
	    HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet) throws HighLightException;

    /**
     * @param hlmProjectRuleSet
     * @param highLightRepo
     * @param hlmProject
     * @param highLightReelRuleSet
     * @param almProject
     * @throws HighLightException
     */
    public void teamVelocityCalculation(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
	    HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet) throws HighLightException;

    /**
     * @param hlmProjectRuleSet
     * @param highLightRepo
     * @param hlmProject
     * @param highLightReelRuleSet
     * @param almProject
     * @throws HighLightException
     */
    public void estimationAccuracyCalculation(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
	    HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet) throws HighLightException;

    /**
     * This calculates the total stories pending in the back log which are
     * estimates i.e. groomed and can be taken up in the future sprint. The
     * Value entered by the user for its team acceptable groomed stories are
     * checked against the actual and on that basis it returns the value whether
     * the rule is passed or failed.
     *
     * @param hlmProjectRuleSet
     * @param highLightRepo
     * @param hlmProject
     * @param highLightReelRuleSet
     * @param almProject
     */
    public void groomedStoriesCalulation(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
	    HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet);

    /**
     * This calculates the efficiency of the team by taking account of total
     * completed stories to total planned stories. This efficiency is checked
     * against the value provided by user for its team acceptable efficiency and
     * on that basis decides whether rule is passed or failed.
     *
     * @param hlmProjectRuleSet
     * @param highLightRepo
     * @param hlmProject
     * @param highLightReelRuleSet
     * @param almProject
     */
    public void efficiencyCalulation(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
	    HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet);

    /**
     * @param hlmProjectRuleSet
     * @param highLightRepo
     * @param hlmProject
     * @param highLightReelRuleSet
     * @param almProject
     */
    public void releaseDeadLineHighLight(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
	    HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet);

    /**
     * @param hlmProjectRuleSet
     * @param highLightRepo
     * @param hlmProject
     * @param highLightReelRuleSet
     * @param almProject
     */
    public void projectEndHighLight(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
	    HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet);

    /**
     * @param hlmProjectRuleSet
     * @param highLightRepo
     * @param hlmProject
     * @param highLightReelRuleSet
     * @param almProject
     */
    public void codeToDefectHighLight(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
	    HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet);

    /**
     * @param hlmProjectRuleSet
     * @param highLightRepo
     * @param hlmProject
     * @param highLightReelRuleSet
     * @param almProject
     */
    public void storyPointsToDefectHighlight(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
	    HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet);

}
