
package com.bolt.dashboard.highlight;

import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.EngScorecard;
import com.bolt.dashboard.core.model.EngScorecardParamData;
import com.bolt.dashboard.core.model.EngScorecardSprint;
import com.bolt.dashboard.core.model.EngagementRule;
import com.bolt.dashboard.core.model.EngagementScorecardSubjectiveData;
import com.bolt.dashboard.core.model.HighLightModel;
import com.bolt.dashboard.core.model.HighLightProjectRuleSet;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.PortfolioConfig;
import com.bolt.dashboard.core.model.ProjectModel;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.ChangeHisortyRepo;
import com.bolt.dashboard.core.repository.CodeQualityRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.EffortHistoryRepo;
import com.bolt.dashboard.core.repository.EngScorecardRepo;
import com.bolt.dashboard.core.repository.EngScorecardSubjectiveDataRepo;
import com.bolt.dashboard.core.repository.HighLightRepo;
import com.bolt.dashboard.core.repository.IterationRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.PortfolioConfigRepo;
import com.bolt.dashboard.core.repository.ProjectRepo;
import com.bolt.dashboard.core.repository.TransitionRepo;
import com.bolt.dashboard.engagementScorecard.EngScorecardCommonCalculations;
import com.bolt.dashboard.gitlab.GitLabClientImplementation;
import com.bolt.dashboard.util.MessagesForHighLight;

/**
 * <AUTHOR>
 *
 */
public class HighLightClientImplementation implements HighLightClient {

	private AnnotationConfigApplicationContext ctx;
	private CodeQualityRep codeQualityRepo;
	private static final Logger LOGGER = LogManager.getLogger(HighLightClientImplementation.class);
	public static final String HIGHEST = "Highest";
	String message, newMessage, highlightMsg, groompoints, volatility, cycleTimeCritical, cycleTimeHigh, teamVelocity,
			estimationAccuracy, teamEfficiency;
	String releaseDeadLine, increaseInDefect, averageDefectStory, estimateTimeToRelease;
	int releaseDeadLines, increaserInDefect, avgDefectPerStoryPoints, teamEffec, vel;
	double storyPointsC1, storyPointsC2, storyPointsC3;

	double velocity;
	// Below code is for new JIRA Structure
	ProjectRepo projectRepo = null;
	static IterationRepo iterationRepo = null;
	static MetricRepo metricsRepo = null;
	TransitionRepo transitionRepo = null;
	EffortHistoryRepo effortHistoryRepo = null;
	ChangeHisortyRepo changeHisortyRepo = null;
	ProjectModel projectModel = null;
	static List<IterationModel> iterationList = null;
	static List<EngScorecardSprint> currentQuarterData = null;
	static String pName = null;
	static List<MetricsModel> metricsList = null;
	static List<MetricsModel> backLogMetrics = null;

	public void init(String projectName) {
		pName = null;
		pName = projectName;
		ctx = DataConfig.getContext();
		projectRepo = ctx.getBean(ProjectRepo.class);
		metricsRepo = null;
		metricsRepo = ctx.getBean(MetricRepo.class);
		iterationRepo = null;
		iterationRepo = ctx.getBean(IterationRepo.class);
		effortHistoryRepo = ctx.getBean(EffortHistoryRepo.class);
		changeHisortyRepo = ctx.getBean(ChangeHisortyRepo.class);
		transitionRepo = ctx.getBean(TransitionRepo.class);
		projectRepo.findByProjectName(projectName);
		iterationList = null;
		iterationList = iterationRepo.findByPName(projectName);
		metricsList = null;
		metricsList = metricsRepo.findByPName(pName);
		backLogMetrics = null;
		backLogMetrics = metricsRepo.findByPNameAndSName(projectName, "BackLog");
		// List<EngScorecardSprint> currentQuarterData=null;
		getEngamentData(projectName, ctx);
	}

	private void getEngamentData(String projName, AnnotationConfigApplicationContext ctx) {

		EngScorecardRepo engRepo = ctx.getBean(EngScorecardRepo.class);
		ALMConfigRepo almConfigRepo = ctx.getBean(ALMConfigRepo.class);
		ALMConfiguration almConfig = almConfigRepo.findByProjectName(pName).get(0);
		EngScorecard scoreCard = engRepo.findByPName(pName);
		List<EngScorecardSprint> engScoreSprintList = null;
		if (scoreCard != null) {
			engScoreSprintList = scoreCard.getEngScoreCardSprint();

		} else {
			scoreCard = new EngScorecard();
			scoreCard.setpName(pName);

		}
		engScoreSprintList = scoreCard.getEngScoreCardSprint();
		engScoreSprintList = engScoreSprintList.stream()
				.filter(score -> score.getState() != null && !(score.getState().equalsIgnoreCase("active")))
				.collect(Collectors.toList());
		DateTime currentTime = new DateTime();
		int currentMonth = currentTime.getMonthOfYear();
		EngScorecardCommonCalculations engScoreCommon = new EngScorecardCommonCalculations();
		// int index=quater1.
		int checkCurrentQuarter = engScoreCommon.checkQuarter(currentMonth);
		int currentQuarterMonth = engScoreCommon.getQuarterStartMonth(checkCurrentQuarter);

		DateTime currentQuarterStartDate = new DateTime(currentTime.getYear(), currentQuarterMonth, 1, 0, 0);
		currentQuarterData = engScoreCommon.getQuarterScoreCardData(engScoreSprintList, currentQuarterStartDate,
				currentTime);

		boolean dataFlag = false;
		DateTime lastQuarter1EndDate = null;
		DateTime lastQuarter1StartDate = currentQuarterStartDate;

		while (!dataFlag) {

			if (currentQuarterData.size() <= 0) {
				lastQuarter1EndDate = currentQuarterStartDate;
				lastQuarter1StartDate = currentQuarterStartDate.minusMonths(3);
				currentQuarterStartDate = lastQuarter1StartDate;
				currentQuarterData = engScoreCommon.getQuarterScoreCardData(engScoreSprintList, lastQuarter1StartDate,
						lastQuarter1EndDate);
				checkCurrentQuarter = checkCurrentQuarter - 1;
			}
			if (currentQuarterData.size() > 0) {

				dataFlag = true;
			}
		}
		String[] tempSprints = almConfig.getFilteredSprints();
		List<String> sprints = new ArrayList<>();
		if (tempSprints != null) {
			sprints.addAll(Arrays.asList(tempSprints));
		}
		currentQuarterData = currentQuarterData.stream().filter(score -> !(sprints.contains(score.getSprintName())))
				.collect(Collectors.toList());
		System.out.println(currentQuarterData.size());

	}

	public HighLightClientImplementation() {

		this.ctx = DataConfig.getContext();
		this.codeQualityRepo = ctx.getBean(CodeQualityRep.class);
	}

	@Override
	public void volatilityCalculation(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
			HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet) {
		float totalStoryCount = 0;
		float baseLineStoryCount = 0;
		HighLightProjectRuleSet instanceVolatility = new HighLightProjectRuleSet();
		instanceVolatility.setRuleName(hlmProjectRuleSet.getRuleName());
		float percentage = 0;
		List<MetricsModel> baseLineStoryList = metricsRepo.findByPNameAndBaselineAndType(pName, "Yes", "Story");
		List<MetricsModel> storyList = metricsRepo.findByPNameAndType(pName, "Story");
		if (!storyList.isEmpty())
			totalStoryCount = storyList.size();
		if (!baseLineStoryList.isEmpty())
			baseLineStoryCount = baseLineStoryList.size();

		if (baseLineStoryCount != 0) {
			percentage = ((totalStoryCount - baseLineStoryCount) / baseLineStoryCount) * 100f;
		}
		instanceVolatility.setValue((int) percentage);
		if ("<".equals(hlmProjectRuleSet.getOperator()))
			if (percentage > hlmProjectRuleSet.getValue()) {
				if (hlmProjectRuleSet.getMessageFailure() == null || "".equals(hlmProjectRuleSet.getMessageFailure()))
					hlmProjectRuleSet.setMessageFailure(MessagesForHighLight.FAILURE_MESSAGE_VOLATILITY);

				message = hlmProjectRuleSet.getMessageFailure();
				volatility = Double.toString(percentage);
				newMessage = message.replace("<Volatility Percentage>", volatility);
				newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjectRuleSet.getValue()));
				instanceVolatility.setMessageFailure(newMessage);

				instanceVolatility.setTabName(hlmProjectRuleSet.getTabName());
				hlmProjectRuleSet.setRulePass(false);
				instanceVolatility.setRulePass(false);
			} else {
				if (hlmProjectRuleSet.getMessageSuccess() == null || "".equals(hlmProjectRuleSet.getMessageSuccess()))
					hlmProjectRuleSet.setMessageSuccess(MessagesForHighLight.SUCCESS_MESSAGE_VOLATILITY);

				message = hlmProjectRuleSet.getMessageSuccess();
				volatility = Double.toString(percentage);
				newMessage = message.replace("<Volatility Percentage>", volatility);
				newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjectRuleSet.getValue()));
				instanceVolatility.setMessageSuccess(newMessage);

				instanceVolatility.setTabName(hlmProjectRuleSet.getTabName());
				hlmProjectRuleSet.setRulePass(true);
				instanceVolatility.setRulePass(true);
				instanceVolatility.setActualValue(percentage);
				instanceVolatility.setMessageSuccess(hlmProjectRuleSet.getMessageSuccess());
			}
		highLightRepo.save(hlmProject);
		highLightReelRuleSet.add(instanceVolatility);

	}

	@Override
	public void cycleTimeCriticalCalculation(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
			HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet) {

		HighLightProjectRuleSet cycleTimeCriticalRule = new HighLightProjectRuleSet();
		long cycleTimeCrictical = 0;
		int totalCriticalStories = 0;
		long totalCriticalStoryTimeTaken = 0;
		cycleTimeCriticalRule.setRuleName(hlmProjectRuleSet.getRuleName());
		IterationModel iterationModel = null;
		String sprintName = null;
		MetricsModel sprintData = null;

		Iterator<MetricsModel> iteratot = metricsList.iterator();
		while (iteratot.hasNext()) {
			sprintData = iteratot.next();
			if (ConstantVariable.KYWRD_STORY.equals(sprintData.getType()) && "Done".equals(sprintData.getState())
					&& "Critical".equals(sprintData.getPriority())) {

				totalCriticalStories++;
				totalCriticalStoryTimeTaken = totalCriticalStoryTimeTaken
						+ (sprintData.getResDate() - sprintData.getCreateDate());

			}

		}

		LOGGER.info("Total time taken in critical" + (int) (totalCriticalStoryTimeTaken / (1000 * 60 * 60 * 24)));
		if (totalCriticalStories != 0)
			cycleTimeCrictical = totalCriticalStoryTimeTaken / totalCriticalStories;

		int cycleTimeInDays = (int) (cycleTimeCrictical / (1000 * 60 * 60 * 24));
		if ("<".equals(hlmProjectRuleSet.getOperator()) || "<=".equals(hlmProjectRuleSet.getOperator()))

			updateHighlightCTC(hlmProjectRuleSet, cycleTimeInDays, cycleTimeCriticalRule,
					cycleTimeInDays > hlmProjectRuleSet.getValue());

		else if (">".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightCTC(hlmProjectRuleSet, cycleTimeInDays, cycleTimeCriticalRule,
					cycleTimeInDays <= hlmProjectRuleSet.getValue());

		hlmProjectRuleSet.setCycleTimeDays(cycleTimeInDays);
		cycleTimeCriticalRule.setCycleTimeDays(cycleTimeInDays);
		highLightRepo.save(hlmProject);
		highLightReelRuleSet.add(cycleTimeCriticalRule);

	}

	@Override
	public void cycleTimeHighCalculation(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
			HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet) {
		HighLightProjectRuleSet cycleTimeHighRule = new HighLightProjectRuleSet();
		cycleTimeHighRule.setRuleName(hlmProjectRuleSet.getRuleName());
		long cycleTimeHighAndHighest = 0;
		int totalHighAndHighestStories = 0;
		long totalHighAndHighestTimeTaken = 0;

		for (MetricsModel sprintData : metricsList) {
			if (ConstantVariable.KYWRD_STORY.equals(sprintData.getType()) && "Done".equals(sprintData.getState())
					&& ((HIGHEST.equals(sprintData.getPriority())) || ("High".equals(sprintData.getPriority())))) {

				totalHighAndHighestStories++;
				totalHighAndHighestTimeTaken = totalHighAndHighestTimeTaken
						+ (sprintData.getResDate() - sprintData.getCreateDate());

			}
		}
		if (totalHighAndHighestStories != 0)
			cycleTimeHighAndHighest = totalHighAndHighestTimeTaken / totalHighAndHighestStories;
		int cycleTimeInDays = (int) (cycleTimeHighAndHighest / (1000 * 60 * 60 * 24));
		if ("<".equals(hlmProjectRuleSet.getOperator()) || "<=".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightCTH(hlmProjectRuleSet, cycleTimeInDays, cycleTimeHighRule,
					cycleTimeInDays > hlmProjectRuleSet.getValue());
		else if (">".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightCTH(hlmProjectRuleSet, cycleTimeInDays, cycleTimeHighRule,
					cycleTimeInDays <= hlmProjectRuleSet.getValue());

		hlmProjectRuleSet.setCycleTimeDays(cycleTimeInDays);
		cycleTimeHighRule.setCycleTimeDays(cycleTimeInDays);
		highLightRepo.save(hlmProject);
		highLightReelRuleSet.add(cycleTimeHighRule);
	}

	@Override
	public void teamVelocityCalculation(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
			HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet) {
		HighLightProjectRuleSet teamVelocityRule = new HighLightProjectRuleSet();

		teamVelocityRule.setRuleName(hlmProjectRuleSet.getRuleName());
		teamVelocityRule.setTabName(hlmProjectRuleSet.getTabName());
		storyPointsC1 = 0;
		storyPointsC2 = 0;
		storyPointsC3 = 0;
		int teamVelocityC1 = 0, teamVelocityC3 = 0;
		double velocityTrend, velocityVariance;

		// for (int i = 2; i <= 5; i++) {
		// Iteration sprint =
		// almProject.getIteration().get(almProject.getIteration().size() - (1 +
		// i));
		// if (i == 2)
		/// teamVelocityC1 = sprint.getVelocity();
		// (i == 4)
		// teamVelocityC3 = sprint.getVelocity();

		// }
		velocityTrend = getVelocityTrend(teamVelocityC1, teamVelocityC3);
		velocityVariance = velocityTrend - 100;

		if ("<".equals(hlmProjectRuleSet.getOperator())) {
			if (velocityVariance < 0) {
				// negative variance, check if variance is within the limit
				// specified
				if (Math.negateExact((int) velocityVariance) <= hlmProjectRuleSet.getValue()) {
					displaySuccessMessage(hlmProjectRuleSet, teamVelocityRule,
							Double.toString(Math.negateExact((int) velocityVariance)));
				} else {
					displayFailureMessage(hlmProjectRuleSet, teamVelocityRule,
							Double.toString(Math.negateExact((int) velocityVariance)));
				}

			} else {
				displayFailureMessage(hlmProjectRuleSet, teamVelocityRule,
						Double.toString(Math.negateExact((int) velocityVariance)));
			}
		} else {
			if (velocityVariance > 0) {
				// positive variance, check if variance is within the limit
				// specified
				if (Math.negateExact((int) velocityVariance) >= hlmProjectRuleSet.getValue()) {
					// Display successful message
					displaySuccessMessage(hlmProjectRuleSet, teamVelocityRule,
							Double.toString(Math.negateExact((int) velocityVariance)));
				} else {
					// Display failure message
					displayFailureMessage(hlmProjectRuleSet, teamVelocityRule,
							Double.toString(Math.negateExact((int) velocityVariance)));
				}

			} else {
				// Display failure message
				displayFailureMessage(hlmProjectRuleSet, teamVelocityRule,
						Double.toString(Math.negateExact((int) velocityVariance)));
			}
		}
		highLightRepo.save(hlmProject);
		highLightReelRuleSet.add(teamVelocityRule);
	}

	@Override
	public void estimationAccuracyCalculation(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
			HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet) {
		HighLightProjectRuleSet estimatioAccuracyRule = new HighLightProjectRuleSet();
		int storyCountWithStoryPoint1 = 0;
		double totalEstimatedEffortForStoryPoint1 = 0;
		double totalActualEffortForStoryPoint1 = 0;
		int storyCountWithStoryPoint2 = 0;
		double totalEstimatedEffortForStoryPoint2 = 0;
		double totalActualEffortForStoryPoint2 = 0;
		int storyCountWithStoryPoint3 = 0;
		double totalEstimatedEffortForStoryPoint3 = 0;
		double totalActualEffortForStoryPoint3 = 0;
		int storyCountWithStoryPoint5 = 0;
		double totalEstimatedEffortForStoryPoint5 = 0;
		double totalActualEffortForStoryPoint5 = 0;
		int storyCountWithStoryPoint8 = 0;
		double totalEstimatedEffortForStoryPoint8 = 0;
		double totalActualEffortForStoryPoint8 = 0;
		int storyCountWithStoryPoint13 = 0;
		double totalEstimatedEffortForStoryPoint13 = 0;
		double totalActualEffortForStoryPoint13 = 0;
		int storyCountWithStoryPoint21 = 0;
		double totalEstimatedEffortForStoryPoint21 = 0;
		double totalActualEffortForStoryPoint21 = 0;
		Map<Long, Double> sPMap = null;
		int sprintCounter = 0;

		for (IterationModel sprint : iterationList) {
			sprintCounter++;
			if (sprintCounter > iterationList.size() - 2)
				break;
			for (MetricsModel sprintData : metricsRepo.findByPNameAndSName(pName, sprint.getsName())) {
				if (sprintData.getStoryPoints().size() != 0)
					sPMap = sprintData.getStoryPoints();
				if (ConstantVariable.KYWRD_STORY.equals(sprintData.getType()) && sPMap.size() > 0) {
					double storyPoints = (double) sPMap.values().toArray()[sPMap.size() - 1];
					if (storyPoints == 1) {
						storyCountWithStoryPoint1++;
						if (sprintData.getOrgEst() != null)
							totalEstimatedEffortForStoryPoint1 = totalEstimatedEffortForStoryPoint1
									+ sprintData.getOrgEst();
						if (sprintData.getEffort() != null)
							totalActualEffortForStoryPoint1 = totalActualEffortForStoryPoint1 + sprintData.getEffort();
					}
					if (storyPoints == 2) {
						storyCountWithStoryPoint2++;
						if (sprintData.getOrgEst() != null)
							totalEstimatedEffortForStoryPoint2 = totalEstimatedEffortForStoryPoint2
									+ sprintData.getOrgEst();
						if (sprintData.getEffort() != null)
							totalActualEffortForStoryPoint2 = totalActualEffortForStoryPoint2 + sprintData.getEffort();
					}
					if (storyPoints == 3) {
						storyCountWithStoryPoint3++;
						if (sprintData.getOrgEst() != null)
							totalEstimatedEffortForStoryPoint3 = totalEstimatedEffortForStoryPoint3
									+ sprintData.getOrgEst();
						if (sprintData.getEffort() != null)
							totalActualEffortForStoryPoint3 = totalActualEffortForStoryPoint3 + sprintData.getEffort();
					}
					if (storyPoints == 5) {
						storyCountWithStoryPoint5++;
						if (sprintData.getOrgEst() != null)
							totalEstimatedEffortForStoryPoint5 = totalEstimatedEffortForStoryPoint5
									+ sprintData.getOrgEst();
						if (sprintData.getEffort() != null)
							totalActualEffortForStoryPoint5 = totalActualEffortForStoryPoint5 + sprintData.getEffort();
					}
					if (storyPoints == 8) {
						storyCountWithStoryPoint8++;
						if (sprintData.getOrgEst() != null)
							totalEstimatedEffortForStoryPoint8 = totalEstimatedEffortForStoryPoint8
									+ sprintData.getOrgEst();
						if (sprintData.getEffort() != null)
							totalActualEffortForStoryPoint8 = totalActualEffortForStoryPoint8 + sprintData.getEffort();
					}
					if (storyPoints == 13) {
						storyCountWithStoryPoint13++;
						if (sprintData.getOrgEst() != null)
							totalEstimatedEffortForStoryPoint13 = totalEstimatedEffortForStoryPoint13
									+ sprintData.getOrgEst();
						if (sprintData.getEffort() != null)
							totalActualEffortForStoryPoint13 = totalActualEffortForStoryPoint13
									+ sprintData.getEffort();
					}
					if (storyPoints == 21) {
						storyCountWithStoryPoint21++;
						if (sprintData.getOrgEst() != null)
							totalEstimatedEffortForStoryPoint21 = totalEstimatedEffortForStoryPoint21
									+ sprintData.getOrgEst();
						if (sprintData.getEffort() != null)
							totalActualEffortForStoryPoint21 = totalActualEffortForStoryPoint21
									+ sprintData.getEffort();
					}
				}
			}
		}

		if (storyCountWithStoryPoint1 != 0) {
			estimatioAccuracyRule
					.setAverageEstimationForStoryPoint1(totalEstimatedEffortForStoryPoint1 / storyCountWithStoryPoint1);
		} else {
			estimatioAccuracyRule.setAverageEstimationForStoryPoint1(0);
		}
		if (storyCountWithStoryPoint2 != 0) {
			estimatioAccuracyRule
					.setAverageEstimationForStoryPoint2(totalEstimatedEffortForStoryPoint2 / storyCountWithStoryPoint2);
		}
		if (storyCountWithStoryPoint3 != 0) {
			estimatioAccuracyRule
					.setAverageEstimationForStoryPoint3(totalEstimatedEffortForStoryPoint3 / storyCountWithStoryPoint3);
		}
		if (storyCountWithStoryPoint5 != 0) {
			estimatioAccuracyRule
					.setAverageEstimationForStoryPoint5(totalEstimatedEffortForStoryPoint5 / storyCountWithStoryPoint5);
		}
		if (storyCountWithStoryPoint8 != 0) {

			estimatioAccuracyRule
					.setAverageEstimationForStoryPoint8(totalEstimatedEffortForStoryPoint8 / storyCountWithStoryPoint8);
		}
		if (storyCountWithStoryPoint13 != 0) {
			estimatioAccuracyRule.setAverageEstimationForStoryPoint13(
					totalEstimatedEffortForStoryPoint13 / storyCountWithStoryPoint13);
		}
		if (storyCountWithStoryPoint21 != 0) {
			estimatioAccuracyRule.setAverageEstimationForStoryPoint21(
					totalEstimatedEffortForStoryPoint21 / storyCountWithStoryPoint21);
		}
		Object[] objectArray = getCurrentSprintData(pName);
		List<MetricsModel> currentsprintData = new ArrayList<>();
		if (objectArray[0] != null)
			currentsprintData = metricsRepo.findByPNameAndSName(pName, ((IterationModel) objectArray[0]).getsName());
		double percentageSP1 = 0.0;
		double percentageSP2 = 0.0;
		double percentageSP3 = 0.0;
		double percentageSP5 = 0.0;
		double percentageSP8 = 0.0;
		double percentageSP13 = 0.0;
		double percentageSP21 = 0.0;
		int counterSP1 = 0, counterSP2 = 0, counterSP3 = 0, counterSP5 = 0, counterSP8 = 0, counterSP13 = 0,
				counterSP21 = 0;
		double estEffSP1 = 0, estEffSP2 = 0, estEffSP3 = 0, estEffSP5 = 0, estEffSP8 = 0, estEffSP13 = 0,
				estEffSP21 = 0;
		for (MetricsModel currentSprintData : currentsprintData) {
			if (currentSprintData.getStoryPoints().size() != 0)
				sPMap = currentSprintData.getStoryPoints();
			if (ConstantVariable.KYWRD_STORY.equals(currentSprintData.getType()) && sPMap.size() > 0) {

				double storyPoints = (double) sPMap.values().toArray()[sPMap.size() - 1];
				if (storyPoints == 1) {
					counterSP1++;
					if (currentSprintData.getOrgEst() != null)
						estEffSP1 = currentSprintData.getOrgEst() + estEffSP1;

				}
				if (storyPoints == 2) {
					counterSP2++;
					if (currentSprintData.getOrgEst() != null)
						estEffSP2 = currentSprintData.getOrgEst() + estEffSP2;
				}
				if (storyPoints == 3) {
					counterSP3++;
					if (currentSprintData.getOrgEst() != null)
						estEffSP3 = currentSprintData.getOrgEst() + estEffSP3;
				}
				if (storyPoints == 5) {
					counterSP5++;
					if (currentSprintData.getOrgEst() != null)
						estEffSP5 = currentSprintData.getOrgEst() + estEffSP5;
				}
				if (storyPoints == 8) {
					counterSP8++;
					if (currentSprintData.getOrgEst() != null)
						estEffSP8 = currentSprintData.getOrgEst() + estEffSP8;
				}
				if (storyPoints == 13) {
					counterSP13++;
					if (currentSprintData.getOrgEst() != null)
						estEffSP13 = currentSprintData.getOrgEst() + estEffSP13;
				}
				if (storyPoints == 21) {
					counterSP21++;
					if (currentSprintData.getOrgEst() != null)
						estEffSP21 = currentSprintData.getOrgEst() + estEffSP21;
				}
			}
		}
		estimationAccuracy = "";
		if (counterSP1 != 0) {
			estEffSP1 = estEffSP1 / counterSP1;
			percentageSP1 = Math.round((estEffSP1 - estimatioAccuracyRule.getAverageEstimationForStoryPoint1())
					/ estimatioAccuracyRule.getAverageEstimationForStoryPoint1());
		}
		if (counterSP2 != 0) {
			estEffSP2 = estEffSP2 / counterSP2;
			percentageSP2 = Math.round((estEffSP2 - estimatioAccuracyRule.getAverageEstimationForStoryPoint2())
					/ estimatioAccuracyRule.getAverageEstimationForStoryPoint2());
		}

		if (counterSP3 != 0) {
			estEffSP3 = estEffSP3 / counterSP3;
			percentageSP3 = Math.round((estEffSP3 - estimatioAccuracyRule.getAverageEstimationForStoryPoint3())
					/ estimatioAccuracyRule.getAverageEstimationForStoryPoint3());
		}
		if (counterSP5 != 0) {
			estEffSP5 = estEffSP5 / counterSP5;
			percentageSP5 = Math.round((estEffSP5 - estimatioAccuracyRule.getAverageEstimationForStoryPoint5())
					/ estimatioAccuracyRule.getAverageEstimationForStoryPoint5());
		}
		if (counterSP8 != 0) {
			estEffSP8 = estEffSP8 / counterSP8;
			percentageSP3 = Math.round((estEffSP8 - estimatioAccuracyRule.getAverageEstimationForStoryPoint8())
					/ estimatioAccuracyRule.getAverageEstimationForStoryPoint8());
		}
		if (counterSP13 != 0) {
			estEffSP13 = estEffSP13 / counterSP13;
			percentageSP13 = Math.round((estEffSP13 - estimatioAccuracyRule.getAverageEstimationForStoryPoint13())
					/ estimatioAccuracyRule.getAverageEstimationForStoryPoint13());
		}
		if (counterSP21 != 0) {
			estEffSP21 = estEffSP21 / counterSP21;
			percentageSP13 = Math.round((estEffSP21 - estimatioAccuracyRule.getAverageEstimationForStoryPoint21())
					/ estimatioAccuracyRule.getAverageEstimationForStoryPoint21());

		}

		if (hlmProjectRuleSet.getOperator().equals("<")) {
			if (percentageSP1 < hlmProjectRuleSet.getValue()) {

				estimationAccuracy = estimationAccuracy + "SP1(" + percentageSP1 + "%),";
			}
			if (percentageSP2 < hlmProjectRuleSet.getValue()) {
				estimationAccuracy = estimationAccuracy + "SP2(" + percentageSP2 + "%),";
			}
			if (percentageSP3 < hlmProjectRuleSet.getValue()) {
				estimationAccuracy = estimationAccuracy + "SP3(" + percentageSP3 + "%),";
			}
			if (percentageSP5 < hlmProjectRuleSet.getValue()) {
				estimationAccuracy = estimationAccuracy + "SP5(" + percentageSP5 + "%),";
			}
			if (percentageSP8 < hlmProjectRuleSet.getValue()) {
				estimationAccuracy = estimationAccuracy + "SP8(" + percentageSP8 + "%),";
			}
		} else {
			if (percentageSP1 > hlmProjectRuleSet.getValue()) {
				estimationAccuracy = estimationAccuracy + "SP1(" + percentageSP1 + "%),";
			}
			if (percentageSP2 > hlmProjectRuleSet.getValue()) {
				estimationAccuracy = estimationAccuracy + "SP2(" + percentageSP2 + "%),";
			}
			if (percentageSP3 > hlmProjectRuleSet.getValue()) {
				estimationAccuracy = estimationAccuracy + "SP3(" + percentageSP3 + "%),";
			}
			if (percentageSP5 > hlmProjectRuleSet.getValue()) {
				estimationAccuracy = estimationAccuracy + "SP5(" + percentageSP5 + "%),";
			}
			if (percentageSP8 > hlmProjectRuleSet.getValue()) {
				estimationAccuracy = estimationAccuracy + "SP8(" + percentageSP8 + "%),";
			}
		}

		if (percentageSP1 > hlmProjectRuleSet.getValue() || percentageSP2 > hlmProjectRuleSet.getValue()
				|| percentageSP3 > hlmProjectRuleSet.getValue() || percentageSP5 > hlmProjectRuleSet.getValue()
				|| percentageSP8 > hlmProjectRuleSet.getValue() || percentageSP13 > hlmProjectRuleSet.getValue()
				|| percentageSP21 > hlmProjectRuleSet.getValue()) {
			// Here the logic for when the stories are greater than
			// threshold

			if (hlmProjectRuleSet.getMessageFailure() == null || "".equals(hlmProjectRuleSet.getMessageFailure()))
				hlmProjectRuleSet.setMessageFailure(MessagesForHighLight.FAILURE_MESSAGE_ESTIMATION_ACCURACY);
			hlmProjectRuleSet.setRulePass(false);
			estimatioAccuracyRule.setTabName(hlmProjectRuleSet.getTabName());
			estimatioAccuracyRule.setRuleName(hlmProjectRuleSet.getRuleName());
			message = hlmProjectRuleSet.getMessageFailure();
			newMessage = message.replace("<Estimate Accuracy>", estimationAccuracy);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjectRuleSet.getValue()));
			estimatioAccuracyRule.setMessageFailure(newMessage);

			estimatioAccuracyRule.setRulePass(false);

		} else if (percentageSP1 < hlmProjectRuleSet.getValue() || percentageSP2 < hlmProjectRuleSet.getValue()
				|| percentageSP3 < hlmProjectRuleSet.getValue() || percentageSP5 < hlmProjectRuleSet.getValue()
				|| percentageSP8 < hlmProjectRuleSet.getValue() || percentageSP13 < hlmProjectRuleSet.getValue()
				|| percentageSP21 < hlmProjectRuleSet.getValue()) {
			// Here the logic for when the stories are less than
			// threshold

			if (hlmProjectRuleSet.getMessageSuccess() == null || "".equals(hlmProjectRuleSet.getMessageSuccess()))
				hlmProjectRuleSet.setMessageSuccess(MessagesForHighLight.SUCCESS_MESSAGE_ESTIMATION_ACCURACY);
			hlmProjectRuleSet.setRulePass(true);
			estimatioAccuracyRule.setRuleName(hlmProjectRuleSet.getRuleName());
			estimatioAccuracyRule.setTabName(hlmProjectRuleSet.getTabName());

			message = hlmProjectRuleSet.getMessageSuccess();
			newMessage = message.replace("<Estimate Accuracy>", estimationAccuracy);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjectRuleSet.getValue()));
			estimatioAccuracyRule.setMessageSuccess(newMessage);

			estimatioAccuracyRule.setRulePass(true);

		}

		highLightRepo.save(hlmProject);
		highLightReelRuleSet.add(estimatioAccuracyRule);

	}

	public void engCyleTime(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
			HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet/*
																						 * , ALMProject almProject
																						 */) {

		HighLightProjectRuleSet engCycleTime = new HighLightProjectRuleSet();
		engCycleTime.setRuleName(hlmProjectRuleSet.getRuleName());
		long cycleTime = 0;

		for (EngScorecardSprint engScorecardSprint : currentQuarterData) {
			for (EngScorecardParamData engParam : engScorecardSprint.getEngScoreParamData()) {
				if (engParam.getSubParamaterName().equalsIgnoreCase("cycle time")) {
					cycleTime = cycleTime + engParam.getTimestamp();
				}
			}
		}

		if (cycleTime > 0) {
			cycleTime = cycleTime / currentQuarterData.size();
		}
		int requiredCycleTime = hlmProjectRuleSet.getValue();
		if (">".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightEng(hlmProjectRuleSet, cycleTime, engCycleTime, cycleTime > requiredCycleTime);

		else if ("<".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightEng(hlmProjectRuleSet, cycleTime, engCycleTime, cycleTime < requiredCycleTime);

		highLightRepo.save(hlmProject);
		highLightReelRuleSet.add(engCycleTime);
	}

	public void engLeadTime(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
			HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet/*
																						 * , ALMProject almProject
																						 */) {

		HighLightProjectRuleSet engCycleTime = new HighLightProjectRuleSet();
		engCycleTime.setRuleName(hlmProjectRuleSet.getRuleName());
		long leadTime = 0;

		for (EngScorecardSprint engScorecardSprint : currentQuarterData) {
			for (EngScorecardParamData engParam : engScorecardSprint.getEngScoreParamData()) {
				if (engParam.getSubParamaterName().equalsIgnoreCase("lead time")) {
					leadTime = leadTime + engParam.getTimestamp();
				}
			}
		}
		if (leadTime > 0) {
			leadTime = leadTime / currentQuarterData.size();
		}
		int requiredLeadTime = hlmProjectRuleSet.getValue();
		if (">".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightEng(hlmProjectRuleSet, leadTime, engCycleTime, leadTime > requiredLeadTime);

		else if ("<".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightEng(hlmProjectRuleSet, leadTime, engCycleTime, leadTime < requiredLeadTime);

		highLightRepo.save(hlmProject);
		highLightReelRuleSet.add(engCycleTime);
	}

	public void engAvgCompletion(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
			HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet/*
																						 * , ALMProject almProject
																						 */) {

		HighLightProjectRuleSet engCycleTime = new HighLightProjectRuleSet();
		engCycleTime.setRuleName(hlmProjectRuleSet.getRuleName());
		long completion = 0;

		for (EngScorecardSprint engScorecardSprint : currentQuarterData) {
			for (EngScorecardParamData engParam : engScorecardSprint.getEngScoreParamData()) {
				if (engParam.getSubParamaterName().equalsIgnoreCase("completion")) {
					completion = completion + Integer.parseInt(engParam.getValue());

				}
			}
		}
		if (completion > 0) {
			completion = completion / currentQuarterData.size();
		}
		int requiredLeadTime = hlmProjectRuleSet.getValue();
		if (">".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightEng(hlmProjectRuleSet, completion, engCycleTime, completion > requiredLeadTime);

		else if ("<".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightEng(hlmProjectRuleSet, completion, engCycleTime, completion < requiredLeadTime);

		highLightRepo.save(hlmProject);
		highLightReelRuleSet.add(engCycleTime);
	}

	public void engAvgVelocity(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
			HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet/*
																						 * , ALMProject almProject
																						 */) {

		HighLightProjectRuleSet engCycleTime = new HighLightProjectRuleSet();
		engCycleTime.setRuleName(hlmProjectRuleSet.getRuleName());
		long avgVelocity = 0;

		for (EngScorecardSprint engScorecardSprint : currentQuarterData) {
			for (EngScorecardParamData engParam : engScorecardSprint.getEngScoreParamData()) {
				if (engParam.getSubParamaterName().equalsIgnoreCase("velocity")) {
					avgVelocity = avgVelocity + Integer.valueOf(engParam.getValue());
				}
			}
		}
		if (avgVelocity > 0) {
			avgVelocity = avgVelocity / currentQuarterData.size();
		}
		int requiredLeadTime = hlmProjectRuleSet.getValue();
		if (">".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightEng(hlmProjectRuleSet, avgVelocity, engCycleTime, avgVelocity > requiredLeadTime);

		else if ("<".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightEng(hlmProjectRuleSet, avgVelocity, engCycleTime, avgVelocity < requiredLeadTime);

		highLightRepo.save(hlmProject);
		highLightReelRuleSet.add(engCycleTime);
	}

	public void groomedStoriesCalulation(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
			HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet/*
																						 * , ALMProject almProject
																						 */) {

		HighLightProjectRuleSet groomedStories = new HighLightProjectRuleSet();
		groomedStories.setRuleName(hlmProjectRuleSet.getRuleName());
		int groomedStoryPoints = 0;

		int averageStoryPoints = 0;
		int totalVelocity = 0;
		// if (almProject.getIteration().size() < 3)
		// return;
		// for (Iteration velocity : almProject.getIteration()) {
		// totalVelocity = totalVelocity + velocity.getVelocity();
		// }
		// averageStoryPoints = (totalVelocity /
		// (almProject.getIteration().size() - 2));

		for (MetricsModel backlogData : backLogMetrics) {
			if (ConstantVariable.KYWRD_STORY.equals(backlogData.getType())) {

				groomedStoryPoints = (groomedStoryPoints);

			}
		}
		int requiredStoryGroomedPoints = hlmProjectRuleSet.getValue() * averageStoryPoints;
		if (">".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightGroomedSP(hlmProjectRuleSet, groomedStoryPoints, groomedStories,
					groomedStoryPoints > requiredStoryGroomedPoints);

		else if ("<".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightGroomedSP(hlmProjectRuleSet, groomedStoryPoints, groomedStories,
					groomedStoryPoints < requiredStoryGroomedPoints);

		highLightRepo.save(hlmProject);
		highLightReelRuleSet.add(groomedStories);
	}

	public void efficiencyCalulation(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
			HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet/*
																						 * , ALMProject almProject
																						 */) {

		HighLightProjectRuleSet efficiencyRule = new HighLightProjectRuleSet();
		efficiencyRule.setRuleName(hlmProjectRuleSet.getRuleName());
		efficiencyRule.setTabName(hlmProjectRuleSet.getTabName());
		int totalPlannedStories = 0;
		int totalVelocity = 0;
		int counter = 0;
		/*
		 * for (Iteration velocity : almProject.getIteration()) { totalPlannedStories =
		 * totalPlannedStories + velocity.getPlannedStoryPoints(); totalVelocity =
		 * totalVelocity + velocity.getVelocity(); counter++; if (counter >=
		 * almProject.getIteration().size() - 2) break; }
		 */
		double efficiencyForProject = 0;
		// Commented because always totalPlannedStories zero, it ever executes
//	if (totalPlannedStories != 0)
//	    efficiencyForProject = ((double) totalVelocity / (double) totalPlannedStories) * 100;
		if (">".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightEffCalc(hlmProjectRuleSet, efficiencyForProject, efficiencyRule,
					MessagesForHighLight.FAILURE_MESSAGE_TEAM_EFFICIENCY,
					MessagesForHighLight.FAILURE_MESSAGE_TEAM_EFFICIENCY);

		else if ("<".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightEffCalc(hlmProjectRuleSet, efficiencyForProject, efficiencyRule,
					MessagesForHighLight.SUCCESS_MESSAGE_TEAM_EFFICIENCY,
					MessagesForHighLight.FAILURE_MESSAGE_TEAM_EFFICIENCY);

		highLightRepo.save(hlmProject);
		highLightReelRuleSet.add(efficiencyRule);
	}

	public void releaseDeadLineHighLight(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
			HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet) {
		/*
		 * HighLightProjectRuleSet release = new HighLightProjectRuleSet();
		 * release.setRuleName(hlmProjectRuleSet.getRuleName()); Iteration currentSprint
		 * = almProject.getIteration().get(almProject.getIteration().size() - 2);
		 * Iteration backLog =
		 * almProject.getIteration().get(almProject.getIteration().size() - 1); String
		 * currentRelaseName = currentSprint.getReleaseName(); long releaseEndDate =
		 * currentSprint.getReleaseDate(); long timeRemainingInRelease = 0; int
		 * pendingGroomedStoryPoints = 0; int totalVelocity = 0; int averageStoryPoints
		 * = 0; long sprintLength = 0; double timeTakenToCompletePendingStory = 0.0;
		 * double averageSprintToCompletePending = 0.0; for (Iteration velocity :
		 * almProject.getIteration()) { totalVelocity = totalVelocity +
		 * velocity.getVelocity(); } averageStoryPoints = (totalVelocity /
		 * (almProject.getIteration().size() - 2));
		 *
		 * for (ALMToolMetric backLogData : backLog.getMetrics()) { if
		 * (ConstantVariable.KYWRD_STORY.equals(backLogData.getNameALMProperty() ) &&
		 * currentRelaseName.equals(backLogData.getFixVersion())) {
		 * pendingGroomedStoryPoints = (int) (pendingGroomedStoryPoints +
		 * backLogData.getStoryPoints()); } } sprintLength = currentSprint.getEndDate()
		 * - currentSprint.getStartDate(); timeRemainingInRelease = releaseEndDate -
		 * currentSprint.getEndDate(); averageSprintToCompletePending = (double)
		 * pendingGroomedStoryPoints / averageStoryPoints;
		 * timeTakenToCompletePendingStory = averageSprintToCompletePending *
		 * sprintLength; long estimatedTimeTakenToCompleteRelease =
		 * currentSprint.getEndDate() + (long) timeTakenToCompletePendingStory; if
		 * (">".equals(hlmProjectRuleSet.getOperator()))
		 * updateHighlightRD(hlmProjectRuleSet, timeTakenToCompletePendingStory,
		 * estimatedTimeTakenToCompleteRelease, release, timeTakenToCompletePendingStory
		 * > timeRemainingInRelease); if ("<".equals(hlmProjectRuleSet.getOperator()))
		 * updateHighlightRD(hlmProjectRuleSet, timeTakenToCompletePendingStory,
		 * estimatedTimeTakenToCompleteRelease, release, timeTakenToCompletePendingStory
		 * >= timeRemainingInRelease); highLightRepo.save(hlmProject);
		 * highLightReelRuleSet.add(release);
		 *
		 */}

	public void projectEndHighLight(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
			HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet) {

		PortfolioConfigRepo portfoliodetail = ctx.getBean(PortfolioConfigRepo.class);
		PortfolioConfig protfolioModel = portfoliodetail.findByProjectName(hlmProject.getProjectName())
				.get(portfoliodetail.findByProjectName(hlmProject.getProjectName()).size() - 1);
		HighLightProjectRuleSet projectEnd = new HighLightProjectRuleSet();
		projectEnd.setRuleName(hlmProjectRuleSet.getRuleName());

		// get current Sprint metrics
		Object[] objectArray = getCurrentSprintData(pName);
		IterationModel currentSprint = (IterationModel) objectArray[0];
		List<IterationModel> pastIteartionList = (List<IterationModel>) objectArray[1];
		long projectEndDate = ConstantVariable.timestamp(protfolioModel.getEndDate(), pName);
		long timeRemainingInProject = 0;
		double pendingGroomedStoryPoints = 0;
		int totalVelocity = 0;
		int averageVelocity = 0;
		long sprintLength = 0;
		double timeTakenToCompletePendingStory = 0.0;
		double averageSprintToCompletePending = 0.0;
		for (IterationModel velocity : iterationRepo.findByPName(pName)) {
			totalVelocity = totalVelocity + velocity.getVelocity();
		}
		averageVelocity = (totalVelocity / (iterationRepo.findByPName(pName).size() - 2));
		for (MetricsModel backLogData : backLogMetrics) {
			if (ConstantVariable.KYWRD_STORY.equals(backLogData.getType()) && backLogData.getStoryPoints().size() > 0) {
				Map<Long, Double> sPMap = backLogData.getStoryPoints();

				double storyPoints = (double) sPMap.values().toArray()[sPMap.size() - 1];
				pendingGroomedStoryPoints = pendingGroomedStoryPoints + storyPoints;
			}
		}
		sprintLength = currentSprint.getEndDate() - currentSprint.getStDate();
		timeRemainingInProject = projectEndDate - currentSprint.getEndDate();
		averageSprintToCompletePending = pendingGroomedStoryPoints / averageVelocity;
		timeTakenToCompletePendingStory = averageSprintToCompletePending * sprintLength;
		long estimatedTimeTakenToCompleteRelease = currentSprint.getEndDate() + (long) timeTakenToCompletePendingStory;
		if (">".equals(hlmProjectRuleSet.getOperator()))
			if (timeTakenToCompletePendingStory > timeRemainingInProject) {
				if (hlmProjectRuleSet.getMessageFailure() == null || "".equals(hlmProjectRuleSet.getMessageFailure()))
					hlmProjectRuleSet.setMessageFailure(MessagesForHighLight.FAILURE_MESSAGE_PROJECT_DEADLINE
							+ convertTime(estimatedTimeTakenToCompleteRelease));

				message = hlmProjectRuleSet.getMessageFailure();

				estimateTimeToRelease = Double.toString(estimatedTimeTakenToCompleteRelease);

				newMessage = message.replace("<EstimateTimetoRelease>", estimateTimeToRelease);
				projectEnd.setMessageFailure(newMessage);

				projectEnd.setRulePass(false);
				projectEnd.setTabName(hlmProjectRuleSet.getTabName());
				hlmProjectRuleSet.setRulePass(false);
			} else {

				if (hlmProjectRuleSet.getMessageSuccess() == null && "".equals(hlmProjectRuleSet.getMessageSuccess()))
					hlmProjectRuleSet.setMessageSuccess(MessagesForHighLight.SUCCESS_MESSAGE_PROJECT_DEADLINE
							+ convertTime(estimatedTimeTakenToCompleteRelease));

				message = hlmProjectRuleSet.getMessageFailure();

				estimateTimeToRelease = Double.toString(estimatedTimeTakenToCompleteRelease);

				newMessage = message.replace("<EstimateTimetoRelease>", estimateTimeToRelease);
				projectEnd.setMessageSuccess(newMessage);

				projectEnd.setRulePass(true);
				projectEnd.setTabName(hlmProjectRuleSet.getTabName());
				hlmProjectRuleSet.setRulePass(true);

			}
		if ("<".equals(hlmProjectRuleSet.getOperator()))
			if (timeTakenToCompletePendingStory < timeRemainingInProject) {
				if (hlmProjectRuleSet.getMessageSuccess() == null || "".equals(hlmProjectRuleSet.getMessageSuccess()))
					hlmProjectRuleSet.setMessageSuccess(MessagesForHighLight.SUCCESS_MESSAGE_PROJECT_DEADLINE
							+ convertTime(estimatedTimeTakenToCompleteRelease));
				message = hlmProjectRuleSet.getMessageFailure();

				estimateTimeToRelease = Double.toString(estimatedTimeTakenToCompleteRelease);

				newMessage = message.replace("<EstimateTimetoRelease>", estimateTimeToRelease);
				projectEnd.setMessageSuccess(newMessage);
				projectEnd.setRulePass(true);
				projectEnd.setTabName(hlmProjectRuleSet.getTabName());
				hlmProjectRuleSet.setRulePass(true);
			} else {

				if (hlmProjectRuleSet.getMessageFailure() == null && "".equals(hlmProjectRuleSet.getMessageFailure()))
					hlmProjectRuleSet.setMessageFailure(MessagesForHighLight.FAILURE_MESSAGE_PROJECT_DEADLINE
							+ convertTime(estimatedTimeTakenToCompleteRelease));

				message = hlmProjectRuleSet.getMessageFailure();

				estimateTimeToRelease = Double.toString(estimatedTimeTakenToCompleteRelease);

				newMessage = message.replace("<EstimateTimetoRelease>", estimateTimeToRelease);
				projectEnd.setMessageFailure(newMessage);

				projectEnd.setRulePass(false);
				projectEnd.setTabName(hlmProjectRuleSet.getTabName());
				hlmProjectRuleSet.setRulePass(false);

			}
		highLightRepo.save(hlmProject);
		highLightReelRuleSet.add(projectEnd);
	}

	public void codeToDefectHighLight(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
			HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet) {

		HighLightProjectRuleSet codeQualityToDefect = new HighLightProjectRuleSet();
		codeQualityToDefect.setRuleName(hlmProjectRuleSet.getRuleName());
		codeQualityToDefect.setDescription(hlmProjectRuleSet.getDescription());
		Object[] objectArray = getCurrentSprintData(pName);
		List<IterationModel> list = (List<IterationModel>) objectArray[1];
		IterationModel currentMinusOne = list.get(list.size() - 1);
		IterationModel currentMinusTwo = list.get(list.size() - 2);
		double bugCountCurrentMinuOne = 0;
		int bugCountCurrentMinuTwo = 0;
		double bugCountTillCurrentMinusTwo = 0;
		double increaserInDefects = 0.0;
		double increaseInTechnicalDebt = 0.0;
		double defectToleranceFactor = 0.0;
		int hoursForCurrentMinusOne = 0;
		int hoursForCurrentMinusTwo = 0;
		String[] separationStringForCurrentMinusOne = null;
		if (currentMinusOne.getTechDebt() != null)
			separationStringForCurrentMinusOne = currentMinusOne.getTechDebt().split(Pattern.quote(" "));
		String[] separationStringForCurrentMinusTwo = null;
		if (currentMinusTwo.getTechDebt() != null)
			separationStringForCurrentMinusTwo = currentMinusTwo.getTechDebt().split(Pattern.quote(" "));
		if (separationStringForCurrentMinusOne != null)
			for (String valueAfterSplit : separationStringForCurrentMinusOne) {
				if (valueAfterSplit != null && !valueAfterSplit.isEmpty())
					if (valueAfterSplit.contains("d")) {
						String[] hoursString = valueAfterSplit.split(Pattern.quote("d"));
						hoursForCurrentMinusOne = Integer.parseInt(hoursString[0]) * 8;
					} else if (valueAfterSplit.contains("h")) {
						String[] hoursString = valueAfterSplit.split(Pattern.quote("h"));
						hoursForCurrentMinusOne = hoursForCurrentMinusOne + Integer.parseInt(hoursString[0]);
					}
			}
		if (separationStringForCurrentMinusTwo != null)
			for (String valueAfterSplit : separationStringForCurrentMinusTwo) {
				if (valueAfterSplit != null && !valueAfterSplit.isEmpty())
					if (valueAfterSplit.contains("d")) {
						String[] hoursString = valueAfterSplit.split(Pattern.quote("d"));
						hoursForCurrentMinusTwo = Integer.parseInt(hoursString[0]) * 8;
					} else if (valueAfterSplit.contains("h")) {
						String[] hoursString = valueAfterSplit.split(Pattern.quote("h"));
						hoursForCurrentMinusTwo = hoursForCurrentMinusTwo + Integer.parseInt(hoursString[0]);
					}
			}

		for (MetricsModel currentMinusOneData : metricsRepo.findByPNameAndSName(pName, currentMinusOne.getsName())) {
			if ("Bug".equals(currentMinusOneData.getType()))
				bugCountCurrentMinuOne++;
		}
		for (int i = 0; i <= iterationRepo.findByPName(pName).size() - 4; i++) {

			IterationModel counterIterator = iterationRepo.findByPName(pName).get(i);
			for (MetricsModel counterIteratorData : metricsRepo.findByPNameAndSName(pName,
					counterIterator.getsName())) {
				if ("Bug".equals(counterIteratorData.getType()))
					bugCountTillCurrentMinusTwo++;
			}

		}
		for (MetricsModel currentMinusTwoData : metricsRepo.findByPNameAndSName(pName, currentMinusTwo.getsName())) {
			if ("Bug".equals(currentMinusTwoData.getType()))
				bugCountCurrentMinuTwo++;
		}
		increaserInDefects = bugCountCurrentMinuOne / bugCountTillCurrentMinusTwo * 100;
		increaseInTechnicalDebt = (double) (hoursForCurrentMinusOne - 1) * 100;
		defectToleranceFactor = increaseInTechnicalDebt + hlmProjectRuleSet.getValue();

		if (">".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightCTD(hlmProjectRuleSet, increaserInDefects, codeQualityToDefect,
					increaserInDefects > defectToleranceFactor);

		if ("<".equals(hlmProjectRuleSet.getOperator()))
			updateHighlightCTD(hlmProjectRuleSet, increaserInDefects, codeQualityToDefect,
					increaserInDefects >= defectToleranceFactor);

		highLightRepo.save(hlmProject);
		highLightReelRuleSet.add(codeQualityToDefect);

	}

	private void updateHighlightEng(HighLightProjectRuleSet hlmProjRuleSet, long cycleTime,
			HighLightProjectRuleSet cycleTimeCriticalRule, boolean cmpValue) {
		if (cmpValue) {
			if (hlmProjRuleSet.getMessageFailure() == null || "".equals(hlmProjRuleSet.getMessageFailure()))
				hlmProjRuleSet.setMessageFailure(MessagesForHighLight.FAILURE_MESSAGE_CYCLETIME_CRTICAL);
			cycleTimeCriticalRule.setTabName(hlmProjRuleSet.getTabName());

			message = hlmProjRuleSet.getMessageFailure();
			cycleTimeCritical = Long.toString(cycleTime);
			newMessage = message.replace("<Cycle Time>", cycleTimeCritical);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			cycleTimeCriticalRule.setMessageFailure(newMessage);

			hlmProjRuleSet.setRulePass(false);
			cycleTimeCriticalRule.setRulePass(false);
		} else {
			if (hlmProjRuleSet.getMessageSuccess() == null || "".equals(hlmProjRuleSet.getMessageSuccess()))
				hlmProjRuleSet.setMessageSuccess(MessagesForHighLight.SUCCESS_MESSAGE_CYCLETIME_CRTICAL);
			cycleTimeCriticalRule.setTabName(hlmProjRuleSet.getTabName());

			message = hlmProjRuleSet.getMessageSuccess();
			cycleTimeCritical = Long.toString(cycleTime);
			newMessage = message.replace("<Cycle Time>", cycleTimeCritical);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			cycleTimeCriticalRule.setMessageSuccess(newMessage);

			hlmProjRuleSet.setRulePass(true);
			cycleTimeCriticalRule.setRulePass(true);
		}
	}

	public void storyPointsToDefectHighlight(HighLightProjectRuleSet hlmProjectRuleSet, HighLightRepo highLightRepo,
			HighLightModel hlmProject, Set<HighLightProjectRuleSet> highLightReelRuleSet/*
																						 * , ALMProject almProject
																						 */) {
		/*
		 *
		 * HighLightProjectRuleSet storyPointsToDefect = new HighLightProjectRuleSet();
		 * int totalCompletedStoryPoints = 0; int averageStoryPoints = 0; int
		 * totalDefectCount = 0; int averageDefect = 0; double
		 * averageDefectPerStoryPoint = 0.0; for (Iteration sprint :
		 * almProject.getIteration()) { totalCompletedStoryPoints =
		 * totalCompletedStoryPoints + sprint.getVelocity(); for (ALMToolMetric
		 * sprintData : sprint.getMetrics()) { if (sprintData.getNameALMProperty() !=
		 * null && "Bug".equalsIgnoreCase(sprintData.getNameALMProperty())) {
		 * totalDefectCount++; } } } averageStoryPoints = totalCompletedStoryPoints /
		 * (almProject.getIteration().size() - 2); averageDefect = totalDefectCount /
		 * (almProject.getIteration().size() - 2); averageDefectPerStoryPoint = (double)
		 * averageDefect / averageStoryPoints; if
		 * (">".equals(hlmProjectRuleSet.getOperator()))
		 * updateHighlightSPTD(hlmProjectRuleSet, averageDefectPerStoryPoint,
		 * storyPointsToDefect, averageDefectPerStoryPoint >
		 * hlmProjectRuleSet.getValue());
		 *
		 * if ("<".equals(hlmProjectRuleSet.getOperator()))
		 * updateHighlightSPTD(hlmProjectRuleSet, averageDefectPerStoryPoint,
		 * storyPointsToDefect, averageDefectPerStoryPoint <
		 * hlmProjectRuleSet.getValue());
		 *
		 * highLightRepo.save(hlmProject);
		 * highLightReelRuleSet.add(storyPointsToDefect);
		 *
		 */}

	private void updateHighlightCTC(HighLightProjectRuleSet hlmProjRuleSet, int cycleTimeInDays,
			HighLightProjectRuleSet cycleTimeCriticalRule, boolean cmpValue) {
		if (cmpValue) {
			if (hlmProjRuleSet.getMessageFailure() == null || "".equals(hlmProjRuleSet.getMessageFailure()))
				hlmProjRuleSet.setMessageFailure(MessagesForHighLight.FAILURE_MESSAGE_CYCLETIME_CRTICAL);
			cycleTimeCriticalRule.setTabName(hlmProjRuleSet.getTabName());

			message = hlmProjRuleSet.getMessageFailure();
			cycleTimeCritical = Integer.toString(cycleTimeInDays);
			newMessage = message.replace("<Cycle Time>", cycleTimeCritical);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			cycleTimeCriticalRule.setMessageFailure(newMessage);

			hlmProjRuleSet.setRulePass(false);
			cycleTimeCriticalRule.setRulePass(false);
		} else {
			if (hlmProjRuleSet.getMessageSuccess() == null || "".equals(hlmProjRuleSet.getMessageSuccess()))
				hlmProjRuleSet.setMessageSuccess(MessagesForHighLight.SUCCESS_MESSAGE_CYCLETIME_CRTICAL);
			cycleTimeCriticalRule.setTabName(hlmProjRuleSet.getTabName());

			message = hlmProjRuleSet.getMessageSuccess();
			cycleTimeCritical = Integer.toString(cycleTimeInDays);
			newMessage = message.replace("<Cycle Time>", cycleTimeCritical);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			cycleTimeCriticalRule.setMessageSuccess(newMessage);

			hlmProjRuleSet.setRulePass(true);
			cycleTimeCriticalRule.setRulePass(true);
		}
	}

	private void updateHighlightCTH(HighLightProjectRuleSet hlmProjRuleSet, int cycleTimeInDays,
			HighLightProjectRuleSet cycleTimeHighRule, boolean cmpValue) {
		if (cmpValue) {
			if (hlmProjRuleSet.getMessageFailure() == null || "".equals(hlmProjRuleSet.getMessageFailure()))
				hlmProjRuleSet.setMessageFailure(MessagesForHighLight.FAILURE_MESSAGE_CYCLETIME_HIGH);
			cycleTimeHighRule.setTabName(hlmProjRuleSet.getTabName());

			message = hlmProjRuleSet.getMessageFailure();
			cycleTimeHigh = Integer.toString(cycleTimeInDays);
			newMessage = message.replace("<Cycle Time>", cycleTimeHigh);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			cycleTimeHighRule.setMessageFailure(newMessage);

			hlmProjRuleSet.setRulePass(false);
			cycleTimeHighRule.setRulePass(false);
		} else {
			if (hlmProjRuleSet.getMessageSuccess() == null || "".equals(hlmProjRuleSet.getMessageSuccess()))
				hlmProjRuleSet.setMessageSuccess(MessagesForHighLight.SUCCESS_MESSAGE_CYCLETIME_HIGH);
			cycleTimeHighRule.setTabName(hlmProjRuleSet.getTabName());

			message = hlmProjRuleSet.getMessageSuccess();
			cycleTimeHigh = Integer.toString(cycleTimeInDays);
			newMessage = message.replace("<Cycle Time>", cycleTimeHigh);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			cycleTimeHighRule.setMessageSuccess(newMessage);

			hlmProjRuleSet.setRulePass(true);
			cycleTimeHighRule.setRulePass(true);
		}
	}

	private void updateHighlightRD(HighLightProjectRuleSet hlmProjRuleSet, double timeTakenToCompletePendingStory,
			long estimatedTimeTakenToCompleteRelease, HighLightProjectRuleSet release, boolean cmpValue) {
		if (cmpValue) {
			if (hlmProjRuleSet.getMessageFailure() == null || "".equals(hlmProjRuleSet.getMessageFailure()))
				hlmProjRuleSet.setMessageFailure(MessagesForHighLight.FAILURE_MESSAGE_RELEASE_DEADLINE
						+ convertTime(estimatedTimeTakenToCompleteRelease));

			message = hlmProjRuleSet.getMessageFailure();
			releaseDeadLine = Double.toString(timeTakenToCompletePendingStory);
			if (releaseDeadLine.contains("E")) {
				releaseDeadLine = releaseDeadLine.substring(0, releaseDeadLine.indexOf('E'));
			}
			releaseDeadLines = (int) (Math.round(Double.parseDouble(releaseDeadLine)));
			releaseDeadLine = Integer.toString(releaseDeadLines);
			newMessage = message.replace("<DeadLine>", releaseDeadLine);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			release.setMessageFailure(newMessage);

			release.setRulePass(false);
			release.setTabName(hlmProjRuleSet.getTabName());
			hlmProjRuleSet.setRulePass(false);
		} else {

			if (hlmProjRuleSet.getMessageSuccess() == null || "".equals(hlmProjRuleSet.getMessageSuccess()))
				hlmProjRuleSet.setMessageSuccess(MessagesForHighLight.SUCCESS_MESSAGE_RELEASE_DEADLINE
						+ convertTime(estimatedTimeTakenToCompleteRelease));

			message = hlmProjRuleSet.getMessageSuccess();
			releaseDeadLine = Double.toString(timeTakenToCompletePendingStory);
			if (releaseDeadLine.contains("E")) {
				releaseDeadLine = releaseDeadLine.substring(0, releaseDeadLine.indexOf('E'));
			}
			releaseDeadLines = (int) (Math.round(Double.parseDouble(releaseDeadLine)));
			releaseDeadLine = Integer.toString(releaseDeadLines);
			newMessage = message.replace("<DeadLine>", releaseDeadLine);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			release.setMessageSuccess(newMessage);

			release.setRulePass(true);
			release.setTabName(hlmProjRuleSet.getTabName());
			hlmProjRuleSet.setRulePass(true);

		}

	}

	private void updateHighlightCTD(HighLightProjectRuleSet hlmProjRuleSet, double increaserInDefects,
			HighLightProjectRuleSet codeQualityToDefect, boolean cmpValue) {
		if (cmpValue) {
			if (hlmProjRuleSet.getMessageFailure() == null || "".equals(hlmProjRuleSet.getMessageFailure()))
				hlmProjRuleSet.setMessageFailure(MessagesForHighLight.FAILURE_MESSAGE_CODE_TO_DEFECT);

			message = hlmProjRuleSet.getMessageFailure();

			increaserInDefect = (int) Math.round(increaserInDefects);
			increaseInDefect = Integer.toString(Math.round(increaserInDefect));

			newMessage = message.replace("<CodetoDefect>", increaseInDefect);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			codeQualityToDefect.setMessageFailure(newMessage);

			codeQualityToDefect.setRulePass(false);
			codeQualityToDefect.setTabName(hlmProjRuleSet.getTabName());
			hlmProjRuleSet.setRulePass(false);
		} else {
			if (hlmProjRuleSet.getMessageSuccess() == null || "".equals(hlmProjRuleSet.getMessageSuccess()))
				hlmProjRuleSet.setMessageSuccess(MessagesForHighLight.SUCCESS_MESSAGE_CODE_TO_DEFECT);

			message = hlmProjRuleSet.getMessageSuccess();
			increaserInDefect = (int) Math.round(increaserInDefects);
			increaseInDefect = Integer.toString(Math.round(increaserInDefect));

			newMessage = message.replace("<CodetoDefect>", increaseInDefect);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			codeQualityToDefect.setMessageSuccess(newMessage);

			codeQualityToDefect.setRulePass(true);
			codeQualityToDefect.setTabName(hlmProjRuleSet.getTabName());
			hlmProjRuleSet.setRulePass(true);
		}

	}

	private void updateHighlightSPTD(HighLightProjectRuleSet hlmProjRuleSet, double avgDefectPerStoryPoint,
			HighLightProjectRuleSet storyPTD, boolean cmpValue) {

		if (cmpValue) {
			if (hlmProjRuleSet.getMessageSuccess() == null || "".equals(hlmProjRuleSet.getMessageSuccess()))
				hlmProjRuleSet.setMessageSuccess(MessagesForHighLight.SUCCESS_MESSAGE_CODE_TO_DEFECT);

			message = hlmProjRuleSet.getMessageSuccess();

			avgDefectPerStoryPoints = (int) Math.round(avgDefectPerStoryPoint);
			averageDefectStory = Integer.toString(Math.round(avgDefectPerStoryPoints));

			newMessage = message.replace("<DefecttoStory>", averageDefectStory);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			storyPTD.setMessageSuccess(newMessage);

			storyPTD.setRulePass(true);
			storyPTD.setTabName(hlmProjRuleSet.getTabName());
			hlmProjRuleSet.setRulePass(true);
		} else {
			if (hlmProjRuleSet.getMessageFailure() == null || "".equals(hlmProjRuleSet.getMessageFailure()))
				hlmProjRuleSet.setMessageFailure(MessagesForHighLight.FAILURE_MESSAGE_CODE_TO_DEFECT);

			message = hlmProjRuleSet.getMessageFailure();

			avgDefectPerStoryPoints = (int) Math.round(avgDefectPerStoryPoint);
			averageDefectStory = Integer.toString(Math.round(avgDefectPerStoryPoints));

			newMessage = message.replace("<DefecttoStory>", averageDefectStory);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			storyPTD.setMessageFailure(newMessage);

			storyPTD.setRulePass(false);
			storyPTD.setTabName(hlmProjRuleSet.getTabName());
			hlmProjRuleSet.setRulePass(false);
		}
	}

	private void updateHighlightEffCalc(HighLightProjectRuleSet hlmProjRuleSet, double efficiencyForProject,
			HighLightProjectRuleSet effRule, String msgSuccess, String msgFailure) {
		int retval = Double.compare(efficiencyForProject, hlmProjRuleSet.getValue());
		if (retval != 0) {
			if (hlmProjRuleSet.getMessageSuccess() == null || "".equals(hlmProjRuleSet.getMessageSuccess()))
				hlmProjRuleSet.setMessageSuccess(msgSuccess);

			message = hlmProjRuleSet.getMessageSuccess();
			teamEffec = (int) Math.round(efficiencyForProject);
			teamEfficiency = Integer.toString(teamEffec);
			newMessage = message.replace("<Efficiency>", teamEfficiency);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			effRule.setMessageSuccess(newMessage);

			effRule.setRulePass(true);
			hlmProjRuleSet.setRulePass(true);
		} else {
			if (hlmProjRuleSet.getMessageFailure() == null || "".equals(hlmProjRuleSet.getMessageFailure()))
				hlmProjRuleSet.setMessageFailure(msgFailure);

			message = hlmProjRuleSet.getMessageFailure();
			teamEffec = (int) Math.round(efficiencyForProject);
			teamEfficiency = Integer.toString(teamEffec);
			newMessage = message.replace("<Efficiency>", teamEfficiency);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			effRule.setMessageFailure(newMessage);

			effRule.setRulePass(true);
		}
	}

	private void updateHighlightGroomedSP(HighLightProjectRuleSet hlmProjRuleSet, long cycleTime,
			HighLightProjectRuleSet groomedStories, boolean cmpValue) {

		if (cmpValue) {
			if (hlmProjRuleSet.getMessageSuccess() == null || "".equals(hlmProjRuleSet.getMessageSuccess()))
				hlmProjRuleSet.setMessageSuccess(MessagesForHighLight.SUCCESS_MESSAGE_GROOMED_STORIES);
			message = hlmProjRuleSet.getMessageSuccess();
			groompoints = Long.toString(cycleTime);
			newMessage = message.replace("<Groomed Stories>", groompoints);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			groomedStories.setMessageSuccess(newMessage);
			hlmProjRuleSet.setRulePass(true);
			groomedStories.setTabName(hlmProjRuleSet.getTabName());
			groomedStories.setRulePass(true);
		} else {
			if (hlmProjRuleSet.getMessageFailure() == null || "".equals(hlmProjRuleSet.getMessageFailure()))
				hlmProjRuleSet.setMessageFailure(MessagesForHighLight.FAILURE_MESSAGE_GROOMED_STORIES);

			message = hlmProjRuleSet.getMessageFailure();
			groompoints = Long.toString(cycleTime);
			newMessage = message.replace("<Groomed Stories>", groompoints);
			newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjRuleSet.getValue()));
			groomedStories.setMessageFailure(newMessage);
			hlmProjRuleSet.setRulePass(false);
			groomedStories.setTabName(hlmProjRuleSet.getTabName());
			groomedStories.setRulePass(false);

		}

	}

	private double getVelocityTrend(int teamVelocity1, int teamVelocity3) {
		double velTrend = 0;
		velTrend = (double) teamVelocity1 / teamVelocity3;
		velTrend = Math.round(velTrend * 100);
		return velTrend;

	}

	private String convertTime(long time) {
		Date date = new Date(time);
		Format format = new SimpleDateFormat("yyyy MM dd HH:mm:ss");
		return format.format(date);
	}

	private void displaySuccessMessage(HighLightProjectRuleSet hlmProjectRuleSet,
			HighLightProjectRuleSet hightlightRule, String highlightValue) {
		message = hlmProjectRuleSet.getMessageSuccess();
		newMessage = message.replace("<Velocity>", highlightValue);
		newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjectRuleSet.getValue()));
		hightlightRule.setMessageSuccess(newMessage);
		hightlightRule.setRulePass(true);
	}

	private void displayFailureMessage(HighLightProjectRuleSet hlmProjectRuleSet,
			HighLightProjectRuleSet hightlightRule, String highlightValue) {
		message = hlmProjectRuleSet.getMessageFailure();
		newMessage = message.replace("<Velocity>", highlightValue);
		newMessage = newMessage.replace("<HighlightValue>", Integer.toString(hlmProjectRuleSet.getValue()));
		hightlightRule.setMessageFailure(newMessage);
		hightlightRule.setRulePass(false);
	}

	public Object[] getCurrentSprintData(String pName) {
		Object[] objectArray = new Object[2];
		String sprintName = "";
		IterationModel model = null;
		IterationModel currentIteration = null;
		List<IterationModel> pastIteartionList = new ArrayList<>();
		List<IterationModel> iterationList = iterationRepo.findByPName(pName);
		if (iterationList != null) {
			Iterator<IterationModel> iterator = iterationList.iterator();
			for (int i = 0; i < iterationList.size() - 1; i++) {
				model = iterator.next();
				if (model.getState().equals("ACTIVE")) {
					objectArray[0] = model;
				}
				pastIteartionList.add(model);
			}
		}
		objectArray[1] = pastIteartionList;
		return objectArray;

	}
}
