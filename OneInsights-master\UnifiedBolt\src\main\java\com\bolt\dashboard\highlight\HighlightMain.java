
package com.bolt.dashboard.highlight;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.HighLightModel;
import com.bolt.dashboard.core.model.HighLightProjectRuleSet;
import com.bolt.dashboard.core.model.HighLightReelModel;
import com.bolt.dashboard.core.repository.HighLightReelRepository;
import com.bolt.dashboard.core.repository.HighLightRepo;

public class HighlightMain {

   
    private static final Logger LOGGER = LogManager.getLogger(HighlightMain.class);
    HighLightRepo highLightRepo;
    AnnotationConfigApplicationContext ctx;
    HighLightReelRepository highLightReelRepo;
    HighLightClientImplementation client;

    public HighlightMain() {

    }
//    public static void main(String[] args) {
//		new HighlightMain().highlightMain("Project K");
//	}

    public void highlightMain(String projectName) {
	LOGGER.info("HighLight Collector started");
	ctx = DataConfig.getContext();
	highLightRepo = ctx.getBean(HighLightRepo.class);
	highLightReelRepo = ctx.getBean(HighLightReelRepository.class);
	HighLightModel hlmProject = highLightRepo.findByProjectName(projectName);
	client = new HighLightClientImplementation();
	client.init(projectName);
	HighLightReelModel highLightReel = new HighLightReelModel();
	highLightReel.setProjectName(hlmProject.getProjectName());
	highLightReel.setUserName(hlmProject.getUserName());
	Set<HighLightProjectRuleSet> highLightReelRuleSet = new HashSet<>();
	Set<HighLightProjectRuleSet> hlmProjectRulesList = hlmProject.getRulesListOfProject();
	for (HighLightProjectRuleSet hlmProjectRuleSet : hlmProjectRulesList) {
	    switch (hlmProjectRuleSet.getRuleName()) {

	    case "Volatility":
		new HighLightClientImplementation().volatilityCalculation(hlmProjectRuleSet, highLightRepo, hlmProject,
			highLightReelRuleSet);
		break;
	    case "CycleTime Critical":
		new HighLightClientImplementation().cycleTimeCriticalCalculation(hlmProjectRuleSet, highLightRepo,
			hlmProject, highLightReelRuleSet);
		break;
	    case "CycleTime High":
		new HighLightClientImplementation().cycleTimeHighCalculation(hlmProjectRuleSet, highLightRepo,
			hlmProject, highLightReelRuleSet);
		break;
	    case "Team Velocity":
		new HighLightClientImplementation().teamVelocityCalculation(hlmProjectRuleSet, highLightRepo,
			hlmProject, highLightReelRuleSet);
		break;
	    case "Estimation Accuracy":
		new HighLightClientImplementation().estimationAccuracyCalculation(hlmProjectRuleSet, highLightRepo,
			hlmProject, highLightReelRuleSet);
		break;
	    case "Groomed Stories":
		new HighLightClientImplementation().groomedStoriesCalulation(hlmProjectRuleSet, highLightRepo,
			hlmProject, highLightReelRuleSet);
		break;
	    case "Team Efficiency":
		new HighLightClientImplementation().efficiencyCalulation(hlmProjectRuleSet, highLightRepo, hlmProject,
			highLightReelRuleSet);
		break;
	    case "Release Deadline":
		new HighLightClientImplementation().releaseDeadLineHighLight(hlmProjectRuleSet, highLightRepo,
			hlmProject, highLightReelRuleSet);
		break;
	    case "Project Deadline":
		new HighLightClientImplementation().projectEndHighLight(hlmProjectRuleSet, highLightRepo, hlmProject,
			highLightReelRuleSet);
		break;
	    case "Code to Defect":
		new HighLightClientImplementation().codeToDefectHighLight(hlmProjectRuleSet, highLightRepo, hlmProject,
			highLightReelRuleSet);
		break;
	    case "Story Points To Defect":
		new HighLightClientImplementation().storyPointsToDefectHighlight(hlmProjectRuleSet, highLightRepo,
			hlmProject, highLightReelRuleSet);
		break;
	    case "Average Cycle Time":
			new HighLightClientImplementation().engCyleTime(hlmProjectRuleSet, highLightRepo,
				hlmProject, highLightReelRuleSet);
			break;
	    case "Average Lead Time":
			new HighLightClientImplementation().engLeadTime(hlmProjectRuleSet, highLightRepo,
				hlmProject, highLightReelRuleSet);
			break;
	    case "Average Completion":
			new HighLightClientImplementation().engAvgCompletion(hlmProjectRuleSet, highLightRepo,
				hlmProject, highLightReelRuleSet);
			break;
	    case "Average Velocity":
			new HighLightClientImplementation().engAvgVelocity(hlmProjectRuleSet, highLightRepo,
				hlmProject, highLightReelRuleSet);
			break;
	    default:
		LOGGER.info("There is no such rule.");
	    }
	}

	HighLightReelModel highLightReelModel = highLightReelRepo.findByProjectName(projectName);

	highLightReel.setRulesListOfProject(highLightReelRuleSet);
	highLightReel.setTimestamp(new Date().getTime());
	if (highLightReelModel == null)
	    highLightReelRepo.save(highLightReel);
	else {
	    highLightReelRepo.delete(highLightReelModel);
	    highLightReelRepo.save(highLightReel);
	}

	LOGGER.info("Highlight collector ended");
    }

}
