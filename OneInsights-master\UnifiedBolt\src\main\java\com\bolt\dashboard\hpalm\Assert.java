package com.bolt.dashboard.hpalm;

import com.bolt.dashboard.exception.HPAlmException;

public class Assert {

    public Assert() {

    }

    public static final void assertTrue(final String errorMessage, boolean assertee) throws HPAlmException {
        if (!assertee) {
            throw new HPAlmException(errorMessage);
        }
    }

    public static final void assertEquals(final String errorMessage, final String expressionOne,
            final String expressionTwo) throws HPAlmException {
        if (!expressionOne.equals(expressionTwo)) {
            throw new HPAlmException(errorMessage);
        }
    }

    public static void assertEquals(String errorMessage, int expressionOne, int expressionTwo) throws HPAlmException {
        if (expressionOne != expressionTwo) {
            throw new HPAlmException(errorMessage);
        }
    }

    public static void assertNull(String errorMessage, String assertee) throws HPAlmException {
        if (assertee != null) {
            throw new HPAlmException(errorMessage);
        }
    }

    public static void assertNotNull(String errorMessage, String assertee) throws HPAlmException {
        if (assertee == null) {
            throw new HPAlmException(errorMessage);
        }
    }
}
