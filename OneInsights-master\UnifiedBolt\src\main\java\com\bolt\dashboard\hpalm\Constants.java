package com.bolt.dashboard.hpalm;

import java.security.SecureRandom;

import org.springframework.beans.factory.annotation.Value;

import com.bolt.dashboard.exception.HPAlmException;

public class Constants {
	 static SecureRandom random = new SecureRandom();
	    static Double randomDouble=random.nextDouble();

	
	@Value("${constants.password}")
	public static String PASSWORD;
	
	
    public static final String HOST = "brilliobolt.cloudapp.net";
    public static final String PORT = "8090";

    public static final String USERNAME = "abhijit.sahu";

    public static final String DOMAIN = "DEFAULT";
    public static final String PROJECT = "BOLT";    
   
    public static final String ENTITYTOPOSTNAME = "req" + Double.toHexString(randomDouble);
    public static final String ENTITYTOPOSTFIELDNAME = "type-id";
    public static final String ENTITYTOPOSTFIELDVALUE = "1";
    public static final String ENTITYTOPOSTFORMAT = "<Entity Type=\"requirement\">" + "<Fields>"
            + Constants.generateFieldXml("%s", "%s") + Constants.generateFieldXml("%s", "%s") + "</Fields>"
            + "</Entity>";

    public static final String entityToPostXml = String.format(ENTITYTOPOSTFORMAT, "name", ENTITYTOPOSTNAME,
            ENTITYTOPOSTFIELDNAME, ENTITYTOPOSTFIELDVALUE);

    public static final CharSequence entityToPostFieldXml = generateFieldXml(Constants.ENTITYTOPOSTFIELDNAME,
            Constants.ENTITYTOPOSTFIELDVALUE);

    private Constants() {
    }

    /**
     * Supports running tests correctly on both versioned and non-versioned
     * projects.
     * 
     * @return true if entities of entityType support versioning
     */
    public static boolean isVersioned(String entityType, final String domain, final String project)
            throws HPAlmException {
        try {
            RestConnector con = RestConnector.getInstance();
            String descriptorUrl = con.buildUrl(
                    "rest/domains/" + domain + "/projects/" + project + "/customization/entities/" + entityType);

            String descriptorXml = con.httpGet(descriptorUrl, null, null).toString();
            EntityDescriptor descriptor = EntityMarshallingUtils.marshal(EntityDescriptor.class, descriptorXml);

            return descriptor.getSupportsVC().getValue();
        } catch (Exception e) {
            throw new HPAlmException(e);
        }
    }

    public static String generateFieldXml(String field, String value) {
        return "<Field Name=\"" + field + "\"><Value>" + value + "</Value></Field>";
    }

    /**
     * This string used to create new "requirement" type entities.
     */

}