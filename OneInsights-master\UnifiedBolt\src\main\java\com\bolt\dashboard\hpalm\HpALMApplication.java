package com.bolt.dashboard.hpalm;

import java.util.Iterator;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.TestManagementTool;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.TestManagementRepo;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class HpALMApplication {


	private static final Logger LOGGER = LogManager.getLogger(HpALMApplication.class.getName());
	AnnotationConfigApplicationContext ctx = null;
	String instanceURL = "";
	String user = null;
	String pass = null;
	String projectCode = null;
	String domain = null;
	String defectUrl = null;
	String projectName = null;
	HpAlmClientImplementation impl = null;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configurationColection = null;
	TestManagementRepo repo = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	ConfigurationToolInfoMetric metric1 = null;

	/**
	 * Private Constructor
	 */
	public HpALMApplication() {

	}

	public void hpAlmMain(String projectName) {
		LOGGER.info("Hp Alm Collector started for " + projectName);
		impl = new HpAlmClientImplementation();
		this.projectName = projectName;
		ctx = DataConfig.getContext();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configurationColection = configurationRepo.findByProjectName(projectName).get(0);
		repo = ctx.getBean(TestManagementRepo.class);
		metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOGGER.info("Tool name  " + metric1.getToolName());
			if ("hpAlm".equals(metric1.getToolName())) {
				LOGGER.info("URL  " + metric1.getUrl());
				instanceURL = metric1.getUrl();
				user = metric1.getUserName();
				
					pass=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				projectCode = metric1.getProjectCode();
				domain = metric1.getDomain();
				break;
			}

		}

		try {
			TestManagementTool tool = impl.getConnection(instanceURL, user, pass, projectCode, domain, projectName);
			tool.setTestType("hpAlm");
			repo.save(tool);
			cleanObject();
			LOGGER.info("HP ALM ENDS.......... ");
		} catch (Exception e) {
			cleanObject();
			LOGGER.info(e);
			LOGGER.info("Hp Alm Collector failed for " + projectName);
		}

		LOGGER.info("Hp Alm Collector ended for " + projectName);
	}

	public void cleanObject() {

		impl = null;
		configurationRepo = null;
		configurationColection = null;
		repo = null;
		metric = null;
		metric1 = null;
	}

}
