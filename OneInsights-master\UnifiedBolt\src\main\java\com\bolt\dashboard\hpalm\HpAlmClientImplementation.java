package com.bolt.dashboard.hpalm;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;

import com.bolt.dashboard.core.model.TestCaseModel;
import com.bolt.dashboard.core.model.TestManagementTool;
import com.bolt.dashboard.core.model.TestSetModel;
import com.bolt.dashboard.exception.HPAlmException;

public class HpAlmClientImplementation implements HpAlmClient {
    String instanceURL = "";
    String user = null;
    String pass = null;
    String projectCode = null;
    String domain = null;
    String defectUrl = null;
    String projectName = null;
    private static final Logger LOG = LogManager.getLogger(HpAlmClientImplementation.class);

    @Override
    public TestManagementTool getConnection(String url, String user, String pass, String projectCode, String domain,
            String projectName) throws HPAlmException {
        instanceURL = url;
        this.user = user;
        this.pass = pass;
        this.domain = domain;
        this.projectCode = projectCode;
        this.projectName = projectName;

        JSONObject testRunsJsonObject = makeRestCall();
        return getDataFromJson(testRunsJsonObject, defectUrl);
    }

    public JSONObject makeRestCall() {
        AlmConnector alm = new AlmConnector();
        try {
            RestConnector conn = RestConnector.getInstance();
            conn.init(new HashMap<String, String>(), instanceURL, domain, projectCode);

            alm.login(user, pass);
            LOG.info("successfully authonticated....");
            conn.getQCSession();

            /*
             * Get the defect with id 300.
             */
            defectUrl = conn.buildEntityCollectionUrl("test-set");

            Map<String, String> requestHeaders = new HashMap<String, String>();
            requestHeaders.put("Accept", "application/json");

            Response res = conn.httpGet(defectUrl, null, requestHeaders);
            return new JSONObject(res.toString());
        } catch (Exception e) {
            LOG.info("Exception   " + e);
            return null;
        }
    }

    public TestManagementTool getDataFromJson(JSONObject jsonObject, String defectUrl) {

        TestManagementTool tool = new TestManagementTool();
        tool.setProjectName(projectName);
        List<TestSetModel> testSetModelList = new ArrayList<>();
        JSONObject testCaseJsonObject = null;
        String newTestUrl;
        JSONArray testJsonArray = jsonObject.getJSONArray("entities");
        for (int i = 1; i < testJsonArray.length(); i++) {

            List<TestCaseModel> testCaseModelList = new ArrayList<>();
            JSONArray fieldArray = ((JSONObject) testJsonArray.get(i)).getJSONArray("Fields");
            String testSetName = null;
            TestSetModel testSetModel = new TestSetModel();
            for (int j = 1; j < fieldArray.length(); j++) {

                if ((fieldArray.get(j)).toString().contains("order-id")) {
                    JSONArray cycleIdArray = ((JSONObject) fieldArray.get(j)).getJSONArray("values");
                    String cycleId = ((JSONObject) cycleIdArray.get(0)).getString("value");
                    testSetModel = new TestSetModel();

                    if (defectUrl.contains("test-sets")) {
                        newTestUrl = defectUrl.replace("test-sets", "test-instances?query=");
                        newTestUrl = newTestUrl + "{cycle-id[" + cycleId + "]}";
                        testCaseJsonObject = makeRestCallForTestCase(newTestUrl);
                        testCaseModelList = getTestData(testCaseJsonObject, testCaseModelList);

                    }

                }
                if ((fieldArray.get(j)).toString().contains("name")) {
                    JSONArray cycleIdArray = ((JSONObject) fieldArray.get(j)).getJSONArray("values");
                    testSetName = ((JSONObject) cycleIdArray.get(0)).getString("value");

                }

            }
            testSetModel.setTestSetName(testSetName);
            testSetModel.setTestCaseList(testCaseModelList);
            testSetModelList.add(testSetModel);
            tool.setTestSetList(testSetModelList);
        }

        return tool;

    }

    public JSONObject makeRestCallForTestCase(String newUrl) {

        AlmConnector alm = new AlmConnector();
        try {
            RestConnector conn = RestConnector.getInstance();
            conn.init(new HashMap<String, String>(), instanceURL, domain, projectCode);

            alm.login(user, pass);
            LOG.info("successfully authonticated....");
            conn.getQCSession();

            /*
             * Get the defect with id 300.
             */

            Map<String, String> requestHeaders = new HashMap<String, String>();
            requestHeaders.put("Accept", "application/json");

            Response res = conn.httpGet(newUrl, null, requestHeaders);
            return new JSONObject(res.toString());
        } catch (Exception e) {
            LOG.info("Exception   " + e);
            return null;
        }
    }

    public List<TestCaseModel> getTestData(JSONObject testJson, List<TestCaseModel> testCaseList) {
        JSONArray entitiesArray = testJson.getJSONArray("entities");
        for (int i = 0; i < entitiesArray.length(); i++) {
            TestCaseModel testCaseModel = new TestCaseModel();
            String execDate = null;
            String execTime = null;
            String scheduleDate = null;
            String scheduleTime = null;
            JSONArray fieldsArray = ((JSONObject) entitiesArray.get(i)).getJSONArray("Fields");
            for (int j = 0; j < fieldsArray.length(); j++) {

                if ((fieldsArray.get(j)).toString().contains("exec-date")) {
                    JSONArray execDateArray = ((JSONObject) fieldsArray.get(j)).getJSONArray("values");
                    execDate = ((JSONObject) execDateArray.get(0)).getString("value");
                }
                if ((fieldsArray.get(j)).toString().contains("exec-time")) {
                    JSONArray execDateArray = ((JSONObject) fieldsArray.get(j)).getJSONArray("values");
                    execTime = ((JSONObject) execDateArray.get(0)).getString("value");
                }
                if ((fieldsArray.get(j)).toString().contains("test-config-id")) {
                    JSONArray execDateArray = ((JSONObject) fieldsArray.get(j)).getJSONArray("values");
                    String testConfigId = ((JSONObject) execDateArray.get(0)).getString("value");
                    testCaseModel.setTestCaseId(testConfigId);
                }
                if ((fieldsArray.get(j)).toString().contains("plan-scheduling-date")) {
                    JSONArray execDateArray = ((JSONObject) fieldsArray.get(j)).getJSONArray("values");
                    scheduleDate = ((JSONObject) execDateArray.get(0)).getString("value");
                }
                if ((fieldsArray.get(j)).toString().contains("plan-scheduling-time")) {
                    JSONArray execDateArray = ((JSONObject) fieldsArray.get(j)).getJSONArray("values");
                    scheduleTime = ((JSONObject) execDateArray.get(0)).getString("value");
                }
                if ((fieldsArray.get(j)).toString().contains("owner")) {
                    JSONArray execDateArray = ((JSONObject) fieldsArray.get(j)).getJSONArray("values");
                    String owner = ((JSONObject) execDateArray.get(0)).getString("value");
                    testCaseModel.setOwner(owner);
                }
                if ((fieldsArray.get(j)).toString().contains("actual-tester")) {
                    JSONArray execDateArray = ((JSONObject) fieldsArray.get(j)).getJSONArray("values");
                    String actualTester = ((JSONObject) execDateArray.get(0)).getString("value");
                    testCaseModel.setActualTester(actualTester);
                }
                if ((fieldsArray.get(j)).toString().contains("name")) {
                    String name = ((JSONObject) fieldsArray.get(j)).getString("Name");
                    if (!"host-name".equals(name)) {
                        JSONArray execDateArray = ((JSONObject) fieldsArray.get(j)).getJSONArray("values");
                        String testCaseName = ((JSONObject) execDateArray.get(0)).getString("value");
                        testCaseModel.setTestCaseName(testCaseName);
                    }

                }
                if ((fieldsArray.get(j)).toString().contains("status")) {
                    JSONArray execDateArray = ((JSONObject) fieldsArray.get(j)).getJSONArray("values");
                    String status = ((JSONObject) execDateArray.get(0)).getString("value");
                    testCaseModel.setExecStatus(status);
                }

            }
            Timestamp startTimestamp = Timestamp.valueOf(execDate + " " + execTime);
            long executionTime = startTimestamp.getTime();
            testCaseModel.setExecDate(executionTime);
            long scheduleTest = startTimestamp.getTime();
            testCaseModel.setPlannedExecDate(scheduleTest);
            testCaseList.add(testCaseModel);
        }

        return testCaseList;
    }
}