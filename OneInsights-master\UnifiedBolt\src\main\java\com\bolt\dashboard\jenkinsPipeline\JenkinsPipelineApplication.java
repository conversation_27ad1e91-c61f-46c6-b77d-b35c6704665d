/**
 * 
 */
package com.bolt.dashboard.jenkinsPipeline;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.atlassian.jira.rest.client.api.RestClientException;
import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.JobDetails;
import com.bolt.dashboard.core.model.JobDetailsMetrics;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.JobDetailsRepo;
import com.bolt.dashboard.jenkins.JenkinsCollectorException;
import com.bolt.dashboard.util.EncryptionDecryptionAES;
import com.google.gson.JsonArray;

/**
 * <AUTHOR> C:\Program Files\MongoDB\Server\3.2\bin
 */
public class JenkinsPipelineApplication {

	private static final Logger LOGGER = LogManager.getLogger(JenkinsPipelineApplication.class);
	AnnotationConfigApplicationContext applicationContext = null;
	BuildToolRep repo = null;
	JenkinsApplication buildToolMetrics = null;
	String result = "SUCCESS";
	String buildType = "JENKINS";
	AnnotationConfigApplicationContext ctx = null;

	/**
	 * Private Constructor
	 * 
	 */
	
//	public static void main(String[] args) {
//		new JenkinsPipelineApplication().jenkinsPipelineMain("test");
//	}
	public JenkinsPipelineApplication() {

	}

	public void jenkinsPipelineMain(String projectName) throws RestClientException {
		LOGGER.info("Jenkins Collector started for " + projectName);
		applicationContext = DataConfig.getContext();
		repo = applicationContext.getBean(BuildToolRep.class);

		buildToolMetrics = new JenkinsPipelineImplementaion();
		try {

			ConfigurationToolInfoMetric metric = getConfigurationDetails(projectName);
			String userName = metric.getUserName();
			String password = EncryptionDecryptionAES.decrypt(metric.getPassword(), ConstantVariable.SECRET_KEY);

			ResponseEntity<String> response = makeRestCall(metric.getUrl() + ConstantVariable.BUILD_URL_PATTERN,
					userName, password);
			if (response !=null && response.getStatusCode() == HttpStatus.OK) {
				JSONObject jsonObject = parseAsNewArray(response);
				if (jsonObject.has("jobs")) {

					JSONArray subGroups = jsonObject.getJSONArray("jobs");

					for (int i = 0; i < subGroups.length(); i++) {
						JSONObject subGroup = subGroups.getJSONObject(i);
						String subGroupUrl = subGroup.getString("url");
						String subGroupName;
						if(subGroup.has("displayName")) {
							subGroupName = subGroup.getString("displayName");
						}else {
							subGroupName = subGroup.getString("name");
						}
						if(subGroupUrl.charAt(subGroupUrl.length()-1) == '/') {
							subGroupUrl = subGroupUrl.substring(0,subGroupUrl.length()-1);
						}

						response = makeRestCall(subGroupUrl + ConstantVariable.BUILD_URL_PATTERN, userName, password);
						if ( response !=null &&response.getStatusCode() == HttpStatus.OK) {
							JSONObject jsonObject1 = parseAsNewArray(response);
							if(jsonObject1.has("jobs")) {
								JSONArray repos = jsonObject1.getJSONArray("jobs");
								
								for (int j = 0; j < repos.length(); j++) {
									JSONObject repo = repos.getJSONObject(j);
									String repoUrl = repo.getString("url");
									if(repoUrl.charAt(repoUrl.length()-1) == '/') {
										repoUrl = repoUrl.substring(0,repoUrl.length()-1);
									}
									buildToolMetrics.getBuildTool(repoUrl, userName, password, subGroupName, this.repo,projectName);
								}
							}
							
						}

					}
				}
			}

			ConstantVariable.getLastRun(projectName, buildType, new Date().getTime(), result);
			cleanObject();
			LOGGER.info("Jenkins Collector ended for " + projectName);
		} catch (Exception e) {
			result = "FAIL";
			ConstantVariable.getLastRun(projectName, buildType, new Date().getTime(), result);
			cleanObject();
			LOGGER.error(e.getMessage());
			LOGGER.error("Exception ", e.fillInStackTrace());

			LOGGER.info("Jenkins Collector failed for " + projectName);
		}
		
	}

	public ConfigurationToolInfoMetric getConfigurationDetails(String projectName) {
		ctx = DataConfig.getContext();
		ConfigurationSettingRep configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		ConfigurationSetting configurationColection = configurationRepo.findByProjectName(projectName).get(0);
		Set<ConfigurationToolInfoMetric> metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
			// LOG.info("Tool name " + metric1.getToolName());
			if ("Jenkins Pipeline".equals(metric1.getToolName())) {
				return metric1;

			}
		}
		return null;
	}

	private ResponseEntity<String> makeRestCall(String url, String userId, String password)
			throws JenkinsCollectorException {

		if (!"".equals(userId) && !"".equals(password)) {
			try {

				return get().exchange(url, HttpMethod.GET, new HttpEntity<>(createHeaders(userId, password)),
						String.class);
			} catch (Exception e) {
				LOGGER.error(e);

				return null;
			}

		} else {
			try {
				return get().exchange(url, HttpMethod.GET, null, String.class);
			} catch (Exception e) {
				LOGGER.error(e.getMessage());
				return null;
			}

		}

	}

	private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(200000000);
		requestFactory.setReadTimeout(2000000000);
		return new RestTemplate(requestFactory);
	}

	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}

	public void cleanObject() {
		repo = null;
		buildToolMetrics = null;
	}

	private JSONObject parseAsNewArray(ResponseEntity<String> response) {
		return (JSONObject) new JSONTokener(response.getBody()).nextValue();
	}
}