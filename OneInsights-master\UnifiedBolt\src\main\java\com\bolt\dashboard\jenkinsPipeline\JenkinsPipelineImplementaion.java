package com.bolt.dashboard.jenkinsPipeline;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.BuildFailurePatternForProjectInJenkinsModel;
import com.bolt.dashboard.core.model.BuildFailurePatternMetrics;
import com.bolt.dashboard.core.model.BuildFileInfo;
import com.bolt.dashboard.core.model.BuildInfo;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.JobDetails;
import com.bolt.dashboard.core.model.JobDetailsMetrics;
import com.bolt.dashboard.core.repository.BuildFailurePatternForProjectRepo;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.JobDetailsRepo;
import com.bolt.dashboard.jenkins.BuildMetricsClientImplementation;
import com.bolt.dashboard.jenkins.JenkinsCollectorException;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class JenkinsPipelineImplementaion implements JenkinsApplication {

	private static final Logger LOGGER = LogManager.getLogger(BuildMetricsClientImplementation.class);
	AnnotationConfigApplicationContext ctx = null;
	private static int buildNumber;
	@SuppressWarnings("unused")
	String projectName = "";
	String result;
	String url = "";
	String jenkinsUrl = "";
	String pass = "";
	String user = "";
	List<String> jobsList = new ArrayList<>();
	BuildToolRep repo = null;
	String repoName;
	String groupName;
	List<String> jobCollection = new ArrayList<>();
	String projectUrl = null;
	private BuildFailurePatternForProjectRepo buildFailurePatternForProjectRepo;

	private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(200000000);
		requestFactory.setReadTimeout(2000000000);
		return new RestTemplate(requestFactory);
	}

	@SuppressWarnings({})
	private BuildTool getBuildMetrics(JSONObject json, String baseUrl, String user, String pass) {
		BuildTool tool1 = new BuildTool();
		tool1.setBuildType("JENKINS Pipeline");
//		tool1.setJobList(jobCollection);
//		tool1.setJobCount(jobsList.size());
//		
//		String jobName = json.getString("fullDisplayName");
//		String[] jobNameArray = null;
//		if (jobName.contains("#")) {
//			jobNameArray = jobName.split(Pattern.quote("#"));
//			jobName = jobNameArray[0];
//			jobName = jobName.substring(0, jobName.length() - 1);
//
//		}
//		if (jobName.contains("»")) {
//			jobNameArray = jobName.split(Pattern.quote("»"));
//			//jobName = jobNameArray[0];
//			jobName = jobNameArray[0].trim()+"/job/"+jobNameArray[1].trim();
//
//		}

		tool1.setJobName(this.repoName);
		BuildToolMetric durationMetric = new BuildToolMetric("duration");

		Object duration = json.get("duration");

		durationMetric.setValue(duration != null ? duration.toString() : "");
		tool1.getMetrics().add(durationMetric);
		
		Object timestamp = null;
		if(json.has("timestamp")) {
			timestamp=json.get("timestamp");
			tool1.setTimestamp(Long.parseLong(timestamp.toString()));
		}else {
			tool1.setTimestamp(0);
		}
		
		BuildToolMetric timeStampmetric = new BuildToolMetric("timestamp");
		timeStampmetric.setValue(timestamp != null ? timestamp.toString() : "");
		tool1.getMetrics().add(timeStampmetric);
		if (json.get("result") == null) {
			result = "FAILURE";
		} else {
			result = json.get("result").toString();
		}
		int buildId = json.getInt("id");
		tool1.setBuildID(buildId);
		if ("FAILURE".equals(result)) {
			List<BuildInfo> buildInfoList = getListBuildInfo(json);
			BuildFailurePatternForProjectInJenkinsModel patternForFailure = patternSettings(baseUrl, user, pass);
			tool1.setPatternDetails(patternForFailure);
			tool1.setBuildInfoList(buildInfoList);
		}
		BuildToolMetric resultMetrics = new BuildToolMetric("result");
		resultMetrics.setValue(result != null ? result.toString() : "");
		tool1.getMetrics().add(resultMetrics);
		String fullDisplayName = json.getString("fullDisplayName");
		

			tool1.setName(projectName);

		

		return tool1;
	}

	public void getBuildTool(String url, String userName, String password, String subgroupName, BuildToolRep repo,String projectName) {
		this.user = userName;
		this.projectName = projectName;
		this.pass = password;
		BuildTool tool = null;
		this.jenkinsUrl = url;
		this.repo = repo;
		this.groupName = subgroupName;
		url = url +  ConstantVariable.BUILD_URL_PATTERN;
//		try {
//			ResponseEntity<String> response = makeRestCall(url + ConstantVariable.BUILD_URL_PATTERN, user, pass);
//			JSONObject jsonObject = parseAsNewArray(response);
//			jobCollection = getJobsList(jsonObject);
//			if (!jobsList.isEmpty()) {
//
//				for (String jobName : jobsList) {
//					jenkinsUrl = projectUrl + "/job/" + jobName + ConstantVariable.BUILD_URL_PATTERN;
//					tool = retrieveJenkinsData(jenkinsUrl, jobName);
//				}
//			} else {
//				jenkinsUrl = jenkinsUrl + ConstantVariable.BUILD_URL_PATTERN;
//				tool = retrieveJenkinsData(jenkinsUrl, job);
//			}
//		} catch (JenkinsCollectorException e) {
//			
//			
//		}
		try {
			tool = retrieveJenkinsData(url);
			repo.save(tool);
		} catch (Exception e) {
			LOGGER.info(e);
		}

	}

	@SuppressWarnings({})
	public int getNextBuildNumber(JSONObject json) {
		buildNumber = (int) json.get("nextBuildNumber");

		LOGGER.info("buildNumber  " + buildNumber);
		return buildNumber;

	}

	private JSONObject parseAsNewArray(ResponseEntity<String> response) {
		return (JSONObject) new JSONTokener(response.getBody()).nextValue();
	}

	private ResponseEntity<String> makeRestCall(String url, String userId, String password)
			throws JenkinsCollectorException {

		if (!"".equals(userId) && !"".equals(password)) {
			try {

				return get().exchange(url, HttpMethod.GET, new HttpEntity<>(createHeaders(userId, password)),
						String.class);
			} catch (Exception e) {
				LOGGER.error(e);

				return null;
			}

		} else {
			try {
				return get().exchange(url, HttpMethod.GET, null, String.class);
			} catch (Exception e) {
				LOGGER.error(e.getMessage());
				return null;
			}

		}

	}

	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}

	public List<BuildInfo> getListBuildInfo(JSONObject json) {
		List<BuildInfo> buildInfoList = new ArrayList<>();
		if (json.has("changeSet")) {

			JSONObject changeSetObject = (JSONObject) json.get("changeSet");
			String changeSetString = changeSetObject.toString();
			if (changeSetString.contains("items")) {
				JSONArray itemsArary = changeSetObject.getJSONArray("items");
				for (int i = 0; i < itemsArary.length(); i++) {
					BuildInfo buildInfo = new BuildInfo();
					JSONObject itemObject = (JSONObject) itemsArary.get(i);
					String fullName = ((JSONObject) itemObject.getJSONObject("author")).getString("fullName");
					String message = itemObject.getString("msg");
					buildInfo.setCommitter(fullName);
					buildInfo.setMessage(message);
					List<BuildFileInfo> buildFileInfoList = new ArrayList<>();
					JSONArray pathsJsonArray = itemObject.getJSONArray("paths");
					for (int j = 0; j < pathsJsonArray.length(); j++) {
						BuildFileInfo buildFileInfo = new BuildFileInfo();
						String fileName = ((JSONObject) pathsJsonArray.get(j)).getString("file");
						String[] fileSeparationString = null;
						if (fileName.contains("/")) {
							fileSeparationString = fileName.split(Pattern.quote("/"));
							fileName = fileSeparationString[fileSeparationString.length - 1];
						}
						String editType = ((JSONObject) pathsJsonArray.get(j)).getString("editType");
						buildFileInfo.setEditType(editType);
						buildFileInfo.setFileNames(fileName);
						buildFileInfoList.add(buildFileInfo);
					}
					buildInfo.setBuildFileInfoList(buildFileInfoList);
					buildInfoList.add(buildInfo);
				}

			}
		}
		return buildInfoList;

	}

	public String getConfigurationDetails() {
		ctx = DataConfig.getContext();
		ConfigurationSettingRep configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		ConfigurationSetting configurationColection = configurationRepo.findByProjectName(projectName).get(0);
		JobDetailsRepo jobDetailsRepo = ctx.getBean(JobDetailsRepo.class);
		JobDetails jobDetails = jobDetailsRepo.findByProjectName(projectName);
		if (jobDetails != null) {
			List<JobDetailsMetrics> jobDetailsMetrics = jobDetails.getMetrics();
			Iterator jobDetailsIter = jobDetailsMetrics.iterator();
			while (jobDetailsIter.hasNext()) {

				Object jobDetail = jobDetailsIter.next();
				JobDetailsMetrics obj = (JobDetailsMetrics) jobDetail;
				String jobName = obj.getJobName();
				jobsList.add(jobName);
			}
		}

		String buildJobName = null;
		Set<ConfigurationToolInfoMetric> metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
			// LOG.info("Tool name " + metric1.getToolName());
			if ("Jenkins".equals(metric1.getToolName())) {
				LOGGER.info("URL  " + metric1.getUrl());
				jenkinsUrl = metric1.getUrl();
				user = metric1.getUserName();

				pass = EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				LOGGER.info("This is what we have done" + jenkinsUrl);
				break;
			}

		}
		String[] jobNameArray = null;

		if (jenkinsUrl.contains("/job/")) {
			jobNameArray = jenkinsUrl.split(Pattern.quote("/job/"));
			buildJobName = jobNameArray[1];
			projectUrl = jobNameArray[0];

		}
		url = jenkinsUrl;
		return buildJobName;
	}

	public BuildTool retrieveJenkinsData(String instanceUrl) throws JenkinsCollectorException {
		BuildTool tool = new BuildTool();

		ResponseEntity<String> response = makeRestCall(instanceUrl, user, pass);
		if(response !=null) {
		JSONObject jsonObject = parseAsNewArray(response);
		buildNumber = getNextBuildNumber(jsonObject);
		this.repoName = jsonObject.getString("displayName");
		JSONObject firstBuildJson = (JSONObject) jsonObject.get("firstBuild");
		int count = (int) firstBuildJson.get("number");
		Set<BuildTool> listOfBuildTools = repo.findByNameAndJobName(projectName, this.repoName);
		List<BuildTool> list = new ArrayList<BuildTool>(listOfBuildTools);
		if (list != null && !list.isEmpty()) {
			BuildTool buildTool = list.get(list.size() - 1);
			count = buildTool.getBuildID();
			count=count+1;
		}
		if (count >= buildNumber) {

			LOGGER.info("No Builds to be stored    ");
		} else {
			for (; count < buildNumber; count++) {
				String baseUrl = jenkinsUrl + "/" + count +ConstantVariable.BUILD_URL_PATTERN;

				try {

					ResponseEntity<String> buildResponse = makeRestCall(baseUrl, user, pass);
					if (!(buildResponse == null)) {
						JSONObject buildObject = parseAsNewArray(buildResponse);
						tool = getBuildMetrics(buildObject, baseUrl, user, pass);
						repo.save(tool);
					} else {
						continue;
					}

				} catch (RestClientException e) {
					LOGGER.error("Exception ", e);

					throw new JenkinsCollectorException(e);
				}
			}
		}
		}
		return tool;

	}

	public List<String> getJobsList(JSONObject object) {
		JSONArray jobsJsonArray = object.getJSONArray("jobs");
		for (int i = 0; i < jobsJsonArray.length(); i++) {
			String jobName = ((JSONObject) jobsJsonArray.get(i)).getString("name");
			jobCollection.add(jobName);
		}

		return jobCollection;

	}

	public BuildFailurePatternForProjectInJenkinsModel patternSettings(String baseUrl, String user, String pass) {
		@SuppressWarnings("resource")
		String newBaseUrl = baseUrl.replace("/api/json", "/logText/progressiveText/api/json");
		ctx = DataConfig.getContext();
		buildFailurePatternForProjectRepo = ctx.getBean(BuildFailurePatternForProjectRepo.class);
		List<BuildFailurePatternForProjectInJenkinsModel> patternList = (List<BuildFailurePatternForProjectInJenkinsModel>) buildFailurePatternForProjectRepo
				.findByProjectName(projectName);
		BuildFailurePatternForProjectInJenkinsModel patternToSave = null;
		if (!patternList.isEmpty()) {
			patternToSave = patternList.get(patternList.size() - 1);
			ResponseEntity<String> response = null;
			try {
				response = makeRestCall(newBaseUrl, user, pass);
			} catch (JenkinsCollectorException e) {
				LOGGER.error(e);
			}
			if(response !=null) {
			StringBuilder responseText = new StringBuilder(response.getBody());
			patternToSave.setTimestampOfCreation(System.currentTimeMillis());
			for (BuildFailurePatternMetrics pattern : patternToSave.getPatternMetrics()) {
				try {
					if ((responseText.toString().indexOf(pattern.getPatternDisplayed()) > 0)) {
						String responseStringText = responseText.toString().substring(
								responseText.toString().indexOf(pattern.getPatternDisplayed()/* .toUpperCase() */));
						if (!responseStringText.isEmpty())
							pattern.setPatternCount(pattern.getPatternCount() + 1);
						buildFailurePatternForProjectRepo.save(patternToSave);
					}

				} catch (StringIndexOutOfBoundsException exception) {
					LOGGER.error(exception);
					continue;
				}

			}
			}

		}

		return patternToSave;
	}

}
