package com.bolt.dashboard.jenkins;

import org.json.simple.parser.ParseException;
import org.springframework.web.client.RestClientException;

import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.repository.BuildToolRep;

/**
 * 
 * <AUTHOR>
 *
 */
public interface BuildMetricsClient {
    /**
     * 
     * @param CQ_URL
     * @return
     * @throws RestClientException
     * @throws ParseException
     */
    BuildTool getBuildTool(BuildToolRep repo, String projectName) throws JenkinsCollectorException;

}
