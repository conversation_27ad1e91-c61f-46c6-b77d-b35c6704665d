package com.bolt.dashboard.jenkins;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.BuildFailurePatternForProjectInJenkinsModel;
import com.bolt.dashboard.core.model.BuildFailurePatternMetrics;
import com.bolt.dashboard.core.model.BuildFileInfo;
import com.bolt.dashboard.core.model.BuildInfo;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.JobDetails;
import com.bolt.dashboard.core.model.JobDetailsMetrics;
import com.bolt.dashboard.core.model.ProjectCoverageDetails;
import com.bolt.dashboard.core.repository.BuildFailurePatternForProjectRepo;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.JobDetailsRepo;
import com.bolt.dashboard.core.repository.ProjectCoverageDetailsRepo;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

@Component
public class BuildMetricsClientImplementation implements BuildMetricsClient {
	private static final Logger LOGGER = LogManager.getLogger(BuildMetricsClientImplementation.class);
	AnnotationConfigApplicationContext ctx = null;
	private static int buildNumber = 1;
	@SuppressWarnings("unused")
	String projectName = "";
	String result;
	String url = "";
	String jenkinsUrl = "";
	String pass = "";
	String user = "";
	List<String> jobsList = new ArrayList<>();
	BuildToolRep repo = null;
	List<String> jobCollection = new ArrayList<>();
	String projectUrl = null;
	private BuildFailurePatternForProjectRepo buildFailurePatternForProjectRepo;
	List<ProjectCoverageDetails> unitTestData = new ArrayList<ProjectCoverageDetails>();
	ProjectCoverageDetailsRepo coverageDetailsRepo = null;
	private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(200000000);
		requestFactory.setReadTimeout(2000000000);
		return new RestTemplate(requestFactory);
	}

	@SuppressWarnings({})
	private BuildTool getBuildMetrics(JSONObject json, String baseUrl, String user, String pass, String jobName) {
		BuildTool tool1 = new BuildTool();
		Object timestamp = null;
		tool1.setBuildType("JENKINS");
		tool1.setJobList(jobCollection);
		tool1.setJobCount(jobsList.size());
//		String jobName = json.getString("fullDisplayName");
//		String[] jobNameArray = null;
//		if (jobName.contains("#")) {
//			jobNameArray = jobName.split(Pattern.quote("#"));
//			jobName = jobNameArray[0];
//			jobName = jobName.substring(0, jobName.length() - 1);
//
//		}   
//		if (jobName.contains("»")) {
//			jobNameArray = jobName.split(Pattern.quote("»"));
//			//jobName = jobNameArray[0];
//			jobName = jobNameArray[0].trim()+"/job/"+jobNameArray[1].trim();
//
//		}
		tool1.setJobName(jobName);
		BuildToolMetric durationMetric = new BuildToolMetric("duration");

		Object duration = json.get("duration");

		durationMetric.setValue(duration != null ? duration.toString() : "");
		tool1.getMetrics().add(durationMetric);
		
		timestamp = json.get("timestamp");
		if (timestamp != null) {
			tool1.setTimestamp(Long.parseLong(timestamp.toString()));
			BuildToolMetric timeStampmetric = new BuildToolMetric("timestamp");
			timeStampmetric.setValue(timestamp.toString());

			tool1.getMetrics().add(timeStampmetric);
		}
		if (json.get("result") == null) {
			result = "FAILURE";
		} else {
			result = json.get("result").toString();
		}
		int buildId = json.getInt("id");
		tool1.setBuildID(buildId);
		if ("FAILURE".equals(result)) {
			List<BuildInfo> buildInfoList = getListBuildInfo(json);
			BuildFailurePatternForProjectInJenkinsModel patternForFailure = patternSettings(baseUrl, user, pass);
			tool1.setPatternDetails(patternForFailure);
			tool1.setBuildInfoList(buildInfoList);
		}
		BuildToolMetric resultMetrics = new BuildToolMetric("result");
		resultMetrics.setValue(result != null ? result.toString() : "");
		tool1.getMetrics().add(resultMetrics);
		tool1.setName(projectName);
		return tool1;
	}

	public BuildTool getBuildTool(BuildToolRep repo, String projectName) throws JenkinsCollectorException {
		LOGGER.info("getBuildTool..");
		
		this.projectName = projectName;
		this.repo = repo;
		BuildTool tool = null;
		String job = getConfigurationDetails();
		//tool = retrieveJenkinsData(jenkinsUrl+ConstantVariable.BUILD_URL_PATTERN, "");
		//String job = getConfigurationDetails();
		LOGGER.info("projectUrl : " + projectUrl + ConstantVariable.BUILD_URL_PATTERN);
		ResponseEntity<String> response = makeRestCall(projectUrl + ConstantVariable.BUILD_URL_PATTERN, user, pass);
		if (response != null) {
			JSONObject jsonObject = parseAsNewArray(response);
			LOGGER.info("projectUrl got response..");
			jobCollection = getJobsList(jsonObject);
		}

		if (!jobsList.isEmpty()) {

			for (String jobName : jobsList) {
				jenkinsUrl = projectUrl + "/job/" + jobName + ConstantVariable.BUILD_URL_PATTERN;
				LOGGER.info("jenkinsUrl: " + jenkinsUrl);
				tool = retrieveJenkinsData(jenkinsUrl, jobName);

			}
		} else {
			jenkinsUrl = jenkinsUrl + ConstantVariable.BUILD_URL_PATTERN;
			LOGGER.info("jenkinsUrl: " + jenkinsUrl);
			tool = retrieveJenkinsData(jenkinsUrl, job);
		}

		return tool;
	}

	@SuppressWarnings({})
	public int getNextBuildNumber(JSONObject json) {
		buildNumber = (int) json.get("nextBuildNumber");

		LOGGER.info("buildNumber  " + buildNumber);
		return buildNumber;

	}

	private JSONObject parseAsNewArray(ResponseEntity<String> response) {
		return (JSONObject) new JSONTokener(response.getBody()).nextValue();
	}

	private ResponseEntity<String> makeRestCall(String url, String userId, String password)
			throws JenkinsCollectorException {

		if (!"".equals(userId) && !"".equals(password)) {
			try {

				return get().exchange(url, HttpMethod.GET, new HttpEntity<>(createHeaders(userId, password)),
						String.class);
			} catch (Exception e) {
				LOGGER.error(e);

				return null;
			}

		} else {
			try {
				return get().exchange(url, HttpMethod.GET, null, String.class);
			} catch (Exception e) {
				LOGGER.error(e.getMessage());
				return null;
			}

		}

	}

	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}

	public List<BuildInfo> getListBuildInfo(JSONObject json) {
		List<BuildInfo> buildInfoList = new ArrayList<>();
		JSONObject changeSetObject = (JSONObject) json.get("changeSet");
		String changeSetString = changeSetObject.toString();
		if (changeSetString.contains("items")) {
			JSONArray itemsArary = changeSetObject.getJSONArray("items");
			for (int i = 0; i < itemsArary.length(); i++) {
				BuildInfo buildInfo = new BuildInfo();
				JSONObject itemObject = (JSONObject) itemsArary.get(i);
				String fullName = ((JSONObject) itemObject.getJSONObject("author")).getString("fullName");
				String message = itemObject.getString("msg");
				buildInfo.setCommitter(fullName);
				buildInfo.setMessage(message);
				List<BuildFileInfo> buildFileInfoList = new ArrayList<>();
				JSONArray pathsJsonArray = itemObject.getJSONArray("paths");
				for (int j = 0; j < pathsJsonArray.length(); j++) {
					BuildFileInfo buildFileInfo = new BuildFileInfo();
					String fileName = ((JSONObject) pathsJsonArray.get(j)).getString("file");
					String[] fileSeparationString = null;
					if (fileName.contains("/")) {
						fileSeparationString = fileName.split(Pattern.quote("/"));
						fileName = fileSeparationString[fileSeparationString.length - 1];
					}
					String editType = ((JSONObject) pathsJsonArray.get(j)).getString("editType");
					buildFileInfo.setEditType(editType);
					buildFileInfo.setFileNames(fileName);
					buildFileInfoList.add(buildFileInfo);
				}
				buildInfo.setBuildFileInfoList(buildFileInfoList);
				buildInfoList.add(buildInfo);
			}

		}
		return buildInfoList;

	}

	public String getConfigurationDetails() {
		ctx = DataConfig.getContext();
		ConfigurationSettingRep configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		ConfigurationSetting configurationColection = configurationRepo.findByProjectName(projectName).get(0);
		JobDetailsRepo jobDetailsRepo = ctx.getBean(JobDetailsRepo.class);
		JobDetails jobDetails = jobDetailsRepo.findByProjectName(projectName);
		if (jobDetails != null) {
			List<JobDetailsMetrics> jobDetailsMetrics = jobDetails.getMetrics();
			Iterator jobDetailsIter = jobDetailsMetrics.iterator();
			while (jobDetailsIter.hasNext()) {

				Object jobDetail = jobDetailsIter.next();
				JobDetailsMetrics obj = (JobDetailsMetrics) jobDetail;
				String jobName = obj.getJobName();
				jobsList.add(jobName);
			}
		}

		String buildJobName = null;
		Set<ConfigurationToolInfoMetric> metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
			// LOG.info("Tool name " + metric1.getToolName());
			if ("Jenkins".equals(metric1.getToolName())) {
				LOGGER.info("URL  " + metric1.getUrl());
				jenkinsUrl = metric1.getUrl();
				user = metric1.getUserName();
				pass = EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);

				break;
			}

		}

//		jenkinsUrl = "http://localhost:8080/view/FUZE_PROD/job/NTS.APNV.Fuze.PurchaseOrderService.Master.DeployPROD";
		String[] jobNameArray = null;

		if (jenkinsUrl.contains("/job/")) {
			jobNameArray = jenkinsUrl.split(Pattern.quote("/job/"));

			buildJobName = jobNameArray[jobNameArray.length - 1];
			projectUrl = "";
			for (int i = 0; i <= jobNameArray.length - 2; i++) {
				projectUrl = projectUrl + jobNameArray[i] + "/job/";
			}
			projectUrl = projectUrl.substring(0, projectUrl.length() - 5);

		}
		// projectUrl = jenkinsUrl;
		return buildJobName;
	}

	public BuildTool retrieveJenkinsData(String instanceUrl, String jobName) throws JenkinsCollectorException {
		BuildTool tool = new BuildTool();

		ResponseEntity<String> response = makeRestCall(instanceUrl, user, pass);
//		
//		String res = "{\"_class\":\"hudson.model.FreeStyleProject\",\"actions\":[{},{},{},{},{},{},{},{\"_class\":\"hudson.plugins.jobConfigHistory.JobConfigHistoryProjectAction\"},{},{},{},{},{},{},{},{},{\"_class\":\"com.cloudbees.plugins.credentials.ViewCredentialsAction\"}],\"description\":\"Compliance Prod GUI build and deploy\",\"displayName\":\"NTS.APNV.Fuze.ComplianceGui.Master2.DeployPROD\",\"displayNameOrNull\":null,\"fullDisplayName\":\"NTS.APNV.Fuze.ComplianceGui.Master2.DeployPROD\",\"fullName\":\"NTS.APNV.Fuze.ComplianceGui.Master2.DeployPROD\",\"name\":\"NTS.APNV.Fuze.ComplianceGui.Master2.DeployPROD\",\"url\":\"https:\\/\\/onejenkinscloud.vpc.verizon.com\\/nts\\/view\\/FUZE_PROD\\/job\\/NTS.APNV.Fuze.ComplianceGui.Master2.DeployPROD\\/\",\"buildable\":true,\"builds\":[],\"color\":\"notbuilt\",\"firstBuild\":null,\"healthReport\":[],\"inQueue\":false,\"keepDependencies\":false,\"lastBuild\":null,\"lastCompletedBuild\":null,\"lastFailedBuild\":null,\"lastStableBuild\":null,\"lastSuccessfulBuild\":null,\"lastUnstableBuild\":null,\"lastUnsuccessfulBuild\":null,\"nextBuildNumber\":34,\"property\":[{\"_class\":\"hudson.plugins.jira.JiraProjectProperty\"},{\"_class\":\"hudson.security.AuthorizationMatrixProperty\"},{\"_class\":\"hudson.plugins.buildblocker.BuildBlockerProperty\"},{\"_class\":\"jenkins.model.BuildDiscarderProperty\"},{\"_class\":\"com.dabsquared.gitlabjenkins.connection.GitLabConnectionProperty\"},{\"_class\":\"com.chikli.hudson.plugin.naginator.NaginatorOptOutProperty\"},{\"_class\":\"com.sonyericsson.rebuild.RebuildSettings\"}],\"queueItem\":null,\"concurrentBuild\":false,\"downstreamProjects\":[{\"_class\":\"hudson.model.FreeStyleProject\",\"name\":\"NTS.APNV.Fuze.ClearCache.Master.DeployPROD\",\"url\":\"https:\\/\\/onejenkinscloud.vpc.verizon.com\\/nts\\/job\\/NTS.APNV.Fuze.ClearCache.Master.DeployPROD\\/\",\"color\":\"blue\"}],\"labelExpression\":\"GINGER_BUILD_UAT\",\"scm\":{\"_class\":\"hudson.plugins.git.GitSCM\"},\"upstreamProjects\":[]}";
//		
//		
//		ResponseEntity<String> response =   new ResponseEntity(res,  HttpStatus.OK);
		
		if (response != null) {
			try {
				LOGGER.info("retrieveJenkinsData response: " + response.getBody().length());
				JSONObject jsonObject = parseAsNewArray(response);
				buildNumber = getNextBuildNumber(jsonObject);
				int count = 1;
				if (jsonObject.has("firstBuild")&& !jsonObject.isNull("firstBuild")) {
					JSONObject firstBuildJson = (JSONObject) jsonObject.get("firstBuild");
					count = (int) firstBuildJson.get("number");
				}

				Set<BuildTool> listOfBuildTools = repo.findByNameAndJobName(projectName, jobName);
				List<BuildTool> list = new ArrayList<BuildTool>(listOfBuildTools);
				if (list != null && !list.isEmpty()) {
					BuildTool buildTool = list.get(list.size() - 1);
					count = buildTool.getBuildID();
				}
				if (count  >= (buildNumber)) {

					LOGGER.info("No Builds to be stored    ");
				} else {
					if (count != 1)
						count++;
					for (int i = count; i < buildNumber; i++) {
						String baseUrl = "";
						String baseUrlForLog = "";
						if (jenkinsUrl.contains(ConstantVariable.BUILD_URL_PATTERN)) {
							baseUrl = jenkinsUrl.replaceAll(ConstantVariable.BUILD_URL_PATTERN, "") + "/" + i
									+ ConstantVariable.BUILD_URL_PATTERN /*
																			 * ConstantVariable . JOBS_URL_FOR_DB*/;
							String temp = baseUrl;										 
							baseUrlForLog = temp.replaceAll(ConstantVariable.BUILD_URL_PATTERN, "")+"/consoleText";
							LOGGER.info("log url:"+baseUrlForLog);
							LOGGER.info("URI :" + baseUrl);
						}
						try {

							ResponseEntity<String> buildResponse = makeRestCall(baseUrl, user, pass);
							if (!(buildResponse == null)) {
								JSONObject buildObject = parseAsNewArray(buildResponse);

								tool = getBuildMetrics(buildObject, baseUrl, user, pass, jobName);
								LOGGER.info(tool.getJobName());
								LOGGER.info(tool.getBuildID());
								repo.save(tool);
							} else {
								LOGGER.info("Empty Response");
								continue;
							}
							
							ResponseEntity<String> logResponse = makeRestCall(baseUrlForLog, user, pass);
							
							if (!(logResponse == null)) {
								processLogDataForTestData(logResponse,tool);
							} else {
								LOGGER.info("Empty log Response");
								continue;
							}

						} catch (Exception e) {
							LOGGER.error("Exception while collecting perticular build", e.getLocalizedMessage());
							throw new JenkinsCollectorException(e);
						}
					}
				}
				if(unitTestData.size() > 0) {
					coverageDetailsRepo = ctx.getBean(ProjectCoverageDetailsRepo.class);
					coverageDetailsRepo.save(unitTestData);
				}
				
			} catch (Exception e) {
				LOGGER.info(e);
				LOGGER.error("Exception while getting first build or next build number",e.getLocalizedMessage());
			}
		}
		return tool;

	}

	
	
	private void processLogDataForTestData(ResponseEntity<String> logResponse, BuildTool tool) {
		ProjectCoverageDetails unitTestObject = new ProjectCoverageDetails();
		String logString = logResponse.getBody();
		String testPattern = "Tests run: [0-9.]*, Failures: [0-9.]*, Errors: [0-9.]*, Skipped: [0-9.]*";
		List<String> testData = matchFinder(testPattern, logString);
		if(testData.size()>0) {
			String testString = testData.get(0);
			List<String> dataList =Arrays.asList( testString.split(",") );
			for(String value:dataList) {
				if(value.toLowerCase().contains("tests run")) {
					unitTestObject.setTotal_test_cases((int)getValue(value));
				}else if(value.toLowerCase().contains("failures")) {
					unitTestObject.setTest_cases_failed((int)getValue(value));
				}
				
			}
			unitTestObject.setStarted_timestamp(tool.getTimestamp());
			unitTestObject.setCreated_timestamp(tool.getTimestamp());
			unitTestObject.setTest_cases_passed(unitTestObject.getTotal_test_cases()-unitTestObject.getTest_cases_failed());
		}
		
		int index = logString.indexOf("Overall coverage");
		if(index>0) {
			String coverage = logString.substring(index);
			List<String> dataList =Arrays.asList( coverage.split(",") );
			for(String value:dataList) {
				if(value.toLowerCase().contains("class")) {
					unitTestObject.setCovered_class_percentage(getValue(value));
				}else if(value.toLowerCase().contains("method")) {
					unitTestObject.setCovered_function_percentage(getValue(value));
				}else if(value.toLowerCase().contains("line")) {
					unitTestObject.setCovered_lines_percentage(getValue(value));
				}else if(value.toLowerCase().contains("branch")) {
					unitTestObject.setCovered_branches_percentage(getValue(value));
				}else if(value.toLowerCase().contains("instruction")) {
					unitTestObject.setCovered_instruction_percentage(getValue(value));
				}
			}
			unitTestObject.setpName(projectName);
		}
		
		unitTestData.add(unitTestObject);
		
		
	}
	
	double getValue(String str){
		String[] arr = str.split(":");
		double number;
		try {
			number =  Double.parseDouble(arr[arr.length-1]);
		}catch(NumberFormatException e) {
			number =0;
		}
		
		return number;
	}

	public List<String> getJobsList(JSONObject object) {
		JSONArray jobsJsonArray = object.getJSONArray("jobs");
		for (int i = 0; i < jobsJsonArray.length(); i++) {
			String jobName = ((JSONObject) jobsJsonArray.get(i)).getString("name");
			jobCollection.add(jobName);
		}
		LOGGER.info("jobCollection: " + jobCollection.size());
		return jobCollection;

	}

	public BuildFailurePatternForProjectInJenkinsModel patternSettings(String baseUrl, String user, String pass) {
		@SuppressWarnings("resource")
		String newBaseUrl = baseUrl.replace("/api/json", "/logText/progressiveText/api/json");
		ctx = DataConfig.getContext();
		buildFailurePatternForProjectRepo = ctx.getBean(BuildFailurePatternForProjectRepo.class);
		List<BuildFailurePatternForProjectInJenkinsModel> patternList = (List<BuildFailurePatternForProjectInJenkinsModel>) buildFailurePatternForProjectRepo
				.findByProjectName(projectName);
		BuildFailurePatternForProjectInJenkinsModel patternToSave = null;
		if (!patternList.isEmpty()) {
			patternToSave = patternList.get(patternList.size() - 1);
			ResponseEntity<String> response = null;
			try {
				response = makeRestCall(newBaseUrl, user, pass);
			} catch (JenkinsCollectorException e) {
				LOGGER.error(e);
			}
			if (response != null) {
				StringBuilder responseText = new StringBuilder(response.getBody());
				patternToSave.setTimestampOfCreation(System.currentTimeMillis());
				for (BuildFailurePatternMetrics pattern : patternToSave.getPatternMetrics()) {
					try {
						if ((responseText.toString().indexOf(pattern.getPatternDisplayed()) > 0)) {
							String responseStringText = responseText.toString().substring(
									responseText.toString().indexOf(pattern.getPatternDisplayed()/* .toUpperCase() */));
							if (!responseStringText.isEmpty())
								pattern.setPatternCount(pattern.getPatternCount() + 1);
							buildFailurePatternForProjectRepo.save(patternToSave);
						}

					} catch (StringIndexOutOfBoundsException exception) {
						LOGGER.error(exception);
						continue;
					}

				}
			}

		}

		return patternToSave;
	}
	
	public static List<String> matchFinder(String patternRegex, String value) {
		Pattern pattern = Pattern.compile(patternRegex);
		Matcher matcher = pattern.matcher(value);

		List<String> listMatches = new ArrayList<String>();
		while (matcher.find()) {
			listMatches.add(matcher.group());
		}
		/*
		 * for (String match : listMatches) { LOGGER.info(match); }
		 */
		return listMatches;

	}
}
