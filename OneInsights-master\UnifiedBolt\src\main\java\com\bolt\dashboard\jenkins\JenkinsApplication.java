/**
 * 
 */
package com.bolt.dashboard.jenkins;

import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.atlassian.jira.rest.client.api.RestClientException;
import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.repository.BuildToolRep;

/**
 * <AUTHOR> C:\Program Files\MongoDB\Server\3.2\bin
 */
public class JenkinsApplication {


	private static final Logger LOGGER = LogManager.getLogger(JenkinsApplication.class);
	AnnotationConfigApplicationContext applicationContext = null;
	BuildToolRep repo = null;
	BuildMetricsClient buildToolMetrics = null;
	String result = "SUCCESS";
	String buildType = "JENKINS";

	/**
	 * Private Constructor
	 */
	
//	public static void main(String[] args) {
//		new JenkinsApplication().jenkinsMain("Network Personalization ART");
//	}
	
	public JenkinsApplication() {

	}

	public void jenkinsMain(String projectName) throws RestClientException {
		LOGGER.info("Jenkins Collector started for " + projectName);
		applicationContext = DataConfig.getContext();
		repo = applicationContext.getBean(BuildToolRep.class);

		buildToolMetrics = new BuildMetricsClientImplementation();
		try {
			buildToolMetrics.getBuildTool(repo, projectName);
			ConstantVariable.getLastRun(projectName, buildType, new Date().getTime(), result);
		} catch (Exception e) {
			result = "FAIL";
			ConstantVariable.getLastRun(projectName, buildType, new Date().getTime(), result);
			cleanObject();
			LOGGER.error(e.getMessage());
			LOGGER.error("Exception ", e.fillInStackTrace());

			LOGGER.info("Jenkins Collector failed for " + projectName);
		}
		cleanObject();
		LOGGER.info("Jenkins Collector ended for " + projectName);
	}

	public void cleanObject() {
		repo = null;
		buildToolMetrics = null;
	}
}