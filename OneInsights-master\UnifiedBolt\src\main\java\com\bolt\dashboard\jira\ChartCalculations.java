package com.bolt.dashboard.jira;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.config.MongoAggregate;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.ComponentSprintWiseStories;
import com.bolt.dashboard.core.model.ComponentVelocityList;
import com.bolt.dashboard.core.model.IssueList;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.ScoreCardSprintData;
import com.bolt.dashboard.core.model.TransitionModel;
import com.bolt.dashboard.core.model.VelocityList;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.ChangeHisortyRepo;
import com.bolt.dashboard.core.repository.EffortHistoryRepo;
import com.bolt.dashboard.core.repository.IterationRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.ProjectIterationRepo;
import com.bolt.dashboard.core.repository.ProjectRepo;
import com.bolt.dashboard.core.repository.ReleaseRepo;
import com.bolt.dashboard.core.repository.TransitionRepo;
import com.bolt.dashboard.util.VelocityCalculations;



public class ChartCalculations {
	List<IterationOutModel> workingBacklog;
	List<IterationOutModel> workingSprints;
	List<String> velocityFields;
	List<String> closestates;
	List<String> taskNames;
	
	VelocityList vlist;
	ArrayList<IssueList> issueList;
	List<String> wIdArr;
	double tempSpRefined = 0;
	double tempSpRemoved = 0;
	double finalStoriesCommited = 0;
	double storiesCompleted = 0;
	double defetcsCompleted = 0;
	ArrayList<IssueList> refinedIssuList;
	ArrayList<IssueList> removedIssuList;
	
	private ALMConfiguration almConfig;
	private TransitionRepo transitionRepo;
	private EffortHistoryRepo effortHistoryRepo;
	private ChangeHisortyRepo changeHisortyRepo;
	private ProjectIterationRepo authorRepo;
	private ProjectRepo projectRepo;
	private ReleaseRepo releaseRepo;
	private IterationRepo iterationRepo;
	private MetricRepo metricRepo;
	private ALMConfigRepo almConfigRepo;
	private MongoAggregate agg = null;
	MongoTemplate mongoTemplate = null;
	AnnotationConfigApplicationContext ctx = null;
	
	public ChartCalculations(
			
			TransitionRepo transitionRepo,
			ProjectIterationRepo authorRepo,
		    IterationRepo iterationRepo,
			MetricRepo metricRepo,
			ALMConfigRepo almConfigRepo){
		this.transitionRepo = transitionRepo;
		this.authorRepo = authorRepo;
		this.iterationRepo = iterationRepo;
		this.metricRepo = metricRepo;
		this.almConfigRepo = almConfigRepo;
		
	}
	
	public ChartCalculations(){
		ctx = DataConfig.getContext();
		
		this.transitionRepo = ctx.getBean(TransitionRepo.class);
		this.authorRepo = ctx.getBean(ProjectIterationRepo.class);
		this.iterationRepo= ctx.getBean(IterationRepo.class);
		this.metricRepo= ctx.getBean(MetricRepo.class);
		this.almConfigRepo= ctx.getBean(ALMConfigRepo.class);
	}
	
	
	
	
	public List<ComponentVelocityList> getComponentVelocity(String projectName, boolean flag) {
		
		List<ComponentVelocityList> response = new ArrayList<ComponentVelocityList>();
		List<IterationOutModel> authorList = authorRepo.findByPName(projectName);
		Collections.sort(authorList, new SprintComparatort());

		List<ScoreCardSprintData> v = getVelocityChart(authorList, projectName);
		ComponentVelocityList c = new ComponentVelocityList();
		c.setComponent("All");
		c.setVelocityList(v);
		response.add(c);
		if (flag) {
			ArrayList<String> componentList = getComponentList(authorList);
			Collections.sort(componentList);

			for (String component : componentList) {
				List<IterationOutModel> tempAuthorList = new ArrayList<IterationOutModel>();
				for (IterationOutModel auth : authorList) {

					IterationOutModel author = new IterationOutModel();
					BeanUtils.copyProperties(auth, author);

					List<MonogOutMetrics> metrics = new ArrayList<MonogOutMetrics>();
					if (auth.getMetrics() != null)
						for (MonogOutMetrics m : auth.getMetrics()) {
							if (m.getComponents() != null && m.getComponents().get(0) != null
									&& m.getComponents().get(0).equals(component)) {

								metrics.add(m);

							}
						}
					if (metrics.size() > 0) {
						author.setMetrics(metrics);
						tempAuthorList.add(author);
					}

				}
				Collections.sort(tempAuthorList, new SprintComparatort());
				v = getVelocityChart(tempAuthorList, projectName);
				c = new ComponentVelocityList();
				c.setComponent(component);
				c.setVelocityList(v);
				response.add(c);

			}
		}
		return response;
	}

//	public static void main(String[] args) {
//	new ChartCalculations().getComponentsSprintStories("SP 1");
//}
	
	@SuppressWarnings("unchecked")
	public List<ComponentSprintWiseStories> getComponentsSprintStories(String projectName) {
		List<ComponentSprintWiseStories> response = new ArrayList<ComponentSprintWiseStories>();

		List<MetricsModel> metricList = metricRepo.findByPNameAndType(projectName, "Story");
		List<TransitionModel> transitionList = (List<TransitionModel>) transitionRepo.findAll();
		List<IterationOutModel> authorList = authorRepo.findByPName(projectName);
		Collections.sort(authorList, new SprintComparatort());

		List<IterationOutModel> itr = getSprintWiseStories(projectName, metricList, transitionList, authorList);
		ComponentSprintWiseStories c = new ComponentSprintWiseStories();
		c.setComponent("All");
		c.setIterationOutModel(itr);
		response.add(c);
		ArrayList<String> componentLists = new ArrayList<String>();
		componentLists.addAll(getComponents(projectName));
		Collections.sort(componentLists);

		boolean cflag = (componentLists.size() > 1) ? true : false;
		if (cflag) {
			ArrayList<String> componentList = getComponentList(authorList);
			Collections.sort(componentList);

			for (String component : componentList) {
				List<IterationOutModel> tempAuthorList = new ArrayList<IterationOutModel>();
				List<MetricsModel> allMetricsOfComponet = new ArrayList<MetricsModel>();
				for (IterationOutModel auth : authorList) {

					IterationOutModel author = new IterationOutModel();
					BeanUtils.copyProperties(auth, author);

					List<MonogOutMetrics> metrics = new ArrayList<MonogOutMetrics>();
					if(auth.getMetrics()!=null)
					for (MonogOutMetrics m : auth.getMetrics()) {
						if (m.getComponents() != null && m.getComponents().get(0) != null
								&& m.getComponents().get(0).equals(component)) {

							metrics.add(m);
							MetricsModel temp = new MetricsModel();
							BeanUtils.copyProperties(m, temp);
							allMetricsOfComponet.add(temp);

						}
					}
					if (metrics.size() > 0) {
						author.setMetrics(metrics);
						tempAuthorList.add(author);
					}

				}
				Collections.sort(tempAuthorList, new SprintComparatort());
				// if(component.equals("NP-01 Service Transparency-Consumer")) {
				// System.out.println("sdsdsd");
				// }
				itr = getSprintWiseStories(projectName, allMetricsOfComponet, transitionList, tempAuthorList);
				c = new ComponentSprintWiseStories();
				c.setComponent(component);
				c.setIterationOutModel(itr);
				response.add(c);

			}
		}
		return response;
	}

	
	public Map<String, List> getIssueHierarchy(String projectName) {
		

		List<MetricsModel> allMetrics = metricRepo.findByPName(projectName);
		List<IterationOutModel> authorList = authorRepo.findByPName(projectName);
		ArrayList<String> componentList = getComponentList(authorList);

		Map<String, List> resp = new IssueHierarchy().getHierarchyData(allMetrics);
		return resp;
	}

	public ArrayList<String> getComponentList(List<IterationOutModel> authorList) {

		ArrayList<String> componentList = new ArrayList<String>();
		for (IterationOutModel auth : authorList) {
	
			if (auth.getMetrics() != null) {
				for (MonogOutMetrics m : auth.getMetrics()) {
					if (m.getComponents() != null && m.getComponents().get(0) != null) {
						String component = m.getComponents().get(0);
						if (componentList.indexOf(component) < 0) {
							componentList.add(component);
						}
					}
				}
			}
		}
		return componentList;
	}

	
	public Map<String, Map> getComponentWiseIssueHierarchy(String projectName) {

		List<IterationOutModel> authorList = authorRepo.findByPName(projectName);
		List<MetricsModel> metrics = metricRepo.findByPName(projectName);
		ArrayList<String> componentList = getComponentList(authorList);

		Map<String, Map> response = new HashMap<String, Map>();

		Map<String, List> resp = new IssueHierarchy().getHierarchyData(metrics);
		response.put("All", resp);

		componentList.forEach(component -> {
			List<MetricsModel> filteredMetrics = metrics.stream()
					.filter(m ->{
						if(m!=null && m.getComponents() !=null && m.getComponents().size() > 0 && m.getComponents().get(0).equalsIgnoreCase(component)) {
							return true;
						}
						else {
							return false;
						}
					}).collect(Collectors.toList());
			Map<String, List> resp1= new IssueHierarchy().getHierarchyData(filteredMetrics);
			response.put(component, resp1);

		});

		return response;
	}

	
	public List<String> getComponents(String projectName) {
		System.out.println("in getComponents --------------------------------"+projectName);
		List<IterationOutModel> authorList = authorRepo.findByPName(projectName);
		
		List<String> copmonents = getComponentList(authorList);
		Collections.sort(copmonents);
		return copmonents;
	}

	public List<IterationOutModel> getSprintWiseStories(String projectName, List<MetricsModel> metricList,
			List<TransitionModel> transitionList, List<IterationOutModel> sprints) {

		almConfig = almConfigRepo.findByProjectName(projectName).get(0);
		List<String> closestates = Arrays.asList(almConfig.getCloseState());

		Map<String, List<TransitionModel>> transitionGrouped = new HashMap<>();

		for (TransitionModel p : transitionList) {
			if (!transitionGrouped.containsKey(p.getwId())) {
				transitionGrouped.put(p.getwId(), new ArrayList<>());
			}
			transitionGrouped.get(p.getwId()).add(p);
		}

		Iterator<MetricsModel> metricitr = metricList.iterator();
		List<MonogOutMetrics> aggregatedMetricList = new ArrayList<MonogOutMetrics>();
		while (metricitr.hasNext()) {
			MetricsModel m = metricitr.next();
			MonogOutMetrics agr = new MonogOutMetrics();
			BeanUtils.copyProperties(m, agr);
			agr.setTransitions(transitionGrouped.get(m.getwId()));
			aggregatedMetricList.add(agr);

		}

		List<IterationOutModel> sprintwise = new ArrayList<IterationOutModel>();

		ListIterator<IterationOutModel> spIterator = sprints.listIterator();
		ArrayList<String> wIdArr = new ArrayList<String>();
		while (spIterator.hasNext()) {

			int tempStory = 0;
			IterationOutModel sprint = spIterator.next();
			IterationOutModel sprintAndstories = new IterationOutModel();
			List<MonogOutMetrics> spStories = new ArrayList<MonogOutMetrics>();
			if (null != sprint.getStDate() && sprint.getStDate() != 0 && sprint.getsId() != 0) {

				sprintAndstories.setsName(sprint.getsName());
				sprintAndstories.setsId(sprint.getsId());
				sprintAndstories.setStDate(sprint.getStDate());

				if (0 != sprint.getCompletedDate()) {
					sprintAndstories.setEndDate(sprint.getCompletedDate());
				} else {
					sprintAndstories.setEndDate(sprint.getEndDate());
				}

				// if (sprint.getsName().equalsIgnoreCase("NP PI-2.1")) {
				// System.out.println("dsfsdfsd");
				// }
				Iterator<MonogOutMetrics> stIterator = aggregatedMetricList.iterator();
				while (stIterator.hasNext()) {
					MonogOutMetrics story = stIterator.next();

					long endDate;
					if (sprint.getState().toLowerCase().contains("active")) {
						endDate = new Date().getTime();
					} else {

						endDate = sprint.getEndDate();

					}
					// if (story.getwId().equalsIgnoreCase("NTSBW-875")) {
					// System.out.println(story.getwId());
					// }
					if (null != story.getAllocatedDate()
							&& story.getType().toLowerCase().equals(almConfig.getStoryName().toLowerCase())) {
						ArrayList<Long> dates = new ArrayList<Long>(story.getAllocatedDate().keySet());
						ArrayList<String> values = new ArrayList<String>(story.getAllocatedDate().values());
						for (int m = dates.size() - 1; m >= 0; m--) {
							if (values.get(m).contains(sprint.getsId() + "")) {
								if (m == dates.size() - 1 && dates.get(m) < sprint.getStDate()) {
									MonogOutMetrics s = new MonogOutMetrics();
									BeanUtils.copyProperties(story, s);
									spStories.add(s);
								} else if (dates.get(m) < sprint.getStDate() && dates.get(m + 1) > sprint.getStDate()
										&& dates.get(m + 1) > endDate) {
									MonogOutMetrics s = new MonogOutMetrics();
									spStories.add(s);
								}
							}
						}

						long completedDate;
						if (sprint.getState().toLowerCase().contains("active")) {
							completedDate = new Date().getTime();
						} else {
							if (sprint.getCompletedDate() != 0) {
								completedDate = sprint.getCompletedDate();
							} else {
								completedDate = sprint.getEndDate();
							}

						}

						if (null != story.getAllocatedDate() && wIdArr.indexOf(story.getwId()) <= -1) {

							for (int i = 0; i < dates.size(); i++) {
								List<String> dateMapValues = Arrays.asList(values.get(i).split(","));
								if (i == dates.size() - 1) {
									if (dates.get(i) < completedDate
											&& dateMapValues.indexOf(sprint.getsId() + "") > -1) {
										String wid = story.getwId();
										List<TransitionModel> tr = story.getTransitions();
										if (tr != null && tr.size() > 0) {
											Collections.sort(tr, new TransitionComparator());
										}
										TransitionModel trans = filterTrans(story.getTransitions(), completedDate);
										if (closestates.indexOf(trans.getCrState()) > -1
												&& trans.getMdfDate() < completedDate
												&& trans.getMdfDate() > sprint.getStDate()
												&& wIdArr.indexOf(story.getwId()) <= -1) {
											tempStory++;
											wIdArr.add(story.getwId());
											// if(sprint.getsId() == 351) {
											// System.out.println(story.getwId());
											// }

										}
									}
								} else {
									if (dates.get(i) < completedDate && dates.get(i + 1) > completedDate
											&& dateMapValues.indexOf(String.valueOf(sprint.getsId())) > -1) {
										String wid = story.getwId();
										List<TransitionModel> tr = story.getTransitions();
										if (tr != null && tr.size() > 0) {
											Collections.sort(tr, new TransitionComparator());
										}

										TransitionModel trans = filterTrans(story.getTransitions(), completedDate);
										if (closestates.indexOf(trans.getCrState()) > -1
												&& trans.getMdfDate() < completedDate
												&& trans.getMdfDate() > sprint.getStDate()
												&& wIdArr.indexOf(story.getwId()) <= -1) {
											tempStory++;
											wIdArr.add(story.getwId());

										}
									}
								}

							}
						}
					}

				}

			}
			sprintAndstories.setMetrics(spStories);
			sprintAndstories.setClosedStories(tempStory);
			sprintwise.add(sprintAndstories);

		}
		return sprintwise;
	}

	TransitionModel filterTrans(List<TransitionModel> tr, long date) {
		List<TransitionModel> newTr = new ArrayList<TransitionModel>();
		if (null != tr) {
			for (int i = 0; i < tr.size(); i++) {
				if (tr.get(i).getMdfDate() < date) {
					newTr.add(tr.get(i));
				}
			}
		}

		if (newTr.size() > 0) {
			return newTr.get(newTr.size() - 1);
		} else {
			return new TransitionModel();
		}
	}

	public List<ScoreCardSprintData> getVelocityChart(List<IterationOutModel> authorList, String projectName) {
		
		List<ALMConfiguration> almConfigurations =almConfigRepo.findByProjectName(projectName);
		VelocityCalculations velocity = new VelocityCalculations();
		return velocity.calcVelocity(authorList, almConfigurations.get(0));

	}

	public double callSP(String flag, IterationOutModel sp) {
		double tempSp = 0;
		tempSpRefined = 0;
		tempSpRemoved = 0;
		issueList = new ArrayList<IssueList>();

		long date;
		if (flag.equals("start")) {
			date = sp.getStDate();
		} else {
			if (sp.getState().toLowerCase().contains("active")) {
				date = new Date().getTime();
			} else {
				if( sp.getCompletedDate()!=0 ) {
					date = sp.getCompletedDate();
				}else {
					date = sp.getEndDate();
				}

				

			}

		}
//		 if (sp.getsName().equals("WE PI-1.4") ) {
//		 System.out.println("dsfsdfsd");
//		 }

		for (IterationOutModel it : workingBacklog) {

			ArrayList<String> keys, dateMapValues;
			ArrayList<Long> dateMapKeys, storyPointMapKeys;
			Map storyPointMap;
			Map<Long, String> dateMap;
			if(it.getMetrics()!=null)
			for (MonogOutMetrics ele : it.getMetrics()) {
				
				
				if (ele.getAllocatedDate() != null && (velocityFields.indexOf(ele.getType()) > -1)
						&& ele.getStoryPoints() != null && (vlist.workItemArr.indexOf(ele.getwId()) < 0)) {
					
					dateMap = ele.getAllocatedDate();
					dateMapKeys = new ArrayList<Long>(ele.getAllocatedDate().keySet());
					Collections.sort(dateMapKeys);
					
					storyPointMapKeys = new ArrayList<Long>(ele.getStoryPoints().keySet());
					storyPointMap = ele.getStoryPoints();
					Collections.sort(storyPointMapKeys);
					for (int i = 0; i < dateMapKeys.size(); i++) {
						List<String> dateValues = Arrays.asList(dateMap.get(dateMapKeys.get(i)).split(","));
						if (i == dateMapKeys.size() - 1 && dateMapKeys.get(i) < date
								&& (dateValues.contains(String.valueOf(sp.getsId())))) {
							tempSp = tempSp
									+ storyLoop(storyPointMapKeys, sp, storyPointMap, issueList, ele, date, false);
							storyLoopRefined(true, storyPointMapKeys, sp, storyPointMap, issueList, ele, date);
						} else if ( (dateMapKeys.get(i) < date) && (dateValues.contains(String.valueOf(sp.getsId())))
								&& (dateMapKeys.get(i + 1) > date)) {
							tempSp = tempSp
									+ storyLoop(storyPointMapKeys, sp, storyPointMap, issueList, ele, date, false);
							
							List<String> dateValues1 = Arrays.asList(dateMap.get(dateMapKeys.get(i+1)).split(","));
							
							 if      (dateValues1.contains(String.valueOf(sp.getsId()))) {
								   storyLoopRefined(true, storyPointMapKeys, sp, storyPointMap, issueList, ele, date);       
							 }else {
								  storyLoopRefined(false, storyPointMapKeys, sp, storyPointMap, issueList, ele, date);
							 }
			                       
			                                          
			                              
						}

					}
				}

			}

		}
		return tempSp;
	}

	public double calcClosedSP(IterationOutModel sp) {
		double tempSp = 0;
		issueList = new ArrayList<IssueList>();

		long date;

		if (sp.getState().toLowerCase().contains("active")) {
			date = new Date().getTime();
		} else {
			if (sp.getCompletedDate() != 0) {
				date = sp.getCompletedDate();
			} else {
				date = sp.getEndDate();
			}
		}
//		 if (sp.getsName().equals("WE PI-1.5") ) {
//			 System.out.println("closed - dsfsdfsd");
//			 }


		wIdArr = new ArrayList<String>();
		for (IterationOutModel it : workingBacklog) {

			ArrayList<String> keys;
			ArrayList<Long> dateMapKeys, storyPointMapKeys;
			Map storyPointMap;
			Map<Long, String> dateMap;
			if(it.getMetrics()!=null)
			for (MonogOutMetrics ele : it.getMetrics()) {
			
				if (ele.getsId() != 0) {
//					 if(ele.getwId().equals("NTSCC-474")) {
//					 System.out.println("Closed--dsdsd");
//					 }
					if (ele.getAllocatedDate() != null && (velocityFields.indexOf(ele.getType()) > -1)
							&& ele.getStoryPoints() != null) {
						
						dateMap = ele.getAllocatedDate();
						dateMapKeys = new ArrayList<Long>(ele.getAllocatedDate().keySet());
						Collections.sort(dateMapKeys);

						storyPointMapKeys = new ArrayList<Long>(ele.getStoryPoints().keySet());
						storyPointMap = ele.getStoryPoints();

						Collections.sort(storyPointMapKeys);

						for (int i = 0; i < dateMapKeys.size(); i++) {
							List<String> dateValues = Arrays.asList(dateMap.get(dateMapKeys.get(i)).split(","));
							if (i == dateMapKeys.size() - 1 && dateMapKeys.get(i) < date
									&& dateValues.contains(String.valueOf(sp.getsId()))) {
								String wid = ele.getwId();
								TransitionModel trans = filterTrans(ele.getTransitions(), date);
//								if (closestates.indexOf(trans.getCrState()) > -1 && trans.getMdfDate() < date
//										&& trans.getMdfDate() > sp.getStDate()
//										&& trans.getMdfDate() > dateMapKeys.get(i)
//										&& wIdArr.indexOf(ele.getwId()) <= -1) {
								if (closestates.indexOf(trans.getCrState()) > -1 && trans.getMdfDate() < date
										&& trans.getMdfDate() > sp.getStDate()
										&& wIdArr.indexOf(ele.getwId()) <= -1) {
									wIdArr.add(ele.getwId());
									tempSp = tempSp + storyLoop(storyPointMapKeys, sp, storyPointMap, issueList, ele,
											date, true);
								}

							} else if (dateMapKeys.get(i) < date && dateValues.indexOf(sp.getsId() + "") > -1
									&& dateMapKeys.get(i + 1) > date) {
								String wid = ele.getwId();
								TransitionModel trans = filterTrans(ele.getTransitions(), date);
//								if (closestates.indexOf(trans.getCrState()) > -1 && trans.getMdfDate() < date
//										&& trans.getMdfDate() > sp.getStDate()
//										&& trans.getMdfDate() > dateMapKeys.get(i)
//										&& wIdArr.indexOf(ele.getwId()) <= -1) {
								if (closestates.indexOf(trans.getCrState()) > -1 && trans.getMdfDate() < date
										&& trans.getMdfDate() > sp.getStDate()
										&& wIdArr.indexOf(ele.getwId()) <= -1) {

									wIdArr.add(ele.getwId());
									tempSp = tempSp + storyLoop(storyPointMapKeys, sp, storyPointMap, issueList, ele,
											date, true);
								}

							}

						}
					}
				}
			}
		}
		return tempSp;
	}

	double storyLoop(List<Long> keys, IterationOutModel it, Map story, List<IssueList> issueList, MonogOutMetrics m,
			long date, boolean colsedflag) {
		double temp = 0;
		for (int i = 0; i < keys.size(); i++) {
			if (keys.get(i) <= date && i == keys.size() - 1) {
				temp = temp + (double) story.get(keys.get(i));
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getStoryName()) && colsedflag) {
					storiesCompleted++;
				}
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getDefectName()) && colsedflag) {
					defetcsCompleted++;
				}
				IssueList iss = new IssueList();
				iss.setStoryPoints((double) story.get(keys.get(i)));
				iss.setwId(m.getwId());
				iss.setAssignee(m.getAssgnTo());
				iss.setState(m.getState());
				iss.setType(m.getType());
				iss.setSortId(m.getwId().split("-")[1]);
				issueList.add(iss);
				vlist.workItemArr.add(m.getwId());
			} else if (keys.get(i) <= date && keys.get(i + 1) > date) {
				temp = temp + (double) story.get(keys.get(i));
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getStoryName()) && colsedflag) {
					storiesCompleted++;
				}
				if (m.getType() != null && m.getType().equalsIgnoreCase(almConfig.getDefectName()) && colsedflag) {
					defetcsCompleted++;
				}
				IssueList iss = new IssueList();
				iss.setStoryPoints((double) story.get(keys.get(i)));
				iss.setwId(m.getwId());
				iss.setAssignee(m.getAssgnTo());
				iss.setState(m.getState());
				iss.setType(m.getType());
				iss.setSortId(m.getwId().split("-")[1]);
				issueList.add(iss);
				vlist.workItemArr.add(m.getwId());
			}
		}
		return temp;
	}

	double storyLoopRefined(boolean isRefined, List<Long> keys, IterationOutModel it, Map story,
			List<IssueList> issueList, MonogOutMetrics m, long date) {
		double temp = 0;

		List<TransitionModel> tr = m.getTransitions();
		if (tr != null) {
			Collections.sort(tr, new TransitionComparator());
		}

		for (int i = 0; i < keys.size(); i++) {
			if (keys.get(i) <= date && i == keys.size() - 1) {

				if (tr != null && tr.size() > 0) {

					if (!tr.get(tr.size() - 1).getCrState().toUpperCase().equals("WITHDRAWN") && isRefined) {
						tempSpRefined += (double) story.get(keys.get(i));
						if (m.getType().equals(almConfig.getStoryName())) {
							finalStoriesCommited++;
						}

						IssueList issue = new IssueList();
						issue.setStoryPoints((double) story.get(keys.get(i)));
						issue.setwId(m.getwId());
						issue.setAssignee(m.getAssgnTo());
						issue.setState(m.getState());
						issue.setType(m.getType());
						issue.setSortId(m.getwId().split("-")[1]);
						refinedIssuList.add(issue);
						vlist.workItemArr.add(m.getwId());
					} else {
						tempSpRemoved += (double) story.get(keys.get(i));
						if (m.getType().equals(almConfig.getStoryName())) {
							finalStoriesCommited++;
						}
						IssueList issue = new IssueList();
						issue.setStoryPoints((double) story.get(keys.get(i)));
						issue.setwId(m.getwId());
						issue.setAssignee(m.getAssgnTo());
						issue.setState(m.getState());
						issue.setType(m.getType());
						issue.setSortId(m.getwId().split("-")[1]);
						removedIssuList.add(issue);
						vlist.workItemArr.add(m.getwId());
					}
				} else if (m.getsName() != null && vlist.activeSprint != null
						&& vlist.activeSprint.equals(m.getsName())) {
					tempSpRefined += (double) story.get(keys.get(i));
					if (m.getType().equals(almConfig.getStoryName())) {
						finalStoriesCommited++;
					}
					IssueList issue = new IssueList();
					issue.setStoryPoints((double) story.get(keys.get(i)));
					issue.setwId(m.getwId());
					issue.setAssignee(m.getAssgnTo());
					issue.setState(m.getState());
					issue.setType(m.getType());
					issue.setSortId(m.getwId().split("-")[1]);
					refinedIssuList.add(issue);
					vlist.workItemArr.add(m.getwId());
				} else {
					tempSpRemoved += (double) story.get(keys.get(i));
					IssueList issue = new IssueList();
					issue.setStoryPoints((double) story.get(keys.get(i)));
					issue.setwId(m.getwId());
					issue.setAssignee(m.getAssgnTo());
					issue.setState(m.getState());
					issue.setType(m.getType());
					issue.setSortId(m.getwId().split("-")[1]);
					removedIssuList.add(issue);
					vlist.workItemArr.add(m.getwId());
				}
			} else if (keys.get(i) <= date && keys.get(i + 1) > date) {
				if (tr != null && tr.size() > 0) {

					if (!tr.get(tr.size() - 1).getCrState().toUpperCase().equals("WITHDRAWN")
							&& !m.getState().toUpperCase().equals("WITHDRAWN") && isRefined) {
						tempSpRefined += (double) story.get(keys.get(i));
						if (m.getType().equals(almConfig.getStoryName())) {
							finalStoriesCommited++;
						}
						IssueList issue = new IssueList();
						issue.setStoryPoints((double) story.get(keys.get(i)));
						issue.setwId(m.getwId());
						issue.setAssignee(m.getAssgnTo());
						issue.setState(m.getState());
						issue.setType(m.getType());
						issue.setSortId(m.getwId().split("-")[1]);
						refinedIssuList.add(issue);
						vlist.workItemArr.add(m.getwId());
					} else {
						tempSpRemoved += (double) story.get(keys.get(i));
						IssueList issue = new IssueList();
						issue.setStoryPoints((double) story.get(keys.get(i)));
						issue.setwId(m.getwId());
						issue.setAssignee(m.getAssgnTo());
						issue.setState(m.getState());
						issue.setType(m.getType());
						issue.setSortId(m.getwId().split("-")[1]);
						removedIssuList.add(issue);
						vlist.workItemArr.add(m.getwId());
					}
				} else if (vlist.activeSprint!=null && vlist.activeSprint.equals(m.getsName())) {
					tempSpRefined += (double) story.get(keys.get(i));
					if (m.getType().equals(almConfig.getStoryName())) {
						finalStoriesCommited++;
					}
					IssueList issue = new IssueList();
					issue.setStoryPoints((double) story.get(keys.get(i)));
					issue.setwId(m.getwId());
					issue.setAssignee(m.getAssgnTo());
					issue.setState(m.getState());
					issue.setType(m.getType());
					issue.setSortId(m.getwId().split("-")[1]);
					refinedIssuList.add(issue);
					vlist.workItemArr.add(m.getwId());
				} else {
					tempSpRemoved += (double) story.get(keys.get(i));
					IssueList issue = new IssueList();
					issue.setStoryPoints((double) story.get(keys.get(i)));
					issue.setwId(m.getwId());
					issue.setAssignee(m.getAssgnTo());
					issue.setState(m.getState());
					issue.setType(m.getType());
					issue.setSortId(m.getwId().split("-")[1]);
					removedIssuList.add(issue);
					vlist.workItemArr.add(m.getwId());
				}
			}
		}

		return temp;
	}

	class TransitionComparator implements Comparator {

		@Override
		public int compare(Object o1, Object o2) {
			TransitionModel t1 = (TransitionModel) o1;
			TransitionModel t2 = (TransitionModel) o2;

			if (t1.getMdfDate() != null && t2.getMdfDate() != null) {
				if (t1.getMdfDate().equals(t2.getMdfDate()))
					return 0;
				else if (t1.getMdfDate() > t2.getMdfDate())
					return 1;
				else
					return -1;
			} else if (t1.getMdfDate() == null) {
				return 1;
			} else {
				return -1;
			}

		}

	}

	class SprintComparatort implements Comparator {

		@Override
		public int compare(Object o1, Object o2) {
			IterationOutModel s1 = (IterationOutModel) o1;
			IterationOutModel s2 = (IterationOutModel) o2;

			if (s1.getStDate() != null && s2.getStDate() != null) {
				if (s1.getStDate().equals(s2.getStDate()))
					return 0;
				else if (s1.getStDate() > s2.getStDate())
					return 1;
				else
					return -1;
			} else if (s1.getStDate() == null) {
				return 1;
			} else {
				return -1;
			}

		}

	}


}
