package com.bolt.dashboard.jira;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;

import com.bolt.dashboard.core.model.IterationModel;

/**
 * <AUTHOR>
 *
 */
public class IterationInfo {
    static int sprintId;
    IterationModel iteration = null;
    String sprintName = "";
    Set<IterationModel> iterationSet = null;
    
    private static final Logger LOGGER = LogManager.getLogger(IterationInfo.class);

    /*
     * Storing sprint details like sprint name ,sprint id ,start date, end date
     * ,completed date and sprint state in iterationSet
     *
     * @param json as sprint details from JIRA rest API(customfield_10007)
     *
     * @param projectName as projectName
     *
     * @param iterationSet as collection of iterationModel
     *
     * @param pAlmType as almType of project
     *
     * @param return collection of objects coinatining iterationset (collection
     * of iterationModel ) ,sprint name ,sprintId
     */
    public List<Object> populateIteration(JSONObject json, String projectName, Set<IterationModel> iterationSet,
	    String pAlmType ,String projKey, Map<String, String> customFieldsMap) {
	// all iteration related stuffs;
	boolean response = true;
	Set<String> sprintSet = new LinkedHashSet<>();
	List<Object> objectList = new ArrayList<>();
	iteration = new IterationModel();
	String tempKey=customFieldNames.JIRA_SPRINT_FIELD_BRILLIO!=null?customFieldNames.JIRA_SPRINT_FIELD_BRILLIO:customFieldsMap.get("Sprint");
	JSONArray sprintJson = (JSONArray) json.get(tempKey);
	
	String sprintStatus = "";
	sprintName = "BackLog";
	long startDate = 0;
	long endDate = 0;
	long completedDate = 0;
	int id = 0;
	
	List<Integer> multipleSprints = new ArrayList<>() ;
	if (sprintJson != null && sprintJson.size() > 0) {

	    for (int i = 0; i < sprintJson.size(); i++) {
	    	try {
	    		JSONObject sprintData = (JSONObject)sprintJson.get(i);
	    		if(sprintData.get("name").toString() != null)
	    			sprintName=(String) sprintData.get("name");
	    		if (sprintData.get("state").toString() != null)
	    			sprintStatus=(String) sprintData.get("state");
	    		if (sprintData.get("startDate")!=null &&sprintData.get("startDate").toString() != null)
	    			//startDate=(long) sprintData.get("startDate");
	    		startDate=new DateTime(sprintData.get("startDate")).getMillis();
	    		//ConstantVariable.timestamp(startDate,projectName);
	    		if (sprintData.get("endDate")!=null && sprintData.get("endDate").toString() != null)
	    			//endDate=(long)sprintData.get("endDate");
	    			endDate=new DateTime(sprintData.get("endDate")).getMillis();
	    		//ConstantVariable.timestamp(endDate,projectName);
	    		if (sprintData.get("completeDate")!=null &&sprintData.get("completeDate").toString() != null)
	    			//completedDate=(long)sprintData.get("completeDate");
	    			completedDate=new DateTime(sprintData.get("completeDate")).getMillis();
	    		//ConstantVariable.timestamp(completedDate,projectName);
	    		if (sprintData.get("id").toString() != null) {
	    			String s = ""+sprintData.get("id");
	    		
	    			id= Integer.parseInt(s);
	    		}
	    	}
	    	catch(Exception e)
	    	{
	    		LOGGER.info(e);
	    	}
		/*String stringValue = null;
		String[] separationString = sprintData.split(("\","));
			
		for (String partOfString : separationString) {
			
			LOGGER.info("JSON response :" + partOfString);
		    stringValue = getSprintInfo(partOfString);
		    if (partOfString.contains("name")) {
			sprintName = stringValue;
			sprintSet.add(sprintName);
		    }
		
		    if (partOfString.contains("state"))
			sprintStatus = stringValue;
		    if (partOfString.contains("startDate") && !("<null>".equals(stringValue)))
			startDate = ConstantVariable.timestamp(stringValue,projectName);
		    if (partOfString.contains("endDate") && !("<null>".equals(stringValue)))
			endDate = ConstantVariable.timestamp(stringValue,projectName);
		    if (partOfString.contains("completeDate") && !("<null>".equals(stringValue)))
			completedDate = ConstantVariable.timestamp(stringValue,projectName);
		    if (partOfString.contains("id") && partOfString.contains("[") && !("<null>".equals(stringValue)))
			id = Integer.parseInt(stringValue);
		    if (partOfString.contains("id") && !("<null>".equals(stringValue)))
		    id= Integer.parseInt(stringValue);
		}*/
		iteration.setEndDate(endDate);
		iteration.setsName(sprintName);
	  
		iteration.setStDate(startDate);
		iteration.setProjKey(projKey);
		iteration.setpAlmType(pAlmType);
		sprintId = id;
		iteration.setsId(id);
		//multipleSprints.add(id);
		iteration.setCompletedDate(completedDate);

	    }

	}
	iteration.setsName(sprintName);
	iteration.setState(sprintStatus);
	iteration.setpName(projectName);
	iteration.setpAlmType(pAlmType);
	Iterator<IterationModel> modelIterator = iterationSet.iterator();
	while (modelIterator.hasNext()) {
	    IterationModel iter = modelIterator.next();
	    if (iter.getsId()==(iteration.getsId())) {
		response = false;
	    }
	}
	if (response)
	    iterationSet.add(iteration);
	objectList.add(iterationSet);
	objectList.add(sprintName);
	objectList.add(sprintId);
	objectList.add(sprintSet);
	objectList.add(multipleSprints);
	return objectList;
    }

    /**
     * splits the string value and returns the last index value
     */
    public String getSprintInfo(String partOfString) {
	String value = null;
	String[] separationString1 = partOfString.split(Pattern.quote("="));
	//String[] separationString2 =
	//String[] separationString2 = partOfString.split(Pattern.quote(":"));
	
	if( separationString1.length!= 2)
	{
		 separationString1 = partOfString.split("\":");
		 
	}
	
	
	if (separationString1.length == 2) {
	    value = separationString1[1];
	}
	if (value != null) {
		if (value.contains("\"")) {
			value = value.substring(0, value.length() - 2);
		}
		value = value.replace("\"", "");
	}
	
	return value;
    }
    
   
}
