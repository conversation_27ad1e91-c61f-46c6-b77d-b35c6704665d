package com.bolt.dashboard.jira;

import java.util.Date;
import java.util.Iterator;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

/**
 * <AUTHOR>
 *
 */
public class JIRAApplication {
	private static final Logger LOGGER = LogManager.getLogger(JIRAApplication.class);
	AnnotationConfigApplicationContext ctx;
	JIRAClient almClientMetrics = null;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configurationColection = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	ConfigurationToolInfoMetric metric1 = null;

	/**
	 * Private constructor
	 */
//	
//	 public static void main(String[] args) { new
//	  JIRAApplication().jiraMain("BrillioOne");
//	 }
	 
	public JIRAApplication() {

	}

	public void jiraMain(String projectName) {

		LOGGER.info("Jira Collector started for" + projectName);

		ctx = DataConfig.getContext();
		String instanceURL = "";
		almClientMetrics = new ALMClientImplementation();
		String user = null;
		String pass = null;
		String key = null;
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);

		// collecting url, credentials and project key from configuration

		configurationColection = configurationRepo.findByProjectName(projectName).get(0);
		if (configurationColection != null) {
			metric = configurationColection.getMetrics();
			Iterator iter = metric.iterator();
			String toolName = null;

			while (iter.hasNext()) {
				toolName = "";
				Object configuration1 = iter.next();
				metric1 = (ConfigurationToolInfoMetric) configuration1;
				if ("Jira Defects".equals(metric1.getToolName()) && !(metric1.getUrl().equals(""))) {
					toolName = "JIRA DEFECTS";
				} else if ("Jira".equals(metric1.getToolName())) {
					toolName = "JIRA";
				} else if ("JIRA-KANBAN".equals(metric1.getToolName())) {
					toolName = "JIRA-KANBAN";
				}
				if (!(metric1.getUrl().toString().equals("")) && (!(toolName.equals("")) || toolName.equals("JIRA")
						|| toolName.equals("JIRA-KANBAN") || toolName.equals("JIRA DEFECTS"))) {
					instanceURL = metric1.getUrl() + "/rest/api/latest/search";
					user = metric1.getUserName();
					
						pass=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
					key = metric1.getProjectCode();
					try {
						almClientMetrics.getALMToolData(instanceURL, user, pass, projectName, key, toolName);
						ConstantVariable.getLastRun(projectName, toolName, new Date().getTime(), "SUCCESS");
						LOGGER.info(toolName + " Collector ended for " + projectName);
					} catch (Exception e) {
						LOGGER.error(e);
						LOGGER.info(e.getMessage());
						LOGGER.info(toolName + " Collector failed for " + projectName);
						ConstantVariable.getLastRun(projectName, toolName, new Date().getTime(), "FAIL");
					}

				}

			}
		} else {
			LOGGER.error("No date found for project " + projectName + " in Configuration Collection ");
		}
		cleanObject();
	}

	public void cleanObject() {

		almClientMetrics = null;
		configurationRepo = null;
		configurationColection = null;
		metric = null;
		metric1 = null;
	}

	public String deleteJiraIssues(String projectName) throws JiraExceptions {
		ctx = DataConfig.getContext();
		String instanceURL = "";
		DeleteJiraIssues delIssue = new DeleteJiraIssues();
		String user = null;
		String pass = null;
		String key = null;
		String res = null;
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configurationColection = configurationRepo.findByProjectName(projectName).get(0);
		if (configurationColection != null) {

			metric = configurationColection.getMetrics();
			Iterator iter = metric.iterator();
			String toolName = null;

			while (iter.hasNext()) {
				toolName = "";
				Object configuration1 = iter.next();
				metric1 = (ConfigurationToolInfoMetric) configuration1;
				if ("Jira Defects".equals(metric1.getToolName()) && !(metric1.getUrl().equals(""))) {
					toolName = "JIRA DEFECTS";
				} else if ("Jira".equals(metric1.getToolName())) {
					toolName = "JIRA";
				} else if ("Jira-kanban".equals(metric1.getToolName())) {
					toolName = "JIRA-KANBAN";
				}
				if (!(metric1.getUrl().toString().equals("")) && (!(toolName.equals("")) || toolName.equals("JIRA")
						|| toolName.equals("JIRA-KANBAN") || toolName.equals("JIRA DEFECTS"))) {
					instanceURL = metric1.getUrl() + "/rest/api/latest/search";
					user = metric1.getUserName();
					pass = metric1.getPassword();
					key = metric1.getProjectCode();
					res = delIssue.handleDeletedIssues(instanceURL, user, pass, projectName, key);
				}

			}
		} else {
			LOGGER.error("No date found for project " + projectName + " in Configuration Collection ");
			cleanObject();
			return "No Configuration for Project";
		}
		cleanObject();
		return res;

	}
}
