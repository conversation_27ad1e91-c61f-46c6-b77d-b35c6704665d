package com.bolt.dashboard.jira;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.commons.codec.binary.Base64;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.sonar.SonarCollectorException;
import com.bolt.dashboard.util.RestClient;

/**
 * <AUTHOR>
 *
 */
public class JiraAuthentication {
	String utf = "UTF-8";
	private static final Logger LOGGER = LogManager.getLogger(JiraAuthentication.class);

	/**
	 * JIRA rest API authontication for getting total issue
	 *
	 * @param jiraUrl  as JIRA Rest api url
	 * @param userName as username
	 * @param password as password
	 * @param key      as project key
	 * @return rest API response
	 **/
	public Object jiraConnectionForStatus(String jiraUrl, String userName,
			String password/* , String key */) throws JiraExceptions {

		HttpURLConnection connectionStatus = null;
		try {

			URL jiraURL = new URL(jiraUrl);

			String loginSearch = userName + ":" + password;
			byte[] encodedBytes = Base64.encodeBase64(loginSearch.getBytes());
			String loginCreds = new String(encodedBytes);

			connectionStatus = (HttpURLConnection) jiraURL.openConnection();
			connectionStatus.setRequestMethod("GET");
			connectionStatus.setRequestProperty("Accept", "*/*");
			connectionStatus.setRequestProperty("Content-Type", "application/json");
			String basicAuth="Basic " + loginCreds;
			connectionStatus.setRequestProperty("Authorization", basicAuth);
			connectionStatus.setUseCaches(false);
			connectionStatus.setDoOutput(true);
			connectionStatus.setDoInput(true);

			

			Reader in;
				connectionStatus.connect();
				in = new BufferedReader(new InputStreamReader(connectionStatus.getInputStream(), utf));
				return new JSONParser().parse(in) ;
			} catch (UnsupportedEncodingException e) {
				
			LOGGER.info(e);
			} catch (IOException e) {
				
				LOGGER.info(e);
			} catch (ParseException e) {
				
				LOGGER.info(e);
			}catch(Exception ex){
				LOGGER.info(ex);
			}
			
	finally {
		if (connectionStatus != null)
			connectionStatus.disconnect();
	}
		return connectionStatus;
}
//		try {
//			return makeRestCall(jiraUrl, userName, password, "GET", "");
//
//		} catch (Exception ex) {
//			LOGGER.error("JIRA Authentication failed..");
//			throw new JiraExceptions(ex);
//		}
	 

	/**
	 * Rest API authontication with pagination
	 *
	 * @param jiraUrl     as JIRA Rest api url
	 *
	 * @param userName    as username
	 *
	 * @param password    as password
	 *
	 * @param key         as project key
	 *
	 * @param start       as pagination start count
	 *
	 * @param maxResult   as pagination maxResult count
	 *
	 * @param lastRunDate as last collector run time
	 *
	 * @return rest API response
	 **/
	public ResponseEntity<String> jiraConnection(String jiraUrl, String userName, String password, String key,
			int start, int maxresult, Date lastRunDate, boolean deleteFlag) throws JiraExceptions {

		HttpURLConnection connection = null;
		String dataTJ = null;
		try {

			URL jiraURL = new URL(jiraUrl);

			if (deleteFlag) {
				dataTJ = "{\"jql\":\"project=" + key + " Order BY Sprint\",\"startAt\":" + start + ",\"maxResults\":"
						+ maxresult + "}";
			} else {
				if (lastRunDate == null) {
					if (key.equals("CORE") || key.equals("MAND") || key.equals("MIOS")) {
						dataTJ = "{\"jql\":\"project=" + key + " AND created > "
								+ String.valueOf(ConstantVariable.getPreviousDate()) + " Order BY Sprint\",\"startAt\":"
								+ start + ",\"maxResults\":" + maxresult + ",\"expand\":[\"changelog\"]}";
					} else {
						dataTJ = "{\"jql\":\"project=" + key + " Order BY Sprint\",\"startAt\":" + start
								+ ",\"maxResults\":" + maxresult + ",\"expand\":[\"changelog\"]}";
					}

				} else {
					dataTJ = "{\"jql\":\"project=" + key + " AND updated <= '"
							+ new SimpleDateFormat("yyyy-MM-dd HH:mm").format(new Date()) + "' AND" + " updated> '"
							+ new SimpleDateFormat("yyyy-MM-dd HH:mm").format(lastRunDate)
							+ "' Order BY Sprint\",\"startAt\":" + start + ",\"maxResults\":" + maxresult
							+ ",\"expand\":[\"changelog\"]}";
				}
			}

//			byte[] dataBytes = dataTJ.getBytes("UTF-8");
//			String login1 = userName + ":" + password;
//			byte[] encodedBytes = Base64.encodeBase64(login1.getBytes());
//			String loginCreds = new String(encodedBytes);
//			connection = (HttpURLConnection) jiraURL.openConnection();
//			connection.setRequestMethod("POST");
//			connection.setRequestProperty("Accept", "*/*");
//			connection.setRequestProperty("Content-Type", "application/json");
//			connection.setRequestProperty("Authorization", "Basic " + loginCreds);
//			connection.setUseCaches(false);
//			connection.setDoOutput(true);
//			connection.setDoInput(true);
//			connection.connect();
//			DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
//			wr.write(dataBytes);
//			wr.flush();
//			wr.close();
//			Reader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), utf));
//			StringBuilder testStringNew = new StringBuilder();
//			testStringNew.append(((JSONObject) new JSONParser().parse(in)).toJSONString());
//			return testStringNew;
			return makeRestCall(jiraUrl, userName, password, "POST", dataTJ);
		} catch (Exception ex) {

			LOGGER.error(ex.getMessage());
			throw new JiraExceptions(ex.getMessage());
		} 
	}

	public StringBuilder alternateJiraConnection(String jiraUrl, String userName, String password, String key,
			int start, int maxresult, Long cRunDate, Long currentTimeMiliseconds, boolean deleteFlag)
			throws JiraExceptions {
		HttpURLConnection connection = null;

		try {

			if (deleteFlag) {
				jiraUrl = jiraUrl + "?jql=project=%27" + key + "%27%20Order%20BY%20Sprint&startAt=" + start
						+ "&maxResults=" + maxresult;
				// jiraUrl=
				// "https://jira.move.com/rest/api/latest/search?jql=project=%27IN%27%20Order%20BY%20Sprint&startAt="+start+"&maxResults="+maxresult;
			} else {
				if (cRunDate == null) {
					// jiraUrl="https://jira.move.com/rest/api/latest/search?jql=project='IN'
					// &startAt="+start+"&maxResults="+maxresult+"&expand=changelog";
					// jiraUrl="https://jira.move.com/rest/api/latest/search?jql=project='IN'&startAt="+start+"&maxResults="+maxresult+"&expand=changelog";
					jiraUrl = jiraUrl + "?jql=project=%27" + key + "%27%20Order%20BY%20Sprint&startAt=" + start
							+ "&maxResults=" + maxresult + "&expand=changelog";

				} else {
//					 long currentMil=ConstantVariable.timestamp(new Date());
					jiraUrl = jiraUrl + "?jql=project=%27" + key + "%27%20AND%20updated%3C=" + currentTimeMiliseconds
							+ "%20AND%20updated%3E" + cRunDate + "%20Order%20BY%20Sprint&startAt=" + start
							+ "&maxResults=" + maxresult + "&expand=changelog";
					
//					jiraUrl = jiraUrl + "?jql=project='" + key + "' AND updated<=" + currentTimeMiliseconds
//							+ " AND updated>" + cRunDate + " Order BY Sprint&startAt=" + start
//							+ "&maxResults=" + maxresult + "&expand=changelog";

				}
			}
			URL ur = new URL(jiraUrl);
//			 byte[] dataBytes = dataTJ.getBytes("UTF-8");
		
			
			String login1 = userName + ":" + password;
			byte[] encodedBytes = Base64.encodeBase64(login1.getBytes());
			String loginCreds = new String(encodedBytes);
			connection = (HttpURLConnection) ur.openConnection();
			connection.setRequestMethod("GET");

			connection.setRequestProperty("Content-Type", "application/json");
			String basicAuth="Basic " + loginCreds;
			connection.setRequestProperty("Authorization",basicAuth);
			connection.setUseCaches(false);
			connection.setDoInput(true);
			connection.connect();
			Reader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), utf));
			StringBuilder testStringNew = new StringBuilder();
			testStringNew.append(((JSONObject) new JSONParser().parse(in)).toJSONString());
			return testStringNew;
//			return makeRestCall(jiraUrl, userName, password, "GET","").getBody();
		} catch (Exception ex) {

			LOGGER.error(ex);
			throw new JiraExceptions(ex.getMessage());
		} finally {
			if (connection != null)
				connection.disconnect();
		}

	}

	public RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000);
		requestFactory.setReadTimeout(20000);
		return new RestTemplate(requestFactory);
	}

	public ResponseEntity<String> makeRestCall(String url, String userId, String password, String type, String dataTJ) {
		// Basic Auth only.

		if (!"".equals(userId) && !"".equals(password)) {
			if (type.equalsIgnoreCase("GET")) {
				UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(url);
				UriComponents uriComponents = builder.build();
				URI uri = uriComponents.toUri();
				return get().exchange(uri, HttpMethod.GET, new HttpEntity<>(createHeaders(userId, password)),
						String.class);
			} else if (type.equalsIgnoreCase("POST")) {
				return get().postForEntity(url, new HttpEntity<>(dataTJ, createHeaders(userId, password)),
						String.class);

			}
		} else {
			return get().exchange(url, HttpMethod.GET, null, String.class);
		}
		return null;

	}

	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		//headers.setContentType(MediaType.APPLICATION_JSON);
       // headers.add("Accept", "application/json");
        //headers.add("Accept-Encoding", "gzip, deflate");
		headers.set("Authorization", authHeader);

		return headers;
	}
}
