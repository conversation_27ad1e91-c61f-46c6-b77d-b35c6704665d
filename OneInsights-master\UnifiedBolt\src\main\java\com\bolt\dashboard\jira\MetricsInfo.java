package com.bolt.dashboard.jira;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.CustomFields;
import com.bolt.dashboard.core.model.MetricsModel;

/**
 * <AUTHOR>
 *
 */
public class MetricsInfo {
	public void getAssignee(JSONObject json, MetricsModel metrics) {
		if (!((JSONObject) json.get("assignee") == null))
			metrics.setAssgnTo(((JSONObject) json.get("assignee")).get("displayName").toString());
	}

	/**
	 * set description if issue in metrics
	 **/
	public void getDescription(JSONObject json, MetricsModel metrics) {
		if (json.get("summary") != null)
			metrics.setSumm(json.get("summary").toString());
		/*
		 * if (json.get(ConstantVariable.KYWRD_ISSUETYPE) != null) {
		 * metrics.setSumm((String) ((JSONObject)
		 * json.get(ConstantVariable.KYWRD_ISSUETYPE)).get("name")); }
		 */
	}

	// Relook this method

	public void getFixVersion(JSONObject fieldsOfJSONData, MetricsModel metrics) {
		//JSONArray temp=(JSONArray)fieldsOfJSONData.get("fixVersions");

		Map<Long, String> fixVersionMap = new HashMap<>();
		if (fieldsOfJSONData.get("fixVersions") != null) {
			JSONArray array = (JSONArray) fieldsOfJSONData.get("fixVersions");
			Object relDate = null;
			long time;
			for (int i = 0; i < array.size(); i++) {
				JSONObject data = (JSONObject) array.get(i);
				String relName = data.get("name").toString();
				relDate = data.get("releaseDate");
				if (!(relDate == null)) {
					time = ConstantVariable.timestamp(relDate,metrics.getpName());
					fixVersionMap.put(time, relName);
				}else{
					time = 0;
					fixVersionMap.put(time, relName);
				}
			}
			metrics.setFixVer(fixVersionMap);
		}
	}

	public void getIssueLinks(JSONObject json, MetricsModel metrics, List<String> taskList) {
		JSONArray issueLinkJson = (JSONArray) json.get("issuelinks");
		List<String> outWardList = new ArrayList<>();

		for (Object issueLinkFields : issueLinkJson)
			try {
				JSONObject jsonLinkField = (JSONObject) new JSONParser()
						.parse(new StringBuilder(issueLinkFields.toString()).toString());
				JSONObject outwardIssue = (JSONObject) jsonLinkField.get("outwardIssue");
				JSONObject inwardIssue = (JSONObject) jsonLinkField.get("inwardIssue");
				if (outwardIssue != null && outwardIssue.get("key") != null)
					outWardList.add(outwardIssue.get("key").toString());
				if (inwardIssue != null && inwardIssue.get("key") != null)
					taskList.add(inwardIssue.get("key").toString());
			} catch (Exception e) {
			}
		if (!taskList.isEmpty()) {
			metrics.setTaskList(taskList);
			metrics.setInWardIssueLink(taskList);
		}
		if (!outWardList.isEmpty())
			metrics.setOutWardIssueLink(outWardList);
	}

	public void getPriority(JSONObject json, MetricsModel metrics, Map<String, String> customFieldsMap) {
		if (!((JSONObject) json.get("priority") == null))
			metrics.setPriority(((JSONObject) json.get("priority")).get("name").toString());
		else{
			
			String tempKey=customFieldNames.priority_level!=null?customFieldNames.priority_level:customFieldsMap.get("Priority Level");
			   if( json.get(tempKey)!=null)
				metrics.setPriority((String) ((JSONObject) json.get(tempKey)).get(ConstantVariable.KYWRD_VALUE));
		}
	}

	public Map<Long, String> getSprintAllocation(MetricsModel almToolJIRA, JSONArray historyArray, JSONObject fields) {
		Map<Long, String> sprintAlocationDate = new TreeMap();
		boolean flag = false;
		almToolJIRA.getsId();
		for (int i = 0; i < historyArray.size(); i++) {
			JSONObject historyObject = (JSONObject) historyArray.get(i);
			JSONArray itemsArray = (JSONArray) historyObject.get("items");
			for (int j = 0; j < itemsArray.size(); j++){
				if (((JSONObject) itemsArray.get(j)).get("field").toString().equals("Sprint"))
					if (flag == false && ((JSONObject) itemsArray.get(j)).get("from") == null) {
						sprintAlocationDate
								.putAll(splitSprintIds(ConstantVariable.timestamp(historyObject.get("created"),almToolJIRA.getpName()),
										((JSONObject) itemsArray.get(j)).get("to").toString()));
						flag = true;
					} else if (flag == false && ((JSONObject) itemsArray.get(j)).get("from") != null) {

						sprintAlocationDate.putAll(splitSprintIds(almToolJIRA.getCreateDate(),
								((JSONObject) itemsArray.get(j)).get("from").toString()));
						sprintAlocationDate
								.putAll(splitSprintIds(ConstantVariable.timestamp(historyObject.get("created"),almToolJIRA.getpName()),
										((JSONObject) itemsArray.get(j)).get("to").toString()));
						flag = true;
					} else if (flag == true && ((JSONObject) itemsArray.get(j)).get("from") != null)
						sprintAlocationDate
								.putAll(splitSprintIds(ConstantVariable.timestamp(historyObject.get("created"),almToolJIRA.getpName()),
										((JSONObject) itemsArray.get(j)).get("to").toString()));
					else
						sprintAlocationDate
								.putAll(splitSprintIds(ConstantVariable.timestamp(historyObject.get("created"),almToolJIRA.getpName()),
										((JSONObject) itemsArray.get(j)).get("to").toString()));
			}
			
		}
		if (sprintAlocationDate.isEmpty())
			sprintAlocationDate.putAll(splitSprintIds(ConstantVariable.timestamp(fields.get("created"),almToolJIRA.getpName()),
					String.valueOf(almToolJIRA.getsId())));
		return sprintAlocationDate;

	}

	/**
	 * set issue status in metrics
	 **/
	public void getStatus(JSONObject json, MetricsModel metrics) {
		if (json.get(ConstantVariable.KYWRD_STATUS) != null) {
			JSONObject data = (JSONObject) json.get(ConstantVariable.KYWRD_STATUS);
//			if (data.containsKey("statusCategory")) {
//				data.get("statusCategory");
//			}

			metrics.setState((String) ((JSONObject) json.get(ConstantVariable.KYWRD_STATUS)).get("name"));
		}
	}

	public Map<Long, Double> getStoryPointAllocation(MetricsModel almToolJIRA, JSONArray historyArray) {
		Map<Long, Double> storyPointAllocation = new TreeMap();
      
		boolean flag = false;
		for (int i = 0; i < historyArray.size(); i++) {
			JSONObject historyObject = (JSONObject) historyArray.get(i);
			JSONArray itemsArray = (JSONArray) historyObject.get("items");
			for (int j = 0; j < itemsArray.size(); j++) {
				if (((JSONObject) itemsArray.get(j)).get("field").toString().equals("Story Points"))
					if (flag == false && ((JSONObject) itemsArray.get(j)).get("fromString") == null) {
						storyPointAllocation.put(ConstantVariable.timestamp(historyObject.get("created"),almToolJIRA.getpName()),
								Double.parseDouble(((JSONObject) itemsArray.get(j)).get("toString").toString()));
						flag = true;
					} else if (flag == false && ((JSONObject) itemsArray.get(j)).get("fromString") != null) {

						storyPointAllocation.put(almToolJIRA.getCreateDate(),
								Double.parseDouble(((JSONObject) itemsArray.get(j)).get("fromString").toString()));
						if (!((JSONObject) itemsArray.get(j)).get("toString").equals(""))
							storyPointAllocation.put(ConstantVariable.timestamp(historyObject.get("created"),almToolJIRA.getpName()),
									Double.parseDouble(((JSONObject) itemsArray.get(j)).get("toString").toString()));
						else
							storyPointAllocation.put(ConstantVariable.timestamp(historyObject.get("created"),almToolJIRA.getpName()), 0.0);
						flag = true;
					} else if (flag == true && ((JSONObject) itemsArray.get(j)).get("fromString") != null) {
						if (!((JSONObject) itemsArray.get(j)).get("toString").equals(""))
							storyPointAllocation.put(ConstantVariable.timestamp(historyObject.get("created"),almToolJIRA.getpName()),
									Double.parseDouble(((JSONObject) itemsArray.get(j)).get("toString").toString()));
						else
							storyPointAllocation.put(ConstantVariable.timestamp(historyObject.get("created"),almToolJIRA.getpName()), 0.0);
					} else
						storyPointAllocation.put(ConstantVariable.timestamp(historyObject.get("created"),almToolJIRA.getpName()),
								Double.parseDouble(((JSONObject) itemsArray.get(j)).get("toString").toString()));
				if (((JSONObject) itemsArray.get(j)).get("field").toString().equals("status"))
					if (!((JSONObject) itemsArray.get(j)).get("fromString").equals("Done")
							&& ((JSONObject) itemsArray.get(j)).get("toString").equals("Done"))
						almToolJIRA.setDoneDate(ConstantVariable.timestamp(historyObject.get("created"),almToolJIRA.getpName()));
			}
		}

		return storyPointAllocation;
	}

	public void getSubtask(MetricsModel metric, JSONObject fieldJson) {
		List<String> subTaskList = new ArrayList<>();
		JSONArray subTasksArray = (JSONArray) fieldJson.get("subtasks");
		if (!subTasksArray.isEmpty())
			for (int i = 0; i <= subTasksArray.size() - 1; i++) {
				JSONObject subtaskJson = (JSONObject) subTasksArray.get(i);
				subTaskList.add(subtaskJson.get("key").toString());
			}
		if (!subTaskList.isEmpty())
			metric.setSubtaskList(subTaskList);
	}

	public List<String> getEpicIssue(MetricsModel metric, JSONArray histArray) {
		List<String> epicIssues = new ArrayList<>();
		for (int i = 0; i < histArray.size(); i++) {
			JSONObject object = (JSONObject) histArray.get(i);
			if (metric.getType().equals("Epic")) {
				JSONArray itemArray = (JSONArray) object.get("items");
				for (int k = 0; k < itemArray.size(); k++) {
					JSONObject obj = (JSONObject) itemArray.get(k);
					if ("Epic Child".equals(obj.get("field")) && (obj.get("toString")!= null)) {
						epicIssues.add(obj.get("toString").toString());
					}

				}
			}
		}
		return epicIssues;
	}

	public void populateOrgEstimate(JSONObject fieldsOfJSONData, MetricsModel metrics) {
		if (fieldsOfJSONData.get(ConstantVariable.ALM_TIMEORIGINALESTIMATE) != null)
			metrics.setOrgEst(
					Double.parseDouble(fieldsOfJSONData.get(ConstantVariable.ALM_TIMEORIGINALESTIMATE).toString()));
	}

	public void setActEst(JSONObject fieldsOfJSONData, MetricsModel metrics) {
		Double timeSpent = metrics.getEffort() != null ? metrics.getEffort() : 0.0;
		Double originalEstimate = metrics.getOrgEst() != null ? metrics.getOrgEst() : 0.0;
		Double deviation = timeSpent - originalEstimate;
		if (deviation > 0.0) {
			metrics.setActEst(originalEstimate + deviation);
			metrics.setExtEffort(deviation);
		} else {
			metrics.setActEst(originalEstimate);
			metrics.setExtEffort(0.0);
		}
	}

	public void setAffectedVersionsData(JSONObject fieldsOfJSONData, MetricsModel metrics, Map<String, String> customFieldsMap) {
		String tempKey=customFieldNames.affectedVersions!=null?customFieldNames.affectedVersions:customFieldsMap.get("Affects Version/s");
		if (fieldsOfJSONData.get(tempKey) != null) {
			JSONArray array = (JSONArray) fieldsOfJSONData.get(tempKey);
			List<String> affectedVersionsList = new ArrayList<String>();
			for (int i = 0; i < array.size(); i++) {
				JSONObject data = (JSONObject) array.get(i);
				affectedVersionsList.add(data.get("name").toString());
			}
			metrics.setAffectedVersions(affectedVersionsList);
		}
	}

	public void setBaseLine(MetricsModel almToolJIRA, JSONObject fieldsOfJSONData, Map<String, String> customFieldsMap) {
		String tempKey=customFieldNames.baseLineRequirement!=null?customFieldNames.baseLineRequirement:customFieldsMap.get("Baselined Requirement");
		if (fieldsOfJSONData.get(tempKey) != null)
			almToolJIRA.setBaseline((String) ((JSONObject) fieldsOfJSONData.get(tempKey))
					.get(ConstantVariable.KYWRD_VALUE));
	}

	/**
	 * set componets in metrics Metrics containing only Brillio components
	 * should add for FordDirect is pending
	 * @param customFieldsMap 
	 **/
	public void setComponentData(JSONObject fieldsOfJSONData, MetricsModel metrics, Map<String, String> customFieldsMap) {
		String tempKey=customFieldNames.component!=null?customFieldNames.component:customFieldsMap.get("Component/s");

		
		if (fieldsOfJSONData.get(tempKey) != null) {
			JSONArray array = (JSONArray) fieldsOfJSONData.get(tempKey);
			List<String> components = new ArrayList<String>();
			for (int i = 0; i < array.size(); i++) {
				JSONObject data = (JSONObject) array.get(i);
				components.add(data.get("name").toString());
			}
			if (!components.isEmpty())
				metrics.setComponents(components);
		}else if (fieldsOfJSONData.get("components") != null) {
			JSONArray array = (JSONArray) fieldsOfJSONData.get("components");
			List<String> components = new ArrayList<String>();
			for (int i = 0; i < array.size(); i++) {
				JSONObject data = (JSONObject) array.get(i);
				components.add(data.get("name").toString());
			}
			if (!components.isEmpty())
				metrics.setComponents(components);
		}
	}

	/**
	 * collect creation time from fields json and set update cretaion time in
	 * metrics
	 ***/
	public void setCreationDate(JSONObject fieldsOfJSONData, MetricsModel metrics) {
		Long createdDate = ConstantVariable.timestamp(fieldsOfJSONData.get(ConstantVariable.ALM_CREATED).toString(),metrics.getpName());
		if (fieldsOfJSONData.get(ConstantVariable.ALM_CREATED) != null)
			metrics.setCreateDate(createdDate);
	}

	/**
	 * collect defect injector from fields json and set defect injector
	 * @param customFieldsMap 
	 ***/
	public void setDefectInjector(JSONObject fieldsOfJSONData, MetricsModel metrics, Map<String, String> customFieldsMap) {
		String tempKey=customFieldNames.JIRA_FIELD_DEFECT_INJECTOR!=null?customFieldNames.JIRA_FIELD_DEFECT_INJECTOR:customFieldsMap.get("Defect Injector");
		if (fieldsOfJSONData.get(tempKey) != null) {
			String defectInjector = ((JSONObject) fieldsOfJSONData.get(tempKey))
					.get("displayName").toString();

			metrics.setDefectInjector(defectInjector);
		}
	}

	public void setEffort(JSONObject fieldsOfJSONData, MetricsModel metrics) {
		if (fieldsOfJSONData.get(ConstantVariable.KYWRD_TIMESPENT) != null)
			metrics.setEffort(Double.parseDouble(fieldsOfJSONData.get(ConstantVariable.KYWRD_TIMESPENT).toString()));
	}

	public void setIssueType(JSONObject fieldsOfJSONData, MetricsModel metrics) {
		if (fieldsOfJSONData.get(ConstantVariable.KYWRD_ISSUETYPE) != null)
			metrics.setType((String) ((JSONObject) fieldsOfJSONData.get(ConstantVariable.KYWRD_ISSUETYPE)).get("name"));
		if (!(((JSONObject) fieldsOfJSONData.get(ConstantVariable.KYWRD_STATUS)).get("name") == null)) {
			JSONObject data = (JSONObject) fieldsOfJSONData.get(ConstantVariable.KYWRD_STATUS);
			if (data.containsKey("statusCategory")) {
				JSONObject statusCategory = (JSONObject) data.get("statusCategory");
				metrics.setStatusCategory(statusCategory.get("name").toString());
			}
			metrics.setState((String) ((JSONObject) fieldsOfJSONData.get(ConstantVariable.KYWRD_STATUS)).get("name"));
		}
	}
	/*
	 * public String getSprintName(JSONObject history){ String sprintName="";
	 * JSONArray historyArray = (JSONArray) history.get("histories"); for (int i
	 * = 0; i < historyArray.size(); i++) { JSONObject historyObject =
	 * (JSONObject) historyArray.get(i); JSONArray itemsArray = (JSONArray)
	 * historyObject.get("items"); for (int j = 0; j < itemsArray.size(); j++) {
	 * if (((JSONObject)
	 * itemsArray.get(j)).get("field").toString().equals("Sprint")){ sprintName
	 * =((String)((JSONObject) itemsArray.get(j)).get("toString")); } } }
	 *
	 *
	 * return sprintName; }
	 */

	public void setRemainingEffort(MetricsModel almToolJIRA, JSONObject fieldsOfJSONData) {
		if (fieldsOfJSONData.get(ConstantVariable.KYWRD_TIMEESTIMATE) != null)
			almToolJIRA.setRemTime(
					Double.parseDouble(fieldsOfJSONData.get(ConstantVariable.KYWRD_TIMEESTIMATE).toString()));
	}

	/**
	 * collect resolution adte from fields json and set update resolution time
	 * in metrics
	 ***/
	public void setResolutionDate(JSONObject fieldsOfJSONData, MetricsModel metrics) {
		if (fieldsOfJSONData.get(ConstantVariable.ALM_RESOLUTIONDATE) != null)
			metrics.setResDate(ConstantVariable.timestamp(fieldsOfJSONData.get(ConstantVariable.ALM_RESOLUTIONDATE),metrics.getpName()));
	}

	/**
	 * set severity in metrics
	 * @param customFieldsMap 
	 **/
	public void setSeverity(JSONObject fieldsOfJSONData, MetricsModel metrics, Map<String, String> customFieldsMap) {
		String tempKey=customFieldNames.defectSeverity!=null?customFieldNames.defectSeverity:customFieldsMap.get("Defect Severity");
		if (fieldsOfJSONData.get(tempKey) != null)
			metrics.setSeverity((String) ((JSONObject) fieldsOfJSONData.get(tempKey))
					.get(ConstantVariable.KYWRD_VALUE));
	}

	public double setStoryPoints(MetricsModel almToolJIRA, JSONObject fieldsOfJSONData, Map<String, String> customFieldsMap) {
		String tempKey=customFieldNames.storyPoints!=null?customFieldNames.storyPoints:customFieldsMap.get("Story Points");
		if (fieldsOfJSONData.get(tempKey) != null)
			return Double.parseDouble(fieldsOfJSONData.get(tempKey).toString());
		else
			return 0.0;
	}

	/**
	 * collect updated date from fields json and set updated date
	 ***/
	public void setUpdatedDate(JSONObject fieldsOfJSONData, MetricsModel metrics) {
		if (fieldsOfJSONData.get(ConstantVariable.updated) != null)
			metrics.setUpdatedDate(
					ConstantVariable.timestamp(fieldsOfJSONData.get(ConstantVariable.updated).toString(),metrics.getpName()));
	}

	public Map<Long, String> splitSprintIds(long timestamp, String sprintId) {
		Map<Long, String> allocationMap = new HashMap<>();
		List<String> allIds = null;
		sprintId = sprintId.replaceAll("\\s","");
		/*if (sprintId.contains(",")) {
			allIds = Arrays.asList(sprintId.split("\\s*,\\s*"));

			for (int m = 0; m < allIds.size(); m++)
				allocationMap.put(timestamp + m, allIds.get(m));
		} else*/
			allocationMap.put(timestamp, sprintId);

		return allocationMap;
	}

	public void setTargetReleaseReports(JSONObject fieldsOfJSONData, MetricsModel metric, String targetReleaseKey) {
		if (fieldsOfJSONData.get(targetReleaseKey) != null) {
			JSONArray array = (JSONArray) fieldsOfJSONData.get(targetReleaseKey);
			List<String> targets = new ArrayList<String>();
			for (int i = 0; i < array.size(); i++) {
				JSONObject data = (JSONObject) array.get(i);
				targets.add(data.get("name").toString());
				/* almToolJIRA.setReleased((Boolean) data.get("released")); */
			}
			if (targets.size() > 0)
				metric.setTargetRelease(targets);
		}
	}

	// Below functions are used for MOVE

	public void setDefectSquads(MetricsModel almToolJIRA, JSONObject fieldsOfJSONData, String customFieldID) {
		if (fieldsOfJSONData.get(customFieldID) != null) {
			String defectSquads = ((JSONObject) fieldsOfJSONData.get(customFieldID)).get("value").toString();

			almToolJIRA.setSquads(defectSquads);
		}
	}

	public void setDefectCategory(MetricsModel almToolJIRA, JSONObject fieldsOfJSONData, String customFieldID) {
		if (fieldsOfJSONData.get(customFieldID) != null) {
			String field = ((JSONObject) fieldsOfJSONData.get(customFieldID)).get("value").toString();

			almToolJIRA.setCategory(field);
		}
	}

	public void setWhenFound(MetricsModel almToolJIRA, JSONObject fieldsOfJSONData, String customFieldID) {
		if (fieldsOfJSONData.get(customFieldID) != null) {
			String field = ((JSONObject) fieldsOfJSONData.get(customFieldID)).get("value").toString();

			almToolJIRA.setWhenFound(field);
		}
	}

	public void setHowFound(MetricsModel almToolJIRA, JSONObject fieldsOfJSONData, String customFieldID) {
		if (fieldsOfJSONData.get(customFieldID) != null) {
			String field = ((JSONObject) fieldsOfJSONData.get(customFieldID)).get("value").toString();

			almToolJIRA.setHowFound(field);
		}
	}

	public void setWhereFound(MetricsModel almToolJIRA, JSONObject fieldsOfJSONData, String customFieldID) {
		if (fieldsOfJSONData.get(customFieldID) != null) {
			String field = ((JSONObject) fieldsOfJSONData.get(customFieldID)).get("value").toString();
			almToolJIRA.setWhereFound(field);
		}
	}

	public void setEnvironment(JSONObject json, MetricsModel metrics, Map<String, String> customFieldsMap) {
		
		String tempKey=customFieldNames.environment!=null?customFieldNames.environment:customFieldsMap.get(customFieldNames.environmetKey);
		if (json.get(tempKey) != null)
			metrics.setEnvironment((String) ((JSONObject) json.get(tempKey))
					.get(ConstantVariable.KYWRD_VALUE));
	}
	
	public void getLpmCustomFileds(JSONObject json, MetricsModel metrics, Map<String, String> customFieldsMap) {
		List<CustomFields> customFields = new ArrayList<CustomFields>();

		
		for(Map.Entry<String, String> map:customFieldsMap.entrySet()) {
			String name = map.getKey();
			String key = map.getValue();
			if(json.get(key) != null) {
				CustomFields field = new CustomFields();
				field.setName(name);
				field.setValue(json.get(key) );
				customFields.add(field);
			}
		}
		if(customFields.size()>0) {
			metrics.setCustomFields(customFields);
		}
	}
}
