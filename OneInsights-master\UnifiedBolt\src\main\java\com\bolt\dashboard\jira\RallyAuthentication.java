package com.bolt.dashboard.jira;

import java.nio.charset.StandardCharsets;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 *
 */
public class RallyAuthentication {
	String utf = "UTF-8";
	private static final Logger LOGGER = LogManager.getLogger(RallyAuthentication.class);

	@SuppressWarnings("unused")
	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;

	}

	@SuppressWarnings("unused")
	private HttpHeaders createAPIHeaders(final String apiKey) {
		HttpHeaders headers = new HttpHeaders();
		headers.set("zsessionid", apiKey);
		return headers;

	}

	private ResponseEntity<String> makeRestCallAPI(String url, String apiKey) {
		// Basic Auth only.
		if (!"".equals(apiKey)) {
			return get().exchange(url, HttpMethod.GET, new HttpEntity<>(createAPIHeaders(apiKey)), String.class);

		} else {
			return get().exchange(url, HttpMethod.GET, null, String.class);
		}
	}

	@SuppressWarnings("unused")
	private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(200000000);
		requestFactory.setReadTimeout(2000000000);
		return new RestTemplate(requestFactory);
	}

	public JSONObject callRallyUrl(String url, String apiKey) {

		JSONObject rallyJson = null;
		JSONArray arrResult = null;
		JSONObject result = null;
		// rallyJson = (JSONObject) new JSONParser().parse(new
		// FileReader("C:\\Mahesh\\backup\\Bolt\\NspData
		// json\\rally_feature_name.json"));
		// apiKey ="_vKmo6yBTTgaK4SloSYeTqpL3krf3uE48mRAEmCeBfXQ";
		// ResponseEntity<String> response = makeRestCall(url, userId, password);
		LOGGER.info("callRallyUrl: " + url);
	
		if (!apiKey.equals("")) {
			ResponseEntity<String> response = makeRestCallAPI(url, apiKey);
			try {
				rallyJson = (JSONObject) new JSONParser().parse(response.getBody());
				JSONObject feature = (JSONObject) rallyJson.get("QueryResult");
				arrResult = (JSONArray) feature.get("Results");
				result = (JSONObject) arrResult.get(0);
				// https:\/\/rally1.rallydev.com\/slm\/webservice\/v2.0\/portfolioitem\/feature\/482925751956
			} catch (ParseException e) {
				LOGGER.info(e);
			}
		}

//		 String featureName = feature.get("Name").toString();
//			LOGGER.info(result);
		return result;
	}
}
