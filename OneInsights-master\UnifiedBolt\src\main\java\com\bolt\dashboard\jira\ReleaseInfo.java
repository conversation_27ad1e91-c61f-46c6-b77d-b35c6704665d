package com.bolt.dashboard.jira;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TimeZone;

import org.json.simple.JSONArray;
import org.json.simple.JSONObject;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.Release;
import com.bolt.dashboard.core.model.ReleaseDetails;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.ReleaseRepo;

/**
 * <AUTHOR>
 *
 */
public class ReleaseInfo {
	Map<String, Integer> releaseCount = new LinkedHashMap<>();
	Map<String, Integer> doneCount = new LinkedHashMap<>();
	ALMConfigRepo almConfigRepo =null;
	ALMConfiguration almConfiguration = null;
	ReleaseRepo repo =null;
	public void getReleaseDetails(JSONArray releaseJsonArray, String projectName, String almType) {
		repo = DataConfig.getContext().getBean(ReleaseRepo.class);
		almConfigRepo= DataConfig.getContext().getBean(ALMConfigRepo.class);
		almConfiguration=almConfigRepo.findByProjectName(projectName).get(0);
		ReleaseDetails releaseDetails;
		getReleaseStoryCount(projectName, almType);
		List<ReleaseDetails> releases=repo.findByProjectName(projectName);
		if (releases.isEmpty())
			releaseDetails = new ReleaseDetails();
		else {
			
			releaseDetails = releases.get(0);
		}

		releaseDetails.setProjectName(projectName);
		List<Release> releaseList = new ArrayList<>();
		Release release;
		for (int i = 0; i < releaseJsonArray.size(); i++) {
			JSONObject releaseJson = (JSONObject) releaseJsonArray.get(i);
			release = new Release();

			if (!(releaseJson.get("id") == null))
				release.setRelId(releaseJson.get("id").toString());
			if (!(releaseJson.get("name") == null))
				release.setRelName(releaseJson.get("name").toString());
			if (!(releaseJson.get("released") == null))
				release.setReleased((boolean) releaseJson.get("released"));
			if (releaseJson.get("startDate") != null)
				release.setStDate(getTime(releaseJson.get("startDate").toString()));
			if (releaseJson.get("releaseDate") != null)
				release.setRelDate(getTime(releaseJson.get("releaseDate").toString()));
			if (releaseJson.get("userStartDate") != null)
				release.setUserstDate(getTime(releaseJson.get("userStartDate").toString()));
			if (releaseJson.get("userReleaseDate") != null) {
				
				release.setUserrelDate(getTime(releaseJson.get("userReleaseDate").toString()));
			}

			Iterator<Entry<String, Integer>> it = releaseCount.entrySet().iterator();
			while (it.hasNext()) {
				Entry<String, Integer> en = it.next();
				if (en.getKey().equalsIgnoreCase(releaseJson.get("name").toString()))
					release.setTotStCount(en.getValue());
			}
			Iterator<Entry<String, Integer>> it1 = doneCount.entrySet().iterator();
			while (it1.hasNext()) {
				Entry<String, Integer> en = it1.next();
				if (en.getKey().equalsIgnoreCase(releaseJson.get("name").toString()))
					release.setDoneStCount(en.getValue());
			}
			releaseList.add(release);
		}

		releaseDetails.setReleases(releaseList);
		repo.save(releaseDetails);
		cleanObject();
	}

	private void cleanObject() {
		
		almConfigRepo=null;
		almConfiguration=null;
		repo=null;
	}

	public long getTime(String time) {
		SimpleDateFormat f;
		Date parseDate;
		
		if (time.contains("-")) {
			try {
				f = new SimpleDateFormat("yyyy-MM-dd");
				f.setTimeZone(TimeZone.getTimeZone(almConfiguration.getTimeZone()));
				parseDate = f.parse(time);
				long milliseconds = parseDate.getTime();
				return milliseconds;
			} catch (ParseException e) {
				return 0;
			}
		} else {
			try {
				f = new SimpleDateFormat("dd/MMM/yy");
				f.setTimeZone(TimeZone.getTimeZone(almConfiguration.getTimeZone()));
				parseDate = f.parse(time);
				long milliseconds = parseDate.getTime();
				return milliseconds;
			} catch (ParseException e) {
				return 0;
			}

		}
	}

	public void getReleaseStoryCount(String projectName, String almType) {
		boolean flag = false;
		MetricRepo mRepo = DataConfig.getContext().getBean(MetricRepo.class);
		

		String str = Arrays.toString(almConfiguration.getCloseState());
		//List<MetricsModel> metricsList = mRepo.findByPNameAndTypeAndPAlmType(projectName, "Story", almType);
		List<MetricsModel> metricsList = mRepo.findByPNameAndPAlmType(projectName, almType);
		int StoryCount = 0;
		int doneStoryCount = 0;
		ListIterator<MetricsModel> listIt = metricsList.listIterator();
		while (listIt.hasNext()) {
			MetricsModel metricsModel = listIt.next();
			if (str.contains(metricsModel.getState())) {
				flag = true;
			} else
				flag = false;
			Map<Long, String> map = metricsModel.getFixVer();
			if(map!=null) {
			Iterator<Entry<Long, String>> i = map.entrySet().iterator();
			while (i.hasNext()) {
				Entry<Long, String> entry = i.next();
				String value = entry.getValue();

				if (releaseCount.entrySet().isEmpty()) {
					StoryCount = 1;
					releaseCount.put(value, StoryCount);
				} else {
					Iterator<Entry<String, Integer>> xIt = releaseCount.entrySet().iterator();
					int temp = 1;
					boolean x = false;
					while (xIt.hasNext()) {

						Entry<String, Integer> xMap = xIt.next();
						if (xMap.getKey().equalsIgnoreCase(value)) {
							x = true;
							StoryCount = xMap.getValue() + 1;
							break;
						}

					}
					if (x)
						releaseCount.put(value, StoryCount);
					else
						releaseCount.put(value, temp);
				}

				if (flag) {
					if (doneCount.entrySet().isEmpty()) {
						doneStoryCount = 1;
						doneCount.put(value, doneStoryCount);
					} else {
						int temp = 1;
						boolean y = false;
						Iterator<Entry<String, Integer>> xIt = doneCount.entrySet().iterator();
						while (xIt.hasNext()) {
							Entry<String, Integer> xMap = xIt.next();
							if (xMap.getKey().equalsIgnoreCase(value)) {
								y = true;
								doneStoryCount = xMap.getValue() + 1;

								break;
							}

						}
						if (y)
							doneCount.put(value, doneStoryCount);
						else
							doneCount.put(value, temp);
					}

				}
			}
		  }
		}

	}
}
