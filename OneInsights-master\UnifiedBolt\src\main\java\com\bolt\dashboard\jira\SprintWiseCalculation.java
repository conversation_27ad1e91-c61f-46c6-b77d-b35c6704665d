package com.bolt.dashboard.jira;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONException;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.query.Criteria;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.config.MongoAggregate;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.core.model.CodeQualityMetric;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.ProjectModel;
import com.bolt.dashboard.core.model.defectCount;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.CodeQualityRep;
import com.bolt.dashboard.core.repository.IterationRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.ProjectRepo;
import com.mongodb.BasicDBObject;

/**
 * <AUTHOR>
 *
 */
public class SprintWiseCalculation {
	private static final Logger LOGGER = LogManager.getLogger(SprintWiseCalculation.class);
    IterationRepo iterationRepo = DataConfig.getContext().getBean(IterationRepo.class);

    AnnotationConfigApplicationContext ctx = DataConfig.getContext();
    MetricRepo metricsRepo = DataConfig.getContext().getBean(MetricRepo.class);
    private MongoAggregate agg  = new MongoAggregate();;
    IterationModel model = null;
    Set<String> assignedTo = new LinkedHashSet<>();
    Map<String, Integer> sprintMap = new LinkedHashMap<>();
    long startDate = 0;
    long endDate = 0;
    String sprintName = null;
    String projectName = null;
    String[] closedState;
    String ALM_STORY = "Story";
    ALMConfiguration configuration;
    long lastSprintEndDate = 0;

    /**
     * collect sprint name ,startdate ,end date and calculate team size , story
     * points , build failure and last day technical debt
     **/
    public void getSprintData(String pName) {
	this.projectName = pName;

	List<IterationModel> list = iterationRepo.findByPName(this.projectName);
	Iterator<IterationModel> it = list.iterator();
	configuration = DataConfig.getContext().getBean(ALMConfigRepo.class).findByProjectName(this.projectName).get(0);
	storyEffortCalculation(pName);
	for (int i = 0; i < list.size(); i++) {
	    model = list.get(i);
	    sprintName = model.getsName();

if(sprintName.equals("Sprint 9:LP Dovetailing"))
	
	    if (i == 0 && !sprintName.equals("BackLog")) {
		model = list.get(i);
		startDate = model.getStDate();
		if (!(model.getCompletedDate() == 0))
		    endDate = model.getCompletedDate();
		else
		    endDate = model.getEndDate();

		lastSprintEndDate = endDate;

	    } else {

		startDate = lastSprintEndDate;
		if (!(list.get(i).getCompletedDate() == 0))
		    endDate = list.get(i).getCompletedDate();
		else if (!(list.get(i).getEndDate() == null))
		    endDate = list.get(i).getEndDate();

		lastSprintEndDate = endDate;
	    }
	    // calculate teamsize , story points ,buildfailure nad td
	    getTeamsize(list.get(i));
	    getPlannedStoryPoint(list.get(i));
	   // getBuildFailure();
	    getTD();
	}

    }

    @SuppressWarnings("unchecked")
    public void getTeamsize(IterationModel mainModel) {

	List<String> list = new ArrayList<String>();

	try {
	    list = DataConfig.getInstance().mongoTemplate().getCollection("Metrics").distinct("assgnTo",
		    new BasicDBObject("sName", mainModel.getsName()).append("pName", mainModel.getpName()));
	} catch (Exception e) {
		LOGGER.info(e);
	}
	mainModel.setTeamSize(list.size());
	ctx.getBean(IterationRepo.class).save(mainModel);

    }

    public void getPlannedStoryPoint(IterationModel mainModel) {
	int defectsCount = 0;
	int closedDefectsCount = 0;
	int openedDefectsCount = 0;
	String defectName;
	List<MetricsModel> modelList = DataConfig.getContext().getBean(MetricRepo.class)
		.findByPNameAndSName(mainModel.getpName(), mainModel.getsName());
	closedState = configuration.getCloseState();
	defectName = configuration.getDefectName();

	String str = Arrays.toString(closedState);

	int velocitySprint = 0;
	int plannedStoryPoints = 0;
	// list of Metric model
	for (MetricsModel currentSprintData : modelList) {
	    if (defectName.equalsIgnoreCase(currentSprintData.getType())) {
		defectsCount++;
		if (str.contains(currentSprintData.getState())) {
		    closedDefectsCount++;
		} else {
		    openedDefectsCount++;
		}
	    }
	}
	mainModel.setVelocity(velocitySprint);
	mainModel.setTotalStoryPoints(plannedStoryPoints);
	mainModel.setTotDefects(defectsCount);
	mainModel.setTotClosedDefects(closedDefectsCount);
	mainModel.setTotOpenDefetct(openedDefectsCount);
	ctx.getBean(IterationRepo.class).save(mainModel);

    }

    /**
     *
     * **/
    public int[] getSCMDetails() {
	return null;

    }

    public int getBuildFailure() {
	Map<String, Integer> buildFailCount = new HashMap<>();
	String jobName;
	String result = null;
	long timestamp = 0;
	int buildFail = 0;
	List<BuildTool> toolSet = ctx.getBean(BuildToolRep.class).findByNameAndTimestampBetween(projectName, startDate,
		endDate);
	Iterator<BuildTool> it = toolSet.iterator();
	while (it.hasNext()) {
	    BuildTool tool = it.next();
	    jobName = tool.getJobName();
	    Iterator<BuildToolMetric> metricIt = tool.getMetrics().iterator();
	    while (metricIt.hasNext()) {
		BuildToolMetric metric = metricIt.next();
		if (metric.getName().equals("result"))
		    result = metric.getValue().toString();

		if (metric.getName().equals("timestamp"))
		    timestamp = Long.parseLong(metric.getValue().toString());

		if (!(result == null) && result.equals("FAILURE") && (startDate < timestamp && timestamp < endDate)) {

		    for (Map.Entry<String, Integer> entry : buildFailCount.entrySet()) {
			String key = entry.getKey();
			Integer tab = entry.getValue();
			if (key.equals(jobName))
			    buildFailCount.put(jobName, tab++);
		    }
		    if (buildFailCount.isEmpty())
			buildFailCount.put(jobName, 1);
		}

	    }
	}
	IterationModel mainModel = ctx.getBean(IterationRepo.class).findBySNameAndPName(model.getsName(),
		model.getpName());
	mainModel.setBuildFail(buildFailCount);
	ctx.getBean(IterationRepo.class).save(mainModel);
	return 0;

    }

    public void getTD() {
	String td = null;
	List<CodeQuality> cQlist = (List<CodeQuality>) ctx.getBean(CodeQualityRep.class)
		.findByNameAndTimestampBetween(projectName, startDate, endDate);
	if (cQlist.isEmpty()) {

	} else {
	    CodeQuality cQ = cQlist.get(cQlist.size() - 1);
	    Set<CodeQualityMetric> metrics = cQ.getMetrics();
	    Iterator<CodeQualityMetric> metricIt = metrics.iterator();
	    while (metricIt.hasNext()) {
		CodeQualityMetric metric = metricIt.next();
		if (metric.getName().equals("sqale_index")) {
		    td = metric.getFormattedValue();
		    break;
		}
	    }

	    IterationModel mainModel = ctx.getBean(IterationRepo.class).findBySNameAndPName(model.getsName(),
		    model.getpName());
	    mainModel.setTechDebt(td);
	    ctx.getBean(IterationRepo.class).save(mainModel);
	}
    }
    
    public List<IterationOutModel> getCrtItr(String pName, String almType) {
    	List<String> states = Arrays.asList("ACTIVE","active","Active");
		List<IterationOutModel> dataList = agg.getCurrentItr(pName, almType, states);
		if (dataList.isEmpty()) {
			dataList = new ArrayList<>();
			states=Arrays.asList("CLOSED","closed","Closed");
			List<IterationOutModel> data = agg.getCurrentItr(pName, almType, states);
			if (!data.isEmpty()) {
				dataList.add(data.get(0));
			}
		}
		return dataList;
	}
// public static void main(String[] args) {
// 	new SprintWiseCalculation().getDefectsSev("NTSCC", null);
// }
    public void getDefectsSev(String pName, ProjectModel pDetails) {
    	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
    	ALMConfiguration config = ctx.getBean(ALMConfigRepo.class).findByProjectName(pName).get(0);
    	Object[] str = config.getCloseState();
    	String defectName = config.getDefectName();

    	MatchOperation filterSName = Aggregation
    		.match(new Criteria("pName").is(pName).and("type").is(defectName).and("state").nin(str));
    	GroupOperation group = Aggregation.group(config.getPriorityName()).count().as("count");
    	Aggregation mtrAggr = Aggregation.newAggregation(filterSName, group);
    	List<defectCount> list = new ArrayList<>();
    	try {
    	    list = DataConfig.getInstance().mongoTemplate().aggregate(mtrAggr, "Metrics", defectCount.class)
    		    .getMappedResults();
    	} catch (JSONException e) {
    	  LOGGER.error("Error In Sprint Wise Calculation ",e );
    	} catch (Exception e) {
    		 LOGGER.error("Error In Sprint Wise Calculation ",e );
    	}
    	pDetails.setDefectCount(list);
    }

    public void storyEffortCalculation(String pName) {
	MetricRepo repo = DataConfig.getContext().getBean(MetricRepo.class);
	List<MetricsModel> metricsList = repo.findByPNameAndType(pName, "Story");
	List<String> taskList = new ArrayList<>();
	for (MetricsModel model : metricsList) {
	    if (model.getSubtaskList() != null)
		taskList = model.getSubtaskList();
	    else if (model.getInWardIssueLink() != null)
		taskList = model.getInWardIssueLink();
	    Object[] objectArray = getTotalEffort(taskList);
	    MetricsModel metric = repo.findByWId(model.getwId());
	    metric.setOrgEst((Double) objectArray[0]);
	    metric.setEffort((Double) objectArray[1]);
	    repo.save(metric);
	}

    }

    public Object[] getTotalEffort(List<String> metricsList) {
	double orgEstmt = 0;
	double actualEffort = 0;
	Object[] objectArray = new Object[2];
	for (String wId : metricsList) {
	    MetricsModel metrics = DataConfig.getContext().getBean(MetricRepo.class).findByWId(wId);
	    if (metrics!=null ){
	    	if( metrics.getOrgEst() != null)
		orgEstmt = orgEstmt + metrics.getOrgEst();
	    if (metrics.getEffort() != null)
		actualEffort = actualEffort + metrics.getEffort();
	    }
	}
	objectArray[0] = orgEstmt;
	objectArray[1] = actualEffort;
	return objectArray;
    }
}
