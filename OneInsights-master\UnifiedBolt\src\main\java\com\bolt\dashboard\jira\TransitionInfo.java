package com.bolt.dashboard.jira;

import java.util.Date;
import java.util.List;

import org.joda.time.DateTime;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.TransitionModel;

/**
 * <AUTHOR>
 *
 */
public class TransitionInfo {

	public void populateTransition(TransitionMetrices transitionMetrices, String wId,MetricsModel metric,String pName,String projKey) {
		JSONArray filteredStatusArray = transitionMetrices.getFilteredStatusArray();
		List<Long> modifiedDateList = transitionMetrices.getModifiedDateList();
		transitionMetrices.getwId();
	
		String taskLastState = transitionMetrices.getLastState();
		long taskEffort = transitionMetrices.getEffort();
		long creationTime = transitionMetrices.getCrTime();
		transitionMetrices.getFirstState();
		List<TransitionModel> taskDetailsList = transitionMetrices.getTaskDetailsList();
		transitionMetrices.getpName();
		transitionMetrices.getsName();
		long modifiedDate = 0;
		long stateWaitTime = 0;
		long previousStateWaitTime = 0;
		long leadTime = 0;
		
		if (filteredStatusArray.isEmpty()) {
			metric.setWaitTime(ConstantVariable.timestamp(new DateTime(),pName) - creationTime);
		}
		for (int i = 0; i < filteredStatusArray.size(); i++) {
			modifiedDate = modifiedDateList.get(i);
			TransitionModel taskDetails = new TransitionModel();
			String fromState = null;
			String toState = null;
			JSONObject filteredStatusJsonObject = (JSONObject) filteredStatusArray.get(i);
			fromState = filteredStatusJsonObject.get("fromString").toString();
			toState = filteredStatusJsonObject.get("toString").toString();
			taskDetails.setCrState(toState);
			taskDetails.setFrmState(fromState);
			taskDetails.setMdfDate(modifiedDate);
			
			if (i == 0) {
				if (i == modifiedDateList.size() - 1) {
					previousStateWaitTime = modifiedDate - creationTime;
					stateWaitTime = ConstantVariable.timestamp(new DateTime(),pName) - modifiedDate;
				} else {
					previousStateWaitTime = modifiedDate - creationTime;
					stateWaitTime = modifiedDateList.get(i + 1) - modifiedDateList.get(i);
				}
				if (taskLastState.contains(toState)) {
					stateWaitTime = 0;
					leadTime = modifiedDate - creationTime;
				}
			} else if (taskLastState.contains(toState)) {
				stateWaitTime = 0;
				leadTime = modifiedDate - creationTime;
			} else if (i == modifiedDateList.size() - 1)
				stateWaitTime = ConstantVariable.timestamp(new DateTime(),pName) - modifiedDate;
			else
				stateWaitTime = modifiedDateList.get(i + 1) - modifiedDateList.get(i);
			taskDetails.setwId(wId);
			taskDetails.setLeadTime(leadTime);
			taskDetails.setCreateTime(creationTime);
			taskDetails.setEffort(taskEffort);
			taskDetails.setWaitTime(stateWaitTime);
			taskDetails.setPreStateWaitTime(previousStateWaitTime);
			taskDetails.setpName(pName);
			taskDetails.setProjKey(projKey);
			taskDetailsList.add(taskDetails);
		}
		transitionMetrices.setTaskDetailsList(taskDetailsList);

	}
}
