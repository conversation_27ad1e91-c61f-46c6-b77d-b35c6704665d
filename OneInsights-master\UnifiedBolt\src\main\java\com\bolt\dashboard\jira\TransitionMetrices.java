package com.bolt.dashboard.jira;

import java.util.List;

import org.json.simple.JSONArray;

import com.bolt.dashboard.core.model.TransitionModel;

/**
 * <AUTHOR>
 *This class is used for replacing  multiple arguments with one single Object 
 *while calculating transition for individual JIRA workItemID
 */
public class TransitionMetrices {
	
	JSONArray filteredStatusArray;
	List<Long> modifiedDateList;
	String wId;
	String lastState;
	long effort;
	long crTime;
	String firstState;
	List<TransitionModel> taskDetailsList = null;
	String pName;
	String sName;
    String projKey;

	public String getProjKey() {
		return projKey;
	}

	public void setProjKey(String projKey) {
		this.projKey = projKey;
	}

	public JSONArray getFilteredStatusArray() {
		return filteredStatusArray;
	}

	public void setFilteredStatusArray(JSONArray filteredStatusArray) {
		this.filteredStatusArray = filteredStatusArray;
	}

	public List<Long> getModifiedDateList() {
		return modifiedDateList;
	}

	public void setModifiedDateList(List<Long> modifiedDateList) {
		this.modifiedDateList = modifiedDateList;
	}

	public String getwId() {
		return wId;
	}

	public void setwId(String wId) {
		this.wId = wId;
	}

	public String getLastState() {
		return lastState;
	}

	public void setLastState(String lastState) {
		this.lastState = lastState;
	}

	public long getEffort() {
		return effort;
	}

	public void setEffort(long effort) {
		this.effort = effort;
	}

	public long getCrTime() {
		return crTime;
	}

	public void setCrTime(long crTime) {
		this.crTime = crTime;
	}

	public String getFirstState() {
		return firstState;
	}

	public void setFirstState(String firstState) {
		this.firstState = firstState;
	}

	public List<TransitionModel> getTaskDetailsList() {
		return taskDetailsList;
	}

	public void setTaskDetailsList(List<TransitionModel> taskDetailsList) {
		this.taskDetailsList = taskDetailsList;
	}

	public String getpName() {
		return pName;
	}

	public void setpName(String pName) {
		this.pName = pName;
	}

	public String getsName() {
		return sName;
	}

	public void setsName(String sName) {
		this.sName = sName;
	}

}