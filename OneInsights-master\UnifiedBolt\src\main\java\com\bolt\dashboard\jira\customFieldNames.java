package com.bolt.dashboard.jira;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.json.simple.JSONArray;
import org.json.simple.JSONObject;

import com.bolt.dashboard.core.ConstantVariable;

/**
 * <AUTHOR> This class is used for initializing all customfield
 *         values from JIRA rest api
 */

public class customFieldNames {
	static String storyPoints = null;
	static String targetRelease = null;
	static String component = null;
	static String fixVersions = null;
	static String releaseDate = null;
	static String modules = null;
	static String affectedVersions = null;
	static String clientJiraId = null;
	static String defectSeverity = null;
	static String baseLineRequirement = null;
	static String JIRA_SPRINT_FIELD_BRILLIO = null;
	static String JIRA_FIELD_DEFECT_INJECTOR = null;
	static String TARGET_RELEASE = null;
	static String JIRA_FIELD_DEFECT_TYPE = null;
	static String KYWRD_STATUS = "status";

	static Set<String> taskStateList = new LinkedHashSet<>();
	static Set<String> subTaskStateList = new LinkedHashSet<>();
	static List<String> storyStateList = new ArrayList<>();
	static List<String> bugStateList = new ArrayList<>();
	static List<String> epicStateList = new ArrayList<>();
	static List<String> testStateList = new ArrayList<>();
	static List<String> newFeatureList = new ArrayList<>();
	static List<String> improvementStateList = new ArrayList<>();
	static List<String> bugExternalStateList = new ArrayList<>();
	static List<String> newFeatureExternalStateList = new ArrayList<>();
	static List<String> improvementExternalStateList = new ArrayList<>();
	static List<String> taskExternalStateList = new ArrayList<>();
	static List<String> subTaskExternalStateList = new ArrayList<>();

	// below fields are added for MOVE
	static String defectSquads = null;
	static String defectCategory = null;
	static String whenFound = null;
	static String whereFound = null;
	static String howFound = null;
	static String environment = null;
	public static String environmetKey = "";
	static String priority_level = null;
	public static String acFeature_capability = "";
	public static String URL = null;

	public static Map<String, String> getCustomFieldsInfo(JSONArray customFieldsJsonArray) {
		Map<String, String> customFieldMap = new HashMap<>();
		// LOG.info("inside getCustomFieldsInfo()");
		for (int i = 0; i < customFieldsJsonArray.size(); i++) {
			JSONObject customObject = (JSONObject) customFieldsJsonArray.get(i);
			String value = customObject.get("id").toString();
			String key = customObject.get("name").toString();

			JIRA_SPRINT_FIELD_BRILLIO = "Sprint".equals(key) ? value : JIRA_SPRINT_FIELD_BRILLIO;
			JIRA_FIELD_DEFECT_INJECTOR = "Defect Injector".equals(key) ? value : JIRA_FIELD_DEFECT_INJECTOR;
			JIRA_FIELD_DEFECT_TYPE = "BUG TYPE".equals(key) ? value : JIRA_FIELD_DEFECT_TYPE;
			storyPoints = "Story Points".equals(key) ? value : storyPoints;
			defectSeverity = "Defect Severity".equals(key) ? value : defectSeverity;
			baseLineRequirement = "Baselined Requirement".equals(key) ? value : baseLineRequirement;
			targetRelease = "Target Release".equals(key) ? value : targetRelease;
			fixVersions = "Fix Version/s".equals(key) ? value : fixVersions;
			releaseDate = "ReleaseDate".equals(key) ? value : releaseDate;
			component = "Component/s".equals(key) ? value : component;
			modules = "Modules".equals(key) ? value : modules;
			affectedVersions = "Affects Version/s".equals(key) ? value : affectedVersions;
			clientJiraId = "Client JIRA-ID".equals(key) ? value : clientJiraId;
			// This fields are added for MOVE
			defectCategory = "Category".equals(key) ? value : defectCategory;
			defectSquads = "CORE Squads".equals(key) ? value : defectSquads;
			whenFound = "When Found".equals(key) ? value : whenFound;
			whereFound = "Where Found".equals(key) ? value : whereFound;
			howFound = "How Found".equals(key) ? value : howFound;
			// field for Verizon
			priority_level = "Priority Level".equals(key) ? value : priority_level;
			acFeature_capability = "AC Feature / Capability".equals(key) ? value : acFeature_capability;
			URL = "URL".equals(key) ? value : URL;

			if (!environmetKey.equals(""))
				environment = environmetKey.equals(key) ? value : environment;
			customFieldMap.put(key, value);
		}
		return customFieldMap;
	}

	public void getProjectStatus(JSONArray statusArray) {
		for (Object statusObject : statusArray) {
			JSONObject statusJsonObject = (JSONObject) statusObject;
			if ("Task".equals(statusJsonObject.get("name"))) {
				JSONArray array = (JSONArray) statusJsonObject.get(ConstantVariable.JIRA_STATUSES);
				taskStateList = getTaskStateList(array);
			}
			if ("Sub-task".equals(statusJsonObject.get("name"))) {
				JSONArray array = (JSONArray) statusJsonObject.get(ConstantVariable.JIRA_STATUSES);
				subTaskStateList = getTaskStateList(array);
			}
			if ("Story".equals(statusJsonObject.get("name"))) {
				JSONArray array = (JSONArray) statusJsonObject.get(ConstantVariable.JIRA_STATUSES);
				storyStateList = getStoryStateList(array);
			}
			if ("Bug".equals(statusJsonObject.get("name"))) {
				JSONArray array = (JSONArray) statusJsonObject.get(ConstantVariable.JIRA_STATUSES);
				bugStateList = getBugStateList(array);
			}
			if ("Epic".equals(statusJsonObject.get("name"))) {
				JSONArray array = (JSONArray) statusJsonObject.get(ConstantVariable.JIRA_STATUSES);
				epicStateList = getEpicStateList(array);
			}
			if ("Test".equals(statusJsonObject.get("name"))) {
				JSONArray array = (JSONArray) statusJsonObject.get(ConstantVariable.JIRA_STATUSES);
				testStateList = getTestStateList(array);
			}
			if ("New Feature".equals(statusJsonObject.get("name"))) {
				JSONArray array = (JSONArray) statusJsonObject.get(ConstantVariable.JIRA_STATUSES);
				newFeatureList = getStateList(array, newFeatureList);
			}
			if ("Improvement".equals(statusJsonObject.get("name"))) {
				JSONArray array = (JSONArray) statusJsonObject.get(ConstantVariable.JIRA_STATUSES);
				improvementStateList = getStateList(array, improvementStateList);
			}
			if ("Bug External".equals(statusJsonObject.get("name"))) {
				JSONArray array = (JSONArray) statusJsonObject.get(ConstantVariable.JIRA_STATUSES);
				bugExternalStateList = getStateList(array, bugExternalStateList);
			}
			if ("New Feature External".equals(statusJsonObject.get("name"))) {
				JSONArray array = (JSONArray) statusJsonObject.get(ConstantVariable.JIRA_STATUSES);
				newFeatureExternalStateList = getStateList(array, newFeatureExternalStateList);
			}
			if ("Improvement External".equals(statusJsonObject.get("name"))) {
				JSONArray array = (JSONArray) statusJsonObject.get(ConstantVariable.JIRA_STATUSES);
				improvementExternalStateList = getStateList(array, improvementExternalStateList);
			}
			if ("Task External".equals(statusJsonObject.get("name"))) {
				JSONArray array = (JSONArray) statusJsonObject.get(ConstantVariable.JIRA_STATUSES);
				taskExternalStateList = getStateList(array, taskExternalStateList);
			}
			if ("Sub-task External".equals(statusJsonObject.get("name"))) {
				JSONArray array = (JSONArray) statusJsonObject.get(ConstantVariable.JIRA_STATUSES);
				subTaskExternalStateList = getStateList(array, subTaskExternalStateList);
			}
		}
	}

	public List<String> getBugStateList(JSONArray array) {
		for (int i = 0; i < array.size(); i++) {
			String state = ((JSONObject) array.get(i)).get("name").toString();
			bugStateList.add(state);
		}
		return bugStateList;
	}

	public List<String> getStateList(JSONArray array, List<String> newFeatureList2) {
		for (int i = 0; i < array.size(); i++) {
			String state = ((JSONObject) array.get(i)).get("name").toString();
			newFeatureList2.add(state);

		}
		return newFeatureList2;
	}

	public List<String> getEpicStateList(JSONArray array) {
		for (int i = 0; i < array.size(); i++) {
			String state = ((JSONObject) array.get(i)).get("name").toString();
			epicStateList.add(state);

		}
		return epicStateList;
	}

	public List<String> getStoryStateList(JSONArray array) {
		for (int i = 0; i < array.size(); i++) {
			String state = ((JSONObject) array.get(i)).get("name").toString();
			storyStateList.add(state);

		}
		return storyStateList;
	}

	public Set<String> getSubTaskStateList(JSONArray array) {
		for (int i = 0; i < array.size(); i++) {
			String state = ((JSONObject) array.get(i)).get("name").toString();
			subTaskStateList.add(state);

		}
		return subTaskStateList;
	}

	public List<String> getTestStateList(JSONArray array) {
		for (int i = 0; i < array.size(); i++) {
			String state = ((JSONObject) array.get(i)).get("name").toString();
			testStateList.add(state);

		}
		return testStateList;
	}

	public Set<String> getTaskStateList(JSONArray array) {
		String temporaryValue = "";
		for (int i = 0; i < array.size(); i++) {
			String statusCatagoryName = ((JSONObject) ((JSONObject) array.get(i)).get("statusCategory")).get("name")
					.toString();
			if (!"Done".equals(statusCatagoryName)) {

				String state = ((JSONObject) array.get(i)).get("name").toString();
				taskStateList.add(state);
			} else {
				temporaryValue = ((JSONObject) array.get(i)).get("name").toString();

			}

		}
		taskStateList.add(temporaryValue);
		return taskStateList;
	}

	public static Map<String, String> getCustomFieldsOfLpm(JSONArray customFieldsJsonArray) {
		Map<String, String> customfiledsOfLpm = new HashMap<String, String>();
		List<String> customFiledNamesOfLpm = Arrays.asList("Portfolio ID", "Portfolio Name", "Business Hypothesis",
				"WSJF = COD / Epic Size (Story Points)", "User Business Value / Business Value (BV) (1,2,3,5,8)",
				"Time Criticality", "Risk Reduction /Opportunity Enablement Value",
				"Cost of Delay (COD) = User Business Value / Business Value (BV) + Time Criticality + Risk Reduction or Opportunity Enablement Value",
				"Feature Prioritization Parameter1", "Feature Prioritization Parameter2",
				"Feature Prioritization Parameter3", "Non-functional Requirements (NFR)", "PI Actual", "PI Planned",
				"Implementing ART / Tribe IDs (custom Team JIRA field can be used)",
				"Feature Investment Horizon = H1/H2/H3", "MVP or Full Implementation Feature", "description",
				"Portfolio Heads", "Value Stream IDs", "Partners", "Customer / Market Segments", "Channels",
				"Revenue Streams", "Suppliers", "Objectives", "Key Results", "Strategic Theme Total Budget consumed",
				"Strategic Theme Total Budget allocated", "Strategic Theme Investment Horizon (H1)- Budget allocated",
				"Strategic Theme Investment Horizon (H2)- Budget allocated",
				"Strategic Theme Investment Horizon (H3)- Budget allocated",
				"Strategic Theme Investment Horizon (H1)- Budget consumed",
				"Strategic Theme Investment Horizon (H2)- Budget consumed",
				"Strategic Theme Investment Horizon (H3)- Budget consumed", "Portfolio Total Budget allocated",
				"Portfolio Total Budget consumed", "Portfolio Investment Horizon (H1)- Budget allocated",
				"Portfolio Investment Horizon (H2)- Budget allocated",
				"Portfolio Investment Horizon (H3)- Budget allocated",
				"Portfolio Investment Horizon (H1)- Budget consumed",
				"Portfolio Investment Horizon (H2)- Budget consumed",
				"Portfolio Investment Horizon (H3)- Budget consumed", "Partners", "Value Proposition",
				"Value Stream Budget consumed", "Value Stream Budget allocated",
				"Value Stream Type = Developmental / Operational", "Customers", "Solutions",
				"Agile Release Train IDs (custom Team JIRA field can be used)", "KPIs", "Strategic Theme ID",
				"Strategic Theme Name", "Value Stream Name", "Epic Size", "MVP cost", "Epic Total Cost ",
				"Epic Prioritization Parameter1", "Epic Prioritization Parameter2", "Epic Prioritization Parameter3",
				"Business Owners", "Epic Sponsor", "Epic Key Stakeholders", "Epic Hypothesis",
				"Epic (Business) Outcomes", "Leading Success Indicators (KPIs)", "Non-functional Requirements (NFR)",
				"MVP Feature IDs", "Epic ROI", "PI planned start", "PI actual start", "PI actual complete",
				"Epic Investment Horizon = H1/H2/H3", "In Scope", "Out of Scope", "Analysis summary",
				"Internal / external customers and impact", "Impact on existing soln, programs, services",
				"Impact on sales, distribution, deployment, support", "Architecture impact", "Solution Analysis",
				"Development Strategy (Inhouse / Outsourced)",
				"Go-No-Go = MVP / Pivot (Evolve/Update) / FullImplementation (Keep/Persevere) / Abandon (Kill)",
				"Epic Budget Consumed", "Epic Budget Rebaselined", "Portfolio Total Budget Rebaselined",
				"Portfolio Investment Horizon (H1)- Budget Rebaselined",
				"Portfolio Investment Horizon (H2)- Budget Rebaselined",
				"Portfolio Investment Horizon (H3)- Budget Rebaselined", "Strategic Theme Total Budget Rebaselined",
				"Strategic Theme Investment Horizon (H1)- Budget Rebaselined",
				"Strategic Theme Investment Horizon (H2)- Budget Rebaselined",
				"Strategic Theme Investment Horizon (H3)- Budget Rebaselined");

		for (int i = 0; i < customFieldsJsonArray.size(); i++) {
			JSONObject customObject = (JSONObject) customFieldsJsonArray.get(i);
			String value = customObject.get("id").toString();
			String key = customObject.get("name").toString();
			if (customFiledNamesOfLpm.contains(key)) {
				customfiledsOfLpm.put(key, value);
			}
		}

		return customfiledsOfLpm;
	}
}
