package com.bolt.dashboard.octopusDeploy;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class OctopusDeployApplication {
	private static final Logger LOGGER = LogManager.getLogger(OctopusDeployApplication.class);
	AnnotationConfigApplicationContext ctx = null;
	BuildToolRep repo = null;
	OctopusDeployClientImplementation octopusClientImplementation = null;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configuration = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	ConfigurationToolInfoMetric metric1 = null;
	JSONObject idJson = null;
	Set<BuildTool> collection = null;
	Set<BuildTool> prevRec = null;

	public String replace(String str) {
		return str.replaceAll(" ", "%20");
	}

	public OctopusDeployApplication() {

	}

	public void octoMain(String projectName) {
		LOGGER.info("Octopus Deploy collector started for " + projectName);
		String instanceURL = "";
		String apiToken = null;

		Collection<BuildTool> buildToolSet = null;
		long lastTimeStamp = 0;
		ctx = DataConfig.getContext();
		repo = ctx.getBean(BuildToolRep.class);
		octopusClientImplementation = new OctopusDeployClientImplementation();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configuration = configurationRepo.findByProjectName(projectName).get(0);
		metric = configuration.getMetrics();
		Iterator iter = metric.iterator();
		String projName = "";
		LOGGER.info("Project name  " + configuration.getProjectName());
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOGGER.info("Tool name  " + metric1.getToolName());
			if ("Octopus Deploy".equals(metric1.getToolName())) {
				LOGGER.info("URL  " + metric1.getUrl());
				instanceURL = metric1.getUrl();
			
					apiToken=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				projName = replace(metric1.getProjectCode());
				break;
			}
		}
		try {

			idJson = octopusClientImplementation.makeRestCall(instanceURL + "/api/projects?name:" + projName, apiToken);
			String projectId = ((JSONObject) ((JSONArray) idJson.get("Items")).get(0)).get("Id").toString();

			String jobName = "Octo";
			collection = repo.findByNameAndJobName(projectName, jobName);

			prevRec = repo.findByNameAndJobName(projectName, jobName);
			List<BuildTool> list = new ArrayList<BuildTool>(prevRec);
			if (list != null && !list.isEmpty()) {
				BuildTool buildTool = list.get(list.size() - 1);
				lastTimeStamp = buildTool.getTimestamp();
			}

			buildToolSet = octopusClientImplementation.getBuildTool(instanceURL, apiToken, repo, projectId,
					lastTimeStamp, projectName);
			List deployData = new ArrayList<BuildTool>(buildToolSet);

			Collections.sort(deployData);
			repo.save(deployData);
			cleanObject();
		} catch (Exception e) {
			cleanObject();
			LOGGER.error(e.getMessage());
			LOGGER.info(e.getStackTrace());
			LOGGER.info(e);
			LOGGER.info("Octopus Deploy collector failed for " + projectName);
		}
		LOGGER.info("Octopus Deploy  ended for " + projectName);
	}

	public void cleanObject() {
		repo = null;
		octopusClientImplementation = null;
		configurationRepo = null;
		configuration = null;
		metric = null;
		metric1 = null;
		idJson = null;
		prevRec = null;
	}
}
