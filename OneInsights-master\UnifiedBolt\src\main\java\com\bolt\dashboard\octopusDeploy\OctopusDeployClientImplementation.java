package com.bolt.dashboard.octopusDeploy;

import java.util.Date;
import java.util.LinkedHashSet;
import java.util.Set;

import javax.ws.rs.core.MediaType;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.sun.jersey.api.client.Client;
import com.sun.jersey.api.client.WebResource;
import com.sun.jersey.api.client.config.DefaultClientConfig;

public class OctopusDeployClientImplementation implements OctopusDeployClient {
	private static final Logger LOGGER = LogManager.getLogger(OctopusDeployClientImplementation.class);
	Set<BuildTool> toolSet = new LinkedHashSet<>();
	BuildToolRep repo = null;

	private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000000);
		requestFactory.setReadTimeout(20000);
		return new RestTemplate(requestFactory);
	}

	@Override
	public Set<BuildTool> getBuildTool(String octopusdeployUrl, String apiKey, BuildToolRep repo, String projectId,
			long lastTimeStamp, String projectName) throws OctopusDeployException {
		JSONObject getTotalTasks = makeRestCall(octopusdeployUrl + "/api/tasks/?project=" + projectId, apiKey);
	
		int maxValue=Integer.parseInt(getTotalTasks.get("TotalResults").toString()); 
		int skipCount = 0;
		int takeCount = 50;
		while (maxValue > skipCount) {
			JSONObject taskJson = makeRestCall(
					octopusdeployUrl + "/api/tasks/?project=" + projectId + "&skip=" + skipCount + "&take=" + takeCount,
					apiKey);
			JSONArray allTasks = (JSONArray) taskJson.get("Items");
			addData(allTasks, projectName, lastTimeStamp);
			skipCount += takeCount;
		}
		return toolSet;
	}

	public void addData(JSONArray allTasks, String projectName, long lastTimeStamp) {
		for (int i = 0; i < allTasks.size(); i++) {
			long startTime=0;
			BuildTool build = new BuildTool();
			String taskName = (String) ((JSONObject) allTasks.get(i)).get("Name").toString();
			if(!((JSONObject) allTasks.get(i)).get("State").toString().equals("TimedOut")){
			  startTime = new DateTime((String) ((JSONObject) allTasks.get(i)).get("StartTime").toString())
						.getMillis();
			}
			if (taskName.equals("Deploy") && (startTime > lastTimeStamp)) {
				String deployId = (String) ((JSONObject) ((JSONObject) allTasks.get(i)).get("Arguments"))
						.get("DeploymentId").toString();
				
				String[] deployInt = deployId.split("(?<=\\D)(?=\\d)");
				String deployState = (String) ((JSONObject) allTasks.get(i)).get("State").toString();

				long duration = new DateTime((String) ((JSONObject) allTasks.get(i)).get("CompletedTime").toString())
						.getMillis() - startTime;
				BuildToolMetric durationMetric = new BuildToolMetric("duration");
				durationMetric.setValue(duration);
				build.getMetrics().add(durationMetric);
				long timeStamp = new Date().getTime();
				build.setTimestamp(timeStamp);
				BuildToolMetric timeStampmetric = new BuildToolMetric("timestamp");
				timeStampmetric.setValue(startTime);
				build.getMetrics().add(timeStampmetric);
				BuildToolMetric resultMetrics = new BuildToolMetric("result");
				resultMetrics.setValue(deployState != null ? deployState.toString() : "");
				build.getMetrics().add(resultMetrics);
				build.setName(projectName);
				build.setJobName("Octo");
				build.setBuildID(Integer.parseInt(deployInt[1]));
				build.setBuildType("Octopus Deploy");
				toolSet.add(build);

			}
		}
	}

	public JSONObject makeRestCall(String url, String apiToken) {

		try {
			WebResource resource = Client.create(new DefaultClientConfig()).resource(url);

			WebResource.Builder builder = resource.accept(MediaType.APPLICATION_JSON);
			builder.type(MediaType.APPLICATION_JSON);
			builder.header("x-octopus-apikey", apiToken);

			String something = builder.get(String.class);
			JSONParser parser = new JSONParser();
			JSONObject json = (JSONObject) parser.parse(something);
			return json;

		} catch (Exception e) {
			LOGGER.info(e.getMessage());

			return null;
		}

	}

}
