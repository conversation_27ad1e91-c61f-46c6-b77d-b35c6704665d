package com.bolt.dashboard.opsramp;

import java.util.ArrayList;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.OpsResources;
import com.bolt.dashboard.core.repository.OpsRampRepo;

public class OpsRampImplementation implements OpsRampClient {
	private static final Logger LOGGER = LogManager.getLogger(OpsRampImplementation.class);
	private OpsRampRepo opsRampRepo = null;

	private ArrayList<OpsResources> resArr;

	private String tenantUrl = "https://brillio.api.pov.opsramp.com/api/v2/tenants/";
	private String tokenUrl = "https://brillio.api.pov.opsramp.com/auth/oauth/token";
	private String tenantId = "client_1063";
	private String client_secret = "F6Btz2jfUDdXcVBFeEJjrY9EGPthNxvRcqXB3ghxzz9h5rWtBa6HD98sjbSe2s6w";
	private String client_id = "HUK4ZCmXVYrGT4YwZv55yJRVfWykE4E7";
	private String grant_type = "client_credentials";
	private String resourcePart = "/resources/search?pageNo=";

	private String accessToken = "";
	private String accessTokenExpiry = "";

	Date expiryDate = null;

	public void init() {
		AnnotationConfigApplicationContext	ctx = DataConfig.getContext();
		opsRampRepo = ctx.getBean(OpsRampRepo.class);
		resArr = new ArrayList<OpsResources>();
		resArr.clear();

	}

	@Override
	public void getResourcesData() {
		init();
		String baseUrl = tenantUrl + tenantId + resourcePart;
		boolean isExpires = getExpiryToken();
		if (accessToken.equals("") && isExpires) {
			try {
				getAccessToken();
				processRequest(baseUrl);
			} catch (Exception e) {

				LOGGER.error(e.getMessage());
			}

		} else
			processRequest(baseUrl);

	}

	public void processRequest(String url) {
		int currentPageNo = 1;
		try {
			ResponseEntity<String> response = makeRestCall(url + currentPageNo, accessToken);
if(response!=null) {
			JSONObject resourceDataObj = parseAsObject(response);
			JSONArray arrObj = resourceDataObj.getJSONArray("results");
			LOGGER.info("arrObj size : " + arrObj.length());
			int totalPages = (int) resourceDataObj.get("totalPages");
			int pageNo = (int) resourceDataObj.get("pageNo");
			currentPageNo++;
			LOGGER.info("pageNo : " + pageNo);
			updateResultData(resourceDataObj);
			while (currentPageNo <= totalPages) {
				response = makeRestCall(url + currentPageNo, accessToken);
				resourceDataObj = parseAsObject(response);
				arrObj = resourceDataObj.getJSONArray("results");
				LOGGER.info("arrObj size : " + arrObj.length());
				updateResultData(resourceDataObj);
				currentPageNo++;

			}
}

		} catch (NumberFormatException | JSONException e) {

			LOGGER.info(e);
		} 

	}

	public void updateResultData(JSONObject resourceDataObj) {
		resArr.clear();
//		DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ssZ");
//		String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";
//		DateFormat formatter = new SimpleDateFormat(DEFAULT_PATTERN);

		try {
			JSONArray arr = resourceDataObj.getJSONArray("results");
			if (arr.length() > 0) {
				for (int i = 0; i < arr.length(); i++) {
					JSONObject o = arr.getJSONObject(i);
					OpsResources opsRes = new OpsResources();
					opsRes.setPrimaryId(o.getInt("primaryId"));
					opsRes.setResId(o.getString("id"));
					opsRes.setHostName(o.getString("hostName"));
					opsRes.setIpAddress(o.getString("ipAddress"));
					opsRes.setClient_uniqueId(o.getJSONObject("client").getString("uniqueId"));
					opsRes.setClient_name(o.getJSONObject("client").getString("name"));
					if (o.has("location"))
						opsRes.setIdentity(o.getString("identity"));
					opsRes.setCreatedDate(o.getString("createdDate"));
					if (o.has("updatedDate"))
						opsRes.setUpdatedDate(o.getString("updatedDate"));
					else
						opsRes.setUpdatedDate("");
					opsRes.setClassCode(o.getString("classCode"));
					opsRes.setDeviceType(o.getString("deviceType"));
					opsRes.setDevicePath(o.getString("devicePath"));
					opsRes.setType(o.getString("type"));
					opsRes.setState(o.getString("state"));
//					opsRes.setMake(o.getString("make"));
					opsRes.setSource(o.getString("source"));
					opsRes.setStatus(o.getString("status"));
					opsRes.setAliasName(o.getString("aliasName"));
					if (o.has("location")) {
						opsRes.setLocation_name(o.getJSONObject("location").getString("name"));
						opsRes.setLocationCity(o.getJSONObject("location").getString("city"));
					} else {
						opsRes.setLocation_name("");
						opsRes.setLocationCity("");
					}
					opsRes.setName(o.getString("name"));
					opsRes.setResourceName(o.getString("resourceName"));
					opsRes.setResourceType(o.getString("resourceType"));
					opsRes.setFrequency(o.getInt("frequency"));
					opsRes.setPaused(o.getBoolean("paused"));
					opsRes.setDeleted(o.getBoolean("deleted"));
					opsRes.setValidateSSL(o.getBoolean("validateSSL"));
					opsRes.setPort(o.getInt("port"));
					opsRes.setEncrypted(o.getBoolean("encrypted"));
					opsRes.setTimeout(o.getInt("timeout"));
					opsRes.setDevice(o.getBoolean("device"));
					opsRes.setSaId(o.getInt("saId"));
					if (o.has("osName"))
						opsRes.setOsName(o.getString("osName"));
					else
						opsRes.setOsName("");
					if (o.has("model"))
						opsRes.setModel(o.getString("model"));
					else
						opsRes.setModel("");
	resArr.add(opsRes);
				}
			}
		} catch (JSONException e) {
			LOGGER.error(e.getMessage());

		}
		if (!resArr.isEmpty()) {

			opsRampRepo.save(resArr);
		}

	}

	private boolean getExpiryToken() {
		Date now = new Date();
		if (accessTokenExpiry.equals(""))
			return true;
		else if (now.compareTo(expiryDate) >= 1) {
			return true;
		} else
			return false;

	}

	public HttpEntity<String> getAccessToken() {

		HttpHeaders headers = new HttpHeaders();
		headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);

		UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(tokenUrl)

				.queryParam("client_secret", client_secret).queryParam("client_id", client_id)
				.queryParam("grant_type", grant_type);

		HttpEntity<?> entity = new HttpEntity<>(headers);

		ResponseEntity<String> response = (ResponseEntity<String>) post().exchange(builder.toUriString(),
				HttpMethod.POST, entity, String.class);
		JSONObject incidentDataObj = parseAsObject(response);
		accessToken = (String) incidentDataObj.get("access_token");
		System.out.print(incidentDataObj.get("access_token"));
		int expire = (int) incidentDataObj.get("expires_in");
		System.out.println(incidentDataObj.get("expires_in"));
		Date now = new Date();
		expiryDate = new Date(now.getTime() + expire * 1000);
		accessTokenExpiry = expiryDate.toString();
		return response;

	}

	public RestOperations post() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(30000);
		requestFactory.setReadTimeout(30000);
		return new RestTemplate(requestFactory);
	}

	public RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(30000);
		requestFactory.setReadTimeout(30000);
		return new RestTemplate(requestFactory);
	}

	private ResponseEntity<String> makeRestCall(String url, String accesstoken) {
		// Basic Auth only.
		if (!"".equals(accesstoken)) {
			return get().exchange(url, HttpMethod.GET, new HttpEntity<>(createHeaders(accesstoken)), String.class);

		} else
			return null;

	}

	private HttpHeaders createHeaders(final String accesstoken) {

		String authHeader = "Bearer " + accesstoken;
		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);

		return headers;
	}

	private JSONObject parseAsObject(ResponseEntity<String> staticsticResponse) {

		return (JSONObject) new JSONTokener(staticsticResponse.getBody()).nextValue();
	}

}
