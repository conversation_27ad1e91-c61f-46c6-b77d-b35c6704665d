

/*package com.bolt.dashboard.projectComparison;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import java.util.regex.Pattern;


import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ALMProject;
import com.bolt.dashboard.core.model.ALMToolMetric;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.core.model.CodeQualityMetric;
import com.bolt.dashboard.core.model.Iteration;
import com.bolt.dashboard.core.model.PortfolioConfig;
import com.bolt.dashboard.core.model.ProjectComparison;
import com.bolt.dashboard.core.model.ProjectComparisonMetrics;
import com.bolt.dashboard.core.model.ProjectHealth;
import com.bolt.dashboard.core.model.ProjectHealthRuleSet;
import com.bolt.dashboard.core.model.SprintComparison;
import com.bolt.dashboard.core.model.SprintComparisonMetrics;
import com.bolt.dashboard.core.repository.ALMRepository;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.CodeQualityRep;
import com.bolt.dashboard.core.repository.PortfolioConfigRepo;
import com.bolt.dashboard.core.repository.ProjectComparisonRepo;
import com.bolt.dashboard.core.repository.SCMToolRepository;
import com.bolt.dashboard.core.repository.SprintComparisonRepo;
import com.bolt.dashboard.core.repository.TestManagementRepo;
import com.bolt.dashboard.sprintcomparison.SprintComparisonApplication;

public class ProjectComparisonApplication {
	@SuppressWarnings("unused")
	 private static final Logger LOGGER = LogManager.getLogger(ProjectComparisonApplication.class);
	AnnotationConfigApplicationContext ctx;
	ALMRepository almRepo = null;
	TestManagementRepo testmgmtRepo = null;
	SCMToolRepository scmRepo = null;
	SprintComparisonRepo sprintComparisonRepo = null;

	Set<String> assignedToSet = null;
	BuildToolRep buildRepo = null;
	CodeQualityRep codeQualityRepo = null;
	SprintComparison comparison = null;
	static String projectName = null;
	List<ProjectHealth> projecthealthdata = null;
	ProjectHealth lastentries = null;
	List<ProjectHealthRuleSet> entriesList = null;
	PortfolioConfigRepo portfolioConfigRepo = null;
	ProjectComparisonRepo projectComparisonRepo = null;
	Double[] codeQualityArray = null;

	*//**
	 * Private constructor
	 *//*
	public ProjectComparisonApplication() {
		ctx = DataConfig.getContext();
	}

	@SuppressWarnings("unused")
	public void projectComparisonMain(String name) {
		LOGGER.info("Project Comparsion Collector started");
		projectName = name;
		ProjectComparisonApplication application = new ProjectComparisonApplication();
		application.getRepository();
		SprintComparison sprintComparison = application.getProjectComparisonData(projectName);

	}

	public void getRepository() {
		almRepo = ctx.getBean(ALMRepository.class);
		testmgmtRepo = ctx.getBean(TestManagementRepo.class);
		scmRepo = ctx.getBean(SCMToolRepository.class);

		buildRepo = ctx.getBean(BuildToolRep.class);
		codeQualityRepo = ctx.getBean(CodeQualityRep.class);
		sprintComparisonRepo = ctx.getBean(SprintComparisonRepo.class);
		portfolioConfigRepo = ctx.getBean(PortfolioConfigRepo.class);
		projectComparisonRepo = ctx.getBean(ProjectComparisonRepo.class);

	}

	@Override
	protected void finalize() throws Throwable {
		
		 * almRepo = null; testmgmtRepo = null; scmRepo = null; buildRepo =
		 * null; codeQualityRepo = null; sprintComparisonRepo = null;
		 * portfolioConfigRepo = null; projectComparisonRepo = null;
		 
	}

	public SprintComparison getProjectComparisonData(String projectName) {
		ProjectComparison projectComparison = new ProjectComparison();
		List<ProjectComparisonMetrics> projectComparisonMetricsList = new ArrayList<>();
		PortfolioConfigRepo repo = ctx.getBean(PortfolioConfigRepo.class);
		List<PortfolioConfig> portfolioConfigs = repo.findByProjectName(projectName);
		Iterator<PortfolioConfig> configIterator = portfolioConfigs.iterator();
		while (configIterator.hasNext()) {
			ProjectComparisonMetrics projectComparisonMetrics = new ProjectComparisonMetrics();
			PortfolioConfig portfolioConfig = configIterator.next();
			// projectName = portfolioConfig.getProjectName();

			List<SprintComparison> sprintComparisons = sprintComparisonRepo.findByProjectName(projectName);
			List<Iteration> almDataList = getALMData();
			if (!sprintComparisons.isEmpty()) {
				projectComparisonMetrics.setProjectName(projectName);
				comparison = sprintComparisons.get(sprintComparisons.size() - 1);
				Iterable<CodeQuality> codeQualityIterable = codeQualityRepo.findByName(projectName);
				if (codeQualityIterable.iterator().hasNext()) {
					codeQualityArray = getCodeQualityData(codeQualityIterable);
					double codeQualityPercentage = (codeQualityArray[1] / codeQualityArray[0]);
					projectComparisonMetrics.setTechnicalDebt(Math.floor(codeQualityPercentage * 100 / 100));
					int[] defectArray = getDefect(almDataList);

					double defectPercentage = (defectArray[0] / codeQualityArray[0]);
					projectComparisonMetrics.setDefects(Math.floor(defectPercentage * 100 / 100));

				}

				double totalEfficiency = getEfficiency(sprintComparisons);
				double totalIteration = almDataList.size() - 2;
				double efficiencyAverage = totalEfficiency / totalIteration;
				projectComparisonMetrics.setEfficiency(Math.floor(efficiencyAverage));
				double totalVelocity = getVelocity(sprintComparisons);
				double velocityAverage = totalVelocity / totalIteration;
				projectComparisonMetrics.setVelocity(Math.floor(velocityAverage));
				double[] buidDataArray = getBuildData();
				if (!(buidDataArray[1] == 0) && !(buidDataArray[0] == 0)) {
					double buildPercentage = (buidDataArray[1] / buidDataArray[0]) * 100;
					projectComparisonMetrics.setBuildFailure(Math.floor(buildPercentage));
				} else {
					projectComparisonMetrics.setBuildFailure(0.0);
				}

			}

			projectComparisonMetricsList.add(projectComparisonMetrics);
		}
		projectComparison.setMetrics(projectComparisonMetricsList);
		projectComparisonRepo.save(projectComparison);
		LOGGER.info("Project Comparsion Collector ended");
		return comparison;
	}

	@SuppressWarnings("unused")
	public double[] getBuildData() {

		Set<BuildTool> buildToolSet = null;

		Set<BuildTool> toolSet = buildRepo.findByName(projectName);
		double[] buildArray = getBuildRate(toolSet);

		return buildArray;

	}

	public double[] getBuildRate(Set<BuildTool> buildToolSet) {
		double[] buildArray = new double[2];
		double buildCount = 0;
		double successBuildCount = 0;
		Iterator<BuildTool> buildToolSetIterator = buildToolSet.iterator();
		while (buildToolSetIterator.hasNext()) {
			buildCount++;
			BuildTool tool = buildToolSetIterator.next();
			Set<BuildToolMetric> metrics = tool.getMetrics();
			for (BuildToolMetric metric : metrics) {
				if (metric.getName().equalsIgnoreCase("result") && metric.getValue().equals("SUCCESS"))
					successBuildCount++;
			}
		}
		buildArray[0] = buildCount;
		buildArray[1] = successBuildCount;
		return buildArray;

	}

	public double getVelocity(List<SprintComparison> sprintComparisons) {
		double totalVelocity = 0;
		SprintComparison sprintComparison = sprintComparisons.get(sprintComparisons.size() - 1);
		Set<SprintComparisonMetrics> metricsSet = sprintComparison.getMetrics();

		Iterator<SprintComparisonMetrics> metricSetIterator = metricsSet.iterator();
		while (metricSetIterator.hasNext()) {
			SprintComparisonMetrics metrics = metricSetIterator.next();
			totalVelocity = totalVelocity + metrics.getVelocity();

		}

		return totalVelocity;

	}

	public double getEfficiency(List<SprintComparison> sprintComparisons) {
		double totalEfficiency = 0;
		SprintComparison sprintComparison = sprintComparisons.get(sprintComparisons.size() - 1);
		Set<SprintComparisonMetrics> metricsSet = sprintComparison.getMetrics();

		Iterator<SprintComparisonMetrics> metricSetIterator = metricsSet.iterator();
		while (metricSetIterator.hasNext()) {
			SprintComparisonMetrics metrics = metricSetIterator.next();
			totalEfficiency = totalEfficiency + metrics.getEfficiency();

		}

		return totalEfficiency;

	}

	public double getProjectEfficiency(List<SprintComparisonMetrics> metrics) {
		double efficiency = 0;

		return efficiency;

	}

	public double getProjectVelocity(List<SprintComparisonMetrics> metrics) {
		double velocity = 0;
		while (!(metrics.size() == 0)) {
			Iterator<SprintComparisonMetrics> metricsIterator = metrics.iterator();
			while (metricsIterator.hasNext()) {
				velocity = metricsIterator.next().getVelocity();
			}
		}

		return velocity;

	}

	public int getBackLogStory(Iteration it) {

		int backlogStoryCount = 0;
		List<ALMToolMetric> metric = it.getMetrics();
		Iterator<ALMToolMetric> iterationIterator = metric.iterator();
		while (iterationIterator.hasNext()) {
			ALMToolMetric almToolMetric = iterationIterator.next();

			if (almToolMetric.getNameALMProperty().equalsIgnoreCase("Story")) {
				backlogStoryCount++;

			}

		}

		return backlogStoryCount;

	}

	@SuppressWarnings("unused")
	public int[] getStoryCount(List<Iteration> iterationList) {
		int storyCount = 0;
		int closedStoryCount = 0;
		int iterationCount = 0;
		Iteration backlogIteration = iterationList.get(iterationList.size() - 1);
		int backlogStoryCount = getBackLogStory(backlogIteration);

		for (int i = iterationList.size() - 2; i >= 0; i--) {
			iterationCount++;
			List<ALMToolMetric> metric = iterationList.get(i).getMetrics();
			Iterator<ALMToolMetric> iterationIterator = metric.iterator();
			while (iterationIterator.hasNext()) {
				ALMToolMetric almToolMetric = iterationIterator.next();

				if (almToolMetric.getNameALMProperty().equalsIgnoreCase("Story")
						&& (!iterationList.get(i).getIterationName().equalsIgnoreCase("BackLog"))) {
					storyCount++;
					if (almToolMetric.getState().equalsIgnoreCase("Done")
							|| "Resolved".equalsIgnoreCase(almToolMetric.getState())) {
						closedStoryCount++;

					}

				}

			}
		}
		double averageStoryPerIteration = (storyCount - closedStoryCount) / iterationCount;

		return null;

	}

	public int[] getCriticalDefect(List<Iteration> iterationList) {
		int criticalBugCount = 0;
		int criticalClosedBugCount = 0;
		int[] criticalDefectArray = new int[2];
		for (Iteration iteration : iterationList) {
			List<ALMToolMetric> metricList = iteration.getMetrics();
			Iterator<ALMToolMetric> metricIterator = metricList.iterator();
			while (metricIterator.hasNext()) {
				ALMToolMetric metric = metricIterator.next();
				if (("Critical".equals(metric.getDefectsSeverity()) || metric.getAlmPriority().contains("Critical"))
						|| ("Fatal".equals(metric.getDefectsSeverity()) || metric.getAlmPriority().contains("Fatal"))) {
					criticalBugCount++;
					if (metric.getState().equalsIgnoreCase("Done") || metric.getState().equalsIgnoreCase("CLOSED"))
						criticalClosedBugCount++;
				}

			}

		}
		criticalDefectArray[0] = criticalBugCount;
		criticalDefectArray[1] = criticalClosedBugCount;

		return criticalDefectArray;

	}

	public int[] getDefect(List<Iteration> iterationList) {
		int bougCount = 0;
		int closedBugCount = 0;
		int[] defectArray = new int[2];
		for (Iteration iteration : iterationList) {
			List<ALMToolMetric> metricList = iteration.getMetrics();
			Iterator<ALMToolMetric> metricIterator = metricList.iterator();
			while (metricIterator.hasNext()) {
				ALMToolMetric metric = metricIterator.next();
				if (metric.getNameALMProperty().equals("Bug") || metric.getNameALMProperty().equals("Defect")) {
					bougCount++;
					if (metric.getState().equals("Done") || metric.getState().equals("Closed"))
						closedBugCount++;
				}

			}

		}
		defectArray[0] = bougCount;
		defectArray[1] = closedBugCount;

		return defectArray;

	}

	@SuppressWarnings("rawtypes")
	public Double[] getCodeQualityData(Iterable<CodeQuality> codeQualityIterable) {
		Double[] codeQualityDataArray = new Double[2];
		double linesOfCode = 0;
		double td = 0;

		@SuppressWarnings("unchecked")
		List<CodeQuality> list = new ArrayList();
		for (CodeQuality item : codeQualityIterable) {
			list.add(item);
		}
		Set<CodeQualityMetric> metricsSet = list.get(list.size() - 1).getMetrics();
		Iterator<CodeQualityMetric> it = metricsSet.iterator();
		while (it.hasNext()) {
			CodeQualityMetric metric = it.next();
			if (metric.getName().equals("ncloc")) {

				linesOfCode = Double.parseDouble(metric.getValue().toString());

			}
			if (metric.getName().equals("sqale_index")) {
				td = splitTechnicalDebt(metric.getFormattedValue().toString());

				// td = Double.parseDouble(debt)*8;

			}
		}
		Set<SprintComparisonMetrics> metricsList = comparison.getMetrics();
		Iterator<SprintComparisonMetrics> sprintComparisonMetricsIterator = metricsList.iterator();
		while (sprintComparisonMetricsIterator.hasNext()) {
			sprintComparisonMetricsIterator.next().getTechnicalDebt();
		}

		codeQualityDataArray[0] = linesOfCode;
		codeQualityDataArray[1] = td;
		return codeQualityDataArray;

	}

	public double splitTechnicalDebt(String td) {
		double technicalDebt = 0;
		String debt = "";
		String[] fileSeparationString = null;
		if (td.contains(",")) {

			fileSeparationString = td.split(Pattern.quote(","));
			for (int i = 0; i < fileSeparationString.length; i++) {
				debt = debt.concat(fileSeparationString[i]);
			}
			td = debt;
		}
		if (td.contains(" ")) {

			fileSeparationString = td.split(Pattern.quote(" "));

			debt = fileSeparationString[0];

			td = debt;
		}
		if (td.contains("d")) {
			technicalDebt = Double.parseDouble(td) * 8;

		}
		if (td.contains("h")) {
			technicalDebt = Double.parseDouble(td.substring(0, td.length() - 1)) * 1;
		}
		return technicalDebt;
	}

	@SuppressWarnings("unused")
	public List<Iteration> getALMData() {
		Iteration backlogIteration = null;
		
		String almType = new SprintComparisonApplication().getAlmType(projectName);
		Iterable<ALMProject> almProjectIterable = almRepo.findByAlmTypeAndProjectName(almType, projectName);
		List<ALMProject> almProjectList = new ArrayList<>();
		Iterator<ALMProject> it = almProjectIterable.iterator();
		for (ALMProject item : almProjectIterable) {
			almProjectList.add(item);
		}
		ALMProject project = almProjectList.get(almProjectList.size() - 1);
		List<Iteration> iterationList = project.getIteration();

		return iterationList;

	}
}*/