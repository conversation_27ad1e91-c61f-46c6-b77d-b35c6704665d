package com.bolt.dashboard.projectHealth;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.bolt.dashboard.circleci.CircleCIClientImplementation;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.HealthData;
import com.bolt.dashboard.core.model.HealthDataMetrics;
import com.bolt.dashboard.core.model.HealthProjectSprintMetrics;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.ProjectHealth;
import com.bolt.dashboard.core.model.ProjectHealthApplicationPhase;
import com.bolt.dashboard.core.model.ProjectHealthConfig;
import com.bolt.dashboard.core.model.ProjectHealthRuleSet;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;

public class ALMData {
	String projectName = null;
	String almType = null;
	String defectType = null;
	String sprintName = null;
	String iterationName = null;

	long[] datesArray = null;
	long ppppIterationEndDate = 0;
	long startDate = 0;
	ProjectHealthCalculation pHC = new ProjectHealthCalculation();
	FutureSprintCal pHFSC = new FutureSprintCal();
	int phaseWeightage = 0;
	private static final Logger LOGGER = LogManager.getLogger(ALMData.class);

	public void getALMConfigData(String projectName) throws ProjectHealthException {
		this.projectName = projectName;
		ALMConfiguration almConfiguration = ProjectHealthRepos.almConfigRepo.findByProjectName(this.projectName).get(0);

		ProjectHealthVariables.setSTORY(almConfiguration.getStoryName());
		ProjectHealthVariables.setBug(almConfiguration.getDefectName());
		ProjectHealthVariables.setCloseState(Arrays.asList(almConfiguration.getCloseState()).toString());
		ProjectHealthVariables.setCriticalPriority(Arrays.asList(almConfiguration.getCriticalPriority()));

		ProjectHealthVariables.setHighPriority(Arrays.asList(almConfiguration.getHighPriority()));
		ProjectHealthVariables.setLowPriority(Arrays.asList(almConfiguration.getLowPriority()));
		ProjectHealthVariables.setMedPriority(Arrays.asList(almConfiguration.getMedPriority()));
		ProjectHealthVariables.setTaskName(Arrays.asList(almConfiguration.getTaskName()));
		ProjectHealthVariables.setPriorityName(almConfiguration.getPriorityName());
	}

	public void getALMData() throws ProjectHealthException {

		// Add to filter based on toolType
		// toolType from configuration
		almType = getAlmDefectToolType(this.projectName, "ALM");
		defectType = getAlmDefectToolType(this.projectName, "Defects Summary");
		ProjectHealthVariables.setIterationList(ProjectHealthRepos.iterationRepo.findByPName(this.projectName));
		List<IterationModel> citList = new ArrayList<>();
		List<IterationModel> pitList = new ArrayList<>();
	
		for (int i = ProjectHealthVariables.getIterationList().size() - 1; i > 0; i--) {
			IterationModel model = ProjectHealthVariables.getIterationList().get(i);
			if (model.getsName().equals("BackLog"))
				ProjectHealthVariables.setBackLogIteration(ProjectHealthRepos.metricsRepo
						.findByPNameAndSName(ProjectHealthVariables.getProjectName(), model.getsName()));

			else if (model.getState().equalsIgnoreCase("ACTIVE")) {
				citList.add(model);
			} else if (ProjectHealthVariables.getIterationList().get(i).getState().equalsIgnoreCase("CLOSED")) {
				pitList.add(model);
			}
		}
		ProjectHealthVariables.setCurrentIterationList(citList);
		ProjectHealthVariables.setpIterationList(pitList);
	}

	public String getAlmDefectToolType(String project, String tool) {
		String toolName = null;
		ConfigurationSettingRep configuartionRepo = ProjectHealthRepos.configRepo;
		ConfigurationSetting configuration = configuartionRepo.findByProjectName(project).get(0);
		for (ConfigurationToolInfoMetric m : configuration.getMetrics()) {
			if (m.getToolType().equals(tool) && m.getUrl() != null && !(m.getUrl().equals(""))) {
				toolName = m.getToolName().toUpperCase();
				break;
			}
		}
		return toolName;
	}

	public void getIteration() throws ProjectHealthException {
		ProjectHealthVariables.setCurrentIterationName(new ArrayList<>());
		ProjectHealthVariables.setIterationSprintArray(new String[5]);
		HealthData healthEntry = ProjectHealthRepos.healthDataRepo.findLastByProjectName(this.projectName);// previous
																											// projectHealth
																											// data
																											// from
																											// 'Health'
																											// collection

		ProjectHealthVariables.setHealthEntryLastEntry(healthEntry);

		ProjectHealthVariables.setConfig(ProjectHealthRepos.configRepo.findByProjectName(this.projectName).get(0));

		ProjectHealthVariables.getCurrentIterationList().forEach(cSprint -> {
			// runs for all 'Active' Sprints
			ProjectHealthVariables.setIterationSprintArray(new String[5]);
			datesArray = new long[4];
			int sprintDaysCompleted = 0;
			int totalSprintDays = 0;

			datesArray[0] = cSprint.getStDate();
			datesArray[1] = cSprint.getEndDate();
			iterationName = cSprint.getsName();
			sprintName = "Current";
			ProjectHealthVariables.setSprintId(cSprint.getsId());
			ProjectHealthVariables.setIteration(cSprint);
			ProjectHealthVariables.getCurrentIterationName().add(iterationName);
			for (long ts = datesArray[0]; ts < new Date().getTime(); ts = ts + 86400000) {
				sprintDaysCompleted++;
			}
			for (long ts = datesArray[0]; ts < datesArray[1]; ts = ts + 86400000) {
				totalSprintDays++;
			}
			if (sprintDaysCompleted > totalSprintDays) {
				sprintDaysCompleted = totalSprintDays;
			}

			// differentiate between JIRA and JIRA defect
			if (!(almType == null))
				ProjectHealthVariables.setMetricsListData(
						ProjectHealthRepos.metricsRepo.findByPNameAndPAlmType(this.projectName, almType));
			if (!(defectType == null)) {
				ProjectHealthVariables.setDefectsListData(
						ProjectHealthRepos.metricsRepo.findByPNameAndPAlmType(this.projectName, defectType));
			} else {
				ProjectHealthVariables.setDefectsListData(ProjectHealthVariables.getMetricsListData());
			}
			ProjectHealthVariables.getIterationSprintArray()[0] = cSprint.getsName();

			getHealthRuleData(sprintName, iterationName, ProjectHealthVariables.getIterationSprintArray(),
					sprintDaysCompleted, totalSprintDays);

		});

		pHC.colorForCurrentSprint(ProjectHealthVariables.getSprintPhaseHashMap(), iterationName);
		// iterate for last 3 iterations
		for (int i = 0; i < 3; i++) {
			// logic for c-1,c-2,c-3
			// store sprint name

			datesArray = new long[4];
			IterationModel model = ProjectHealthVariables.getpIterationList().get(i);
			long endD = model.getEndDate();
			String sprintN = null;
			if (ProjectHealthVariables.getCurrentIterationList().size() != 0) {
				endD = ProjectHealthVariables.getCurrentIterationList()
						.get(ProjectHealthVariables.getCurrentIterationList().size() - 1).getStDate();
				sprintN = ProjectHealthVariables.getCurrentIterationList()
						.get(ProjectHealthVariables.getCurrentIterationList().size() - 1).getsName();
			}
			if (!(almType == null))
				ProjectHealthVariables.setMetricsListData(
						ProjectHealthRepos.metricsRepo.findByPNameAndPAlmType(this.projectName, almType));
			if (!(defectType == null)) {
				ProjectHealthVariables.setDefectsListData(
						ProjectHealthRepos.metricsRepo.findByPNameAndPAlmType(this.projectName, defectType));
			} else if (defectType == null) {
				ProjectHealthVariables.setDefectsListData(ProjectHealthVariables.getMetricsListData());
			}

			if (i == 0) {
				datesArray[0] = model.getStDate();
				datesArray[1] = endD;
				sprintName = "C-1";
				ProjectHealthVariables.getIterationSprintArray()[0] = model.getsName();
				ProjectHealthVariables.getIterationSprintArray()[1] = sprintN;

			} else {

				datesArray[0] = model.getStDate();
				datesArray[1] = ProjectHealthVariables.getpIterationList().get(i - 1).getStDate();
			}
			if (i == 1) {
				sprintName = "C-2";
				ProjectHealthVariables.getIterationSprintArray()[0] = model.getsName();
				ProjectHealthVariables.getIterationSprintArray()[1] = ProjectHealthVariables.getpIterationList()
						.get(i - 1).getsName();
				ProjectHealthVariables.getIterationSprintArray()[2] = sprintN;
			} else if (i == 2) {
				sprintName = "C-3";
				ProjectHealthVariables.getIterationSprintArray()[0] = model.getsName();
				ProjectHealthVariables.getIterationSprintArray()[1] = ProjectHealthVariables.getpIterationList()
						.get(i - 1).getsName();
				ProjectHealthVariables.getIterationSprintArray()[2] = ProjectHealthVariables.getpIterationList()
						.get(i - 2).getsName();
				ProjectHealthVariables.getIterationSprintArray()[3] = sprintN;
			}
			iterationName = model.getsName();
			ProjectHealthVariables.setSprintId(model.getsId());
			ProjectHealthVariables.setIteration(model);
			List<String> names = ProjectHealthVariables.getPastIterationName();
			names.add(iterationName);
			ProjectHealthVariables.setPastIterationName(names);
			if (!(ProjectHealthVariables.getpIterationList().size() > 3)) {
				ppppIterationEndDate = ProjectHealthVariables.getpIterationList().get(4).getEndDate();
				startDate = ppppIterationEndDate;
			} else {
				startDate = model.getStDate();
			}
			ProjectHealthVariables.setMetricsListData(
					ProjectHealthRepos.metricsRepo.findByPNameAndSName(this.projectName, model.getsName()));
			// collecting all health rules
			getHealthRuleData(sprintName, iterationName, ProjectHealthVariables.getIterationSprintArray(), 0, 0);
			pHC.colorForPastSprint(ProjectHealthVariables.getSprintPhaseHashMap(), sprintName);

		}

		// Logic for NEXT/Future Sprint
		if (ProjectHealthVariables.getHealthEntryLastEntry() != null) {
			sprintName = "Next";
			iterationName = sprintName;
			getHealthRuleData(sprintName, iterationName, ProjectHealthVariables.getIterationSprintArray(), 0, 0);
			pHC.colorForNextSprint(ProjectHealthVariables.getSprintPhaseHashMap());
		}
		// HealthRulesPerDay healthRulesPerDay= new HealthRulesPerDay();
		// *---------============>*/ healthRulesPerDay.pHFirstRun(projectName);
	}

	public void getHealthRuleData(String sprintName, String iterationName, String[] iterationSprintArray,
			int newCounter, int totalDays) {
		// get all rule data from Health collection for project
		List<ProjectHealth> phaseList = ProjectHealthVariables.getProjecthealthdata();
		// go through each rules in past, current and future
		phaseList.forEach(projectHealth -> {
			String sprintNamex = "";
			if (sprintName.contains("C-"))
				sprintNamex = "Past";
			else if (sprintName.contains("Current"))
				sprintNamex = "Current";
			else
				sprintNamex = "Next";
			if (sprintNamex.equalsIgnoreCase(projectHealth.getSprintName())) {
				ProjectHealthVariables.setApplicationPhaseList(projectHealth.getApplicationPhaseList());
				ProjectHealthVariables.setProjectHealthConfigs(projectHealth.getConfig());
				ProjectHealthVariables.setSprintName(projectHealth.getSprintName());
				// set rag configuration value
				for (ProjectHealthConfig c : ProjectHealthVariables.getProjectHealthConfigs()) {
					if (c.getRagName().equals("Green")) {
						ProjectHealthVariables.setGreenFromValue(c.getFrom());
					} else if (c.getRagName().equals("Amber")) {
						ProjectHealthVariables.setAmberFromValue(c.getFrom());
						ProjectHealthVariables.setAmberToValue(c.getTo());
					}
				}
				try {
					getRuleSets(ProjectHealthVariables.getApplicationPhaseList(), sprintName,
							ProjectHealthVariables.getConfiguration(), iterationName, iterationSprintArray, newCounter,
							totalDays);
				} catch (Exception e) {
					LOGGER.error(e.getMessage());
				}
			}
		});

	}

	public void getRuleSets(List<ProjectHealthApplicationPhase> phaseList, String sprintName,
			List<ProjectHealthConfig> config, String iterationName, String[] iterationSprintArray, int newCounter,
			int totalDays) throws InstantiationException, NoSuchMethodException, SecurityException,
			IllegalAccessException, IllegalArgumentException, InvocationTargetException {
		ProjectHealthRuleSet sprintComparisionEntries = new ProjectHealthRuleSet();
		ProjectHealthVariables.setPhaseHashMap(new HashMap<String, Integer>());
		int arrCount = 0;
		List<HealthDataMetrics> healthDataList = new ArrayList<>();
		Object[] sprintProgressObjects = new Object[11];
		Iterator<ProjectHealthApplicationPhase> phaseIterator = phaseList.iterator();
		while (phaseIterator.hasNext()) {

			ProjectHealthApplicationPhase phase = phaseIterator.next();
			phaseWeightage = phase.getApplicationPhaseWeightage();
			sprintProgressObjects[0] = phase;
			sprintProgressObjects[1] = sprintComparisionEntries;
			sprintProgressObjects[2] = arrCount;
			sprintProgressObjects[3] = healthDataList;
			sprintProgressObjects[4] = datesArray;
			sprintProgressObjects[5] = startDate;
			sprintProgressObjects[6] = sprintName;
			sprintProgressObjects[7] = iterationName;
			sprintProgressObjects[8] = iterationSprintArray;
			sprintProgressObjects[9] = newCounter;
			sprintProgressObjects[10] = totalDays;

			if (phaseWeightage != 0) {
				String phaseName = phase.getApplicationPhaseName();
				if (sprintName.equals("Next"))
					pHFSC.updateFutureSprintHealth(sprintName, phase, phaseWeightage, healthDataList);

				else if (phaseName.equals("Sprint Progress")) {
					new ApplicationPhaseRules().sprintProgressRules(sprintProgressObjects);
				} else if (phaseName.equals("Functional Testing")) {
					new ApplicationPhaseRules().functionalTestingRules(sprintProgressObjects);
				}

				else if (phaseName.equals("Code Quality")) {
					new ApplicationPhaseRules().codeQualityRules(sprintProgressObjects);
				} else if (phaseName.equals("Build Quality")) {
					new ApplicationPhaseRules().BuildQualityRules(sprintProgressObjects);
				}
				HealthProjectSprintMetrics sprintMetric = null;
				sprintMetric = new HealthProjectSprintMetrics();
				sprintMetric.setHealthdataMetrics(healthDataList);
				sprintMetric.setPhaseName(phaseName);
				sprintMetric.setSprintName(sprintName);
				sprintMetric.setIterationName(iterationName);
				sprintMetric.setPhaseStatus(pHC.calculateProjectHealthApplicationsStatus(healthDataList, phaseName,
						iterationName, phaseWeightage));
				sprintMetric.setPhaseValue((int) ProjectHealthVariables.getTotalWeightage());
				ProjectHealthVariables.getSprintDataList().add(sprintMetric);
				healthDataList = new ArrayList<>();

			}

		}
	}
}
