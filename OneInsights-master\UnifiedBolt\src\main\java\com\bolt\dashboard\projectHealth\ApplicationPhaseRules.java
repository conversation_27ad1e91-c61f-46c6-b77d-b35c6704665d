package com.bolt.dashboard.projectHealth;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.bolt.dashboard.core.model.HealthDataMetrics;
import com.bolt.dashboard.core.model.ProjectHealthApplicationPhase;
import com.bolt.dashboard.core.model.ProjectHealthRuleSet;
import com.bolt.dashboard.sonar.SonarApplication;

public class ApplicationPhaseRules {
	ProjectHealthSprintProgress pHSP = new ProjectHealthSprintProgress();
	ProjectHealthFunctionalTesting pHFT = new ProjectHealthFunctionalTesting();
	ProjectHealthCodeQuality pHCQ = new ProjectHealthCodeQuality();
	ProjectHealthBuildQuality pHBQ = new ProjectHealthBuildQuality();
	String sprintName = null;
	String iterationName = null;
	int newCounter = 0;
	int totalDays = 0;
	List<String> spRuleNames = Arrays.asList("Task Completion (Open vs Closed)", "Velocity (Story Open vs Closed)",
			"Req Volatility - Task","Req Volatility - Story", "Task(Remaining vs total %)", "Team Efficiency", "Sprint Completion", "Task Risk",
			"Effort Overrun");
	List<String> ftRuleNames = Arrays.asList("Bug(Open VS Closed %)", "Defects per Story", "Defects ReOpened",
			"Number of Critical Bug", "Stories in backlog", ProjectHealthVariables.getRULE_CRITICAL_DEFECT());
	List<String> cqRuleNames = Arrays.asList("Technical Debt", "UI Code Coverage", "Service Code Coverage");

	private static final Logger LOGGER = LogManager.getLogger(ApplicationPhaseRules.class);

	public void sprintProgressRules(Object[] sprintProgressObjects) throws InstantiationException, NoSuchMethodException, SecurityException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {
		ProjectHealthApplicationPhase phase = (ProjectHealthApplicationPhase) sprintProgressObjects[0];
		ProjectHealthRuleSet sprintComparisionEntries = (ProjectHealthRuleSet) sprintProgressObjects[1];
		int arrCount = (int) sprintProgressObjects[2];
		@SuppressWarnings("unchecked")
		List<HealthDataMetrics> healthDataList = (List<HealthDataMetrics>) sprintProgressObjects[3];
        int newLength = ((long[])sprintProgressObjects[4]).length;	
		long[] datesArray = Arrays.copyOf(((long[])sprintProgressObjects[4]), newLength);
		long startDate = (long) sprintProgressObjects[5];
		sprintName = (String) sprintProgressObjects[6];
		iterationName = (String) sprintProgressObjects[7];
		newCounter = (int) sprintProgressObjects[9];
		totalDays = (int) sprintProgressObjects[10];

		List<ProjectHealthRuleSet> ruleSetList = phase.getRuleSet();
		Iterator<ProjectHealthRuleSet> projectHealthRuleSetIterator = ruleSetList.iterator();

		while (projectHealthRuleSetIterator.hasNext()) {
			ProjectHealthVariables.setRuleHealthEntries(new HealthDataMetrics());
			ProjectHealthRuleSet set = projectHealthRuleSetIterator.next();
			if (set.getSelect()) {
				sprintComparisionEntries.setDeveloperState(set.getDeveloperState());
				sprintComparisionEntries.setOperator(set.getOperator());
				sprintComparisionEntries.setRuleName(set.getRuleName());
				sprintComparisionEntries.setSelect(set.getSelect());
				sprintComparisionEntries.setSprintName(set.getSprintName());
				datesArray[3] = startDate;
				
					sprintComparisionEntries.setValue(setCEValue(set, sprintProgressObjects));
				

				sprintComparisionEntries.setWeightage(set.getWeightage());
				for (String ruleName : spRuleNames) {
					if (ruleName.equals(sprintComparisionEntries.getRuleName())) {
						if ("Sprint Completion".equals(ruleName)) {
							pHSP.ruleIterationCompletion(iterationName, sprintComparisionEntries);
						} else {
							
							String methodName = getMethodName(ruleName);
							Method method = ProjectHealthSprintProgress.class.getMethod(methodName,
									ProjectHealthRuleSet.class, long[].class);
							method.invoke(new ProjectHealthSprintProgress(), sprintComparisionEntries, datesArray);
							
							// pHSP.test(sprintComparisionEntries, datesArray);
						}
						ProjectHealthVariables.getRuleHealthEntries().setGoal(set.getValue());
						healthDataList.add(arrCount, ProjectHealthVariables.getRuleHealthEntries());
						arrCount++;
					}
				}

			}

		}
	}
	
	public String setCEValue(ProjectHealthRuleSet set,Object[] sprintProgressObjects){
		if (newCounter != 0 && totalDays != 0 && (!"Technical debt".equals(set.getRuleName()))
				&& (!"Number of Critical Bug".equals(set.getRuleName())) && !(set.getValue() == null)) {
			String value = Integer.toString((int) (Integer.parseInt(set.getValue())
					/ (double) (int) sprintProgressObjects[10] * (int) sprintProgressObjects[9]));
			return value;
		} else {
			return set.getValue();
		}
		
		
	}

	public void functionalTestingRules(Object[] sprintProgressObjects) throws NoSuchMethodException, SecurityException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {

		ProjectHealthApplicationPhase phase = (ProjectHealthApplicationPhase) sprintProgressObjects[0];
		ProjectHealthRuleSet sprintComparisionEntries = (ProjectHealthRuleSet) sprintProgressObjects[1];
		int arrCount = (int) sprintProgressObjects[2];
		@SuppressWarnings("unchecked")
		List<HealthDataMetrics> healthDataList = (List<HealthDataMetrics>) sprintProgressObjects[3];
        int newLength = ((long[])sprintProgressObjects[4]).length;	
		long[] datesArray = Arrays.copyOf(((long[])sprintProgressObjects[4]), newLength);
		sprintName = (String) sprintProgressObjects[6];
		iterationName = (String) sprintProgressObjects[7];
		newCounter = (int) sprintProgressObjects[9];
		totalDays = (int) sprintProgressObjects[10];
		List<ProjectHealthRuleSet> ruleSetList = phase.getRuleSet();
		Iterator<ProjectHealthRuleSet> projectHealthRuleSetIterator = ruleSetList.iterator();

		while (projectHealthRuleSetIterator.hasNext()) {
			ProjectHealthVariables.setRuleHealthEntries(new HealthDataMetrics());
			ProjectHealthRuleSet set = projectHealthRuleSetIterator.next();
			if (set.getSelect()) {
				sprintComparisionEntries.setDeveloperState(set.getDeveloperState());
				sprintComparisionEntries.setOperator(set.getOperator());
				sprintComparisionEntries.setRuleName(set.getRuleName());
				sprintComparisionEntries.setSelect(set.getSelect());
				sprintComparisionEntries.setSprintName(set.getSprintName());
				String value1 = null;
				if (newCounter != 0 && totalDays != 0 && (!"Technical debt".equals(set.getRuleName()))
						&& (!"Number of Critical Bug".equals(set.getRuleName()))) {
					if (set.getValue().contains("h"))
						value1 = set.getValue().replace("h", "");
					else if (set.getValue().contains("m"))
						value1 = set.getValue().replace("m", "");
					else
						value1 = set.getValue();
					String value = Integer.toString((int) (Integer.parseInt(value1)
							/ (double) (int) sprintProgressObjects[10] * (int) sprintProgressObjects[9]));
					sprintComparisionEntries.setValue(value);
				} else {
					sprintComparisionEntries.setValue(set.getValue());
				}

				// sprintComparisionEntries.setValue(set.getValue());
				sprintComparisionEntries.setWeightage(set.getWeightage());
				for (String ruleName : ftRuleNames) {
					if (ruleName.equals(sprintComparisionEntries.getRuleName())) {

						String methodName = getMethodName(ruleName);
						
						Method method = ProjectHealthFunctionalTesting.class.getMethod(methodName,
								ProjectHealthRuleSet.class, long[].class);
						method.invoke(new ProjectHealthFunctionalTesting(), sprintComparisionEntries, datesArray);
						// pHSP.test(sprintComparisionEntries, datesArray);

						ProjectHealthVariables.getRuleHealthEntries().setGoal(set.getValue());
						healthDataList.add(arrCount, ProjectHealthVariables.getRuleHealthEntries());
						arrCount++;
					}
				}
			}

		}
	}

	public void codeQualityRules(Object[] sprintProgressObjects) throws NoSuchMethodException, SecurityException, IllegalAccessException, IllegalArgumentException, InvocationTargetException {

		ProjectHealthApplicationPhase phase = (ProjectHealthApplicationPhase) sprintProgressObjects[0];
		ProjectHealthRuleSet sprintComparisionEntries = (ProjectHealthRuleSet) sprintProgressObjects[1];
		int arrCount = (int) sprintProgressObjects[2];
		@SuppressWarnings("unchecked")
		List<HealthDataMetrics> healthDataList = (List<HealthDataMetrics>) sprintProgressObjects[3];
		
        int newLength = ((long[])sprintProgressObjects[4]).length;	
		long[] datesArray = Arrays.copyOf(((long[])sprintProgressObjects[4]), newLength);
		sprintName = (String) sprintProgressObjects[6];
		iterationName = (String) sprintProgressObjects[7];
		// String[] iterationSprintArray = (String[]) sprintProgressObjects[8];
		newCounter = (Integer) sprintProgressObjects[9];
		totalDays = (Integer) sprintProgressObjects[10];
		List<ProjectHealthRuleSet> ruleSetList = phase.getRuleSet();
		Iterator<ProjectHealthRuleSet> projectHealthRuleSetIterator = ruleSetList.iterator();
		pHCQ.getCodeQualityInfo(ProjectHealthVariables.getProjectName(), datesArray);
		pHCQ.getSprintCodeCoverageData();
		while (projectHealthRuleSetIterator.hasNext()) {
			ProjectHealthVariables.setRuleHealthEntries(new HealthDataMetrics());
			ProjectHealthRuleSet set = projectHealthRuleSetIterator.next();

			if (set.getSelect()) {
				if (newCounter != 0 && totalDays != 0 && (!"Technical debt".equals(set.getRuleName()))
						&& (!"Number of Critical Bug".equals(set.getRuleName()))) {
					String value = Integer.toString((int) (Integer.parseInt(set.getValue())
							/ (double) (int) sprintProgressObjects[10] * (int) sprintProgressObjects[9]));
					sprintComparisionEntries.setValue(value);
				} else {
					sprintComparisionEntries.setValue(set.getValue());
				}
				sprintComparisionEntries.setDeveloperState(set.getDeveloperState());
				sprintComparisionEntries.setOperator(set.getOperator());
				sprintComparisionEntries.setRuleName(set.getRuleName());
				sprintComparisionEntries.setSelect(set.getSelect());
				sprintComparisionEntries.setSprintName(set.getSprintName());

				sprintComparisionEntries.setWeightage(set.getWeightage());
				if (cqRuleNames.contains(sprintComparisionEntries.getRuleName())) {
					for (String ruleName : cqRuleNames) {
						if (ruleName.equals(sprintComparisionEntries.getRuleName())) {

							String methodName = getMethodName(ruleName);
							Method method = ProjectHealthCodeQuality.class.getMethod(methodName,
									ProjectHealthRuleSet.class, long[].class);
							
							method.invoke(new ProjectHealthCodeQuality(), sprintComparisionEntries, datesArray);
							// pHSP.test(sprintComparisionEntries, datesArray);

						}
					}
				} else {
					pHCQ.callCodeQualityBlock(sprintComparisionEntries, ProjectHealthVariables.getRuleHealthEntries());

				}
				ProjectHealthVariables.getRuleHealthEntries().setGoal(set.getValue());
				healthDataList.add(arrCount, ProjectHealthVariables.getRuleHealthEntries());
				arrCount++;

			}

		}
	}

	public void BuildQualityRules(Object[] sprintProgressObjects) {

		ProjectHealthApplicationPhase phase = (ProjectHealthApplicationPhase) sprintProgressObjects[0];
		ProjectHealthRuleSet sprintComparisionEntries = (ProjectHealthRuleSet) sprintProgressObjects[1];
		int arrCount = (int) sprintProgressObjects[2];
		@SuppressWarnings("unchecked")
		List<HealthDataMetrics> healthDataList = (List<HealthDataMetrics>) sprintProgressObjects[3];
		int newLength = ((long[])sprintProgressObjects[4]).length;
		
		long[] datesArray = Arrays.copyOf(((long[])sprintProgressObjects[4]), newLength);
		// long startDate = (long) sprintProgressObjects[5];
		sprintName = (String) sprintProgressObjects[6];
		iterationName = (String) sprintProgressObjects[7];
		// String[] iterationSprintArray = (String[]) sprintProgressObjects[8];
		newCounter = (int) sprintProgressObjects[9];
		totalDays = (int) sprintProgressObjects[10];
		List<ProjectHealthRuleSet> ruleSetList = phase.getRuleSet();
		Iterator<ProjectHealthRuleSet> projectHealthRuleSetIterator = ruleSetList.iterator();
		pHBQ.buildDataInit(datesArray);
		while (projectHealthRuleSetIterator.hasNext()) {
			ProjectHealthVariables.setRuleHealthEntries(new HealthDataMetrics());
			ProjectHealthRuleSet set = projectHealthRuleSetIterator.next();

			if (set.getSelect()) {
				sprintComparisionEntries.setDeveloperState(set.getDeveloperState());
				sprintComparisionEntries.setOperator(set.getOperator());
				sprintComparisionEntries.setRuleName(set.getRuleName());
				sprintComparisionEntries.setSelect(set.getSelect());
				sprintComparisionEntries.setSprintName(set.getSprintName());
				String value1 = null;
				if (newCounter != 0 && totalDays != 0 && (!"Technical debt".equals(set.getRuleName()))
						&& (!"Number of Critical Bug".equals(set.getRuleName()))) {
					if (set.getValue().contains("h"))
						value1 = set.getValue().replace("h", "");
					else if (set.getValue().contains("m"))
						value1 = set.getValue().replace("m", "");
					else
						value1 = set.getValue();
					String value = Integer.toString((int) (Integer.parseInt(value1)
							/ (double) (int) sprintProgressObjects[10] * (int) sprintProgressObjects[9]));
					sprintComparisionEntries.setValue(value);
				} else {
					sprintComparisionEntries.setValue(set.getValue());
				}

				// sprintComparisionEntries.setValue(set.getValue());
				sprintComparisionEntries.setWeightage(set.getWeightage());

				if ("No of Build Failures".equals(set.getRuleName())) {
					ProjectHealthVariables.getRuleHealthEntries().setGoal(set.getValue());
					pHBQ.ruleNoOfBuildFailuresBlock(sprintComparisionEntries,
							ProjectHealthVariables.getRuleHealthEntries());
					healthDataList.add(arrCount, ProjectHealthVariables.getRuleHealthEntries());
					arrCount++;

				} else if ("MTTR".equals(set.getRuleName())) {
					ProjectHealthVariables.getRuleHealthEntries().setGoal(set.getValue());
					pHBQ.ruleMTTRBlock(sprintComparisionEntries, ProjectHealthVariables.getRuleHealthEntries(),
							datesArray);
					healthDataList.add(arrCount, ProjectHealthVariables.getRuleHealthEntries());
					arrCount++;
				} else if ("Avg Build Time".equals(set.getRuleName())) {
					ProjectHealthVariables.getRuleHealthEntries().setGoal(set.getValue());
					pHBQ.ruleBuildAvgTimeBlock(sprintComparisionEntries, ProjectHealthVariables.getRuleHealthEntries());
					healthDataList.add(arrCount, ProjectHealthVariables.getRuleHealthEntries());
					arrCount++;
				}
			}

		}
	}
	
	public String getMethodName(String ruleName){
		String methodName = ruleName.replaceAll(" ", "");
		methodName = methodName.replaceAll("\\p{P}", "");
		methodName = methodName.replaceAll("\\%\\-", "");
		methodName = Character.toLowerCase(methodName.charAt(0)) + methodName.substring(1);

		return methodName;
	}
}
