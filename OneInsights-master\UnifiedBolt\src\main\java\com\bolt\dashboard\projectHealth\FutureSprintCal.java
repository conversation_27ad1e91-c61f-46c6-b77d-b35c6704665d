package com.bolt.dashboard.projectHealth;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.bolt.dashboard.core.model.HealthDataMetrics;
import com.bolt.dashboard.core.model.HealthProjectSprintMetrics;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.ProjectHealthApplicationPhase;
import com.bolt.dashboard.core.model.ProjectHealthRuleSet;

public class FutureSprintCal {
	private static final Logger LOGGER = LogManager.getLogger(FutureSprintCal.class);
	ProjectHealthCalculation pHC= new ProjectHealthCalculation();
	ProjectHealthCodeQuality pHCQ=new ProjectHealthCodeQuality();
	Map<Integer, Map<String, String>> perSprintOperatorMap= null;
	Map<Integer, Map<String, Integer>> perSprintPointsMap= null;
	Map<Integer, Map<String, Integer>> perSprintWeightageMap= null;
	Map<Integer, Map<String, Integer>> perSprintMap= null;
	double backlogPlannedStoryVal=0;
	/*------------Linear regression algorithm data accumulation for  future sprint ----------------------------- */
	public void updateFutureSprintHealth(String sprintName
					,ProjectHealthApplicationPhase phase
					,int phaseWeightage, List<HealthDataMetrics> nextHealthList) {
		int[] dependentArr = null, independentArr = null;
		int goalForNextSprint = 0;
		int goalWeightageForNextSprint = 0;

		String ruleOperatorForNextSprint = null;
		String ruleNameForNextSprint = null;

		HealthProjectSprintMetrics currentSprintRules = new HealthProjectSprintMetrics();
		perSprintOperatorMap= new HashMap<Integer, Map<String, String>>();
		perSprintPointsMap= new HashMap<Integer, Map<String, Integer>>();
		perSprintWeightageMap= new HashMap<Integer, Map<String, Integer>>();
		perSprintMap= new HashMap<Integer, Map<String, Integer>>();
		currentSprintRules.setSprintName(sprintName);

		List<String> pastRuleNameList = new ArrayList<String>();
		for (ProjectHealthRuleSet z : phase.getRuleSet()) {
			pastRuleNameList.add(z.getRuleName());
		}

		int count = 0;
		for (HealthProjectSprintMetrics healthProjectSprintMetrics :ProjectHealthVariables.getHealthEntryLastEntry().getShealthdataMetrics()) {
			
			
			
			for (HealthDataMetrics healthMetrics : healthProjectSprintMetrics.getHealthdataMetrics()) {
				pastRuleNameList.remove(healthMetrics.getRuleName());
			}
			String entryPhaseName = healthProjectSprintMetrics.getPhaseName();
			String sName = healthProjectSprintMetrics.getSprintName();
			if (entryPhaseName != null && !"Next".equals(sName) && entryPhaseName.equals(phase.getApplicationPhaseName())) {

				ProjectHealthVariables.setGoalHashMap(new HashMap<String, Integer>());
				ProjectHealthVariables.setPointsHashMap(new HashMap<String, Integer>());
				ProjectHealthVariables.setOperatorHashMap(new HashMap<String, String>());
				ProjectHealthVariables.setWeightageHashMap(new HashMap<String, Integer>());
				HealthProjectSprintMetrics currentIterationData = healthProjectSprintMetrics;

				if (sName.matches("C-3|C-2|C-1|Current")) {
					for (int k = 0; k < currentIterationData.getHealthdataMetrics().size(); k++) {
						HealthDataMetrics consideredHealthdataMetrics = currentIterationData.getHealthdataMetrics()
								.get(k);
						String consideredRuleName = consideredHealthdataMetrics.getRuleName();
						if (consideredHealthdataMetrics.getGoal() != null) {
							if (consideredHealthdataMetrics.getGoal().toString().contains("h")
									|| consideredHealthdataMetrics.getGoal().toString().contains("m")) {
								String goal = consideredHealthdataMetrics.getGoal();
								long goalValue = ProjectHealthBuildQuality.getTimeInMilliSeconds(goal);

								consideredHealthdataMetrics.setGoal(Long.toString(goalValue));
							}
							ProjectHealthVariables.getGoalHashMap().put(consideredRuleName,
									Integer.parseInt(consideredHealthdataMetrics.getGoal()));
							ProjectHealthVariables.getOperatorHashMap().put(consideredRuleName,
									consideredHealthdataMetrics.getOperator());
							ProjectHealthVariables.getWeightageHashMap().put(consideredRuleName,
									consideredHealthdataMetrics.getWeightage());
							ProjectHealthVariables.getPointsHashMap().put(consideredRuleName,
									consideredHealthdataMetrics.getSprintValue());
						}

					}

				}
				for (String pastRuleName : pastRuleNameList) {
					ProjectHealthRuleSet consideredHealthdataMetrics = null;
					
					for (ProjectHealthRuleSet rule :  phase.getRuleSet()) {
						if (pastRuleName.equals(rule.getRuleName())&&rule.getSelect()==true) {
							consideredHealthdataMetrics = rule;

							String consideredRuleName = pastRuleName;

							if ("Stories in backlog".equals(consideredRuleName)) {
								ProjectHealthVariables.getGoalHashMap().put(consideredRuleName,
										Integer.parseInt(consideredHealthdataMetrics.getValue()));
								ProjectHealthVariables.getOperatorHashMap().put(consideredRuleName,
										consideredHealthdataMetrics.getOperator());
								ProjectHealthVariables.getWeightageHashMap().put(consideredRuleName,
										consideredHealthdataMetrics.getWeightage());
								for ( MetricsModel backLogData : ProjectHealthVariables.getBackLogIteration()) {
									try {
										if (ProjectHealthVariables.getSTORY()
												.equals(backLogData.getType())) {
													backlogPlannedStoryVal++; 
										}
									} catch (NullPointerException e) {
										LOGGER.info(e);
										continue;
									}
								}
								ProjectHealthVariables.getPointsHashMap().put(consideredRuleName,
										(int) backlogPlannedStoryVal);
							}

							else if (consideredRuleName.equals(ProjectHealthVariables.getRULE_CRITICAL_DEFECT())) {
								double criticalBugVal = 0;
								ProjectHealthVariables.getGoalHashMap().put(consideredRuleName,
										Integer.parseInt(consideredHealthdataMetrics.getValue()));
								ProjectHealthVariables.getOperatorHashMap().put(consideredRuleName,
										consideredHealthdataMetrics.getOperator());
								ProjectHealthVariables.getWeightageHashMap().put(consideredRuleName,
										consideredHealthdataMetrics.getWeightage());
								for ( MetricsModel backLogData : ProjectHealthVariables.getBackLogIteration()) {
									try {

										if (ProjectHealthVariables.getBug().equals(backLogData.getType())) {
											if (getPrioritySeverity(backLogData,
													ProjectHealthVariables.getCriticalPriority())) {
												criticalBugVal++;
											}
										}

									} catch (NullPointerException e) {
										LOGGER.info(e);
										continue;
									}
								}
								double doMathResult = (pHC.doMath(criticalBugVal,
										Integer.parseInt(consideredHealthdataMetrics.getValue()),
										consideredHealthdataMetrics.getOperator()));

								if (doMathResult == 1) {
									ProjectHealthVariables.getPointsHashMap().put(consideredRuleName,
											consideredHealthdataMetrics.getWeightage());
								} else if (doMathResult> Integer
										.parseInt(consideredHealthdataMetrics.getValue())) {
									ProjectHealthVariables.getPointsHashMap().put(consideredRuleName, 0);
								} else {
									int diff = (int) Math.floor(doMathResult
											* consideredHealthdataMetrics.getWeightage()
											/ Integer.parseInt(consideredHealthdataMetrics.getValue()));
									ProjectHealthVariables.getPointsHashMap().put(consideredRuleName,
											Math.abs(consideredHealthdataMetrics.getWeightage() - diff));
								}
							} else if ("Code Quality".equals(phase.getApplicationPhaseName())) {
								HealthDataMetrics healthDataMetrics=new HealthDataMetrics();
								ProjectHealthVariables.setRuleHealthEntries(new HealthDataMetrics());
								pHCQ.callCodeQualityBlock(consideredHealthdataMetrics,
										healthDataMetrics);
								ProjectHealthVariables.getGoalHashMap().put(consideredHealthdataMetrics.getRuleName(),
										(int) (ProjectHealthVariables.getRuleHealthEntries().getValue()));
								ProjectHealthVariables.getOperatorHashMap().put(
										consideredHealthdataMetrics.getRuleName(),
										ProjectHealthVariables.getRuleHealthEntries().getOperator());
								ProjectHealthVariables.getWeightageHashMap().put(
										consideredHealthdataMetrics.getRuleName(),
										ProjectHealthVariables.getRuleHealthEntries().getWeightage());
								ProjectHealthVariables.getPointsHashMap().put(
										consideredHealthdataMetrics.getRuleName(),
										ProjectHealthVariables.getRuleHealthEntries().getSprintPoints());
							}
						}
					}

				}

				if (!ProjectHealthVariables.getOperatorHashMap().isEmpty()) {
					perSprintOperatorMap.put(count,
							ProjectHealthVariables.getOperatorHashMap());
					perSprintMap.put(count, ProjectHealthVariables.getGoalHashMap());
					perSprintPointsMap.put(count,
							ProjectHealthVariables.getPointsHashMap());
					perSprintWeightageMap.put(count,
							ProjectHealthVariables.getWeightageHashMap());
					count++;
				}
			}

		}
		int arr = 0;
		Map<String, Integer> ruleGoal = perSprintMap.get(0);
		Map<String, Integer> rulePoints = perSprintPointsMap.get(0);
		List<String> keys0 = new ArrayList<String>(ruleGoal.keySet());
		List<String> keys1 = new ArrayList<String>(rulePoints.keySet());
		dependentArr = new int[perSprintMap.size()];
		independentArr = new int[perSprintMap.size()];
		for (int r = 0; r < keys1.size(); r++) {

			HealthDataMetrics metricsNext = new HealthDataMetrics();

			Object obj = keys0.get(r);

			for (int q = 0; q < perSprintMap.size(); q++) {

				Map<String, Integer> ruleGoal1 = perSprintMap.get(q);
				Map<String, Integer> rulePoints1 = perSprintPointsMap.get(q);
				Map<String, String> ruleOperator1 = perSprintOperatorMap.get(q);
				if (ruleGoal1.get(obj) != null) {
					dependentArr[q] = ruleGoal1.get(obj);
					independentArr[q] = rulePoints1.get(obj);
					goalForNextSprint = ruleGoal.get(obj);
					ruleOperatorForNextSprint = ruleOperator1.get(obj);
					goalWeightageForNextSprint = getGoalWeightageForNextSprint( phase.getRuleSet(), (String) obj);
					ruleNameForNextSprint = (String) obj;
				}
			}

			metricsNext.setGoal(Integer.toString(goalForNextSprint).toString());
			metricsNext.setGoalValue(Integer.toString(goalForNextSprint).toString());
			metricsNext.setOperator(ruleOperatorForNextSprint);
			//

			metricsNext.setWeightage(goalWeightageForNextSprint);
			int sum = 0;
			double p1;
			for (int arrayValue : independentArr) {
				sum = sum + arrayValue;
			}
			if (sum != 0/*&&healthMetrics.get<=2*/) {
				p1 = futurePointsAllocation(dependentArr, independentArr, goalForNextSprint);
			} else {
				p1 = 0;
			}
			/*if (bidRepo.findByCName(chocolateName).size() == 0)
				expectedNextSP = expectedSPAllocation(eSP, sBP, nextMinSP);
			else
				expectedNextSP = getCorrectedPValue(bidList.get(dataSize - 1).getBeta(),
						bidList.get(dataSize - 1).getBeta1(), nextMinSP);*/
			double doMathResult=pHC.doMath(p1, goalForNextSprint, ruleOperatorForNextSprint);
			metricsNext.setRuleName(ruleNameForNextSprint);
			pHC.calculateRuleHealth(doMathResult, metricsNext);

			if ("Stories in backlog".equals(metricsNext.getRuleName())) {
              doMathResult=pHC.doMath(backlogPlannedStoryVal,
						Double.parseDouble(metricsNext.getGoal()), metricsNext.getOperator());
				if (doMathResult == 1) {
					metricsNext.setSprintPoints(metricsNext.getWeightage());
				} else if (doMathResult > Integer.parseInt(metricsNext.getGoal())) {
					metricsNext.setSprintPoints(0);
				} else {
					int diff = (int) Math.floor(doMathResult * metricsNext.getWeightage()
							/ Integer.parseInt(metricsNext.getGoal()));
					metricsNext.setSprintPoints(Math.abs(metricsNext.getWeightage() - diff));
				}

			}

			metricsNext.setSprintValue(Math.round((int) p1));

			nextHealthList.add(arr, metricsNext);
			arr++;

		}

	}

	public int getGoalWeightageForNextSprint(List<ProjectHealthRuleSet> rules, String ruleName) {
		int weightage = 0;
		for (ProjectHealthRuleSet rule : rules) {
			if (rule.getRuleName().equals(ruleName)) {
				weightage = rule.getWeightage();
				break;
			}
		}
		return weightage;
	}
	/*------------Linear regression algorithm data accumulation for  future sprint ends----------------------------- */

	/*------------Linear regression algorithm ----------------------------- */

	public double futurePointsAllocation(int[] dependent, int[] independent, int independentFutureValue) {
		int sumDependent = 0, meanDependent = 0;
		int sumIndependent = 0, meanIndependent = 0;
		int[] meanDependentDiff = null, meanIndependentDiff = null;
		meanDependentDiff = new int[dependent.length];
		meanIndependentDiff = new int[independent.length];
		double beta = 0, beta1 = 0;
		int numerator = 0, denominator = 0;
		double predictedValue = 0;

		for (int i = 0; i < dependent.length; i++) {
			sumDependent += dependent[i];
		}
		for (int j = 0; j < independent.length; j++) {
			sumIndependent += independent[j];
		}

		meanDependent = sumDependent / dependent.length;
		meanIndependent = sumIndependent / independent.length;

		for (int k = 0; k < dependent.length; k++) {
			meanDependentDiff[k] = dependent[k] - meanDependent;
		}
		for (int l = 0; l < independent.length; l++) {
			meanIndependentDiff[l] = independent[l] - meanIndependent;
		}

		for (int m = 0; m < meanDependentDiff.length; m++) {
			numerator += meanDependentDiff[m] * meanIndependentDiff[m];
		}
		for (int n = 0; n < meanDependentDiff.length; n++) {
			denominator += Math.pow(meanDependentDiff[n], 2);
		}
		if (numerator != 0 && denominator != 0)
			beta1 = (double) numerator / denominator;
		else
			beta1 = 0;
		beta = meanIndependent - (beta1 * meanDependent);

		predictedValue = beta + (beta1 * independentFutureValue);
		return predictedValue;
	}

	/*------------Linear regression algorithm ends----------------------------- */
	
	/*------------Prediction correction---------------------------------------*/
	/*private double getCorrectedPValue(double lastBeta, double lastBeta1, int nextMinSP) {
		double currentBeta = 0, currentBeta1 = 0, alpha = 0.01, predictedValue = 0;
		for (int i = 1; i < bidList.size(); i++) {
			BidPDetails lastRecord = bidList.get(i - 1);
			if (i == 1)
				predictedValue = lastRecord.getExpectedSellingPrice();

			int lastSellingPrice = lastRecord.getSellPrice();
			int error = (int) (predictedValue - lastSellingPrice);

			currentBeta = lastBeta - alpha * error;
			currentBeta1 = lastBeta1 - alpha * error * nextMinSP;

			lastBeta = currentBeta;
			lastBeta1 = currentBeta1;
			predictedValue = currentBeta + (currentBeta1 * nextMinSP);

			if (i == dataSize - 1) {
				thisBid.setBeta(currentBeta);
				thisBid.setBeta1(currentBeta1);
			}
		}

		return predictedValue;

	}*/
	/*------------Prediction correction ends---------------------------------------*/
	// Checks for Priority or severity
		public boolean getPrioritySeverity(MetricsModel sprintData, List<String> criticalPriority) {
			if (ProjectHealthVariables.getPriorityName() != null)
				if (ProjectHealthVariables.getPriorityName().equals("almPriority")) {
					ProjectHealthVariables.setPriorityFunction(sprintData.getPriority());
				} else {
					ProjectHealthVariables.setPriorityFunction(sprintData.getSeverity());
				}

			boolean found = false;
			for (String element : criticalPriority) {
				if (!(ProjectHealthVariables.getPriorityFunction() == null)
						&& ProjectHealthVariables.getPriorityFunction().equals(element)) {
					found = true;
					break;
				}
			}
			return found;
		}
}
