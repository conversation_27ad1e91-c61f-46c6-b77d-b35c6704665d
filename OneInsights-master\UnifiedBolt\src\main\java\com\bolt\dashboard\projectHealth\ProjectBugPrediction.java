package com.bolt.dashboard.projectHealth;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.IntStream;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import com.bolt.dashboard.core.model.HealthData;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.sprintPredictedBugMetrics;
import com.bolt.dashboard.core.repository.MetricRepo;

public class ProjectBugPrediction {
	MetricRepo metricRepo = null;
	private static final Logger LOGGER = LogManager.getLogger(ProjectBugPrediction.class);

	@Autowired
	public ProjectBugPrediction() {

	}

	/*--------------------------------------------NAIVE BAYES Implementation----------------------------*/
	public HealthData predictBug(String projectName,
			HealthData health/*
								 * ,long startDateofpastSprint,long
								 * endDateofpastSprint,long
								 * startDateofppastSprint, long
								 * endDateofppastSprint,long
								 * startDateofpppastSprint ,long
								 * endDateofpppastSprint,long
								 * endDateofppppastSprint, long
								 * startDateofCurrentSprint,long
								 * endDateofCurrentSprint
								 */) {
		List<IterationModel> iterationList = null;
		List<MetricsModel> metricsList = null;
		iterationList = ProjectHealthRepos.iterationRepo.findByPName(projectName);
		int[] tempDefect = null;
		int[] tempCount = null;
		int[] defect = null;
		int[] lastTask = null;
		int[] lastDefect = null;
		int[] noDefectTasks = null;
		int[] bugPredict = null;
		int[] task = null;
		List<String> teamMemberList = new ArrayList<>();
		String[] teamMember = null;
		int count;
		int defectCount;
		int totalPrediction = 0;
		String assgined = null;
		int index = -1;
		int teamMemberIndex = -1;
		int teamIndex = -1;
		List<sprintPredictedBugMetrics> sprintPredictedBugMetrics = new ArrayList<>();
		// List<IterationModel> IterationIt = (List<IterationModel>)
		// iterationList.iterator();
		if (!iterationList.isEmpty()) {
			sprintPredictedBugMetrics firstSprintbugMetrics = new sprintPredictedBugMetrics();
			firstSprintbugMetrics.setSprintName(iterationList.get(0).getsName());
			firstSprintbugMetrics.setPredictedValue(0);
			sprintPredictedBugMetrics.add(firstSprintbugMetrics);
		}
		for (IterationModel sprint : iterationList) {
			String iterationName = iterationList.get(index + 1).getsName();
			if (!iterationName.equals("BackLog")) {
				sprintPredictedBugMetrics bugMetrics = new sprintPredictedBugMetrics();
				index += 1;
				metricsList = ProjectHealthRepos.metricsRepo.findByPNameAndSName(projectName, iterationName);
				for (MetricsModel sprintMetrics : metricsList) {
					assgined = sprintMetrics.getAssgnTo();
					if (teamMemberList.size() == 0) {
						teamMemberList.add(assgined);
						teamMember = teamMemberList.toArray(new String[teamMemberList.size()]);
					} else if ((!Arrays.asList(teamMember).contains(assgined)) && assgined != null) {
						teamMemberList.add(assgined);
						teamMember = teamMemberList.toArray(new String[teamMemberList.size()]);
					}
				}

				if (teamMember.length != 0 || teamMember != null) {
					defect = new int[teamMember.length];
					task = new int[teamMember.length];
					for (int i = 0; i < teamMember.length; i++) {
						defect[i] = 0;
						task[i] = 0;
					}
				}
				count = 0;
				defectCount = 0;
				totalPrediction = 0;
				tempDefect = new int[teamMember.length];
				tempCount = new int[teamMember.length];
				lastTask = new int[teamMember.length];
				lastDefect = new int[teamMember.length];
				noDefectTasks = new int[teamMember.length];
				bugPredict = new int[teamMember.length];

				if (index < (iterationList.size() - 1)) {
					long predictionStartDate = iterationList.get(0).getStDate();
					long predictonEndDate = sprint.getEndDate();
					for (int i = 0; i < index; i++) {
						teamMemberIndex = -1;
						for (String member : teamMember) {
							teamMemberIndex += 1;
							if(member!=null) {

							int bugIdentified = 0;
							// metricsList=ProjectHealthRepos.metricsRepo.findByPNameAndSName(projectName,iterationName);
							for (MetricsModel sprintData : metricsList) {
								try {
									if (("Bug".equals(sprintData.getType()) || "Defect".equals(sprintData.getType()))
											&& (sprintData.getCreateDate() > predictionStartDate
													&& sprintData.getCreateDate() < predictonEndDate)
											&& member.equals(sprintData.getAssgnTo())) {
										bugIdentified++;
									}

								} catch (NullPointerException e) {
									LOGGER.info(e);
									continue;
								}
							}
							tempDefect[teamMemberIndex] = bugIdentified;

							int memCount = 0;
							for (MetricsModel sprintData : metricsList) {
								try {
									if ((sprintData.getCreateDate() > predictionStartDate
											&& sprintData.getCreateDate() < predictonEndDate)
											&& member.equals(sprintData.getAssgnTo())) {
										memCount++;
									}

								} catch (NullPointerException e) {
									LOGGER.info(e);
									continue;
								}
							}
							tempCount[teamMemberIndex] = memCount;

							defectCount = tempDefect[teamMemberIndex];
							count = tempCount[teamMemberIndex];
							defect[teamMemberIndex] = defect[teamMemberIndex] + defectCount;
							task[teamMemberIndex] = task[teamMemberIndex] + count;
							count = 0;
							defectCount = 0;
							}
						}
					}
					for (int k = 0; k < task.length; k++) {
						lastTask[k] = task[k];
						lastDefect[k] = defect[k];
					}
					int defectSum = IntStream.of(defect).sum();
					int taskSum = IntStream.of(task).sum();
					teamIndex = -1;
					for (String member : teamMember) {
						teamIndex += 1;
						if (task[teamIndex] < defect[teamIndex]) {
							noDefectTasks[teamIndex] = 0;
						} else {
							noDefectTasks[teamIndex] = task[teamIndex] - defect[teamIndex];
						}
						if (defectSum != 0 && taskSum != 0 && defect[teamIndex] != 0 && task[teamIndex] != 0) {
							bugPredict[teamIndex] = (int) (((double) defectSum / (double) defect[teamIndex])
									* ((double) task[teamIndex] / (double) taskSum)
									/ ((double) defectSum / (double) taskSum));
						} else {
							bugPredict[teamIndex] = 0;
						}
						totalPrediction = (int) (totalPrediction + Math.ceil(bugPredict[teamIndex]));
					}
				}
				// -------------------------------------------------------------------------------------//

				// for(sprintList){
				//
				// }

				// --------------------------------------------------------------------------------------//
				if (index + 1 <= iterationList.size() - 1
						&& !iterationList.get(index + 1).getsName().equals("BackLog")) {

					bugMetrics.setSprintName(iterationList.get(index + 1).getsName());
				} else
					bugMetrics.setSprintName("NEXT");

				bugMetrics.setPredictedValue(totalPrediction);
				sprintPredictedBugMetrics.add(bugMetrics);
			}
		}
		health.setSprintPredictedBugMetrics(sprintPredictedBugMetrics);
		return health;
	}
}
