package com.bolt.dashboard.projectHealth;

import java.util.ArrayList;
import java.util.Date;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.HealthData;

public class ProjectHealthApplication {

	private static final Logger LOGGER = LogManager.getLogger(ProjectHealthApplication.class);
	ALMData almData = new ALMData();
	/* HealthRulesPerDay healthRulesPerDay=new HealthRulesPerDay(); */
	ProjectHealthCalculation pHC = new ProjectHealthCalculation();
	AnnotationConfigApplicationContext ctx;
	String result = "SUCCESS";

//	public static void main(String[] args) {
//		new ProjectHealthApplication().projectHealthMain("BrillioOne");
//	}

	public void projectHealthMain(String projectName) {
		ProjectHealthVariables.setProjectName(projectName);
		ProjectHealthVariables.setHealthdata(new HealthData());
		ProjectHealthVariables.getHealthdata().setProjectName(projectName);
		ProjectHealthVariables.getHealthdata().setTimeStamp(new Date().getTime());
		ProjectHealthVariables.setSprintDataList(new ArrayList<>());
		ProjectHealthVariables.setSprintHealthList(new ArrayList<>());
		ProjectHealthVariables.setCurrentIterationName(new ArrayList<>());
		/* inetializing required repos */
		try {
			ProjectHealthRepos.repoInit();
			/* Get ALMConfiguration details */
			ProjectHealthVariables
					.setProjecthealthdata(ProjectHealthRepos.projectHealthRep.findLastByProjectName(projectName));// configured

			if (ProjectHealthVariables.getProjecthealthdata().size() != 0) {
				almData.getALMConfigData(projectName);
				/* collecting current , past and backlog iteration */
				almData.getALMData();

				if (ProjectHealthVariables.getpIterationList().size() > 3) {
					almData.getIteration();
				} else {
					LOGGER.info("For Project " + projectName + "past sprint count is less than 3");
				}
				/*---------============>*/ // healthRulesPerDay.pHFirstRun(projectName);
			}
		} catch (Exception e) {
			result = "FAIL";
			ConstantVariable.getLastRun(projectName, "PROJECT HEALTH", new Date().getTime(), result);
			// ProjectHealthRepos.repoDestroy();
		}
		// pHC.populateColor();
		ProjectHealthVariables.getHealthdata().setShealthdataMetrics(ProjectHealthVariables.getSprintDataList());
		ProjectHealthVariables.getHealthdata().setSprintHealthMetrics(ProjectHealthVariables.getSprintHealthList());
		ProjectHealthVariables.setHealthdata(
				new ProjectBugPrediction().predictBug(projectName, ProjectHealthVariables.getHealthdata()));

		ProjectHealthVariables.setHealthdata(ProjectHealthVariables.getHealthdata());
		ctx = DataConfig.getContext();

		ProjectHealthRepos.healthDataRepo.save(ProjectHealthVariables.getHealthdata());
		// cleanObject and collector last run
		ProjectHealthVariables.setSprintDataList(new ArrayList<>());
		ConstantVariable.getLastRun(projectName, "PROJECT HEALTH", new Date().getTime(), result);
		ProjectHealthRepos.repoDestroy();
		LOGGER.info("Project Health Collector ended for " + projectName);

	}

}
