package com.bolt.dashboard.projectHealth;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.HealthDataMetrics;
import com.bolt.dashboard.core.model.ProjectHealthRuleSet;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;

public class ProjectHealthBuildQuality {
	// private static final Log LOG =
	ProjectHealthCalculation pHC = new ProjectHealthCalculation();
	ProjectHealthApplication pHA = new ProjectHealthApplication();
	ProjectHealthVariables pHV = new ProjectHealthVariables();
	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
	double doMathResult;
	List<BuildTool> sprintArray = null;

	public void buildDataInit(long[] datesArray) {
		ConfigurationSettingRep configRepo= ctx.getBean(ConfigurationSettingRep.class);
		ConfigurationSetting config=configRepo.findByProjectName(ProjectHealthVariables.getProjectName()).get(0);
		String toolName="JENKINS";
		if(config!=null) {
		
			for(ConfigurationToolInfoMetric metric: config.getMetrics()) {
				 if(metric.getToolType().equals("Build")) {
					 toolName=metric.getToolName();
				 }
			}
			if(toolName.contains("BITBUCKET")){
				toolName="BITBUCKET";
				ProjectHealthVariables.setBUILD_STATUS("FAILED");
				ProjectHealthVariables.setBUILD_SUCCESS("SUCCESSFUL");
			}
		}

		Set<BuildTool> tools = ProjectHealthRepos.buildRepo.findByName(ProjectHealthVariables.getProjectName());
		
		tools = tools.stream()
				.filter(tool -> tool.getTimestamp() > datesArray[0] && tool.getTimestamp() < datesArray[1])
				.collect(Collectors.toSet());
		sprintArray = new ArrayList(tools);
	}

	/* rule for Number of build failures */
	public void ruleNoOfBuildFailuresBlock(ProjectHealthRuleSet sprintEntries, HealthDataMetrics metricEntries) {

		double noOfBuildFailures = 0;
		pHC.updateMetricEntries(sprintEntries, metricEntries);
		metricEntries.setGoalValue(metricEntries.getGoal());
		for (BuildTool buildTool : sprintArray) {

			Set<BuildToolMetric> metrics = buildTool.getMetrics();

			Iterator<BuildToolMetric> iterator = metrics.iterator();

			while (iterator.hasNext()) {
				BuildToolMetric metric = iterator.next();
				if (metric.getName().equals(ProjectHealthVariables.getBUILD_STATUS_KEY())
						&& metric.getValue().equals(ProjectHealthVariables.getBUILD_STATUS())) {
					noOfBuildFailures++;
				}
			}
		}

		doMathResult = pHC.doMath(noOfBuildFailures, Integer.parseInt(metricEntries.getGoal()),
				metricEntries.getOperator());
		metricEntries.setGoal(metricEntries.getGoalValue());
		pHC.calculateRuleHealth(doMathResult, metricEntries);
		metricEntries.setSprintValue(Math.round((int) noOfBuildFailures));
		if (Double.doubleToRawLongBits(noOfBuildFailures) == 0) {
			pHC.setRulesToGreen(metricEntries);
		}
		metricEntries.setSprintValueAsString(Integer.toString((int) noOfBuildFailures));
	}

	/* rule for Build Average Time */
	public void ruleBuildAvgTimeBlock(ProjectHealthRuleSet sprintEntries, HealthDataMetrics metricEntries) {

		double totalBuildTime = 0, avgBuildTime = 0, totalBuilds = 0;

		String goalAsString = metricEntries.getGoal();
		pHC.updateMetricEntries(sprintEntries, metricEntries);
		long milliSeconds = getTimeInMilliSeconds(goalAsString);
		metricEntries.setGoalValue(Long.toString(milliSeconds));
		metricEntries.setGoal(goalAsString);
		for (BuildTool buildTool : sprintArray) {
			Set<BuildToolMetric> metrics = buildTool.getMetrics();

			Iterator<BuildToolMetric> iterator = metrics.iterator();

			while (iterator.hasNext()) {
				BuildToolMetric metric = iterator.next();
				if (metric.getName().equals(ProjectHealthVariables.getBUILD_DURATION_KEY())) {
					totalBuildTime = totalBuildTime + Double.parseDouble(metric.getValue().toString());
					totalBuilds++;
					break;
				}
			}
		}
		
		if(totalBuilds > 0) {
			avgBuildTime = totalBuildTime / totalBuilds;
		}

//		avgBuildTime = totalBuildTime / totalBuilds;

		doMathResult = pHC.doMath(avgBuildTime, milliSeconds, metricEntries.getOperator());
		metricEntries.setGoal(metricEntries.getGoalValue());
		pHC.calculateRuleHealth(doMathResult, metricEntries);
		metricEntries.setSprintValue(Math.round((int) avgBuildTime));
		if (Double.doubleToRawLongBits(totalBuilds) == 0) {
			pHC.setRulesToGreen(metricEntries);
		}
		metricEntries.setSprintValueAsString(getTimeInString((long) avgBuildTime));
	}

	/* rule for MTTR */
	public void ruleMTTRBlock(ProjectHealthRuleSet sprintEntries, HealthDataMetrics metricEntries, long[] datesArray) {

		long failureTs = 0, successTs = 0, mttrValue = 0, ruleCount = 0, avgMttr = 0;
		boolean entry = false;

		String goalAsString = metricEntries.getGoal();
		pHC.updateMetricEntries(sprintEntries, metricEntries);
		long milliSeconds = getTimeInMilliSeconds(goalAsString);
		metricEntries.setGoal(goalAsString);
		metricEntries.setGoalValue(Long.toString(milliSeconds));
		Collections.sort(sprintArray, new Comparator<BuildTool>() {
			public int compare(BuildTool s1, BuildTool s2) {
				return (int) (s1.getTimestamp() - s2.getTimestamp());
			}
		});
		for (BuildTool buildTool : sprintArray) {
			Set<BuildToolMetric> metrics = buildTool.getMetrics();

			Iterator<BuildToolMetric> iterator = metrics.iterator();

			while (iterator.hasNext()) {
				BuildToolMetric metric = iterator.next();
				if (metric.getValue().equals(ProjectHealthVariables.getBUILD_STATUS()) && failureTs == 0) {
					failureTs = buildTool.getTimestamp();
					entry = true;

				}
				if (metric.getValue().equals(ProjectHealthVariables.getBUILD_SUCCESS()) && entry) {
					successTs = buildTool.getTimestamp();
					mttrValue += (successTs - failureTs);
					ruleCount++;
					failureTs = 0;
					entry = false;
				}
			}
		}
		if (ruleCount == 0) {
			if (entry) {
				mttrValue += datesArray[1] - failureTs;
				avgMttr = mttrValue / (ruleCount + 1);
			}

		} else {
			if (entry) {
				mttrValue += datesArray[1] - failureTs;
				avgMttr = mttrValue / (ruleCount + 1);
			} else {
				avgMttr = mttrValue / ruleCount;
			}
		}

		doMathResult = pHC.doMath(avgMttr, milliSeconds, metricEntries.getOperator());
		metricEntries.setGoal(metricEntries.getGoalValue());
		pHC.calculateRuleHealth(doMathResult, metricEntries);
		metricEntries.setSprintValue(Math.round((int) avgMttr));
		metricEntries.setSprintValueAsString(getTimeInString(avgMttr));
	}

	// Gets time in milliSeconds
	public static long getTimeInMilliSeconds(String time) {
		long milliSeconds = 0;
		String[] timeArray = time.split(" ");
		for (String t : timeArray) {
			if (t.contains("d") || t.contains("D")) {
				int days = Integer.parseInt(t.substring(0, t.length() - 1));
				milliSeconds += days * 86400 * 1000;
			} else if (t.contains("h") || t.contains("H")) {
				int hours = Integer.parseInt(t.substring(0, t.length() - 1));
				milliSeconds += hours * 3600 * 1000;
			} else if (t.contains("m") || t.contains("M")) {
				int minutes = Integer.parseInt(t.substring(0, t.length() - 1));
				milliSeconds += minutes * 60 * 1000;
			} else if (t.contains("s") || t.contains("S")) {
				int seconds = Integer.parseInt(t.substring(0, t.length() - 1));
				milliSeconds += seconds * 1000;
			} else {
				return Long.parseLong(time);
			}
		}
		return milliSeconds;
	}

	// convert timeFormate to String
	public static String getTimeInString(long timeInMilliSeconds) {
		StringBuilder time = new StringBuilder();
		if (timeInMilliSeconds > 0) {
			time.append(TimeUnit.MILLISECONDS.toHours(timeInMilliSeconds) + "h ");
			time.append(TimeUnit.MILLISECONDS.toMinutes(timeInMilliSeconds) % TimeUnit.HOURS.toMinutes(1) + "m");
			return time.toString();
		} else {
			return "0m";
		}
	}

}
