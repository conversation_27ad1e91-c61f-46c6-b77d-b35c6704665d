package com.bolt.dashboard.projectHealth;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.bolt.dashboard.core.model.HealthDataMetrics;
import com.bolt.dashboard.core.model.HealthSprintMetrics;
import com.bolt.dashboard.core.model.ProjectHealthApplicationPhase;
import com.bolt.dashboard.core.model.ProjectHealthConfig;
import com.bolt.dashboard.core.model.ProjectHealthRuleSet;

public class ProjectHealthCalculation /* extends ProjectHealthApplication */ {
	static ProjectHealthApplication pHA = new ProjectHealthApplication();
	boolean v;
	public static final String GREEN_HEALTH = "GREEN";
	public static final String AMBER_HEALTH = "AMBER";
	public static final String RED_HEALTH = "RED";
	public static final String NO_HEALTH = "GREY";
	ProjectHealthVariables pHV = new ProjectHealthVariables();


	public void updateMetricEntries(ProjectHealthRuleSet sprintEntries, HealthDataMetrics metricEntries) {

		metricEntries.setRuleName(sprintEntries.getRuleName());
		metricEntries.setOperator(sprintEntries.getOperator());
		metricEntries.setWeightage(sprintEntries.getWeightage());
		metricEntries.setGoal(sprintEntries.getValue());
		metricEntries.setGoalValue(sprintEntries.getValue());
	}

	public double doMath(double first, double second, String op) {
		if (op == null) {
			return 0;
		}

		switch (op) {
		case ">":
			if (first > second)
				return 1;
			else
				return (second - first);

		case "<":
			if (first < second)
				return 1;
			else
				return (first - second);

		case "==":
			v = first == second;
			if (v)
				return 1;
			else
				return (Math.abs(first - second));

		case ">=":
			if (first >= second)
				return 1;
			else
				return (second - first);
		case "<=":
			if (first <= second)
				return 1;
			else
				return (first - second);
		default:
			return 0;
		}
	}

	public void calculateRuleHealth(Double doMathResult, HealthDataMetrics metricEntries) {
		if (doMathResult == 1) {
			metricEntries.setSprintPoints(metricEntries.getWeightage());
			metricEntries.setResultColor(GREEN_HEALTH);

		} else if (doMathResult >= Double.parseDouble(metricEntries.getGoalValue())) {
			metricEntries.setSprintPoints(0);
			metricEntries.setResultColor(RED_HEALTH);
		}

		else {
			int diff = (int) Math
					.floor(doMathResult * metricEntries.getWeightage() / Integer.parseInt(metricEntries.getGoal()));
			metricEntries.setSprintPoints(Math.abs(metricEntries.getWeightage() - diff));
			if (doMathResult <= (Integer.parseInt(metricEntries.getGoal()) * (.5))) {
				metricEntries.setResultColor(AMBER_HEALTH);
			} else {
				metricEntries.setResultColor(RED_HEALTH);
			}
		}
	}

	public void setRulesToGreen(HealthDataMetrics metricEntries) {
		metricEntries.setSprintPoints(metricEntries.getWeightage());
		metricEntries.setResultColor(GREEN_HEALTH);

	}

	public void setRulesToRed(HealthDataMetrics metricEntries) {
		metricEntries.setSprintPoints(metricEntries.getWeightage());
		metricEntries.setResultColor(RED_HEALTH);

	}

	// -----------------------------------------------------------------------
	public void setRAGConfigurationData() {
		for (ProjectHealthConfig config : ProjectHealthVariables.getConfiguration()) {
			if ("Green".equals(config.getRagName())) {
				ProjectHealthVariables.setGreenFromValue(config.getFrom());
			} else if ("Amber".equals(config.getRagName())) {
				ProjectHealthVariables.setAmberFromValue(config.getFrom());
				ProjectHealthVariables.setAmberToValue(config.getTo());
			}
		}
	}

	public String calculateSprintColor(int points) {

		if (points >= ProjectHealthVariables.getGreenFromValue()) {
			return GREEN_HEALTH;
		} else if (points >= ProjectHealthVariables.getAmberFromValue()
				&& points <= ProjectHealthVariables.getAmberToValue()) {
			return AMBER_HEALTH;
		} else {

			return RED_HEALTH;
		}
	}

	// -----------------------------------------------------------------------

	public String calculateProjectHealthApplicationsStatus(List<HealthDataMetrics> healthDataMetrics, String phaseName,
			String sprintName, int phaseWeighatage) {
		ProjectHealthVariables.setTotalWeightage(0);

		for (int i = 0; i < healthDataMetrics.size(); i++) {

			ProjectHealthVariables.setTotalWeightage(
					ProjectHealthVariables.getTotalWeightage() + healthDataMetrics.get(i).getSprintPoints());

		}
		ProjectHealthVariables.getPhaseHashMap().put(phaseName,
				(int) (ProjectHealthVariables.getTotalWeightage() * phaseWeighatage) / 100);
		ProjectHealthVariables.getSprintPhaseHashMap().put(sprintName, ProjectHealthVariables.getPhaseHashMap());

		if (ProjectHealthVariables.getTotalWeightage() >= ProjectHealthVariables.getGreenFromValue()
				&& ProjectHealthVariables.getTotalWeightage() <= 100) {
			return GREEN_HEALTH;
		} else if (ProjectHealthVariables.getTotalWeightage() >= ProjectHealthVariables.getAmberFromValue()
				&& ProjectHealthVariables.getTotalWeightage() <= ProjectHealthVariables.getAmberToValue()) {
			return AMBER_HEALTH;
		} else {

			return RED_HEALTH;
		}

	}

	public void colorForNextSprint(Map<String, Map<String, Integer>> sprintPhaseHashMap) {

		int pointsSum = 0;

		for (Object key : sprintPhaseHashMap.keySet()) {

			ProjectHealthVariables.setPhaseHashMap(ProjectHealthVariables.getSprintPhaseHashMap().get(key));

			for (Object key2 : ProjectHealthVariables.getPhaseHashMap().keySet()) {
				int value = ProjectHealthVariables.getPhaseHashMap().get(key2);
				pointsSum = pointsSum + phasePercentVal((String) key, value);
			}
		}

		HealthSprintMetrics sprintMetric = null;
		sprintMetric = new HealthSprintMetrics();
		sprintMetric.setSprintName(ProjectHealthVariables.getNEXT_ITEARTION());
		sprintMetric.setSprintVal(pointsSum);
		sprintMetric.setSprintStatus(calculateSprintColor(pointsSum));
		ProjectHealthVariables.getSprintHealthList().add(sprintMetric);
		ProjectHealthVariables.setSprintPhaseHashMap(new HashMap<String, Map<String, Integer>>());
	}

	public int phasePercentVal(String current, int val) {
		ProjectHealthVariables.setPhaseWeightageArray(new ArrayList<>());
		int pointsSum = 0;
		for (int k = 1; k <= ProjectHealthVariables.getProjecthealthdata().size(); k++) {
			ProjectHealthVariables.setEntriesList(new ArrayList<>());
			// go through each rules in past, current and future
			ProjectHealthVariables.setLastentries(ProjectHealthVariables.getProjecthealthdata()
					.get(ProjectHealthVariables.getProjecthealthdata().size() - k));
			ProjectHealthVariables.setSprintName(ProjectHealthVariables.getLastentries().getSprintName());
			ProjectHealthVariables
					.setApplicationPhaseList(ProjectHealthVariables.getLastentries().getApplicationPhaseList());
		
				for (ProjectHealthApplicationPhase phaseList : ProjectHealthVariables.getApplicationPhaseList()) {

					ProjectHealthVariables.getPhaseWeightageArray().add(phaseList.getApplicationPhaseWeightage());
					pointsSum += val * phaseList.getApplicationPhaseWeightage() / 100;
				}
				

		}
		return pointsSum;

	}

	public void colorForCurrentSprint(Map<String, Map<String, Integer>> sprintPhaseIterationHashMap,
			String iterationName) {
		

		for (Object key : sprintPhaseIterationHashMap.keySet()) {
			int pointsSum = 0;
			ProjectHealthVariables.setPhaseHashMap(ProjectHealthVariables.getSprintPhaseHashMap().get(key));

			for (Object key2 : ProjectHealthVariables.getPhaseHashMap().keySet()) {
				int value = ProjectHealthVariables.getPhaseHashMap().get(key2);
				pointsSum = pointsSum + phasePercentVal((String) key, value);
			}
			HealthSprintMetrics sprintMetric = null;
			sprintMetric = new HealthSprintMetrics();
			sprintMetric.setSprintName(ProjectHealthVariables.getCURRENT_ITERATION());
			sprintMetric.setIterationName((String) key);
			sprintMetric.setSprintVal(pointsSum);
			if (pointsSum == 0)
				sprintMetric.setSprintStatus(ProjectHealthVariables.getNO_HEALTH());
			else
				sprintMetric.setSprintStatus(calculateSprintColor(pointsSum));
			ProjectHealthVariables.getSprintHealthList().add(sprintMetric);
		}

		
		ProjectHealthVariables.setSprintPhaseHashMap(new HashMap<String, Map<String, Integer>>());
	}

	public void colorForPastSprint(Map<String, Map<String, Integer>> sprintPhaseHashMap, String sprintN) {
		int pointsSum = 0;

		for (Object key : sprintPhaseHashMap.keySet()) {

			ProjectHealthVariables.setPhaseHashMap(ProjectHealthVariables.getSprintPhaseHashMap().get(key));
			for (Object key2 : ProjectHealthVariables.getPhaseHashMap().keySet()) {
				int value = ProjectHealthVariables.getPhaseHashMap().get(key2);
				pointsSum = pointsSum + phasePercentVal(ProjectHealthVariables.getPAST_ITERATION(), value);
			}

		}

		HealthSprintMetrics sprintMetric = null;
		sprintMetric = new HealthSprintMetrics();
		sprintMetric.setSprintName(sprintN);
		sprintMetric.setSprintVal(pointsSum);
		sprintMetric.setSprintStatus(calculateSprintColor(pointsSum));
		ProjectHealthVariables.getSprintHealthList().add(sprintMetric);
		ProjectHealthVariables.setSprintPhaseHashMap(new HashMap<String, Map<String, Integer>>());
	}

}
