package com.bolt.dashboard.projectHealth;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.core.model.CodeQualityMetric;
import com.bolt.dashboard.core.model.GoalMetric;
import com.bolt.dashboard.core.model.GoalSetting;
import com.bolt.dashboard.core.model.HealthDataMetrics;
import com.bolt.dashboard.core.model.ProjectCodeCoverage;
import com.bolt.dashboard.core.model.ProjectHealthRuleSet;
import com.bolt.dashboard.core.repository.CodeCoverageRepository;
import com.bolt.dashboard.core.repository.CodeQualityRep;

public class ProjectHealthCodeQuality {
	static double doMathResult;
	List<ProjectCodeCoverage> backEndCodeCoverage = new ArrayList<>();
	CodeCoverageRepository codeCoverageRepo = ProjectHealthRepos.codeCoverageRepo;
	List<CodeQuality> codeQuality = null;
	Map<String, List<CodeQuality>> codeQualityData = new java.util.HashMap<>();
	CodeQualityRep codeQualityRepo = ProjectHealthRepos.codeQualityRepo;
	List<GoalSetting> goals;
	ProjectHealthCalculation pHC = new ProjectHealthCalculation();
	List<CodeQuality> presentCq = null;
	List<ProjectCodeCoverage> uiCodeCoverage = new ArrayList<>();

	public void callCodeQualityBlock(ProjectHealthRuleSet sprintEntries, HealthDataMetrics metricEntries) {
		if (goals == null || goals.size() == 0) {
			goals = (List<GoalSetting>) ProjectHealthRepos.goalSettingRep
					.findByProjectNameAndName(ProjectHealthVariables.getProjectName(), "CodeQuality");
		}
		String key = getNameForGivenDisplayName(sprintEntries.getRuleName());
		if (key != null)
			ruleForCodeQuality_ParameterBlock(sprintEntries, metricEntries, key);
		else {
			pHC.updateMetricEntries(sprintEntries, metricEntries);
			pHC.setRulesToRed(metricEntries);
		}

	}

	public List<ProjectCodeCoverage> getDataForDates(List<ProjectCodeCoverage> dataInput, long[] datesArray) {
		List<ProjectCodeCoverage> response = new ArrayList<>();
		for (ProjectCodeCoverage p : dataInput) {
			if (p.getTimestamp() >= datesArray[0] && p.getTimestamp() <= datesArray[1])
				response.add(p);
		}
		return response;
	}

	public void getCodeQualityInfo(String projectName, long[] datesArray) {
		codeQuality = (List<CodeQuality>) codeQualityRepo.findByName(projectName);
		presentCq = new ArrayList<>();
		for (CodeQuality cq : codeQuality)
			if (cq.getTimestamp() >= datesArray[0] && cq.getTimestamp() <= datesArray[1])
				presentCq.add(cq);

	}

	public List<CodeQuality> getCqForSprints(long sdate, long edate) {
		List<CodeQuality> data = new ArrayList<>();
		codeQuality.forEach(cq -> {
			if (cq.getTimestamp() >= sdate && cq.getTimestamp() <= edate)
				data.add(cq);
		});
		return data;
	}

	public List<ProjectCodeCoverage> getDataForDates(List<ProjectCodeCoverage> codecoverage, long sdate, long edate) {
		List<ProjectCodeCoverage> response = new ArrayList<>();
		for (ProjectCodeCoverage p : codecoverage)
			if (p.getTimestamp() >= sdate && p.getTimestamp() <= edate)
				response.add(p);
		return response;
	}

	public String getDuplicateBlocks(String duplicateBlocks) {
		String value = "";
		String[] blocksArray = new String[10];
		if (duplicateBlocks.contains(",")) {
			blocksArray = duplicateBlocks.split(Pattern.quote(","));
			for (String duplicates : blocksArray)
				value = value + duplicates;
			return value;
		} else
			return duplicateBlocks;

	}

	public String getNameForGivenDisplayName(String displayName) {
		String name = null;
		if (goals.get(goals.size() - 1).getMetrics() != null) {
			Set<GoalMetric> goal = goals.get(goals.size() - 1).getMetrics();
			for (GoalMetric metric : goal) {
				
				if (metric.getDisplayName().equals(displayName)) {
					name = metric.getName();
					break;
				}
			}
		}

		return name;
	}

	void getSprintCodeCoverageData() {
		Iterable<ProjectCodeCoverage> coverageData = codeCoverageRepo
				.findByProjectName(ProjectHealthVariables.getProjectName());
		for (ProjectCodeCoverage record : coverageData) {
			String type = record.getCoverageType() != null ? record.getCoverageType() : "";
			if (type.equals("Ui"))
				uiCodeCoverage.add(record);
			else if (type.equals("Backend"))
				backEndCodeCoverage.add(record);
		}
	}

	public void ruleCoverageBlock(ProjectHealthRuleSet sprintEntries, HealthDataMetrics metricEntries,
			List<ProjectCodeCoverage> sprintArray) {

		double coverage = 0;
		pHC.updateMetricEntries(sprintEntries, metricEntries);

		if (!sprintArray.isEmpty()) {
			ProjectCodeCoverage data = sprintArray.get(0);
			if (data.getProjectLevelLinessCoveragePercentage() > 0.0)
				coverage = data.getProjectLevelLinessCoveragePercentage();

		}

		doMathResult = pHC.doMath(coverage, Integer.parseInt(metricEntries.getGoal()), metricEntries.getOperator());
		pHC.calculateRuleHealth(doMathResult, metricEntries);
		metricEntries.setSprintValue(Math.round((int) coverage));
	}

	/* rule Duplication block */
	public void ruleDuplicationBlock(ProjectHealthRuleSet sprintEntries, HealthDataMetrics metricEntries) {

		double duplicationBlock = 0;

		pHC.updateMetricEntries(sprintEntries, metricEntries);
		Set<CodeQualityMetric> metrics = presentCq.get(presentCq.size() - 1).getMetrics();

		Iterator<CodeQualityMetric> iterator = metrics.iterator();
		CodeQualityMetric codeQualityMetricObject = null;
		while (iterator.hasNext()) {
			codeQualityMetricObject = iterator.next();
			if (codeQualityMetricObject.getName().equals("duplicated_blocks"))
				break;
		}

		if (codeQualityMetricObject!=null &&  codeQualityMetricObject.getFormattedValue() != null) {
			duplicationBlock = Double.parseDouble(getDuplicateBlocks(codeQualityMetricObject.getFormattedValue()));
		}else if(codeQualityMetricObject!=null && codeQualityMetricObject.getValue() != null) {
			duplicationBlock = Double.parseDouble(codeQualityMetricObject.getValue().toString());
		}

		doMathResult = pHC.doMath(duplicationBlock, Integer.parseInt(metricEntries.getGoal()),
				metricEntries.getOperator());
		pHC.calculateRuleHealth(doMathResult, metricEntries);
		metricEntries.setSprintValue(Math.round((int) duplicationBlock));
		if (Double.doubleToRawLongBits(duplicationBlock) == 0)
			pHC.setRulesToGreen(metricEntries);
	}

	public void ruleForCodeQuality_ParameterBlock(ProjectHealthRuleSet sprintEntries, HealthDataMetrics metricEntries,
			String parameterKey) {
		/*
		 * String property = getNameForGivenDisplayName(parameterKey);
		
		 */
		double value = 0;

		pHC.updateMetricEntries(sprintEntries, metricEntries);
		if (presentCq.size() != 0) {
			Set<CodeQualityMetric> metrics = presentCq.get(presentCq.size() - 1).getMetrics();

			Iterator<CodeQualityMetric> iterator = metrics.iterator();
			CodeQualityMetric codeQualityMetricObject = null;
			while (iterator.hasNext()) {
				codeQualityMetricObject = iterator.next();
				if (codeQualityMetricObject.getName().equals(parameterKey))
					break;
			}

			if (codeQualityMetricObject!=null &&  codeQualityMetricObject.getValue() != null) {

				value = Double.parseDouble(codeQualityMetricObject.getValue().toString());

			}

			doMathResult = pHC.doMath(value, Integer.parseInt(metricEntries.getGoal()), metricEntries.getOperator());
			pHC.calculateRuleHealth(doMathResult, metricEntries);
			metricEntries.setSprintValue(Math.round((int) value));
			if (Double.doubleToRawLongBits(value) == 0.0)
				pHC.setRulesToGreen(metricEntries);
		} else {

			metricEntries.setSprintValue(Math.round(0));
			pHC.setRulesToRed(metricEntries);
		}

	}

	public void technicalDebt(ProjectHealthRuleSet sprintEntries, long[] datesArray) {
		String value = null;
		double tdValue = 0;
		HealthDataMetrics metricEntries = ProjectHealthVariables.getRuleHealthEntries();
		pHC.updateMetricEntries(sprintEntries, metricEntries);
		getCodeQualityInfo(ProjectHealthVariables.getProjectName(), datesArray);
		if (presentCq != null && !presentCq.isEmpty()) {
			Set<CodeQualityMetric> metricSet = presentCq.get(presentCq.size() - 1).getMetrics();
			Iterator<CodeQualityMetric> metricIterator = metricSet.iterator();
			while (metricIterator.hasNext()) {
				CodeQualityMetric metric = metricIterator.next();
				if ("sqale_index".equals(metric.getName())) {
					if(metric.getFormattedValue()!=null) {
						value = metric.getFormattedValue();
						}
						else {
						
							value=convertMinsToDays((int)Math.round(Double.parseDouble(metric.getValue().toString())));
							//value=metric.getValue().toString();
						}
					
				}
			}
			tdValue=Double.parseDouble(value);
			if (value!=null && value.contains("d")) {
				String[] valueArray = value.split("d");
				tdValue = Integer.parseInt(valueArray[0].replaceAll("[,.]", ""));
			}
			doMathResult = pHC.doMath(tdValue, Integer.parseInt(metricEntries.getGoal()), metricEntries.getOperator());
			pHC.calculateRuleHealth(doMathResult, metricEntries);
			metricEntries.setSprintTDValue((int) tdValue);
			metricEntries.setSprintValue((int) tdValue);
			if (Double.doubleToRawLongBits(tdValue) == 0)
				pHC.setRulesToGreen(metricEntries);
		} else {
			metricEntries.setSprintTDValue(0);
			metricEntries.setSprintValue(0);
			pHC.setRulesToRed(metricEntries);

		}
	}

	private String convertMinsToDays(int value) {
		
		int result=0;
		if(value>0) {
			result=(int)Math.ceil(value/(double)480);
		}
		
		return String.valueOf(result);
	}

	public void uICodeCoverage(ProjectHealthRuleSet sprintEntries, long[] datesArray) {
		HealthDataMetrics metricEntries = ProjectHealthVariables.getRuleHealthEntries();

		List<ProjectCodeCoverage> data = getDataForDates(uiCodeCoverage, datesArray[0], datesArray[1]);
		ruleCoverageBlock(sprintEntries, metricEntries, data);
	}

	public void serviceCodeCoverage(ProjectHealthRuleSet sprintEntries, long[] datesArray) {
		HealthDataMetrics metricEntries = ProjectHealthVariables.getRuleHealthEntries();

		List<ProjectCodeCoverage> data = getDataForDates(backEndCodeCoverage, datesArray[0], datesArray[1]);
		ruleCoverageBlock(sprintEntries, metricEntries, data);

	}

}
