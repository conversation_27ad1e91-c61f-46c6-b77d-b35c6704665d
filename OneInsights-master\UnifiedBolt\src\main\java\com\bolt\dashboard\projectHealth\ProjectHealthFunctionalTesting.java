
package com.bolt.dashboard.projectHealth;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map.Entry;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.bolt.dashboard.core.model.HealthDataMetrics;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.ProjectHealthRuleSet;
import com.bolt.dashboard.core.model.TransitionModel;

public class ProjectHealthFunctionalTesting extends ProjectHealthApplication {
	private static final Logger LOGGER = LogManager.getLogger(ProjectHealthFunctionalTesting.class);
	ProjectHealthCalculation projectHealth = new ProjectHealthCalculation();	
	ProjectHealthApplication pHA = new ProjectHealthApplication();
	ProjectHealthVariables pHV = new ProjectHealthVariables();
	double doMathResult;
	List<IterationModel> iterationSprintArray = new ArrayList<IterationModel>();

	// create function declare List<MetricModel> inetialize
	// rule bug open vs closed

	public void bugOpenVSClosed(ProjectHealthRuleSet sprintEntries,long[] datesArray) {
		double bugVal = 0, bugIdentified = 0, bugClosed = 0;
		HealthDataMetrics metricEntries= ProjectHealthVariables.getRuleHealthEntries();
			
		projectHealth.updateMetricEntries(sprintEntries, metricEntries);
		// check defectList ==null ProjectHealthVariables.getMetricsListData()
		// else ProjectHealthVariables.getDefectListData()

		List<MetricsModel> metricsList = ProjectHealthVariables.getDefectsListData();
	metricsList = ProjectHealthRepos.metricsRepo.findByPNameAndSName(ProjectHealthVariables.getProjectName(),
		metricsList.get(0).getsName());
		for (MetricsModel sprintData : metricsList) {
			try {

		if ((ProjectHealthVariables.getBug().equals(sprintData.getType())))
		/*
		 * && (sprintData.getCreateDate() > startDate &&
		 * sprintData.getCreateDate() < nextSprintStartDate))
		 */ {
					if (ProjectHealthVariables.getCloseState().indexOf(sprintData.getState()) > -1) {
						bugClosed = bugClosed + 1;
					}
					bugIdentified++;
				}

			} catch (NullPointerException e) {
				continue;
			}
		}
		if (bugIdentified != 0) {
			bugVal = (bugClosed / bugIdentified) * 100;
			doMathResult = projectHealth.doMath(bugVal, Integer.parseInt(metricEntries.getGoal()), metricEntries.getOperator());
			projectHealth.calculateRuleHealth(doMathResult, metricEntries);
			metricEntries.setSprintValue((int) bugVal);
		}
		if (Double.doubleToRawLongBits(bugIdentified) == 0) {
			metricEntries.setSprintValue(100);
			projectHealth.setRulesToGreen(metricEntries);
		}
	}

	// Rule for stories in backlog

	public void storiesinbacklog(ProjectHealthRuleSet sprintEntries,long[] dateArray) {
		double storyPlannedVal = 0;
		 HealthDataMetrics metricEntries =  ProjectHealthVariables.getRuleHealthEntries();
		projectHealth.updateMetricEntries(sprintEntries, metricEntries);
		List<MetricsModel> metricsList = ProjectHealthVariables.getBackLogIteration();
		for (MetricsModel sprintData : metricsList) {
			try {
				if (ProjectHealthVariables.getSTORY().equals(sprintData.getType())) {
					storyPlannedVal = storyPlannedVal + 1;
				}

			} catch (NullPointerException e) {
				continue;
			}
		}

		doMathResult = projectHealth.doMath(storyPlannedVal, Integer.parseInt(metricEntries.getGoal()),
				metricEntries.getOperator());
		projectHealth.calculateRuleHealth(doMathResult, metricEntries);
		metricEntries.setSprintValue((int) storyPlannedVal);
		if (Double.doubleToRawLongBits(storyPlannedVal) == 0) {
			metricEntries.setSprintPoints(metricEntries.getWeightage());
			metricEntries.setResultColor(ProjectHealthVariables.getGREEN_HEALTH());
		}

	}

	// rule number of Open Critical Defect

	public void criticalDefect(ProjectHealthRuleSet sprintEntries,long[] datesArray) {
		long startDate = 0;
		long nextSprintStartDate = 0;
		double criticalBugVal = 0;
		 HealthDataMetrics metricEntries = ProjectHealthVariables.getRuleHealthEntries();
			
		projectHealth.updateMetricEntries(sprintEntries, metricEntries);

		startDate = datesArray[0];
		nextSprintStartDate = datesArray[1];
		List<MetricsModel> metricsList = ProjectHealthVariables.getDefectsListData();
		for (MetricsModel sprintData : metricsList) {
			try {

				if ((ProjectHealthVariables.getBug().equals(sprintData.getType()))
						&& (sprintData.getCreateDate() > startDate
								&& sprintData.getCreateDate() < nextSprintStartDate)) {
					if (!(ProjectHealthVariables.getCloseState().indexOf(sprintData.getState()) > -1)) {
						criticalBugVal++;
					}
				}

			} catch (NullPointerException e) {
				continue;
			}
		}
		doMathResult = projectHealth.doMath(criticalBugVal, Integer.parseInt(metricEntries.getGoal()),
				metricEntries.getOperator());

		projectHealth.calculateRuleHealth(doMathResult, metricEntries);
		metricEntries.setSprintValue((int) criticalBugVal);
		if (Double.doubleToRawLongBits(criticalBugVal) == 0) {
			metricEntries.setSprintPoints(metricEntries.getWeightage());
			metricEntries.setResultColor(ProjectHealthVariables.getGREEN_HEALTH());
		}

	}

	// Checks for Priority or severity
	public boolean getPrioritySeverity(MetricsModel sprintData, List<String> criticalPriority) {
		if (ProjectHealthVariables.getPriorityName() != null)
			if (ProjectHealthVariables.getPriorityName().equals("almPriority")) {
				ProjectHealthVariables.setPriorityFunction(sprintData.getPriority());
			} else {
				ProjectHealthVariables.setPriorityFunction(sprintData.getSeverity());
			}

		boolean found = false;
		for (String element : criticalPriority) {
			if (!(ProjectHealthVariables.getPriorityFunction() == null)
					&& ProjectHealthVariables.getPriorityFunction().equals(element)) {
				found = true;
				break;
			}
		}
		return found;
	} // rule number of critical bug

	public void numberofCriticalBug(ProjectHealthRuleSet sprintEntries,long[] datesArray) {
		double criticalBugVal = 0;
		long startDate = datesArray[0];
		 HealthDataMetrics metricEntries =  ProjectHealthVariables.getRuleHealthEntries();
					
		projectHealth.updateMetricEntries(sprintEntries, metricEntries);

		List<MetricsModel> metricsList = ProjectHealthVariables.getDefectsListData();
		for (MetricsModel sprintData : metricsList) {
			try {

				if (ProjectHealthVariables.getBug().equals(sprintData.getType())
						&& (getPrioritySeverity(sprintData, ProjectHealthVariables.getCriticalPriority()))
						&& (sprintData.getCreateDate() > startDate && sprintData.getCreateDate() < datesArray[1])) {
					criticalBugVal++;
				}

			} catch (NullPointerException e) {
				continue;
			}
		}

		doMathResult = projectHealth.doMath(criticalBugVal, Integer.parseInt(metricEntries.getGoal()),
				metricEntries.getOperator());
		projectHealth.calculateRuleHealth(doMathResult, metricEntries);
		if (Double.doubleToRawLongBits(criticalBugVal) == 0) {
			metricEntries.setSprintPoints(metricEntries.getWeightage());
			metricEntries.setResultColor(ProjectHealthVariables.getGREEN_HEALTH());
		}
		metricEntries.setSprintValue((int) criticalBugVal);
	}

	public double getDefectValueFromSprint(List<MetricsModel> ruleSprint) {
		double counterValue = 0;

		for (MetricsModel sprintData : ruleSprint) {
			if ((ProjectHealthVariables.getBug().equals(sprintData.getType()))
					&& (!(ProjectHealthVariables.getCloseState().indexOf(sprintData.getState()) > -1))) {
				counterValue++;
			}
		}
		return counterValue;
	}

	// Defects per Story rule
	public void defectsperStory(ProjectHealthRuleSet sprintEntries, long[] datesArray) {
		int totalStories = 0;
		float weightagePerStory = 0;
		long startDate = 0;
		List<String> storyID = new ArrayList<String>();
		ProjectHealthVariables.setStoryBugHashMap(new HashMap<String, Integer>());
		HealthDataMetrics metricEntries = ProjectHealthVariables.getRuleHealthEntries();
		projectHealth.updateMetricEntries(sprintEntries, metricEntries);
		List<MetricsModel> ruleSprint = ProjectHealthVariables.getDefectsListData();

		startDate = datesArray[0];
		long nextSprintStartDate = datesArray[1];

		for (MetricsModel sprintData : ruleSprint) {
			try {

				if ((ProjectHealthVariables.getSTORY().equals(sprintData.getType()))
						&& sprintData.getCreateDate() > startDate && sprintData.getCreateDate() < nextSprintStartDate) {
					storyID.add(sprintData.getwId());
					ProjectHealthVariables.getStoryBugHashMap().put(sprintData.getwId(), 0);
				}

			} catch (NullPointerException e) {
				continue;
			}
		}
		int bugCount = 0;
		for (String storyWorkID : storyID) {
			for (String sprintName : ProjectHealthVariables.getIterationSprintArray()) {
				List<MetricsModel> metricsArray = ProjectHealthRepos.metricsRepo
						.findByPNameAndSName(ProjectHealthVariables.getProjectName(), sprintName);
				for (MetricsModel iterationSprintData : metricsArray) {
					if ((ProjectHealthVariables.getBug().equals(iterationSprintData.getType()))
							&& (iterationSprintData.getCreateDate() > startDate
									&& iterationSprintData.getCreateDate() < nextSprintStartDate)) {

						if (ProjectHealthVariables.getStoryBugHashMap().get(storyWorkID) != null) {
							bugCount = ProjectHealthVariables.getStoryBugHashMap().get(storyWorkID);
						}
						List<String> sKeyList = iterationSprintData.getOutWardIssueLink();
			if (sKeyList != null) {
						for (String sKey : sKeyList) {
							if (storyWorkID.equals(sKey)) {
								bugCount++;
								ProjectHealthVariables.getStoryBugHashMap().put(storyWorkID, bugCount);
				}
							}
						}

					}
				}

			}

		}
		totalStories = ProjectHealthVariables.getStoryBugHashMap().size();
		for (String storyWorkID : storyID) {
			if (ProjectHealthVariables.getStoryBugHashMap().get(storyWorkID) != null
					&& (Integer.parseInt(sprintEntries.getValue()) != 0) && ProjectHealthVariables.getStoryBugHashMap()
							.get(storyWorkID) >= (Integer.parseInt(sprintEntries.getValue()))) {
				ProjectHealthVariables.getStoryBugHashMap().remove(storyWorkID);
			}
		}
		if (ProjectHealthVariables.getStoryBugHashMap().size() == totalStories) {
			projectHealth.setRulesToGreen(metricEntries);
			metricEntries.setSprintValue(0);
		} else {
			weightagePerStory = sprintEntries.getWeightage() / (float)totalStories;
			metricEntries.setSprintPoints((int) weightagePerStory * ProjectHealthVariables.getStoryBugHashMap().size());
			if (ProjectHealthVariables.getStoryBugHashMap().size() >= (storyID.size() / 2)
					&& ProjectHealthVariables.getStoryBugHashMap().size() < storyID.size()) {
				metricEntries.setResultColor(ProjectHealthVariables.getAMBER_HEALTH());
				metricEntries.setSprintValue(
						Math.round((float) ProjectHealthVariables.getStoryBugHashMap().size() / totalStories));
			} else {
				metricEntries.setResultColor(ProjectHealthVariables.getRED_HEALTH());
				metricEntries.setSprintValue(
						Math.round((float) ProjectHealthVariables.getStoryBugHashMap().size() / totalStories));
			}
		}

	}

	// Defects reOpen rule
	public void defectsReOpened(ProjectHealthRuleSet sprintEntries,
			 long[] datesArray) {
		int stateCounter = 0, currentIndex = 0, fromIndex = 0;
		String bugLastState;
		long startDate = 0;
		List<String> bugID = new ArrayList<String>();
		 HealthDataMetrics metricEntries =  ProjectHealthVariables.getRuleHealthEntries();
		ProjectHealthVariables.setStateHashMap(new HashMap<String, Integer>());
		ProjectHealthVariables.setBugReopenCountHashMap(new HashMap<String, Integer>());
		projectHealth.updateMetricEntries(sprintEntries, metricEntries);
		List<MetricsModel> metricsList = ProjectHealthVariables.getDefectsListData();
		for (String stateSet : metricsList.get(0).getStateSet()) {

			ProjectHealthVariables.getStateHashMap().put(stateSet, stateCounter);
			stateCounter++;
		}

		// getSprintArray(sName);

		startDate = datesArray[0];
		long nextSprintStartDAte = datesArray[1];
		for (MetricsModel sprintData : metricsList) {
			try {

				if ((ProjectHealthVariables.getBug().equals(sprintData.getType()))
						&& (sprintData.getCreateDate() > startDate
								&& sprintData.getCreateDate() < nextSprintStartDAte)) {
					bugID.add(sprintData.getwId());
					ProjectHealthVariables.getBugReopenCountHashMap().put(sprintData.getwId(), 0);
				}

			} catch (NullPointerException e) {
				continue;
			}
		}

		for (String bugWorkID : bugID) {
			for (String sprintName : ProjectHealthVariables.getIterationSprintArray()) {
				List<MetricsModel> metricsArray = ProjectHealthRepos.metricsRepo
						.findByPNameAndSName(ProjectHealthVariables.getProjectName(), sprintName);
				for (MetricsModel iterationSprintData : metricsArray) {
					if (ProjectHealthVariables.getBug().equals(iterationSprintData.getType())) {
						int bugReopenCount = 0;

						if (ProjectHealthVariables.getBugReopenCountHashMap().get(bugWorkID) != null) {
							bugReopenCount = ProjectHealthVariables.getBugReopenCountHashMap().get(bugWorkID);
						}
						if (bugWorkID.equals(iterationSprintData.getwId())
								&& ProjectHealthRepos.transitionRepo.findByWId(bugWorkID) != null) {
							for (TransitionModel reopenMetric : ProjectHealthRepos.transitionRepo
									.findByWId(bugWorkID)) {
								bugLastState = reopenMetric.getCrState();
								String bugFromState = reopenMetric.getFrmState();
								if (bugLastState.equals(sprintEntries.getDeveloperState())) {
									for (Entry<String, Integer> entry : ProjectHealthVariables.getStateHashMap()
											.entrySet()) {
										if (entry.getKey().equals(bugLastState)) {
											currentIndex = entry.getValue();
										}
									}
									for (Entry<String, Integer> entry : ProjectHealthVariables.getStateHashMap()
											.entrySet()) {
										if (entry.getKey().equals(bugFromState)) {
											fromIndex = entry.getValue();
										}
									}
									if (fromIndex > currentIndex) {
										bugReopenCount++;
									}
								}

							}
						}
						ProjectHealthVariables.getBugReopenCountHashMap().put(bugWorkID, bugReopenCount);
					}

				}

			}
		}
		int totalBugs = ProjectHealthVariables.getBugReopenCountHashMap().size();

		for (String bugWorkID : bugID) {
			if (ProjectHealthVariables.getBugReopenCountHashMap().get(bugWorkID) != null
					&& ProjectHealthVariables.getBugReopenCountHashMap().get(bugWorkID) <= 0) {
				ProjectHealthVariables.getBugReopenCountHashMap().remove(bugWorkID);
			}
		}
		int bugReopenPercent;
		int totalReopenedbug = ProjectHealthVariables.getBugReopenCountHashMap().size();
		if (totalBugs != 0)
			bugReopenPercent = (totalReopenedbug / totalBugs) * 100;
		else
			bugReopenPercent = 0;
		doMathResult = projectHealth.doMath(bugReopenPercent, Integer.parseInt(metricEntries.getGoal()),
				metricEntries.getOperator());
		calculateValue(doMathResult, metricEntries);
		metricEntries.setSprintValue(Math.round(bugReopenPercent));
	}

	public void calculateValue(double doMathResult, HealthDataMetrics metricEntries) {
		if ((doMathResult) == 1) {
			metricEntries.setSprintPoints(metricEntries.getWeightage());
			metricEntries.setResultColor(ProjectHealthVariables.getGREEN_HEALTH());

		} else if (doMathResult >= Double.parseDouble(metricEntries.getGoal())) {
			metricEntries.setSprintPoints(0);
			metricEntries.setResultColor(ProjectHealthVariables.getRED_HEALTH());
		} else {
			int diff = (int) Math
					.floor(doMathResult * metricEntries.getWeightage() / Integer.parseInt(metricEntries.getGoal()));
			metricEntries.setSprintPoints(Math.abs(metricEntries.getWeightage() - diff));
			if (doMathResult <= (Integer.parseInt(metricEntries.getGoal()) * (.5))) {
				metricEntries.setResultColor(ProjectHealthVariables.getAMBER_HEALTH());
			} else {
				metricEntries.setResultColor(ProjectHealthVariables.getRED_HEALTH());
			}
		}
	}
}
