package com.bolt.dashboard.projectHealth;

import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.ChangeHisortyRepo;
import com.bolt.dashboard.core.repository.CodeCoverageRepository;
import com.bolt.dashboard.core.repository.CodeQualityRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.DefectRepo;
import com.bolt.dashboard.core.repository.EffortHistoryRepo;
import com.bolt.dashboard.core.repository.GoalSettingRep;
import com.bolt.dashboard.core.repository.HealthDataRepo;
import com.bolt.dashboard.core.repository.IterationRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.ProjectHealthRep;
import com.bolt.dashboard.core.repository.ProjectRepo;
import com.bolt.dashboard.core.repository.TransitionRepo;

public class ProjectHealthRepos {
	static ALMConfigRepo almConfigRepo = null;
	static BuildToolRep buildRepo = null;
	static ChangeHisortyRepo changeHistoryRepo = null;
	static CodeCoverageRepository codeCoverageRepo = null;
	static CodeQualityRep codeQualityRepo = null;
	static ConfigurationSettingRep configRepo = null;
	static CodeQualityRep cqRepo = null;
	static AnnotationConfigApplicationContext ctx;
	static DefectRepo defectRepo = null;
	static EffortHistoryRepo effortRepo = null;
	static HealthDataRepo healthDataRepo = null;
	static IterationRepo iterationRepo = null;
	static MetricRepo metricsRepo = null;
	static ProjectHealthRep projectHealthRep = null;
	static ProjectRepo projectRepo = null;
	static TransitionRepo transitionRepo = null;
	static GoalSettingRep goalSettingRep = null;

	public static void repoInit() throws ProjectHealthException {
		ctx = DataConfig.getContext();
		cqRepo = ctx.getBean(CodeQualityRep.class);
		buildRepo = ctx.getBean(BuildToolRep.class);
		almConfigRepo = ctx.getBean(ALMConfigRepo.class);
		projectRepo = ctx.getBean(ProjectRepo.class);
		iterationRepo = ctx.getBean(IterationRepo.class);
		effortRepo = ctx.getBean(EffortHistoryRepo.class);
		transitionRepo = ctx.getBean(TransitionRepo.class);
		changeHistoryRepo = ctx.getBean(ChangeHisortyRepo.class);
		metricsRepo = ctx.getBean(MetricRepo.class);
		defectRepo = ctx.getBean(DefectRepo.class);
		healthDataRepo = ctx.getBean(HealthDataRepo.class);
		projectHealthRep = ctx.getBean(ProjectHealthRep.class);
		configRepo = ctx.getBean(ConfigurationSettingRep.class);
		codeQualityRepo = ctx.getBean(CodeQualityRep.class);
		codeCoverageRepo = ctx.getBean(CodeCoverageRepository.class);
		goalSettingRep = ctx.getBean(GoalSettingRep.class);
	}

	public static void repoDestroy() {
		ctx = null;
		cqRepo = null;
		buildRepo = null;
		almConfigRepo = null;
		projectRepo = null;
		iterationRepo = null;
		effortRepo = null;
		transitionRepo = null;
		changeHistoryRepo = null;
		metricsRepo = null;
		defectRepo = null;
		healthDataRepo = null;
		projectHealthRep = null;
		configRepo = null;
		codeQualityRepo = null;
		codeCoverageRepo = null;
		goalSettingRep = null;

	}
}
