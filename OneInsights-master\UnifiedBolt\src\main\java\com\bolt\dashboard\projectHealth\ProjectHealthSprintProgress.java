package com.bolt.dashboard.projectHealth;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.joda.time.DateTime;

import com.bolt.dashboard.core.model.EffortHistoryModel;
import com.bolt.dashboard.core.model.HealthDataMetrics;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.ProjectHealthRuleSet;

public class ProjectHealthSprintProgress {
	static ProjectHealthCalculation pHC = new ProjectHealthCalculation();
	ProjectHealthRepos pHR = new ProjectHealthRepos();
	double doMathResult;

	// Rule for Sprint completion
	public void ruleIterationCompletion(String iterationName, ProjectHealthRuleSet sprintEntries) {
		IterationModel iteration = ProjectHealthRepos.iterationRepo.findBySNameAndPName(iterationName,
				ProjectHealthVariables.getProjectName());
		double dayDiff = 0;
		long endDate = iteration.getEndDate();
		long completedDate = iteration.getCompletedDate();
		HealthDataMetrics metricEntries = ProjectHealthVariables.getRuleHealthEntries();

		pHC.updateMetricEntries(sprintEntries, metricEntries);
		if (completedDate == 0) {
			doMathResult = 1;
			metricEntries.setSprintPoints(metricEntries.getWeightage());
			metricEntries.setResultColor(ProjectHealthCalculation.GREEN_HEALTH);
			metricEntries.setSprintValue(0);
		} else {
			DateTime endDateTime = new DateTime(endDate);
			DateTime completedDateTime = new DateTime(completedDate);

			if (endDateTime.withTimeAtStartOfDay().isEqual(completedDateTime.withTimeAtStartOfDay())) {
				metricEntries.setSprintPoints(metricEntries.getWeightage());
				metricEntries.setResultColor(ProjectHealthCalculation.GREEN_HEALTH);
				metricEntries.setSprintValue(0);
			} else if (completedDateTime.withTimeAtStartOfDay().isBefore(endDateTime.withTimeAtStartOfDay())) {
				metricEntries.setSprintPoints(metricEntries.getWeightage());
				metricEntries.setResultColor(ProjectHealthCalculation.GREEN_HEALTH);
				metricEntries.setSprintValue(0);
			} else {
				doMathResult = 0;
				dayDiff = getWorkingDaysBetweenTwoDates(new Date(endDate), new Date(completedDate));
				metricEntries.setSprintPoints(0);
				metricEntries.setSprintValue(Math.round((int) dayDiff));
				metricEntries.setResultColor(ProjectHealthCalculation.RED_HEALTH);
			}
		}

	}

	// Task Open vs closed rule
	public void taskCompletionOpenvsClosed(ProjectHealthRuleSet sprintEntries, long[] datesArray) {
		double taskVal = 0f;
		HealthDataMetrics metricEntries = ProjectHealthVariables.getRuleHealthEntries();
		pHC.updateMetricEntries(sprintEntries, metricEntries);
		List<MetricsModel> ruleSprint = ProjectHealthVariables.getMetricsListData();
		ruleSprint = ProjectHealthRepos.metricsRepo.findByPNameAndSName(ProjectHealthVariables.getProjectName(),
				ruleSprint.get(0).getsName());

		taskVal = getTaskOCRule(ruleSprint, datesArray);
		doMathResult = pHC.doMath(taskVal, Integer.parseInt(metricEntries.getGoal()), metricEntries.getOperator());
		pHC.calculateRuleHealth(doMathResult, metricEntries);
		metricEntries.setSprintValue(Math.round((int) taskVal));
	}

	public double getTaskOCRule(List<MetricsModel> ruleSprint, long[] datesArray) {
		double taskVal = 0f, taskPlanned = 0f, taskCompleted = 0f;
		for (MetricsModel sprintData : ruleSprint) {
			try {
				if (ProjectHealthVariables.getTaskName().indexOf(sprintData.getType()) > -1
						&& sprintData.getCreateDate() < datesArray[1]) {
					if (ProjectHealthVariables.getCloseState().indexOf(sprintData.getState()) > -1
							&& sprintData.getDoneDate() < datesArray[1]) {
						taskCompleted = taskCompleted + 1;
					}
					taskPlanned = taskPlanned + 1;
				}

			} catch (NullPointerException e) {
				continue;
			}
		}
		taskVal = Math.floor(calculatePercent(taskCompleted, taskPlanned));

		return taskVal;
	}

	// StoriesOpen vs StoriesClosed
	public void velocityStoryOpenvsClosed(ProjectHealthRuleSet sprintEntries, long[] datesArray) {
		double storyValue = 0f;
		HealthDataMetrics metricEntries = ProjectHealthVariables.getRuleHealthEntries();
		pHC.updateMetricEntries(sprintEntries, metricEntries);
		List<MetricsModel> metrics = ProjectHealthVariables.getMetricsListData();
		IterationModel model = null;
		if (metrics != null) {
			model = ProjectHealthRepos.iterationRepo.findBySNameAndPName(metrics.get(0).getsName(),
					ProjectHealthVariables.getProjectName());
			datesArray[0] = model.getStDate();
			if (model.getCompletedDate() == 0)
				datesArray[1] = model.getEndDate();
			else
				datesArray[1] = model.getCompletedDate();
		}
		
		List<MetricsModel> storyArray=new ArrayList<MetricsModel>() ;
		if(model!=null) {
			storyArray = ProjectHealthRepos.metricsRepo.findByPNameAndSNameAndType(
					ProjectHealthVariables.getProjectName(), model.getsName(), ProjectHealthVariables.getSTORY());
		}
		
		storyValue = getStoryOCRule(storyArray, datesArray);
		doMathResult = pHC.doMath(storyValue, Integer.parseInt(metricEntries.getGoal()), metricEntries.getOperator());

		pHC.calculateRuleHealth(doMathResult, metricEntries);
		metricEntries.setSprintValue(Math.round((int) storyValue));
	}

	public double getStoryOCRule(List<MetricsModel> storyArray, long[] datesArray) {
		double storyPlanned = 0f, storyCompleted = 0f, sPoint = 0, storyValue = 0f;
		for (MetricsModel sprintData : storyArray) {
			try {
				Map<Long, Double> sKeyMap = null;

				sKeyMap = sprintData.getStoryPoints();
				sPoint = getStoryPoint(sKeyMap, datesArray);
				if (ProjectHealthVariables.getCloseState().indexOf(sprintData.getState()) > -1
						&& sprintData.getDoneDate() < datesArray[1]) {

					storyCompleted = storyCompleted + sPoint;

				}
				storyPlanned = storyPlanned + sPoint;
			} catch (NullPointerException e) {
				continue;
			}
		}
		storyValue = Math.round(calculatePercent(storyCompleted, storyPlanned));
		return storyValue;
	}

	public double getStoryPoint(Map<Long, Double> sKeyMap, long[] datesArray) {
		double storyPoint = 0;
		long endDate = datesArray[1];
		List<Long> keyList = null;
		List<Double> valueList = null;
		if (!sKeyMap.isEmpty()) {
			keyList = new ArrayList<Long>(sKeyMap.keySet());
			valueList = new ArrayList<Double>(sKeyMap.values());
		}
		if(keyList!=null) {
			for (int i = 0; i < keyList.size(); i++) {

				if ((i == keyList.size() - 1)) {

					storyPoint = (double) valueList.get(i);
				} else if ((Long) keyList.get(i) < endDate && (Long) keyList.get(i + 1) > endDate) {
					storyPoint = (double) valueList.get(i);
				}

			}
		}
		
		/*
		 * for (int i = 0; i < keyList.size() - 1; i++) { if (i < keyList.size() - 1)
		 * nextTimestamp = (long) keyList.get(i + 1); if ((i == keyList.size() - 1 &&
		 * timeStamp < endDate) || (timeStamp < endDate && (nextTimestamp > endDate))) {
		 * timeStamp = (long) keyList.get(i); storyPoint = (double) valueList.get(i); }
		 *
		 * }
		 */
		return storyPoint;

	}

	// Remaining Task Open Percent rule
	public void taskRemainingvstotal(ProjectHealthRuleSet sprintEntries, long[] datesArray) {
		double taskVal = 0;

		HealthDataMetrics metricEntries = ProjectHealthVariables.getRuleHealthEntries();
		List<MetricsModel> ruleSprint = ProjectHealthVariables.getMetricsListData();
		pHC.updateMetricEntries(sprintEntries, metricEntries);

		taskVal = getTaskOpenPercent(ruleSprint, datesArray);

		doMathResult = pHC.doMath(taskVal, Integer.parseInt(metricEntries.getGoal()), metricEntries.getOperator());
		pHC.calculateRuleHealth(doMathResult, metricEntries);
		metricEntries.setSprintValue(Math.round((int) taskVal));
	}

	public double getTaskOpenPercent(List<MetricsModel> ruleSprint, long[] datesArray) {
		double taskVal = 0, taskPlanned = 0, taskCompleted = 0;
		for (MetricsModel sprintData : ruleSprint) {
			try {
				if (ProjectHealthVariables.getTaskName().indexOf(sprintData.getType()) > -1) {
					if (ProjectHealthVariables.getCloseState().indexOf(sprintData.getState()) > -1
							&& sprintData.getDoneDate() < datesArray[1]) {
						taskCompleted = taskCompleted + 1;
					}
					taskPlanned = taskPlanned + 1;
				}

			} catch (NullPointerException e) {
				continue;
			}
		}
		double taskLeft = taskPlanned - taskCompleted;
		taskVal = Math.round(calculatePercent(taskLeft, taskPlanned));
		return taskVal;
	}

	public double calculatePercent(double entry1, double entry2) {
		double taskVal = 0;

		if (Double.doubleToRawLongBits(entry2) == 0) {
			taskVal = 0;
		} else {
			taskVal = (entry1 / entry2) * 100;
		}

		return taskVal;
	}

	// Requirement Volatility TASK
	public void reqVolatilityTask(ProjectHealthRuleSet healthRuleSet, long[] datesArray) {
		// return doMathResult;

		List<MetricsModel> metrics = ProjectHealthVariables.getMetricsListData();
		HealthDataMetrics healthDataMetrics = ProjectHealthVariables.getRuleHealthEntries();
		// ProjectHealthVariables.getSprintId();

		double reqVolatilityTaskRate = 0f;

		pHC.updateMetricEntries(healthRuleSet, healthDataMetrics);

		if (!metrics.isEmpty()) {

			reqVolatilityTaskRate = getReqVolt(metrics, datesArray);

			doMathResult = pHC.doMath(reqVolatilityTaskRate, Integer.parseInt(healthDataMetrics.getGoal()),
					healthDataMetrics.getOperator());

			pHC.calculateRuleHealth(doMathResult, healthDataMetrics);

			healthDataMetrics.setSprintValue(Math.round((int) reqVolatilityTaskRate));
		} else {
			doMathResult = pHC.doMath(reqVolatilityTaskRate, Integer.parseInt(healthDataMetrics.getGoal()),
					healthDataMetrics.getOperator());
			pHC.calculateRuleHealth(doMathResult, healthDataMetrics);

			healthDataMetrics.setSprintValue(Math.round((int) reqVolatilityTaskRate));
		}

	}

	public double getReqVolt(List<MetricsModel> metrics, long[] datesArray) {
		MetricsModel metricsModel = null;
		Map<Long, String> allocatedDateMap = null;
		double firstTaskCount = 0f, newTaskCount = 0f, reqVolatilityTaskRate = 0f;
		List<String> isssueID = new ArrayList<>();
		String sprintId = Integer.toString(ProjectHealthVariables.getSprintId());

		ListIterator<MetricsModel> mIt = metrics.listIterator();
		while (mIt.hasNext()) {
			metricsModel = mIt.next();
			allocatedDateMap = metricsModel.getAllocatedDate();
			long allocatedDate = 0;
			Object[] objArray = new Object[2];
			if (!(allocatedDateMap == null)) {
				objArray = getKeyValueArray(allocatedDateMap);

				@SuppressWarnings("unchecked")
				List<String> sprints = (List<String>) objArray[1];
				@SuppressWarnings("unchecked")
				List<Long> datesList = (List<Long>) objArray[0];
				for (int i = 0; i < sprints.size(); i++) {
					List<String> sprintArray = Arrays.asList(sprints.get(i).split(","));
					if (sprintArray.contains(sprintId)) {

						isssueID.add(metricsModel.getwId());
						allocatedDate = datesList.get(i);
						if (allocatedDate < datesArray[0]
								&& (i == sprints.size() - 1 || datesList.get(i + 1) > datesArray[0])) {
							firstTaskCount++;
						}
						if (allocatedDate > datesArray[0] && allocatedDate < datesArray[1]
								&& isssueID.contains(metricsModel.getwId())) {
							newTaskCount++;
						}
					}
				}
			}
		}

//	    if (newTaskCount > firstTaskCount && firstTaskCount > 0f) {
//	    	reqVolatilityTaskRate = (newTaskCount / firstTaskCount) * 100;
//	    } else {
//		reqVolatilityTaskRate = 0;
//	    }

		reqVolatilityTaskRate = calculatePercent(newTaskCount, firstTaskCount);

		return reqVolatilityTaskRate;
	}

	public Object[] getKeyValueArray(Map<Long, String> allocatedDateMap) {
		List<Long> keyList = new ArrayList<>();
		List<String> valueList = new ArrayList<>();
		Set<Entry<Long, String>> entrySet = allocatedDateMap.entrySet();

		Iterator<Entry<Long, String>> entryIt = entrySet.iterator();
		while (entryIt.hasNext()) {
			Entry<Long, String> entry = entryIt.next();
			keyList.add(entry.getKey());
			valueList.add(entry.getValue());
		}
		Object[] objArray = new Object[2];
		objArray[0] = keyList;
		objArray[1] = valueList;
		return objArray;
	}

	// Story requirement Volatility
	public double reqVolatilityStory(ProjectHealthRuleSet healthRuleSet, long[] datesArray) {
		HealthDataMetrics healthDataMetrics = ProjectHealthVariables.getRuleHealthEntries();
		pHC.updateMetricEntries(healthRuleSet, healthDataMetrics);
		double reqVolatilityStoryRate = 0f;
		List<MetricsModel> metricList = new ArrayList<>();
		// if (!metricList.isEmpty()) {
		for (int i = 0; i < ProjectHealthVariables.getMetricsListData().size(); i++) {
			MetricsModel model = ProjectHealthVariables.getMetricsListData().get(i);
			if (model.getType().equalsIgnoreCase(ProjectHealthVariables.getSTORY())) {
				metricList.add(model);
			}
		}

		reqVolatilityStoryRate = getReqSVolt(metricList, datesArray);
		doMathResult = pHC.doMath(reqVolatilityStoryRate, Integer.parseInt(healthDataMetrics.getGoal()),
				healthDataMetrics.getOperator());
		pHC.calculateRuleHealth(doMathResult, healthDataMetrics);

		healthDataMetrics.setSprintValue(Math.round((int) reqVolatilityStoryRate));
//	} else {
		doMathResult = pHC.doMath(reqVolatilityStoryRate, Integer.parseInt(healthDataMetrics.getGoal()),
				healthDataMetrics.getOperator());
		pHC.calculateRuleHealth(doMathResult, healthDataMetrics);

		healthDataMetrics.setSprintValue(Math.round((int) reqVolatilityStoryRate));
		// }
		return reqVolatilityStoryRate;
	}

	@SuppressWarnings({ "unchecked", "null" })
	public double getReqSVolt(List<MetricsModel> metrics, long[] datesArray) {
		MetricsModel metricsModel = null;
		Map<Long, String> allocatedDateMap = null;
		Map<Long, Double> sPointsMap = null;
		double firstSPointsCount = 0f, newSpointsCount = 0f, reqVolatilityStoryRate = 0f;
		Set<String> isssueID = new HashSet<String>();
		String sprintId = Integer.toString(ProjectHealthVariables.getSprintId());

		ListIterator<MetricsModel> mIt = metrics.listIterator();
		while (mIt.hasNext()) {
			metricsModel = mIt.next();
			allocatedDateMap = metricsModel.getAllocatedDate();
			sPointsMap = metricsModel.getStoryPoints();
			long allocatedDate = 0;
			Object[] objArray = new Object[2];
			if (allocatedDateMap != null && sPointsMap != null) {
				objArray = getKeyValueArray(allocatedDateMap);
				List<Long> sMapKeySet = new ArrayList<Long>(sPointsMap.keySet());
				List<String> sprints = (List<String>) objArray[1];
				List<Long> datesList = (List<Long>) objArray[0];
				for (int i = 0; i < sprints.size(); i++) {
					List<String> sprintArray = Arrays.asList(sprints.get(i).split(","));
					if (sprintArray.contains(sprintId)) {
						allocatedDate = datesList.get(i);
						// if (allocatedDate < datesArray[0]&&(i==sprints.size()-1||
						// datesList.get(i+1)>datesArray[0])){
						// isssueID.add(metricsModel.getwId());
						// firstTaskCount++;
						for (int j = 0; j < sMapKeySet.size(); j++) {
							if (sMapKeySet.get(j) < datesArray[0]
									&& (j == sPointsMap.size() - 1 || sMapKeySet.get(j + 1) > datesArray[0])) {
								firstSPointsCount = firstSPointsCount + sPointsMap.get(sMapKeySet.get(j));
							
							} else if (sMapKeySet.get(j) > datesArray[0] && sMapKeySet.get(j) < datesArray[1]
									&& (j == sPointsMap.size() - 1 || sMapKeySet.get(j + 1) > datesArray[1])) {
								newSpointsCount = newSpointsCount + sPointsMap.get(sMapKeySet.get(j));
								
							}
						}
						// }
					}
				}
			}
		}

		reqVolatilityStoryRate = calculatePercent(newSpointsCount, firstSPointsCount);

		return reqVolatilityStoryRate;
	}

	// Team Efficiency Rule
	public double teamEfficiency(ProjectHealthRuleSet sprintEntries, long[] datesArray) {

		double efficiency = 0f;
		long startDate = datesArray[3];
		HealthDataMetrics metricEntries = ProjectHealthVariables.getRuleHealthEntries();
		List<MetricsModel> metrics = ProjectHealthVariables.getMetricsListData();
		pHC.updateMetricEntries(sprintEntries, metricEntries);
		// List<ALMToolMetric> almToolMetricsList = ruleSprint.getMetrics();

		efficiency = getEfficiency(metrics, datesArray);
		doMathResult = pHC.doMath(efficiency, Integer.parseInt(metricEntries.getGoal()), metricEntries.getOperator());

		pHC.calculateRuleHealth(doMathResult, metricEntries);
		metricEntries.setSprintValue(Math.round((int) efficiency));
		return efficiency;

	}

	public double getEfficiency(List<MetricsModel> metrics, long[] datesArray) {
		// datesArray[2]->CompletionDate (for HealthRulesPerDay)
		double taskCompleted = 0f, taskPlanned = 0f, bugClosed = 0f, bugIdentified = 0f, bugPercent = 0f;
		double efficiency = 0f;
		long startDate = datesArray[0];
		Iterator<MetricsModel> metricIterator = metrics.iterator();
		double taskPercent = 0;

		while (metricIterator.hasNext()) {

			MetricsModel metric = metricIterator.next();

			if (ProjectHealthVariables.getTaskName().indexOf(metric.getType()) > -1) {
				if (ProjectHealthVariables.getCloseState().indexOf(metric.getState()) > -1 && metric.getDoneDate() != 0
						&& metric.getDoneDate() < datesArray[1]) {
					taskCompleted = taskCompleted + 1;
				}
				taskPlanned = taskPlanned + 1;
			}

			if ((ProjectHealthVariables.getBug().equals(metric.getType()))
					&& (metric.getCreateDate() > startDate && metric.getCreateDate() < datesArray[1])) {
				if (ProjectHealthVariables.getCloseState().indexOf(metric.getState()) > -1) {
					bugClosed = bugClosed + 1;
				}
				bugIdentified++;
			}

		}

		if (datesArray[2] != (0)) {
			int thisDaysCount = (int) Math.ceil((datesArray[1] - datesArray[0]) /(double) 86400000);
			int SprintDaysCount = (int) Math.ceil((datesArray[2] - datesArray[0]) /(double) 86400000);
			double expectedWorkCompleted = (taskPlanned / SprintDaysCount) * thisDaysCount;
			taskPercent = Math.round(calculatePercent(taskCompleted, expectedWorkCompleted));
		}

		else {
			taskPercent = Math.round(calculatePercent(taskCompleted, taskPlanned)); /* task % */
		}
		if (Double.doubleToRawLongBits(bugIdentified) == 0f) {
			efficiency = taskPercent;
		} else {
			bugPercent = calculatePercent(bugClosed, bugIdentified); /* bug % */
			efficiency = (taskPercent + bugPercent) / 2; /*
															 * task and bug contribution to efficiency is 50% each
															 */
		}
		return efficiency;
	}

	// Task Risk Rule
	public void taskRisk(ProjectHealthRuleSet sprintEntries, long[] datesArray) {

		long startDate = datesArray[0];
		long endDate = datesArray[1];
		HealthDataMetrics metricEntries = ProjectHealthVariables.getRuleHealthEntries();
		double sprintWorkingDays = getWorkingDaysBetweenTwoDates(new Date(startDate), new Date(endDate));
		double sprintCurrentDay = getWorkingDaysBetweenTwoDates(new Date(startDate), new Date());
		if (sprintCurrentDay > sprintWorkingDays) {
			sprintCurrentDay = sprintWorkingDays;
		}
		pHC.updateMetricEntries(sprintEntries, metricEntries);

		double score = getScore(sprintWorkingDays, sprintCurrentDay, metricEntries, sprintEntries);
		doMathResult = pHC.doMath(score, Integer.parseInt(metricEntries.getGoal()), metricEntries.getOperator());

		pHC.calculateRuleHealth(doMathResult, metricEntries);
		metricEntries.setSprintValue(Math.round((int) score));

	}

	public double getScore(double sprintWorkingDays, double sprintCurrentDay, HealthDataMetrics metricEntries,
			ProjectHealthRuleSet sprintEntries) {
		double remainingEffort = 0f, totalTasks = 0f, totalEst = 0f, totalEffort = 0f;
		int taskCompletion = 0;

		double value = ((sprintWorkingDays - sprintCurrentDay) * 3600) * 8.0;
		double daysCompletionPercentage = (sprintCurrentDay / sprintWorkingDays) * 100;
		Iterator<MetricsModel> metricListIterator = ProjectHealthVariables.getMetricsListData().iterator();
		while (metricListIterator.hasNext()) {

			MetricsModel metric = metricListIterator.next();
			if (!(metric.getEffort() == null))
				totalEffort = totalEffort + metric.getEffort();
			if (!(metric.getOrgEst() == null))
				totalEst = totalEst + metric.getOrgEst();
			if (!metric.getType().equals(ProjectHealthVariables.getSTORY())
					&& !metric.getType().equals(ProjectHealthVariables.getEPIC())
					&& metric.getStatusCategory().equals(ProjectHealthVariables.getDONE_STATE())) {
				taskCompletion++;
			}
			totalTasks++;
		}
		remainingEffort = totalEst - totalEffort;
		double taskCompletionPercentage = taskCompletion / totalTasks * 100;
		double riskScore = 0, riskWeightage = 30;
		double taskScore = 0, taskWeightage = 70;

		if (remainingEffort <= value) {
			riskScore = riskWeightage;
		} else {
			riskScore = (value / remainingEffort) * riskWeightage;

		}
		taskScore = taskWeightage;
		if (daysCompletionPercentage >= 30) {
			taskScore = getTaskRiskScore(daysCompletionPercentage, taskCompletionPercentage, taskScore, taskWeightage,
					taskCompletion, totalTasks, metricEntries, sprintEntries);
		}

		return riskScore + taskScore;
	}

	// Gets total number of working days
	public double getWorkingDaysBetweenTwoDates(Date startDate, Date endDate) {
		Calendar startCal = Calendar.getInstance();
		startCal.setTime(startDate);

		Calendar endCal = Calendar.getInstance();
		endCal.setTime(endDate);

		double workDays = 0f;

		// Return 0 if start and end are the same
		if (startCal.getTimeInMillis() == endCal.getTimeInMillis()) {
			return 0;
		}

		if (startCal.getTimeInMillis() > endCal.getTimeInMillis()) {
			startCal.setTime(endDate);
			endCal.setTime(startDate);
		}

		do {
			// excluding start date
			startCal.add(Calendar.DAY_OF_MONTH, 1);
			if (startCal.get(Calendar.DAY_OF_WEEK) != Calendar.SATURDAY
					&& startCal.get(Calendar.DAY_OF_WEEK) != Calendar.SUNDAY) {
				++workDays;
			}
		} while (startCal.getTimeInMillis() < endCal.getTimeInMillis()); // excluding
		// end
		// date

		return workDays;
	}

	// Gets Task Risk Score
	public double getTaskRiskScore(double daysCompletionPercentage, double taskCompletionPercentage, double taskScore,
			double taskWeightage, double taskCompletion, double totalTasks, HealthDataMetrics metricEntries,
			ProjectHealthRuleSet sprintEntries) {
		double taskScoreNewValue = taskScore;
		pHC.updateMetricEntries(sprintEntries, metricEntries);
		if (daysCompletionPercentage >= 30 && daysCompletionPercentage <= 40) {
			if (taskCompletionPercentage >= 10) {
				taskScoreNewValue = taskWeightage;
			} else {
				taskScoreNewValue = taskCompletion / totalTasks * taskWeightage;
			}

		} else if (daysCompletionPercentage >= 40 && daysCompletionPercentage <= 50) {
			if (taskCompletionPercentage >= 20) {
				taskScoreNewValue = taskWeightage;
			} else {
				taskScoreNewValue = taskCompletion / totalTasks * taskWeightage;
			}

		} else if (daysCompletionPercentage >= 50 && daysCompletionPercentage <= 60) {
			if (taskCompletionPercentage >= 30) {
				taskScoreNewValue = taskWeightage;
			} else {
				taskScoreNewValue = taskCompletion / totalTasks * taskWeightage;
			}
		} else if (daysCompletionPercentage >= 60 && daysCompletionPercentage <= 70) {
			if (taskCompletionPercentage >= 45) {
				taskScoreNewValue = taskWeightage;
			} else {
				taskScoreNewValue = taskCompletion / totalTasks * taskWeightage;
			}
		} else if (daysCompletionPercentage >= 70 && daysCompletionPercentage <= 80) {
			if (taskCompletionPercentage >= 60) {
				taskScoreNewValue = taskWeightage;
			} else {
				taskScoreNewValue = taskCompletion / totalTasks * taskWeightage;
			}
		} else if (daysCompletionPercentage >= 80 && daysCompletionPercentage <= 90) {
			if (taskCompletionPercentage >= 80) {
				taskScoreNewValue = taskWeightage;
			} else {
				taskScoreNewValue = taskCompletion / totalTasks * taskWeightage;
			}

		} else if (daysCompletionPercentage >= 90 && daysCompletionPercentage <= 1000) {
			if (taskCompletionPercentage >= 100) {
				taskScoreNewValue = taskWeightage;
			} else {
				taskScoreNewValue = taskCompletion / totalTasks * taskWeightage;
			}
		}

		return taskScoreNewValue;
	}

	public void effortOverrun(ProjectHealthRuleSet sprintEntries, long[] datesArray) {

		HealthDataMetrics metricEntries = ProjectHealthVariables.getRuleHealthEntries();
		List<MetricsModel> metricList = ProjectHealthVariables.getMetricsListData();
		pHC.updateMetricEntries(sprintEntries, metricEntries);

		double effortOverRunPercentage = getEffortOverRun(metricList, datesArray);
		if (effortOverRunPercentage < 0)
			effortOverRunPercentage = 0;

		doMathResult = pHC.doMath(effortOverRunPercentage, Integer.parseInt(metricEntries.getGoal()),
				metricEntries.getOperator());

		pHC.calculateRuleHealth(doMathResult, metricEntries);
		metricEntries.setSprintValue(Math.round((int) effortOverRunPercentage));

	}

	public double getEffortOverRun(List<MetricsModel> metricList, long[] datesArray) {
		// datesArray[0]->StartDate, dateArray[1]->EndDate
		double orgEst = 0f;
		double effort = 0f;
		double effortOverRunPercentage = 0f;
		Iterator<MetricsModel> metricIterator = metricList.iterator();
		while (metricIterator.hasNext()) {
			MetricsModel metric = metricIterator.next();
			if (ProjectHealthVariables.getTaskName().indexOf(metric.getType()) > -1) {
				List<EffortHistoryModel> effortList = ProjectHealthRepos.effortRepo
						.findByPNameAndSNameAndWId(metric.getpName(), metric.getsName(), metric.getwId());
				double initialEst = 0f;

				for (EffortHistoryModel effortEntry : effortList) {
					if (effortEntry.getLogDate() > datesArray[0] && effortEntry.getLogDate() < datesArray[1]) {
						initialEst = effortEntry.getInitialWork() + effortEntry.getRemainingWorkInc()
								- effortEntry.getRemainingWorkDec();
						if (effortEntry.getTimeSpent() != null)
							effort = effort + effortEntry.getTimeSpent();
					}
				}

				orgEst = orgEst + initialEst;
			}
		}
		
		if(orgEst > 0) {
			effortOverRunPercentage = Math.round(((effort - orgEst) / orgEst) * 100);			
		}

		return effortOverRunPercentage;
	}
}
