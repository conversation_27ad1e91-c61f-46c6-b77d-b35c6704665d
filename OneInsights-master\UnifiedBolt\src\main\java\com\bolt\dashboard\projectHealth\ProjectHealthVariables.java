package com.bolt.dashboard.projectHealth;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.HealthData;
import com.bolt.dashboard.core.model.HealthDataMetrics;
import com.bolt.dashboard.core.model.HealthProjectSprintMetrics;
import com.bolt.dashboard.core.model.HealthSprintMetrics;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.ProjectHealth;
import com.bolt.dashboard.core.model.ProjectHealthApplicationPhase;
import com.bolt.dashboard.core.model.ProjectHealthConfig;
import com.bolt.dashboard.core.model.ProjectHealthRuleSet;

public class ProjectHealthVariables {

	// new confiuration implementation
	private static final String RULE_CRITICAL_DEFECT = "Critical Defect";
	private static String projectName = null;
	private static String STORY = "";
	private static String EPIC = "";
	private static String bug = "";
	private static List<String> taskName;
	private static String closeState;
	private static List<String> criticalPriority;
	private static List<String> highPriority;
	private static List<String> medPriority;
	private static List<String> lowPriority;
	private static String priorityName;
	private static List<IterationModel> iterationList;
	private static List<IterationModel> pIterationList;
	private static List<IterationModel> currentIterationList;
	private static List<MetricsModel> backLogIteration;
	private static List<ProjectHealth> projecthealthdata = null;
	private static ConfigurationSetting config = null;
	private static List<ProjectHealthConfig> configuration;
	private static List<IterationModel> iterationListData;
	private static List<MetricsModel> metricsListData;
	private static List<MetricsModel> defectsListData;
	private static int greenFromValue, amberToValue, amberFromValue;
	private static Map<String, Integer> phaseHashMap = null;
	private static double totalWeightage;
	private static Map<String, Map<String, Integer>> sprintPhaseHashMap = new HashMap<String, Map<String, Integer>>();
	private static Map<Integer, Map<String, Map<String, Integer>>> sprintPhaseIterationHashMap = new HashMap<Integer, Map<String, Map<String, Integer>>>();
	private static int sprintId = 0;
	private static List<ProjectHealthApplicationPhase> applicationPhaseList = null;
	private static List<ProjectHealthConfig> projectHealthConfigs = null;
	private static String sprintName = null;
	private static HealthData healthdata = null;
	private static List<HealthProjectSprintMetrics> sprintDataList = new ArrayList<>();
	private static List<HealthSprintMetrics> sprintHealthList = new ArrayList<>();
	private static HealthData healthEntryLastEntry = null;
	private static HealthDataMetrics ruleHealthEntries = null;
	private static final String NEXT_ITEARTION = "Next";
	private static List<Integer> phaseWeightageArray = null;
	private static List<ProjectHealthRuleSet> entriesList = null;
	private static ProjectHealth lastentries = null;
	private static final String DONE_STATE = "Done";
	private final String RESOLVED_STATE = "Resolved";
	private static final String PAST_ITERATION = "Past";
	private static final String CURRENT_ITERATION = "Current";
	private static final String NO_HEALTH = "GREY";
	private static int a = 1;
	private static IterationModel iteration = null;
	private static final String GREEN_HEALTH = "GREEN";
	private static Map<String, Integer> storyBugHashMap = null;
	private static Map<String, Integer> stateHashMap = null;
	private static Map<String, Integer> bugReopenCountHashMap = null;
	private static Map<String, Integer> goalHashMap = null;
	private static Map<String, Integer> pointsHashMap = null;
	private static Map<String, String> operatorHashMap = null;
	private static Map<String, Integer> weightageHashMap = null;
	private static String[] iterationSprintArray = null;
	
	private static String priorityFunction = null;
	private static final String AMBER_HEALTH = "AMBER";
	private static final String RED_HEALTH = "RED";
	/* variable declartion for build data - START */
	private static final String BUILD_STATUS_KEY = "result";
	private static String BUILD_STATUS = "FAILURE";
	public static void setBUILD_STATUS(String bUILD_STATUS) {
		BUILD_STATUS = bUILD_STATUS;
	}

	private static String BUILD_SUCCESS = "SUCCESS";
	public static void setBUILD_SUCCESS(String bUILD_SUCCESS) {
		BUILD_SUCCESS = bUILD_SUCCESS;
	}

	private static final String BUILD_DURATION_KEY = "duration";
	private static String ALMTYPE = null;
	private static List<String> pastIterationName = new ArrayList<>();
	private static List<String> currentIterationName = null;

	public static String getRULE_CRITICAL_DEFECT() {
		return RULE_CRITICAL_DEFECT;
	}

	public static String getBUILD_STATUS_KEY() {
		return BUILD_STATUS_KEY;
	}

	public static String getBUILD_STATUS() {
		return BUILD_STATUS;
	}

	public static String getBUILD_SUCCESS() {
		return BUILD_SUCCESS;
	}

	public static String getBUILD_DURATION_KEY() {
		return BUILD_DURATION_KEY;
	}

	public static String getPriorityFunction() {
		return priorityFunction;
	}

	public static String getGREEN_HEALTH() {
		return GREEN_HEALTH;
	}

	public static String getAMBER_HEALTH() {
		return AMBER_HEALTH;
	}

	public static String getRED_HEALTH() {
		return RED_HEALTH;
	}

	public static String getPAST_ITERATION() {
		return PAST_ITERATION;
	}

	public static String getCURRENT_ITERATION() {
		return CURRENT_ITERATION;
	}

	public static String getNEXT_ITEARTION() {
		return NEXT_ITEARTION;
	}

	public static String getSTORY() {
		return STORY;
	}

	public static void setSTORY(String sTORY) {
		STORY = sTORY;
	}

	public static String getBug() {
		return bug;
	}

	public static void setBug(String bug) {
		ProjectHealthVariables.bug = bug;
	}

	public static List<String> getTaskName() {
		return taskName;
	}

	public static void setTaskName(List<String> taskName) {
		ProjectHealthVariables.taskName = taskName;
	}

	public static String getCloseState() {
		return closeState;
	}

	public static void setCloseState(String closeState) {
		ProjectHealthVariables.closeState = closeState;
	}

	public static List<String> getCriticalPriority() {
		return criticalPriority;
	}

	public static void setCriticalPriority(List<String> criticalPriority) {
		ProjectHealthVariables.criticalPriority = criticalPriority;
	}

	public static List<String> getHighPriority() {
		return highPriority;
	}

	public static void setHighPriority(List<String> highPriority) {
		ProjectHealthVariables.highPriority = highPriority;
	}

	public static List<String> getMedPriority() {
		return medPriority;
	}

	public static void setMedPriority(List<String> medPriority) {
		ProjectHealthVariables.medPriority = medPriority;
	}

	public static List<String> getLowPriority() {
		return lowPriority;
	}

	public static void setLowPriority(List<String> lowPriority) {
		ProjectHealthVariables.lowPriority = lowPriority;
	}

	public static String getPriorityName() {
		return priorityName;
	}

	public static void setPriorityName(String priorityName) {
		ProjectHealthVariables.priorityName = priorityName;
	}

	public static void setPriorityFunction(String priorityFunction) {
		ProjectHealthVariables.priorityFunction = priorityFunction;
	}

	public static List<IterationModel> getIterationList() {
		return iterationList;
	}

	public static void setIterationList(List<IterationModel> iterationList) {
		ProjectHealthVariables.iterationList = iterationList;
	}

	public static List<IterationModel> getpIterationList() {
		return pIterationList;
	}

	public static void setpIterationList(List<IterationModel> pIterationList) {
		ProjectHealthVariables.pIterationList = pIterationList;
	}

	public static void setCurrentIterationList(List<IterationModel> currentIterationList) {
		ProjectHealthVariables.currentIterationList = currentIterationList;
	}

	public static List<IterationModel> getCurrentIterationList() {
		return currentIterationList;
	}

	public static ConfigurationSetting getConfig() {
		return config;
	}

	public static void setConfig(ConfigurationSetting config) {
		ProjectHealthVariables.config = config;
	}

	public static List<ProjectHealth> getProjecthealthdata() {
		return projecthealthdata;
	}

	public static void setProjecthealthdata(List<ProjectHealth> projecthealthdata) {
		ProjectHealthVariables.projecthealthdata = projecthealthdata;
	}

	public static List<IterationModel> getIterationListData() {
		return iterationListData;
	}

	public static void setIterationListData(List<IterationModel> iterationListData) {
		ProjectHealthVariables.iterationListData = iterationListData;
	}

	public static List<MetricsModel> getMetricsListData() {
		return metricsListData;
	}

	public static void setMetricsListData(List<MetricsModel> metricsListData) {
		ProjectHealthVariables.metricsListData = metricsListData;
	}

	public static List<ProjectHealthConfig> getConfiguration() {
		return configuration;
	}

	public static void setConfiguration(List<ProjectHealthConfig> configuration) {
		ProjectHealthVariables.configuration = configuration;
	}

	public static int getGreenFromValue() {
		return greenFromValue;
	}

	public static void setGreenFromValue(int greenFromValue) {
		ProjectHealthVariables.greenFromValue = greenFromValue;
	}

	public static int getAmberToValue() {
		return amberToValue;
	}

	public static void setAmberToValue(int amberToValue) {
		ProjectHealthVariables.amberToValue = amberToValue;
	}

	public static int getAmberFromValue() {
		return amberFromValue;
	}

	public static void setAmberFromValue(int amberFromValue) {
		ProjectHealthVariables.amberFromValue = amberFromValue;
	}

	public static Map<String, Integer> getPhaseHashMap() {
		return phaseHashMap;
	}

	public static void setPhaseHashMap(Map<String, Integer> phaseHashMap) {
		ProjectHealthVariables.phaseHashMap = phaseHashMap;
	}

	public static double getTotalWeightage() {
		return totalWeightage;
	}

	public static void setTotalWeightage(double totalWeightage) {
		ProjectHealthVariables.totalWeightage = totalWeightage;
	}

	public static Map<String, Map<String, Integer>> getSprintPhaseHashMap() {
		return sprintPhaseHashMap;
	}

	public static void setSprintPhaseHashMap(Map<String, Map<String, Integer>> sprintPhaseHashMap) {
		ProjectHealthVariables.sprintPhaseHashMap = sprintPhaseHashMap;
	}

	public static String getEPIC() {
		return EPIC;
	}

	public static void setEPIC(String ePIC) {
		EPIC = ePIC;
	}

	public static int getSprintId() {
		return sprintId;
	}

	public static void setSprintId(int sprintId) {
		ProjectHealthVariables.sprintId = sprintId;
	}

	public static List<ProjectHealthApplicationPhase> getApplicationPhaseList() {
		return applicationPhaseList;
	}

	public static void setApplicationPhaseList(List<ProjectHealthApplicationPhase> applicationPhaseList) {
		ProjectHealthVariables.applicationPhaseList = applicationPhaseList;
	}

	public static List<ProjectHealthConfig> getProjectHealthConfigs() {
		return projectHealthConfigs;
	}

	public static void setProjectHealthConfigs(List<ProjectHealthConfig> projectHealthConfigs) {
		ProjectHealthVariables.projectHealthConfigs = projectHealthConfigs;
	}

	public static String getSprintName() {
		return sprintName;
	}

	public static void setSprintName(String sprintName) {
		ProjectHealthVariables.sprintName = sprintName;
	}

	public static Map<Integer, Map<String, Map<String, Integer>>> getSprintPhaseIterationHashMap() {
		return sprintPhaseIterationHashMap;
	}

	public static void setSprintPhaseIterationHashMap(
			Map<Integer, Map<String, Map<String, Integer>>> sprintPhaseIterationHashMap) {
		ProjectHealthVariables.sprintPhaseIterationHashMap = sprintPhaseIterationHashMap;
	}

	public static HealthData getHealthdata() {
		return healthdata;
	}

	public static void setHealthdata(HealthData healthdata) {
		ProjectHealthVariables.healthdata = healthdata;
	}

	public static List<HealthProjectSprintMetrics> getSprintDataList() {
		return sprintDataList;
	}

	public static void setSprintDataList(List<HealthProjectSprintMetrics> sprintDataList) {
		ProjectHealthVariables.sprintDataList = sprintDataList;
	}

	public static List<HealthSprintMetrics> getSprintHealthList() {
		return sprintHealthList;
	}

	public static void setSprintHealthList(List<HealthSprintMetrics> sprintHealthList) {
		ProjectHealthVariables.sprintHealthList = sprintHealthList;
	}


	public static HealthData getHealthEntryLastEntry() {
		return healthEntryLastEntry;
	}

	public static void setHealthEntryLastEntry(HealthData healthEntryLastEntry) {
		ProjectHealthVariables.healthEntryLastEntry = healthEntryLastEntry;
	}

	public static HealthDataMetrics getRuleHealthEntries() {
		return ruleHealthEntries;
	}

	public static void setRuleHealthEntries(HealthDataMetrics ruleHealthEntries) {
		ProjectHealthVariables.ruleHealthEntries = ruleHealthEntries;
	}

	public static List<Integer> getPhaseWeightageArray() {
		return phaseWeightageArray;
	}

	public static void setPhaseWeightageArray(List<Integer> phaseWeightageArray) {
		ProjectHealthVariables.phaseWeightageArray = phaseWeightageArray;
	}

	public static List<ProjectHealthRuleSet> getEntriesList() {
		return entriesList;
	}

	public static void setEntriesList(List<ProjectHealthRuleSet> entriesList) {
		ProjectHealthVariables.entriesList = entriesList;
	}

	public static ProjectHealth getLastentries() {
		return lastentries;
	}

	public static void setLastentries(ProjectHealth lastentries) {
		ProjectHealthVariables.lastentries = lastentries;
	}

	public static String getDONE_STATE() {
		return DONE_STATE;
	}

	public String getRESOLVED_STATE() {
		return RESOLVED_STATE;
	}

	public static String getPastIteration() {
		return PAST_ITERATION;
	}

	public static String getCurrentIteration() {
		return CURRENT_ITERATION;
	}

	public static String getNO_HEALTH() {
		return NO_HEALTH;
	}

	public static int getA() {
		return a;
	}

	public static void setA(int a) {
		ProjectHealthVariables.a = a;
	}

	public static IterationModel getIteration() {
		return iteration;
	}

	public static void setIteration(IterationModel iteration) {
		ProjectHealthVariables.iteration = iteration;
	}

	public static Map<String, Integer> getStoryBugHashMap() {
		return storyBugHashMap;
	}

	public static void setStoryBugHashMap(Map<String, Integer> storyBugHashMap) {
		ProjectHealthVariables.storyBugHashMap = storyBugHashMap;
	}

	public static Map<String, Integer> getStateHashMap() {
		return stateHashMap;
	}

	public static void setStateHashMap(Map<String, Integer> stateHashMap) {
		ProjectHealthVariables.stateHashMap = stateHashMap;
	}

	public static Map<String, Integer> getBugReopenCountHashMap() {
		return bugReopenCountHashMap;
	}

	public static void setBugReopenCountHashMap(Map<String, Integer> bugReopenCountHashMap) {
		ProjectHealthVariables.bugReopenCountHashMap = bugReopenCountHashMap;
	}

	public static String getProjectName() {
		return projectName;
	}

	public static void setProjectName(String projectName) {
		ProjectHealthVariables.projectName = projectName;
	}

	public static String getALMTYPE() {
		return ALMTYPE;
	}

	public static void setALMTYPE(String aLMTYPE) {
		ALMTYPE = aLMTYPE;
	}

	public static List<MetricsModel> getDefectsListData() {
		return defectsListData;
	}

	public static void setDefectsListData(List<MetricsModel> defectsListData) {
		ProjectHealthVariables.defectsListData = defectsListData;
	}

	public static List<String> getCurrentIterationName() {
		return currentIterationName;
	}

	public static void setCurrentIterationName(List<String> currentIterationName) {
		ProjectHealthVariables.currentIterationName = currentIterationName;
	}

	public static List<String> getPastIterationName() {
		return pastIterationName;
	}

	public static void setPastIterationName(List<String> pastIterationName) {
		ProjectHealthVariables.pastIterationName = pastIterationName;
	}

	public static Map<String, Integer> getGoalHashMap() {
		return goalHashMap;
	}

	public static void setGoalHashMap(Map<String, Integer> goalHashMap) {
		ProjectHealthVariables.goalHashMap = goalHashMap;
	}

	public static Map<String, Integer> getPointsHashMap() {
		return pointsHashMap;
	}

	public static void setPointsHashMap(Map<String, Integer> pointsHashMap) {
		ProjectHealthVariables.pointsHashMap = pointsHashMap;
	}

	public static Map<String, Integer> getWeightageHashMap() {
		return weightageHashMap;
	}

	public static void setWeightageHashMap(Map<String, Integer> weightageHashMap) {
		ProjectHealthVariables.weightageHashMap = weightageHashMap;
	}

	public static Map<String, String> getOperatorHashMap() {
		return operatorHashMap;
	}

	public static void setOperatorHashMap(Map<String, String> operatorHashMap) {
		ProjectHealthVariables.operatorHashMap = operatorHashMap;
	}

	public static List<MetricsModel> getBackLogIteration() {
		return backLogIteration;
	}

	public static void setBackLogIteration(List<MetricsModel> backLogIteration) {
		ProjectHealthVariables.backLogIteration = backLogIteration;
	}

	public static String[] getIterationSprintArray() {
		return iterationSprintArray;
	}

	public static void setIterationSprintArray(String[] iterationSprintArray) {
		ProjectHealthVariables.iterationSprintArray = iterationSprintArray;
	}



	

}
