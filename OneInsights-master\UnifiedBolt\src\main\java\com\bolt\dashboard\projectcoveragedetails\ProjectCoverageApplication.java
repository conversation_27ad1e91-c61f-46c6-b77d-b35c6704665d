package com.bolt.dashboard.projectcoveragedetails;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import com.atlassian.jira.rest.client.api.RestClientException;
import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.BuildSteps;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.ProjectCoverageDetails;
import com.bolt.dashboard.core.repository.BuildFailurePatternForProjectRepo;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.ProjectCoverageDetailsRepo;
import com.bolt.dashboard.gitlabpipeline.GitlabPipeline;
import com.bolt.dashboard.gitlabpipeline.GitlabPipelineImplementation;
import com.bolt.dashboard.util.EncryptionDecryptionAES;



public class ProjectCoverageApplication {
	
	AnnotationConfigApplicationContext applicationContext = null;
	ProjectCoverageDetailsRepo repo = null;
	ProjectCoverage projectCoverage = null;
	String result = "SUCCESS";
	String buildType = "Gitlab Pipeline";
	AnnotationConfigApplicationContext ctx = null;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configuration = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	ConfigurationToolInfoMetric metric1 = null;
	private static final Logger LOGGER = LogManager.getLogger(ProjectCoverageApplication.class);

	

//	public static void main(String[] args) {
//		new ProjectCoverageApplication().projectCoverageMain("Network Personalization ART");
//	}
	public void projectCoverageMain(String projectName) throws RestClientException {
		LOGGER.info("Project Code Coverage Collector started for " + projectName);
		ctx = DataConfig.getContext();
		repo = ctx.getBean(ProjectCoverageDetailsRepo.class);
		String instanceURL = "";
		String branch = "";
	
		String groupCode = "";
		String password = null;
		boolean firstRun = false;
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configuration = configurationRepo.findByProjectName(projectName).get(0);
		metric = configuration.getMetrics();
		@SuppressWarnings("rawtypes")
		Iterator iter = metric.iterator();
//		LOGGER.info("Project name  " + configuration.getProjectName());
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
			//LOGGER.info("Tool name  " + metric1.getToolName());
			if ("GITLAB CodeCoverage".equals(metric1.getToolName())) {
			//	LOGGER.info("URL  " + metric1.getUrl());
				instanceURL = metric1.getUrl();
				
					password=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				groupCode = metric1.getProjectCode();
				break;
			}
		}
		applicationContext = DataConfig.getContext();

		projectCoverage = new ProjectCoverageImplementation();
		try {
			String multiRepo[] = groupCode.split(",");
			for (int i = 0; i < multiRepo.length; i++) {
				int page=1;
				int total_pages=1;
				ResponseEntity<String> groupResponse = checkGroup(instanceURL, multiRepo[i], password,page);
				
				if (groupResponse.getStatusCode() == HttpStatus.NOT_FOUND) {
					projectCoverage.getCoverageDetails(instanceURL, repo, firstRun, branch, multiRepo[i], password,
							projectName, "", "");
				} else {
					if(page==1) {
					List<String> pages=groupResponse.getHeaders().get("X-Total-Pages");
					LOGGER.info(pages.get(0));
					total_pages=Integer.parseInt(pages.get(0));
					}
					for(;page<=total_pages;page++) {
						if(page>1) {
					groupResponse = checkGroup(instanceURL, multiRepo[i], password,page);
						}
					JSONArray response = parseAsArray(groupResponse);
					for (int j = 0; j < response.length(); j++) {
						JSONObject projObj = (JSONObject) response.get(j);
						JSONObject nameSpace = projObj.getJSONObject("namespace");
						String groupName = nameSpace.getString("name");
						String repoName = projObj.getString("name");
						int projCode = projObj.getInt("id");
						projectCoverage.getCoverageDetails(instanceURL, repo, firstRun, branch,
								String.valueOf(projCode), password, projectName, groupName, repoName);
					}
				  }
				}
			}
			
			ConstantVariable.getLastRun(projectName, "Gitlab CodeCoverage", new Date().getTime(), result);
		} catch (Exception e) {
			result = "FAIL";
			ConstantVariable.getLastRun(projectName, "Gitlab CodeCoverage", new Date().getTime(), result);
			cleanObject();
			LOGGER.error(e.getMessage());
			LOGGER.error("Gitlab CodeCoverage Exception ", e.fillInStackTrace());

			LOGGER.info("Gitlab CodeCoverage Collector failed for " + projectName);
		}
		cleanObject();
		LOGGER.info("Gitlab CodeCoverage Collector ended for " + projectName);
	}

	public void cleanObject() {
		repo = null;
		projectCoverage= null;
	}

	private JSONArray parseAsArray(ResponseEntity<String> response) {

		return (JSONArray) new JSONTokener(response.getBody()).nextValue();
	}

	private ResponseEntity<String> checkGroup(String instanceURL, String groupCode, String password, int page) {
		
		instanceURL = instanceURL + "/api/v4/groups/" + groupCode + "/projects?private_token=" + password+"&per_page=20&page="+page;
		LOGGER.info(instanceURL);
		ResponseEntity<String> response = makeRestCall(instanceURL);
		if(response!=null)
			LOGGER.info(response.getHeaders().get("X-Total-Pages"));
		
		return response;
	}
	private ResponseEntity<String> makeRestCall(String url) {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000000);
		requestFactory.setReadTimeout(20000000);
		RestTemplate rest = new RestTemplate(requestFactory);

		try {
			ResponseEntity<String> response = rest.exchange(url, HttpMethod.GET, null, String.class);
			return response;
		} catch (HttpClientErrorException e) {
			
			LOGGER.error(e.getMessage());
			LOGGER.error("Error in Gitlab Group ",e.getCause());
			if(e.getMessage().contains("404"))
			return new ResponseEntity<String>(HttpStatus.NOT_FOUND);
		}catch (Exception e) {
			// TODO: handle exception
			LOGGER.info(e.getMessage());
			return new ResponseEntity<String>(HttpStatus.NOT_FOUND);
		}
		return null;

	
	
	
	
	
	
	
	}
	
}
