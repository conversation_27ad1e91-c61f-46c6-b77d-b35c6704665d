package com.bolt.dashboard.projectcoveragedetails;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ProjectCoverageDetails;
import com.bolt.dashboard.core.model.RepoCodeCoverageStatus;
import com.bolt.dashboard.core.repository.ProjectCoverageDetailsRepo;
import com.bolt.dashboard.core.repository.RepoCodeCoverageStatusRepo;

public class ProjectCoverageImplementation implements ProjectCoverage {
	private static final Logger LOGGER = LogManager.getLogger(ProjectCoverageImplementation.class);

	String statementPattern = "Statements\\s+:\\s(\\d+.?\\d+)%+\\s+\\(+\\s+\\d+/\\d+\\s+\\)";
	String branchPattern = "Branches\\s+:\\s(\\d+.?\\d+)%+\\s+\\(+\\s+\\d+/\\d+\\s+\\)";
	String functionsPattern = "Functions\\s+:\\s(\\d+?.?\\d+)%+\\s+\\(+\\s+\\d+/\\d+\\s+\\)";
	String functionsPattern2 = "Functions\\s+:\\s(\\d?.?\\d+)%+\\s+\\(+\\s+\\d+/\\d+\\s+\\)";

	String linesPattern = "Lines\\s+:\\s(\\d+.?\\d+)%+\\s+\\(+\\s+\\d+/\\d+\\s+\\)";
	String coveragePattern = "(\\d+.?\\d+)";
	String coveragePattern2 = "(d?.?\\d+)";
	String passingPattern = "\\s\\d+\\s+passing";
	String failingPattern = "\\s\\d+\\s+failing";
	String statementPattern2 = "Statements\\s+:\\s(\\d?.?\\d+)%+\\s+\\(+\\s+\\d+/\\d+\\s+\\)";
	String linesPattern2 = "Lines\\s+:\\s(\\d?.?\\d+)%+\\s+\\(+\\s+\\d+/\\d+\\s+\\)";
	String branchPattern2 = "Branches\\s+:\\s(\\d?.?\\d+)%+\\s+\\(+\\s+\\d+/\\d+\\s+\\)";

	private static final String SEGMENT_API = "api/v4/projects/";
	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
	RepoCodeCoverageStatusRepo codeCoverageStatusRepo = ctx.getBean(RepoCodeCoverageStatusRepo.class);
	String projectName = "";
	String userName = "";
	String password = "";
	private static long time = 0;
	ProjectCoverageDetailsRepo coverageDetailsRepo = null;
	String jobsurl = null;
	String gitlabUrl = null;
	int size = 0;
	int lastPage = 0;
	long lastBuildid;
	private static long newbuildPipelineTimestamp;
	private static long timestamp;
	int page = 1;
	int per_page = 100;
	long totalPages = 1000000000;
	String pipelineId = null;
	List<ProjectCoverageDetails> coverageDetailsData = new ArrayList<>();
	ProjectCoverageDetails projectCoverage;

	@Override
	public void getCoverageDetails(String baseUrl, ProjectCoverageDetailsRepo repo, boolean firstRun, String branch,
			String projectCode, String pass, String projectName, String groupName, String repoName) {
		try {
			gitlabUrl = baseUrl + SEGMENT_API + projectCode;
			this.projectName = projectName;
			// this.coverageDetailsRepo = repo;

			page = 0;
			ResponseEntity<String> response = makeRestCall(gitlabUrl + "/jobs?private_token=" + pass);
//			LOGGER.info(gitlabUrl + "/jobs?private_token=" + pass);
			JSONArray valueArray=null;
			if(response!=null) {
				valueArray= new JSONArray(response.getBody());
			}

			// ResponseEntity<String> responseProject = makeRestCall(gitlabUrl +
			// "/jobs?private_token=" + pass);
			if (repoName.equals("")) {
				ResponseEntity<String> responseProject = makeRestCall(gitlabUrl + "?private_token=" + pass);
				if(responseProject != null) {
					JSONObject projObj = new JSONObject(responseProject.getBody());
					repoName = projObj.getString("name");
					groupName = repoName;
				}
				
			}
			LOGGER.info("Started for RepoName " + repoName);
			if (valueArray!=null && valueArray.length() > 0) {
				List<ProjectCoverageDetails> coverageDetails = repo.findByRepoNameAndGroupNameOrderByJobIdDesc(repoName,
						groupName);
				if (coverageDetails.isEmpty()) {
					page = 1;
					time = 0;
					// per_page=100;
				} else {
					int totalStoredBuild = coverageDetails.size();

					page = Math.floorDiv(totalStoredBuild, 100);
					if (page == 0)
						page = 1;
					time = coverageDetails.get(0).getCreated_timestamp();

				}

				JSONObject obj = (JSONObject) valueArray.get(0);
				newbuildPipelineTimestamp = getTimeInMiliseconds(obj.getString("created_at"));

				timestamp = newbuildPipelineTimestamp;
				if (newbuildPipelineTimestamp <= time) {

					LOGGER.info("No ChangeSet to be stored    ");
				} else {
					while (page <= totalPages) {
						coverageDetailsData = new ArrayList<>();
						jobsurl = gitlabUrl + "/jobs/?private_token=" + pass + "&page=" + page + "&per_page="
								+ per_page;
//						LOGGER.info("countP: " + jobsurl);
//						LOGGER.info("countP: " + page + " " + per_page);
						valueArray=null;
						response = makeRestCall(jobsurl);
						if(response!=null){
							valueArray = new JSONArray(response.getBody());
						}
						
						if (valueArray !=null && valueArray.length() > 0) {
							obj = (JSONObject) valueArray.get(0);
							newbuildPipelineTimestamp = getTimeInMiliseconds(obj.getString("created_at"));
							timestamp = newbuildPipelineTimestamp;

//							LOGGER.info("pipeline length : " + valueArray.length());
							processPipelineData(valueArray, pass, groupName, repoName);

//							LOGGER.info("Check It Repo " + repoName + " Data " + coverageDetailsData);
							repo.save(coverageDetailsData);

//							LOGGER.info((((page - 1) * 100) + coverageDetailsData.size())
//									+ " coverage details are saved:" + this.projectName);
							page++;
						} else {
							ProjectCoverageDetails temp = repo.findOneByRepoNameAndGroupName(repoName, groupName);
							if (temp == null) {
								setRepoStatus(groupName, repoName,"No Test Job Setup/Failure in Code Coverage Script",false);
							} else {
								List<ProjectCoverageDetails> tempList =repo.findByRepoNameAndGroupName(repoName, groupName);
								boolean zeroCoverage=true;
								for (ProjectCoverageDetails s : tempList) {
									if(s.getCovered_statements()>0 && s.getCovered_lines()>0) {
										zeroCoverage=false;
										break;
									}
								}
								if(zeroCoverage) {
									setRepoStatus(groupName, repoName,"Test Case Execution Failed",false);
								}else {
								setRepoStatus(groupName, repoName,"",true);
								}
							}				
							break;
						}

					}

				}
			} else {
				LOGGER.info("No JOb data for this repo " + repoName);
				setRepoStatus(groupName, repoName,"No Pipeline setup",false);

			}
		} catch (Exception ex) {
			LOGGER.error(ex.getMessage());
		}
		// repo.save(coverageDetailsData);
	}

	private void setRepoStatus(String groupName, String repoName, String cause,boolean status) {
		RepoCodeCoverageStatus codeCoverageStatus=codeCoverageStatusRepo.findByRepoNameAndGroupName(repoName,groupName);
		if(codeCoverageStatus==null) {
			codeCoverageStatus = new RepoCodeCoverageStatus();
		}
		codeCoverageStatus.setRepoName(repoName);
		codeCoverageStatus.setGroupName(groupName);
		codeCoverageStatus.setCoverageStatus(status);
		codeCoverageStatus.setCauseDescription(cause);
		codeCoverageStatus.setpName(this.projectName);
		codeCoverageStatusRepo.save(codeCoverageStatus);
	}

	private void processPipelineData(JSONArray jobsValues, String pass, String groupName, String repoName) {

		for (int i = 0; i < jobsValues.length(); i++) {
			JSONObject job_Obj = jobsValues.getJSONObject(i);

			timestamp = getTimeInMiliseconds(job_Obj.getString("created_at"));
			long startedTimestamp = 0;

			if (time < timestamp) {
//					if(!(job_Obj.get("coverage")).equals(null))	{
//					 projectCoverage.setOverall_coverage(Double.parseDouble(job_Obj.optString("coverage")));
//					}
//					 else {
//						continue;
//					 }
//					if(!job_Obj.getString("stage").equals("test") || job_Obj.getString("status").equals("skipped")){
//						continue;
//					}
//					if(!job_Obj.getString("stage").equals("test")){
//						continue;
//					}
				projectCoverage = new ProjectCoverageDetails();
				projectCoverage.setCreated_timestamp(timestamp);
				if (!job_Obj.isNull("started_at")) {
					startedTimestamp = getTimeInMiliseconds(job_Obj.getString("started_at"));
					projectCoverage.setStarted_timestamp(startedTimestamp);
				}
				int id = job_Obj.optInt("id");
				projectCoverage.setJobId(id);
				projectCoverage.setGroupName(groupName);
				projectCoverage.setRepoName(repoName);
				projectCoverage.setBranchName(job_Obj.optString("ref"));
				projectCoverage.setpName(projectName);
				String temp_date = null;
				JSONObject commitObj = job_Obj.getJSONObject("commit");
				projectCoverage.setCommiter(commitObj.optString("author_name"));
				// JSONArray stepsValues = (JSONArray) jsonObject.get("values");
				String url = gitlabUrl + "/jobs/" + id + "/trace?private_token=" + pass;
				ResponseEntity<String> logResponseData = makeRestCall(url);

				String logs =null;
				if(logResponseData!=null) {
					logs=logResponseData.getBody();
				}

				if (logs != null && logs.contains("Statements")) {
					processLogs(logs);
				} else {
					continue;
				}
				coverageDetailsData.add(projectCoverage);
			}

		}

	}

	private void processLogs(String logs) {
		

		// LOGGER.info(logs);

		List<String> statementCoverage = matchFinder(statementPattern, logs);
		List<String> coverageValues = null;

		if (statementCoverage.size() > 0) {
			coverageValues = matchFinder(coveragePattern, statementCoverage.get(0));
			projectCoverage.setCovered_statements_percentage(Double.parseDouble(coverageValues.get(0)));
			String splitValues[] = coverageValues.get(1).split("/");
			projectCoverage.setCovered_statements(Integer.parseInt(splitValues[0]));
			projectCoverage.setTotal_statements(Integer.parseInt(splitValues[1]));
		} else {
			coverageValues = matchFinder(coveragePattern2, matchFinder(statementPattern2, logs).get(0));
			projectCoverage.setCovered_statements_percentage(Double.parseDouble(coverageValues.get(0)));
			projectCoverage.setCovered_statements(Integer.parseInt(coverageValues.get(1).replaceAll("\\s", "")));
			projectCoverage.setTotal_statements(Integer.parseInt(coverageValues.get(2).replaceAll("/", "")));
		}

		List<String> linesCoverage = matchFinder(linesPattern, logs);
		if (linesCoverage.size() > 0) {
			coverageValues = matchFinder(coveragePattern, linesCoverage.get(0));
			projectCoverage.setCovered_lines_percentage(Double.parseDouble(coverageValues.get(0)));
			String splitValues[] = coverageValues.get(1).split("/");
			projectCoverage.setCovered_lines(Integer.parseInt(splitValues[0]));
			projectCoverage.setTotal_lines(Integer.parseInt(splitValues[1]));
		} else {
			coverageValues = matchFinder(coveragePattern2, matchFinder(linesPattern2, logs).get(0));
			projectCoverage.setCovered_lines_percentage(Double.parseDouble(coverageValues.get(0)));

			projectCoverage.setCovered_lines(Integer.parseInt(coverageValues.get(1).replaceAll("\\s", "")));
			projectCoverage.setTotal_lines(Integer.parseInt(coverageValues.get(2).replaceAll("/", "")));
		}

		List<String> branchCoverage = matchFinder(branchPattern, logs);
		if (branchCoverage.size() > 0) {
			coverageValues = matchFinder(coveragePattern, branchCoverage.get(0));
			projectCoverage.setCovered_branches_percentage(Double.parseDouble(coverageValues.get(0)));
			String splitValues[] = coverageValues.get(1).split("/");
			projectCoverage.setCovered_branches(Integer.parseInt(splitValues[0]));
			projectCoverage.setTotal_branches(Integer.parseInt(splitValues[1]));

		} else {
			coverageValues = matchFinder(coveragePattern2, matchFinder(branchPattern2, logs).get(0));
			projectCoverage.setCovered_branches_percentage(Double.parseDouble(coverageValues.get(0)));

			projectCoverage.setCovered_branches(Integer.parseInt(coverageValues.get(1).replaceAll("\\s", "")));
			projectCoverage.setTotal_branches(Integer.parseInt(coverageValues.get(2).replaceAll("/", "")));
		}

		List<String> functionCoverage = matchFinder(functionsPattern, logs);
		if (functionCoverage.size() > 0) {
			coverageValues = matchFinder(coveragePattern, functionCoverage.get(0));
			projectCoverage.setCovered_function_percentage(Double.parseDouble(coverageValues.get(0)));
			String[] splitValues = coverageValues.get(1).split("/");
			projectCoverage.setCovered_functions(Integer.parseInt(splitValues[0]));
			projectCoverage.setTotal_functions(Integer.parseInt(splitValues[1]));
		} else {
			coverageValues = matchFinder(coveragePattern2, matchFinder(functionsPattern2, logs).get(0));
			projectCoverage.setCovered_function_percentage(Double.parseDouble(coverageValues.get(0)));

			projectCoverage.setCovered_functions(Integer.parseInt(coverageValues.get(1).replaceAll("\\s", "")));
			projectCoverage.setTotal_functions(Integer.parseInt(coverageValues.get(2).replaceAll("/", "")));
		}

		if (logs.contains("passing")) {
			String passingValuesMatch = matchFinder(passingPattern, logs).get(0);
			String passingValue[] = passingValuesMatch.split(" ");
			projectCoverage.setTest_cases_passed(Integer.parseInt(passingValue[1]));

		} else {
			projectCoverage.setTest_cases_passed(0);
		}
		if (logs.contains("failing")) {
			String passingValuesMatch = matchFinder(failingPattern, logs).get(0);
			String failingValue[] = passingValuesMatch.split(" ");
			projectCoverage.setTest_cases_failed(Integer.parseInt(failingValue[1]));

		} else {
			projectCoverage.setTest_cases_failed(0);
		}

		projectCoverage
				.setTotal_test_cases(projectCoverage.getTest_cases_passed() + projectCoverage.getTest_cases_failed());

	}
		public static List<String> matchFinder(String patternRegex, String value) {
		Pattern pattern = Pattern.compile(patternRegex);
		Matcher matcher = pattern.matcher(value);

		List<String> listMatches = new ArrayList<String>();
		while (matcher.find()) {
			listMatches.add(matcher.group());
		}
		/*
		 * for (String match : listMatches) { LOGGER.info(match); }
		 */
		 		return listMatches;

	}
	private long getTimeInMiliseconds(String temp_date) {
		
		String[] splitDate = temp_date.split("T");
		String[] temp = splitDate[1].split("\\.");
		temp = temp[0].split("Z");
		// temp=temp_date.split(".");
		String tempTime = splitDate[0] + " " + temp[0];

		DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
		DateTime createdDate = fmt.parseDateTime(tempTime);
		return createdDate.getMillis();
	}

	private ResponseEntity<String> makeRestCall(String pipelineUrl) {
		
		if (!"".equals(this.userName) && !"".equals(this.password)) {
			try {

				UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(pipelineUrl);
				UriComponents uriComponents = builder.build();
			
				URI uri = uriComponents.toUri();
				return get().exchange(uri, HttpMethod.GET, new HttpEntity<>(createHeaders(userName, password)),
						String.class);

			} catch (Exception e) {
				LOGGER.error(e.getMessage());
				return null;
			}

		} else {
			try {
				return get().exchange(pipelineUrl, HttpMethod.GET, null, String.class);
			} catch (Exception e) {
				LOGGER.error(e.getMessage());
				return null;
			}

		}
	}

	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}

	private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000);
		requestFactory.setReadTimeout(20000);
		return new RestTemplate(requestFactory);
	}

}
