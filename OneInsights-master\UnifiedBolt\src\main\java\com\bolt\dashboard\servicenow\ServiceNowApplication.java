package com.bolt.dashboard.servicenow;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Date;
import java.util.Iterator;
import java.util.Properties;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class ServiceNowApplication {
	String result = "SUCCESS";
	private static final Logger LOGGER = LogManager.getLogger(ServiceNowImplementation.class);
	ServiceNowImplementation serviceNowImpl;
	ServiceNowSLAService slaService;
	ServiceNowInflowTrendService itService;
	ConfigurationSettingRep configurationRepo;
	AnnotationConfigApplicationContext ctx;
	ConfigurationSetting configurationColection;
	String instanceURL;
	String url;
	//String url = "https://brilliodev.service-now.com/api/now/table/incident?";
	String url2;
	String url3; 
	String userName;
	String password;
	String fieldNames = "sys_updated_on, number, state, sys_created_by, impact, active, priority,"
			+ "short_description, sys_class_name, assigned_to, sys_updated_by, sys_created_on,"
			+ " u_sla_duration,closed_at, opened_at, reopened_time, resolved_at, subcategory,"
			+ " close_code, contact_type,incident_state, urgency, category";
	String fields = "pause_duration, pause_time, timezone, sys_updated_on" +
			"business_time_left, duration, sys_id, time_left, sys_updated_by" +
			"sys_created_on, percentage, original_breach_time, sys_created_by" +
			"business_percentage, end_time, sys_mod_count, active, business_pause_duration" +
			"start_time, business_duration, task, stage, planned_end_time, has_breached";
	String type="ServiceNow";

	// sysparm_query=sys_updated_on>=javascript:gs.dateGenerate('2021-09-17
	// 10:23:03')&sysparm_fields=sys_updated_on,number
	public ServiceNowApplication(){}
	
//	public static void main(String[] args) {
//		//new ServiceNowApplication().ServiceNowIT();
//		new ServiceNowApplication().ServiceNowMain("BrillioOne");
//	}

	


	public void ServiceNowMain(String projectName) {
		try {
			ctx = DataConfig.getContext();
			configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
			configurationColection = configurationRepo.findByProjectName(projectName).get(0);
			if (configurationColection != null) {
				Set<ConfigurationToolInfoMetric> metric = configurationColection.getMetrics();
				Iterator iter = metric.iterator();
				while (iter.hasNext()) {
					Object configuration1 = iter.next();
					ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
					LOGGER.info("Tool name  " + metric1.getToolName());
					if ("ServiceNow".equals(metric1.getToolName())) {
						instanceURL = metric1.getUrl();
						userName = metric1.getUserName();
						password = EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
						break;
					}

				}
				url= instanceURL + "/incident?";
				url2 = instanceURL + "/task_sla?";
				url3 = instanceURL + "/sc_req_item?";
				Properties properties = new Properties();

				InputStream in = getClass().getClassLoader().getResourceAsStream("application.properties");
				properties.load(in);

				serviceNowImpl = new ServiceNowImplementation();
				serviceNowImpl.getIncidentRecord(url, userName, password, fieldNames, projectName);

				slaService = new ServiceNowSLAServiceImpl();
				slaService.getAllSLA(url2, userName, password, fields);

				itService = new ServiceNowInflowTrendServiceImpl();
				itService.getAllInflowTrend(url3, userName, password);
				ConstantVariable.getLastRun(projectName, type, new Date().getTime(), result);

				LOGGER.info("Service Now collector finished");
			}
		} catch (Exception e) {
			LOGGER.info("Service Now collector Failed");
			LOGGER.info(e);
			result = "FAIL";
			ConstantVariable.getLastRun(projectName, type, new Date().getTime(), result);
		}

	}
	
//	public void ServiceNowIT() {
//		itService = new ServiceNowInflowTrendServiceImpl();
//		itService.getAllInflowTrend(url3, userName, password);
//	}
}
