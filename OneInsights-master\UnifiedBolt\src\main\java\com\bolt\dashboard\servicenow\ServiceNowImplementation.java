package com.bolt.dashboard.servicenow;

import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ServiceNowInc;
import com.bolt.dashboard.core.model.ServiceNowModel;
import com.bolt.dashboard.core.repository.ServiceNowModelRepo;
import com.bolt.dashboard.core.repository.ServiceNowRepo;
import com.bolt.dashboard.util.DateUtil;
import com.google.gson.JsonObject;

public class ServiceNowImplementation implements ServiceNowClient {
	private static final Logger LOGGER = LogManager.getLogger(ServiceNowImplementation.class);
	private ServiceNowRepo serviceNowRepo = null;
	private AnnotationConfigApplicationContext ctx = null;
	private List<ServiceNowInc> incidentsArr;
	private ArrayList<ServiceNowInc> incArr;
	private int maxLimit=1000;
	private ServiceNowModelRepo snModelRepo;
	String projectName;
	public void init() {
		LOGGER.info("ServiceNow Incident Collector Started");
		ctx = DataConfig.getContext();
		serviceNowRepo = ctx.getBean(ServiceNowRepo.class);
		snModelRepo = ctx.getBean(ServiceNowModelRepo.class);
		incArr= new ArrayList<ServiceNowInc>();
		incArr.clear();

	}

	@Override
	public void getIncidentRecord(String url, String username, String password, String fieldNames,String projectName) {
		init();
		this.projectName = projectName;
		String baseUrl = "";
		incidentsArr = serviceNowRepo.findAll();
		boolean isDelta = false;
		String cRunDate = null;
		List<ServiceNowModel> pDetails = snModelRepo.findAll();

		
		if(pDetails.size()<=0) {
			DateUtil dateUtil = new DateUtil();
			String date= 	dateUtil.getDateInFormat("yyyy-MM-dd HH:mm:ss", new Date());
			ServiceNowModel sn= new ServiceNowModel();
			sn.setcRuns(date);
			sn.setTimeStamp(new Date().getTime());
			cRunDate= date;
			snModelRepo.save(sn);
		}else {
			DateUtil dateUtil = new DateUtil();
			String date= 	dateUtil.getDateInFormat("yyyy-MM-dd HH:mm:ss", new Date());
			cRunDate= pDetails.get(0).getcRuns();
			pDetails.get(0).setTimeStamp(new Date().getTime());
			pDetails.get(0).setcRuns(date);
			snModelRepo.save(pDetails);
		}
		if (incidentsArr.size() == 0) {
//			baseUrl = url +"sysparm_query=^ORDERBYsys_updated_on&sysparm_limit="+maxLimit+ "&sysparm_fields=" + fieldNames;
			baseUrl = url +"sysparm_offset="+0+"&sysparm_limit="+maxLimit;
		} else {	
            isDelta = true;
			baseUrl = url + "sysparm_query=sys_updated_on>javascript:gs.dateGenerate('" + cRunDate
					+ "')^ORDERBYsys_updated_on&sysparm_limit="+maxLimit+"&sysparm_fields=" + fieldNames;
		}
		
		
		String count=processRequest(baseUrl, username, password);
		int totalCount=  Integer.parseInt(count);
		if(totalCount>maxLimit) {
			int startAt = maxLimit;
			while (startAt < totalCount) {
				if(isDelta)
					baseUrl = url + "sysparm_query=sys_updated_on>javascript:gs.dateGenerate('" + cRunDate
					+ "')&sysparm_offset="+startAt+"&sysparm_limit="+maxLimit+"&sysparm_fields=" + fieldNames;
				else
				baseUrl= url+"sysparm_offset="+startAt+"&sysparm_limit="+maxLimit;;
				processRequest(baseUrl, username, password);
				startAt+=maxLimit;
			}
			
		}
		LOGGER.info("ServiceNow Incident Collector ended");

	}
	public String processRequest(String url, String username, String password) {
		ResponseEntity<String> response = makeRestCall(url, username, password);
		String count	=response.getHeaders().get("X-Total-Count").get(0);
		LOGGER.info("count : "+count);
		JSONObject incidentDataObj = parseAsObject(response);
		updateResultData(incidentDataObj,username,password);
		//LOGGER.info(incidentDataObj.toString());
		return count;
	}

	public void updateResultData(JSONObject incidentDataObj,String username,String password) {
		incArr.clear();
		String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";
		DateFormat formatter = new SimpleDateFormat(DEFAULT_PATTERN);
	
		try {
			JSONArray arr = incidentDataObj.getJSONArray("result");
			if (arr.length() > 0) {
				for (int i = 0; i < arr.length(); i++) {
					JSONObject o = arr.getJSONObject(i);
					ServiceNowInc inc = new ServiceNowInc();
					inc.setSys_updated_on(o.getString("sys_updated_on"));
					inc.setNumber(o.getString("number"));
					inc.setState(o.getString("state"));
					inc.setSys_created_by(o.getString("sys_created_by"));
					inc.setImpact(o.getString("impact"));
					inc.setActive(o.getString("active"));
					inc.setPriority(o.getString("priority"));
					inc.setShort_description(o.getString("short_description"));
					inc.setSys_class_name(o.getString("sys_class_name"));
				//	inc.setSys_updated_on(o.getString("assigned_to"));
					inc.setSys_updated_by(o.getString("sys_updated_by"));
					inc.setSys_created_on(o.getString("sys_created_on"));
//					inc.setCreatedOn(formatter.parse(o.getString("sys_created_on")).getTime());
					inc.setU_sla_duration(o.getString("u_sla_duration"));
					inc.setClosed_at(o.getString("closed_at"));
					try {
						if(o.getString("closed_at").length()>0)
						inc.setClosedAt(formatter.parse(o.getString("closed_at")).getTime());
//						else
//							LOGGER.info(o.getString("closed_at"));
						if(o.getString("opened_at").length()>0)
						inc.setOpenedAt(formatter.parse(o.getString("opened_at")).getTime());
//						else
//						LOGGER.info(o.getString("opened_at"));
					} catch (ParseException e) {
						LOGGER.error(e.getMessage());
					}
					inc.setOpened_at(o.getString("opened_at"));
					inc.setReopened_time(o.getString("reopened_time"));
					inc.setResolved_at(o.getString("resolved_at"));
					inc.setSubcategory(o.getString("subcategory"));
					inc.setClose_code(o.getString("close_code"));
					inc.setContact_type(o.getString("contact_type"));
					inc.setIncident_state(o.getString("incident_state"));
					inc.setUrgency(o.getString("urgency"));
					inc.setCategory(o.getString("category"));
					inc.setProjectName(projectName);
					addAssignmentGroud(inc,o,username,password);
					incArr.add(inc);

				}
			}
		} catch (JSONException e) {
			
			LOGGER.error(e.getMessage());
		
		}
		if(incArr.size()>0) {
			saveIndicents(incArr);
//			serviceNowRepo.save(incArr);
		}

	}
	
	
	public void addAssignmentGroud(ServiceNowInc inc,JSONObject o,String username,String password) {
		JSONObject object= o.getJSONObject("assignment_group");
		String url = object.getString("link");
		try {
			if(url != null && !url.equals("")) {
				ResponseEntity<String> response = makeRestCall(url, username, password);
				if(response != null) {
					JSONObject addAssignmentObj = parseAsObject(response);
					JSONObject nameObject = addAssignmentObj.getJSONObject("result");
					String name = nameObject.getString("name");
					if(name != null) {
						inc.setAssignment_group(name);
					}else {
						inc.setAssignment_group("unassigned");
					}
				}

			} else {
				inc.setAssignment_group("unassigned");
			}
		}catch(Exception e) {
			inc.setAssignment_group("unassigned");
		}

	}
	
	public void saveIndicents(List<ServiceNowInc> incArr) {
		List<ServiceNowInc> incList = new ArrayList<>();
		for (ServiceNowInc inc : incArr) {
			ServiceNowInc temp = serviceNowRepo.findByNumber(inc.getNumber());
			if (temp != null)
				inc.setId(temp.getId());
			incList.add(inc);
		}
		serviceNowRepo.save(incList);
	}


	public RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(30000);
		requestFactory.setReadTimeout(30000);
		return new RestTemplate(requestFactory);
	}

	private ResponseEntity<String> makeRestCall(String url, String userId, String password) {
		// Basic Auth only.
		if (!"".equals(userId) && !"".equals(password)) {
			return get().exchange(url, HttpMethod.GET, new HttpEntity<>(createHeaders(userId, password)), String.class);

		} else {
			return get().exchange(url, HttpMethod.GET, null, String.class);
		}

	}

	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);

		return headers;
	}

	private JSONObject parseAsObject(ResponseEntity<String> staticsticResponse) {

		return (JSONObject) new JSONTokener(staticsticResponse.getBody()).nextValue();
	}

}
