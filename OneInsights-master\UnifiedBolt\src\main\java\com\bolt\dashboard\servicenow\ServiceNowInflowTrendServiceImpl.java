package com.bolt.dashboard.servicenow;

import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ServiceNowModel;
import com.bolt.dashboard.core.model.ServiceNowSCReqItem;
import com.bolt.dashboard.core.repository.ServiceNowInflowTrendRepo;
import com.bolt.dashboard.core.repository.ServiceNowModelRepo;
import com.bolt.dashboard.util.DateUtil;

public class ServiceNowInflowTrendServiceImpl implements ServiceNowInflowTrendService {

	private static final Logger LOGGER = LogManager.getLogger(ServiceNowInflowTrendServiceImpl.class);
	private ServiceNowInflowTrendRepo repo = null;
	private AnnotationConfigApplicationContext ctx = null;
	private List<ServiceNowSCReqItem> slaArr;
	private ArrayList<ServiceNowSCReqItem> slaArray;
	private int maxLimit = 1000;
	private ServiceNowModelRepo snModelRepo;

	public void init() {
		LOGGER.info("ServiceNow Inflow Trend Collector started");
		ctx = DataConfig.getContext();
		repo = ctx.getBean(ServiceNowInflowTrendRepo.class);
		snModelRepo = ctx.getBean(ServiceNowModelRepo.class);
		slaArray = new ArrayList<ServiceNowSCReqItem>();
		slaArray.clear();

	}

	@Override
	public void getAllInflowTrend(String url, String username, String password) {
		init();
		String baseUrl = "";
		slaArr = (List<ServiceNowSCReqItem>) repo.findAll();
		boolean isDelta = false;
		String itRunDate = null;
		List<ServiceNowModel> pDetails = snModelRepo.findAll();

		DateUtil dateUtil = new DateUtil();
		String date = dateUtil.getDateInFormat("yyyy-MM-dd HH:mm:ss", new Date());
		if (pDetails.size() <= 0) {
			ServiceNowModel sn = new ServiceNowModel();
			sn.setInflowTrendLastLastRuns(date);
			itRunDate = date;
			snModelRepo.save(sn);
		} else {
			itRunDate = pDetails.get(0).getInflowTrendLastLastRuns();
			pDetails.get(0).setInflowTrendLastLastRuns(date);
			snModelRepo.save(pDetails);
		}

		if (slaArr.size() == 0) {
			baseUrl = url + "sysparm_offset=" + 0 + "&sysparm_limit=" + maxLimit;
		} else {
			isDelta = true;
			baseUrl = url + "sysparm_query=sys_updated_on>javascript:gs.dateGenerate('" + itRunDate
					+ "')^ORDERBYsys_updated_on&sysparm_limit=" + maxLimit;
		}

		String count = processRequest(baseUrl, username, password);
		int totalCount = Integer.parseInt(count);
		if (totalCount > maxLimit) {
			int startAt = maxLimit;
			while (startAt < totalCount) {
				if (isDelta)
					baseUrl = url + "sysparm_query=sys_updated_on>javascript:gs.dateGenerate('" + itRunDate
							+ "')&sysparm_offset=" + startAt + "&sysparm_limit=" + maxLimit;
				else
					baseUrl = url + "sysparm_offset=" + startAt + "&sysparm_limit=" + maxLimit;
				;
				processRequest(baseUrl, username, password);
				startAt += maxLimit;
			}

		}
		LOGGER.info("ServiceNow Inflow Trend Collector ended");

	}

	public String processRequest(String url, String username, String password) {
		ResponseEntity<String> response = makeRestCall(url, username, password);
		String count = response.getHeaders().get("X-Total-Count").get(0);
		LOGGER.info("count : " + count);
		JSONObject incidentDataObj = parseAsObject(response);
		updateResultData(incidentDataObj, username, password);
		//LOGGER.info(incidentDataObj.toString());
		return count;
	}

	public void updateResultData(JSONObject incidentDataObj, String username, String password) {
		slaArray.clear();
		String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";
		DateFormat formatter = new SimpleDateFormat(DEFAULT_PATTERN);

		try {
			JSONArray arr = incidentDataObj.getJSONArray("result");
			if (arr.length() > 0) {
				for (int i = 0; i < arr.length(); i++) {
					JSONObject o = arr.getJSONObject(i);
					ServiceNowSCReqItem inc = new ServiceNowSCReqItem(o.getString("parent"), o.getString("made_sla"),
							o.getString("watch_list"), o.getString("sc_catalog"), o.getString("sn_esign_document"),
							o.getString("upon_reject"), o.getString("requested_for"), o.getString("sys_updated_on"),
							o.getString("task_effective_number"), o.getString("approval_history"),
							o.getString("skills"), o.getString("number"), o.getString("sys_updated_by"),
							o.getString("user_input"), o.getString("price"), o.getString("sys_created_on"),
							o.getString("recurring_frequency"), o.getString("state"), o.getString("task_for"),
							o.getString("route_reason"), o.getString("sys_created_by"), o.getString("knowledge"),
							o.getString("order"), o.getString("closed_at"), o.getString("cmdb_ci"),
							o.getString("backordered"), o.getString("contract"), o.getString("impact"),
							o.getString("active"), o.getString("work_notes_list"), o.getString("business_service"),
							o.getString("priority"), o.getString("sys_domain_path"), o.getString("time_worked"),
							o.getString("expected_start"), o.getString("opened_at"), o.getString("business_duration"),
							o.getString("group_list"), o.getString("configuration_item"), o.getString("work_end"),
							o.getString("approval_set"), o.getString("work_notes"), o.getString("order_guide"),
							o.getString("universal_request"), o.getString("short_description"),
							o.getString("correlation_display"), o.getString("work_start"),
							o.getString("assignment_group"), o.getString("additional_assignee_list"),
							o.getString("description"), o.getString("calendar_duration"), o.getString("close_notes"),
							o.getString("service_offering"), o.getString("sys_class_name"), o.getString("closed_by"),
							o.getString("follow_up"), o.getString("sys_id"), o.getString("contact_type"),
							o.getString("sn_esign_esignature_configuration"), o.getString("urgency"),
							o.getString("company"), o.getString("reassignment_count"), o.getString("activity_due"),
							o.getString("assigned_to"), o.getString("comments"), o.getString("quantity"),
							o.getString("approval"), o.getString("sla_due"), o.getString("comments_and_work_notes"),
							o.getString("due_date"), o.getString("sys_mod_count"), o.getString("recurring_price"),
							o.getString("sys_tags"), o.getString("billable"), o.getString("stage"),
							o.getString("escalation"), o.getString("upon_approval"), o.getString("correlation_id"),
							o.getString("location"), o.getString("estimated_delivery"), o.getJSONObject("opened_by").getString("link"),
							o.getJSONObject("sys_domain").getString("link"), o.getJSONObject("request").getString("link"), 
							o.getJSONObject("cat_item").getString("link"));
//                    System.out.println(i+"/"+arr.length());
					slaArray.add(inc);

				}
			}
		} catch (JSONException e) {
			LOGGER.error(e.getMessage());

		}
		if (slaArray.size() > 0) {
			saveIndicents(slaArray);
		}

	}

	public void saveIndicents(List<ServiceNowSCReqItem> slaArray) {
		List<ServiceNowSCReqItem> slaList = new ArrayList<>();
		for (ServiceNowSCReqItem sla : slaArray) {
			ServiceNowSCReqItem temp = repo.findByNumber(sla.getNumber());
			if (temp != null)
				sla.setId(temp.getId());
			slaList.add(sla);
		}
		repo.save(slaList);
	}

	public RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(30000);
		requestFactory.setReadTimeout(30000);
		return new RestTemplate(requestFactory);
	}

	private ResponseEntity<String> makeRestCall(String url, String userId, String password) {
		// Basic Auth only.
		if (!"".equals(userId) && !"".equals(password)) {
			return get().exchange(url, HttpMethod.GET, new HttpEntity<>(createHeaders(userId, password)), String.class);

		} else {
			return get().exchange(url, HttpMethod.GET, null, String.class);
		}

	}

	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);

		return headers;
	}

	private JSONObject parseAsObject(ResponseEntity<String> staticsticResponse) {

		return (JSONObject) new JSONTokener(staticsticResponse.getBody()).nextValue();
	}

}
