package com.bolt.dashboard.servicenow;


import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ServiceNowModel;
import com.bolt.dashboard.core.model.ServiceNowSLA;
import com.bolt.dashboard.core.repository.ServiceNowModelRepo;
import com.bolt.dashboard.core.repository.ServiceNowSLARepo;
import com.bolt.dashboard.util.DateUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ServiceNowSLAServiceImpl implements ServiceNowSLAService{

    private static final Logger LOGGER = LogManager.getLogger(ServiceNowSLAServiceImpl.class);
    private ServiceNowSLARepo serviceNowSLARepo = null;
    private AnnotationConfigApplicationContext ctx = null;
    private List<ServiceNowSLA> slaArr;
    private ArrayList<ServiceNowSLA> slaArray;
    private int maxLimit=1000;
    private ServiceNowModelRepo snModelRepo;

    public void init() {
        LOGGER.info("ServiceNow SLA Collector started");
        ctx = DataConfig.getContext();
        serviceNowSLARepo = ctx.getBean(ServiceNowSLARepo.class);
        snModelRepo = ctx.getBean(ServiceNowModelRepo.class);
        slaArray= new ArrayList<ServiceNowSLA>();
        slaArray.clear();

    }

    @Override
    public void getAllSLA(String url, String username, String password, String fieldNames) {
        init();
        String baseUrl = "";
        slaArr = (List<ServiceNowSLA>) serviceNowSLARepo.findAll();
        boolean isDelta = false;
        String slaRunDate = null;
        List<ServiceNowModel> pDetails = snModelRepo.findAll();

        DateUtil dateUtil = new DateUtil();
        String date= 	dateUtil.getDateInFormat("yyyy-MM-dd HH:mm:ss", new Date());
        if(pDetails.size()<=0) {
            ServiceNowModel sn= new ServiceNowModel();
            sn.setSlaLastRuns(date);
            sn.setSlaLastTimeStamp(new Date().getTime());
            slaRunDate= date;
            snModelRepo.save(sn);
        }else {
            slaRunDate= pDetails.get(0).getSlaLastRuns();
            pDetails.get(0).setSlaLastTimeStamp(new Date().getTime());
            pDetails.get(0).setSlaLastRuns(date);
            snModelRepo.save(pDetails);
        }

        if (slaArr.size() == 0) {
            baseUrl = url +"sysparm_offset="+0+"&sysparm_limit="+maxLimit;
        } else {
            isDelta = true;
            baseUrl = url + "sysparm_query=sys_updated_on>javascript:gs.dateGenerate('" + slaRunDate
                    + "')^ORDERBYsys_updated_on&sysparm_limit="+maxLimit+"&sysparm_fields=" + fieldNames;
        }

        String count=processRequest(baseUrl, username, password);
        int totalCount=  Integer.parseInt(count);
        if(totalCount>maxLimit) {
            int startAt = maxLimit;
            while (startAt < totalCount) {
                if(isDelta)
                    baseUrl = url + "sysparm_query=sys_updated_on>javascript:gs.dateGenerate('" + slaRunDate
                            + "')&sysparm_offset="+startAt+"&sysparm_limit="+maxLimit+"&sysparm_fields=" + fieldNames;
                else
                    baseUrl= url+"sysparm_offset="+startAt+"&sysparm_limit="+maxLimit;;
                processRequest(baseUrl, username, password);
                startAt+=maxLimit;
            }

        }
        LOGGER.info("ServiceNow SLA Collector ended");
    }
    public String processRequest(String url, String username, String password) {
        ResponseEntity<String> response = makeRestCall(url, username, password);
        String count	=response.getHeaders().get("X-Total-Count").get(0);
        LOGGER.info("count : "+count);
        JSONObject incidentDataObj = parseAsObject(response);
        updateResultData(incidentDataObj, username, password);
        //LOGGER.info(incidentDataObj.toString());
        return count;
    }
    public void updateResultData(JSONObject incidentDataObj, String username, String password) {
        slaArray.clear();
        String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";
        DateFormat formatter = new SimpleDateFormat(DEFAULT_PATTERN);

        try {
            JSONArray arr = incidentDataObj.getJSONArray("result");
            if (arr.length() > 0) {
                for (int i = 0; i < arr.length(); i++) {
                    JSONObject o = arr.getJSONObject(i);
                    ResponseEntity<String> response = makeRestCall(o.getJSONObject("task").getString("link"), username, password);
                    JSONObject temp = parseAsObject(response);
                    JSONObject task = temp.getJSONObject("result");
                    ServiceNowSLA inc = new ServiceNowSLA(
                            o.getString("pause_duration"), o.getString("pause_time"), o.getString("timezone"),
                            o.getString("sys_updated_on"),
                            o.getString("business_time_left"), o.getString("duration"),
                            o.getString("sys_id"), o.getString("time_left"),
                            o.getString("sys_updated_by"), o.getString("sys_created_on"),
                            o.getString("percentage"),
                            o.getString("original_breach_time"), o.getString("sys_created_by"),
                            o.getString("business_percentage"),
                            o.getString("end_time"), o.getString("sys_mod_count"),
                            o.getString("active"), o.getString("business_pause_duration"),
                            o.getString("start_time"), o.getString("business_duration"),
                            o.getString("stage"),
                            o.getString("planned_end_time"), o.getString("has_breached"),
                            task.getString("number"),
                            task.getString("priority")
                    );
                    System.out.println(i+"/"+arr.length());
                    slaArray.add(inc);

                }
            }
        } catch (JSONException e) {
            LOGGER.error(e.getMessage());

        }
        if(slaArray.size()>0) {
            saveIndicents(slaArray);
        }

    }
    public void saveIndicents(List<ServiceNowSLA> slaArray) {
        List<ServiceNowSLA> slaList = new ArrayList<>();
        for (ServiceNowSLA sla : slaArray) {
            ServiceNowSLA temp = serviceNowSLARepo.findBySysId(sla.getSys_id());
            if (temp != null)
                sla.setId(temp.getId());
            slaList.add(sla);
        }
        serviceNowSLARepo.save(slaList);
    }


    public RestOperations get() {
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout(30000);
        requestFactory.setReadTimeout(30000);
        return new RestTemplate(requestFactory);
    }

    private ResponseEntity<String> makeRestCall(String url, String userId, String password) {
        // Basic Auth only.
        if (!"".equals(userId) && !"".equals(password)) {
            return get().exchange(url, HttpMethod.GET, new HttpEntity<>(createHeaders(userId, password)), String.class);

        } else {
            return get().exchange(url, HttpMethod.GET, null, String.class);
        }

    }

    private HttpHeaders createHeaders(final String userId, final String password) {
        String auth = userId + ":" + password;
        byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
        String authHeader = "Basic " + new String(encodedAuth);

        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", authHeader);

        return headers;
    }

    private JSONObject parseAsObject(ResponseEntity<String> staticsticResponse) {

        return (JSONObject) new JSONTokener(staticsticResponse.getBody()).nextValue();
    }
}
