package com.bolt.dashboard.smarttest;

import java.util.Iterator;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.TestManagementTool;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.TestManagementRepo;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class SmartTestApplication {
	private static final Logger LOGGER = LogManager.getLogger(SmartTestApplication.class);
	AnnotationConfigApplicationContext ctx = null;
	String host = "";
	String user = null;
	String pass = null;
	String port = null;

	String projectName = null;
	SmartTestClientImplementation impl = null;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configurationColection = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	ConfigurationToolInfoMetric metric1 = null;

	/**
	 * Private Constructor
	 */
	public SmartTestApplication() {

	}

	public void smartTestMain(String projectName) {
		LOGGER.info("Smart Test Collector started for " + projectName);
		impl = new SmartTestClientImplementation();
		this.projectName = projectName;
		ctx = DataConfig.getContext();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configurationColection = configurationRepo.findByProjectName(projectName).get(0);
		TestManagementRepo repo = ctx.getBean(TestManagementRepo.class);
		metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOGGER.info("Tool name  " + metric1.getToolName());
			if ("smartTest".equals(metric1.getToolName())) {
				LOGGER.info("URL  " + metric1.getUrl());
				host = metric1.getUrl();
				user = metric1.getUserName();
				
					pass=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				break;
			}

		}

		try {
			TestManagementTool tool = impl.getConnection(host, user, pass, projectName);
			tool.setProjectName(projectName);
			tool.setTestType("smartTest");
			repo.save(tool);
			cleanObject();
			LOGGER.info("Smart Test ENDS..........");

		} catch (Exception e) {
			cleanObject();
			LOGGER.error(e.getMessage());
			LOGGER.info(e.getStackTrace());
			LOGGER.info(e);
			LOGGER.info("Smart Test Collector failed for " + projectName);
		}

		LOGGER.info("Smart Test Collector ended for " + projectName);

	}

	public void cleanObject() {
		impl = null;
		configurationRepo = null;
		configurationColection = null;
		metric = null;
		metric1 = null;
	}
}
