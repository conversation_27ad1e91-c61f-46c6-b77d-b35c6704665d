package com.bolt.dashboard.smarttest;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;

import com.bolt.dashboard.core.model.TestCaseModel;
import com.bolt.dashboard.core.model.TestManagementTool;
import com.bolt.dashboard.core.model.TestSetModel;

public class SmartTestClientImplementation implements SmartTestClient {
	private static final Logger LOGGER = LogManager.getLogger(SmartTestClientImplementation.class);
	int oldExecutionId = 0;
	int executionId = 0;
	Connection con = null;
	Statement statement = null;
	ResultSet rs = null;
	PreparedStatement pstmt = null;
	String author = null;
	long execDate;
	String testSetName = null;
	String sprintName;
	String testCaseTypeInString = null;
	ResultSet execucutionIdrs = null;
	
	@Value("${smartTest.password}")
	public String dbPassword;

	@Override
	public TestManagementTool getConnection(String url, String user, String pass, String projectName)
			throws SmartTestExceptions {
		TestManagementTool tool = new TestManagementTool();
		List<TestSetModel> testSetModelList = new ArrayList<>();
		try {
			Class.forName("com.mysql.jdbc.Driver").newInstance();
			String newHost = "jdbc:mysql://" + url;
			con = DriverManager.getConnection(newHost, "boltreports", dbPassword);

			LOGGER.info("successfully connected.....");
			String sql = "SELECT * from execution_tb";
			statement = con.createStatement();
			rs = statement.executeQuery(sql);
			while (rs.next()) {

				executionId = Integer.parseInt(rs.getString(1));
				author = rs.getString(2);
				String execTime = rs.getString(4);
				Timestamp startTimestamp = Timestamp.valueOf(execTime);
				execDate = startTimestamp.getTime();
				testSetName = rs.getString(7);
				sprintName = rs.getString(11);

				TestSetModel testSet = getData(executionId);
				testSet.setSprintName(sprintName);
				testSet.setTestSetName(testSetName);
				testSetModelList.add(testSet);

			}
			tool.setTestSetList(testSetModelList);
			return tool;
		} catch (Exception e) {
			LOGGER.info(e);
			throw new SmartTestExceptions(e);
		}

	}

	public TestSetModel getData(int executionId) throws SmartTestExceptions {
		TestSetModel testSetModel = new TestSetModel();
		List<TestCaseModel> testCaseList = new ArrayList<>();
		try {

			String sqlQuery = "SELECT * from testresult_tb where execution_execution_id =?";
			pstmt = con.prepareStatement(sqlQuery);
			pstmt.setInt(1, executionId);
			execucutionIdrs = pstmt.executeQuery();
			while (execucutionIdrs.next()) {
				String testCaseId = execucutionIdrs.getString(7);

				String testCaseStatus = null;

				LOGGER.info("execId  " + execucutionIdrs.getString(6) + "  testcase Id " + testCaseId);
				TestCaseModel testCaseObject = getTestCaseInfo(testCaseId);

				int result = -1;
				if (execucutionIdrs.getString(5) == null) {
					testCaseStatus = " ";
				} else {
					result = Integer.parseInt(execucutionIdrs.getString(5));
				}
				if (result == 0) {
					testCaseStatus = "Passed";
				} else if (result == 1) {
					testCaseStatus = "Failed";
				} else if (result == 2) {
					testCaseStatus = "ABORTED";
				} else if (result == 3) {
					testCaseStatus = "SKIPPED";
				} else if (result == 4) {
					testCaseStatus = "No run";
				} else if (result == 5) {
					testCaseStatus = "INCOMPLETE";
				}

				testCaseObject.setExecStatus(testCaseStatus);
				testCaseList.add(testCaseObject);

			}
			testSetModel.setTestCaseList(testCaseList);
		} catch (Exception e) {
			LOGGER.info(e);
			throw new SmartTestExceptions(e);
		}
		return testSetModel;

	}

	public TestCaseModel getTestCaseInfo(String testCaseId) throws SmartTestExceptions {
		TestCaseModel testCaseObject = new TestCaseModel();
		try {

			String sqlQuery = "SELECT * from testcase_tb where testcase_id =?";
			pstmt = con.prepareStatement(sqlQuery);
			pstmt.setString(1, testCaseId);
			execucutionIdrs = pstmt.executeQuery();
			int testCaseType = -1;
			while (execucutionIdrs.next()) {
				if (execucutionIdrs.getString(19) == null) {
					testCaseTypeInString = " ";
				} else {
					testCaseType = Integer.parseInt(execucutionIdrs.getString(19));
					testCaseTypeInString = getTestCaseType(testCaseType);
				}
				if (!(execucutionIdrs.getString(1) == null))
					testCaseObject.setTestCaseName(execucutionIdrs.getString(1));
				if (!(author == null))
					testCaseObject.setActualTester(author);
				testCaseObject.setExecDate(execDate);
				testCaseObject.setTestCaseType(testCaseTypeInString);
			}
		} catch (SQLException e) {
			LOGGER.info(e);
			throw new SmartTestExceptions(e);
		}
		return testCaseObject;
	}

	public String getTestCaseType(int testCaseType) {
		String testCaseTypeString = null;
		if (testCaseType == 0) {
			testCaseTypeString = "AUTOMATED";
		} else {
			testCaseTypeString = "MANUAL";
		}
		return testCaseTypeString;

	}
}