package com.bolt.dashboard.smarttestdefect;

import java.util.Iterator;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.SmartTestDefectTool;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.DefectRepo;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class SmartTestDefectApplication {


	private static final Logger LOGGER = LogManager.getLogger(SmartTestDefectApplication.class);
	SmartTestDefectClientImplementation impl = null;
	AnnotationConfigApplicationContext ctx = null;
	String host = "traderjoes.cacbcxiahgwb.us-east-1.rds.amazonaws.com:3306/TraderJoes";
	String user = "boltreports";
	String pass = "boltreports";
	String port = null;
	String projectId = "2";
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configurationColection = null;
	DefectRepo repo = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	ConfigurationToolInfoMetric metric1 = null;

	public SmartTestDefectApplication() {

	}

	public void smartTestMain(String projectName) {
		LOGGER.info("Smart Test Defect started for" + projectName);
		impl = new SmartTestDefectClientImplementation();
		ctx = DataConfig.getContext();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configurationColection = configurationRepo.findByProjectName(projectName).get(0);
		repo = ctx.getBean(DefectRepo.class);
		metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOGGER.info("Tool name  " + metric1.getToolName());
			if ("Smart Test Defect".equals(metric1.getToolName())) {
				LOGGER.info("URL  " + metric1.getUrl());
				host = metric1.getUrl();
				user = metric1.getUserName();
				
					pass=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				projectId = metric1.getProjectCode();
				break;
			}

		}

		try {
			SmartTestDefectTool tool = impl.getConnection(host, user, pass, projectName, projectId);
			tool.setProjectName(projectName);
			tool.setDefectType("Smart Test Defect");
			repo.save(tool);
			cleanObject();
			LOGGER.info("Smart Test ENDS..........");

		} catch (Exception e) {
			cleanObject();
			LOGGER.error(e.getMessage());
			LOGGER.info(e.getStackTrace());
			LOGGER.info(e);
			LOGGER.info("Smart Test Defect failed for " + projectName);
		}

		LOGGER.info("Smart Test Defect ended for " + projectName);
	}

	public void cleanObject() {
		configurationRepo = null;
		configurationColection = null;
		repo = null;
		metric = null;
		metric1 = null;
	}
}
