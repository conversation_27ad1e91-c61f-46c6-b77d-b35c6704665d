package com.bolt.dashboard.smarttestdefect;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;

import com.bolt.dashboard.core.model.DefectTool;
import com.bolt.dashboard.core.model.SmartTestDefectTool;

public class SmartTestDefectClientImplementation implements SmartTestDefectClient {
	private static final Logger LOGGER = LogManager.getLogger(SmartTestDefectClientImplementation.class);

    String projectId;
    Connection con = null;
    Statement statement = null;
    ResultSet rs = null;
    String[] glbDefectState = { "OPENED", "INPROGRESS", "INTRIAGE", "RESOLVED", "CLOSED", "REOPENED", "REJECTED",
            "DEFERRED", "WATCH", "INFONEEDED", "VERIFIED", "RETEST", "FIXED", "TEST" };
    String[] glbDefectPhase = { "OPENED", "INPROGRESS", "INTRIAGE", "RESOLVED", "CLOSED", "REOPENED", "REJECTED",
            "DEFERRED" };
    
    @Value("${smartTest.password}")
	public String dbPassword;

    @Override
    public SmartTestDefectTool getConnection(String url, String user, String pass, String projectName, String projectId)
            throws SmartTestDefectExceptions {
        this.projectId = projectId;
        SmartTestDefectTool tool = new SmartTestDefectTool();
        List<DefectTool> defectToolList = new ArrayList<>();
        try {
            Class.forName("com.mysql.jdbc.Driver").newInstance();
            String newHost = "jdbc:mysql://" + url;
            con = DriverManager.getConnection(newHost, "boltreports", dbPassword);

            LOGGER.info("successfully connected.....");
            String sql = "SELECT * FROM TraderJoes.defect_tb ";
            statement = con.createStatement();
            rs = statement.executeQuery(sql);
            while (rs.next()) {
                DefectTool defectTool = new DefectTool();
                defectTool.setProjectId(projectId);
                defectTool.setDefectId(rs.getString(1));
                if (rs.getString(2) != null) {
                    defectTool.setDefectDescription(rs.getString(2));
                }
                if (rs.getString(3) != null) {
                    int phase = Integer.parseInt(rs.getString(3));
                    String defectPhase = getDefectPhase(phase);
                    defectTool.setDefectDetectedPhase(defectPhase);
                }
                if (rs.getString(4) != null) {
                    int disposition = Integer.parseInt(rs.getString(4));
                    String defectDisposition = getDefectDisposition(disposition);
                    defectTool.setDefectDisposition(defectDisposition);
                }
                if (rs.getString(5) != null) {
                    defectTool.setDefectEnvsystem(rs.getString(5));
                }
                if (rs.getString(6) != null) {
                    int impact = Integer.parseInt(rs.getString(6));
                    String defectImpact = getDefectImpact(impact);
                    defectTool.setDefectImpact(defectImpact);
                }
                if (rs.getString(7) != null) {
                    Timestamp startTimestamp = Timestamp.valueOf(rs.getString(7));
                    long execDate = startTimestamp.getTime();
                    defectTool.setDefectLoggedOn(execDate);
                }

                if (rs.getString(8) != null) {
                    defectTool.setDefectName(rs.getString(8));
                }
                if (rs.getString(9) != null) {
                    defectTool.setDefectNumber(rs.getString(9));
                }
                if (rs.getString(10) != null) {
                    int priority = Integer.parseInt(rs.getString(10));
                    String defectPriority = getPriority(priority);

                    defectTool.setDefectPriority(defectPriority);
                }
                if (rs.getString(11) != null) {
                    defectTool.setDefectReson(rs.getString(11));
                }
                if (rs.getString(12) != null) {
                    int severity = Integer.parseInt(rs.getString(12));
                    String defectSeverity = getSeverity(severity);
                    defectTool.setDefectSeverity(defectSeverity);
                }
                if (rs.getString(13) != null) {
                    int state = Integer.parseInt(rs.getString(13));
                    String defectState = getDefectState(state);

                    defectTool.setDefectState(defectState);
                }
                if (rs.getString(14) != null) {
                    defectTool.setDefectStepsToReproduce(rs.getString(14));
                }
                if (rs.getString(15) != null) {
                    defectTool.setApplicationId(rs.getString(15));
                }
                if (rs.getString(16) != null) {
                    defectTool.setAssignedTo(rs.getString(16));
                }
                if (rs.getString(17) != null) {
                    defectTool.setClosedBy(rs.getString(17));
                }
                if (rs.getString(18) != null) {
                    defectTool.setClosedInRelease(rs.getString(18));
                }
                if (rs.getString(19) != null) {
                    defectTool.setClosedInSprint(rs.getString(19));
                }
                if (rs.getString(20) != null) {
                    defectTool.setDeferredInRelease(rs.getString(20));
                }
                if (rs.getString(21) != null) {
                    defectTool.setDeferredInSprint(rs.getString(21));
                }
                if (rs.getString(22) != null) {
                    defectTool.setFixedInRelease(rs.getString(22));
                }
                if (rs.getString(23) != null) {
                    defectTool.setFixedInSprint(rs.getString(23));
                }
                if (rs.getString(24) != null) {
                    defectTool.setFoundInRelease(rs.getString(24));
                }
                if (rs.getString(25) != null) {
                    defectTool.setFoundInSprint(rs.getString(25));
                }
                if (rs.getString(26) != null) {
                    defectTool.setOpenedBy(rs.getString(26));
                }
                if (rs.getString(27) != null) {
                    defectTool.setProjectId(rs.getString(27));
                }
                if (rs.getString(28) != null) {
                    defectTool.setReTestedInRelease(rs.getString(28));
                }
                if (rs.getString(29) != null) {
                    defectTool.setReTestedInSprint(rs.getString(29));
                }
                if (rs.getString(30) != null) {
                    defectTool.setResolvedBy(rs.getString(30));
                }
                defectToolList.add(defectTool);
            }
            tool.setToolList(defectToolList);
            return tool;
        } catch (Exception ex) {
        	LOGGER.info(ex);
            throw new SmartTestDefectExceptions(ex);
        }

    }

    public String getPriority(int priority) {
        String defectPriority = null;
        if (priority == 0) {
            defectPriority = "CRITICAL";
        } else if (priority == 1) {
            defectPriority = "HIGH";
        } else if (priority == 2) {
            defectPriority = "MEDIUM";
        } else if (priority == 3) {
            defectPriority = "LOW";
        }
        return defectPriority;

    }

    public String getSeverity(int severity) {
        String defectSeverity = null;
        if (severity == 0) {
            defectSeverity = "CRITICAL";
        } else if (severity == 1) {
            defectSeverity = "HIGH";
        } else if (severity == 2) {
            defectSeverity = "MEDIUM";
        } else if (severity == 3) {
            defectSeverity = "LOW";
        }
        return defectSeverity;

    }

    public String getDefectImpact(int impact) {
        String defectImpact = null;
        if (impact == 0) {
            defectImpact = "BLOCKING";
        } else if (impact == 1) {
            defectImpact = "NONBLOCKING";
        }
        return defectImpact;

    }

    public String getDefectDisposition(int disposition) {
        String defectDisposition = null;
        if (disposition == 0) {
            defectDisposition = "FIXED";
        } else if (disposition == 1) {
            defectDisposition = "DEFERRED";
        } else if (disposition == 2) {
            defectDisposition = "DUPLICATE";
        } else if (disposition == 3) {
            defectDisposition = "REJECTEDASDESIGN";
        } else if (disposition == 4) {
            defectDisposition = "REJECTEDNONREPRODUCIBLE";
        } else if (disposition == 5) {
            defectDisposition = "ENHANCEMENT";
        }
        return defectDisposition;

    }

    public String getDefectState(int state) {
        return glbDefectState[state];

    }

    public String getDefectPhase(int phase) {
        return glbDefectPhase[phase];

    }
}