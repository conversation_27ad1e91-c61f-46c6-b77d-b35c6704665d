package com.bolt.dashboard.sonar;

import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.Set;
//import java.util.logging.Logger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.ResponseEntity;

import com.atlassian.httpclient.api.Response;
import com.bolt.dashboard.circleci.CircleCIClientImplementation;
import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.repository.CodeQualityRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.util.EncryptionDecryptionAES;
import com.bolt.dashboard.util.RestClient;

/**
 * Application configuration and bootstrap
 */
@SpringBootApplication
public class SonarApplication {
	private static final Logger LOGGER = LogManager.getLogger(SonarApplication.class);
	AnnotationConfigApplicationContext ctx;
	CodeQualityRep repo;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configurationColection = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	ConfigurationToolInfoMetric metric1 = null;
	List<CodeQuality> cq = null;

	
	
//  public static void main(String[] args) { new
//	  SonarApplication().sonarMain("BrillioOne"); 
//	}
	 
	 
	public void sonarMain(String projectName) {
		LOGGER.info("SonarQube Collector started for " + projectName);
		ctx = DataConfig.getContext();
       boolean latestSonar=false;
		repo = ctx.getBean(CodeQualityRep.class);
		String instanceURL = "";
		String projectId = null;
		String userName = null;
		String password = null;
		RestClient restClient=null;
		Boolean multipleFlag = false;
		LOGGER.info("Start of sonar Main");
		String type = "SonarQube";
		String result = "SUCCESS";
		SonarClient sonar = new SonarClientImplementation();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configurationColection = configurationRepo.findByProjectName(projectName).get(0);
	if(configurationColection!=null)
	{
		metric = configurationColection.getMetrics();

		Stream<ConfigurationToolInfoMetric> sonarMetricStream = metric.stream()
				.filter(metric -> metric.getToolName().equals(type));

		Optional<ConfigurationToolInfoMetric> sonarMetric = sonarMetricStream.findFirst();
		
		if (sonarMetric.isPresent()) {
			LOGGER.info("URL  " + sonarMetric.get().getUrl());
			instanceURL = sonarMetric.get().getUrl();
			projectId = sonarMetric.get().getProjectCode();
			userName = sonarMetric.get().getUserName();
			
				password=EncryptionDecryptionAES.decrypt(sonarMetric.get().getPassword(), ConstantVariable.SECRET_KEY);
		}
		
		try {
			
		
			String[] projectSeparationString = null;
			if (projectId != null) {
				projectSeparationString = projectId.split(Pattern.quote(","));
                 restClient=new RestClient();
                 ResponseEntity<String> versionResponse= restClient.makeGetRestCall(instanceURL+"/api/webservices/list",userName,password);
                 JSONParser jsonParser = new JSONParser();
                 JSONObject jsonObject=(JSONObject) jsonParser.parse (versionResponse.getBody()); 
                 JSONArray jsonArray =(JSONArray) jsonObject.get("webServices");
                     
                     Iterator it = jsonArray.iterator();
                     while(it.hasNext()) {
                     	JSONObject obj = (JSONObject) it.next();
                     	if(obj.get("path").equals("api/measures")) {
                     		JSONArray actions = (JSONArray)obj.get("actions");
                     		Iterator actionsIt = actions.iterator();
                     		while(actionsIt.hasNext()) {
                     			JSONObject action = (JSONObject) actionsIt.next();
                     			if(action.get("key").equals("search_history")) {
                     				latestSonar = true;
                     				break;
                     			}
                     			
                     		}
                     		break;
                     	}
                     }
//                  String version=versionResponse.getBody();
//                  String versionArray[]= version.split("\\.");
//                  if(Integer.parseInt(versionArray[0])>6 || Integer.parseInt(versionArray[1])>2) {
//                 	 latestSonar=true;
//                  }
				if (projectSeparationString.length > 1) {
					multipleFlag = true;
				}
				
				for (String projectCode : projectSeparationString) {
					String url = "";
					LOGGER.info("projectCode for Sonar   " + projectCode);
					if(!latestSonar) {
					if (instanceURL.contains("id")) {
						url = instanceURL + projectCode;
						String[] separationString = url.split(Pattern.quote("/"));
						String nameUrl = separationString[separationString.length - 1];
						nameUrl = nameUrl.split(Pattern.quote("="))[0];
						url = url.replace(nameUrl, "api/resources?format=json&resource");
						LOGGER.info("instanceURL     " + url);
					} else {
						url = instanceURL + "/" + projectCode;
						url = url.replace("dashboard/index/", "api/resources?format=json&resource=");
						LOGGER.info("instanceURL   " + url);
					}

					cq = sonar.getCodeQuality(url, userName, password,
							projectName, projectCode, multipleFlag);
					}
					else {
						cq=sonar.getCodeQualityLatest(instanceURL, userName, password,projectName, projectCode, multipleFlag);
						//repo.delete(repo.findByNameAndVersion(projectName, projectCode));
						List<CodeQuality> historyData = (List<CodeQuality>) repo.findByNameAndVersion(projectName, projectCode);
						
						if(historyData.size()>0) {
						
						historyData.sort(Comparator.comparing(CodeQuality::getTimestamp));
						long lastTimeStamp = historyData.get(historyData.size()-1).getTimestamp();
						
						List<CodeQuality> filtered = cq.stream().filter((ele)-> {
							if(ele.getTimestamp() >= lastTimeStamp) {
								return true;
							} else {
								return false;
							}
						}).collect(Collectors.toList());
						
						List<CodeQuality> redundunt = historyData.stream().filter((ele)->{
							if(ele.getTimestamp() == lastTimeStamp) {
								return true;
							}else {
								return false;
							}
						}).collect(Collectors.toList());
						
						repo.delete(redundunt);
						cq = filtered;
					}
				}
					
					repo.save(cq);

				}
			}

			ConstantVariable.getLastRun(projectName, type, new Date().getTime(), result);
			cleanObject();
			LOGGER.info("End of sonar main");
		} catch (Exception e) {
			result = "FAIL";
			ConstantVariable.getLastRun(projectName, type, new Date().getTime(), result);
			cleanObject();
			LOGGER.error(e);
			LOGGER.info("SonarQube Collector failed for " + projectName);
		}
	}else{
		LOGGER.error("NO data found for project "+projectName+"From Configuartion Collection..");
	}
	}

	public static boolean filterEmployees(String[] projectSeparationString) {
		return true;
	}

	public void cleanObject() {
		repo = null;
		configurationRepo = null;
		configurationColection = null;
		metric = null;
		metric1 = null;
		cq = null;
	}
}