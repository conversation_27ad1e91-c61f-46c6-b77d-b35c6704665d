package com.bolt.dashboard.sonar;

import java.text.ParseException;
import java.util.List;

import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.sonar.SonarCollectorException;

/**
 * <AUTHOR>
 *
 */
public interface SonarClient {
	/**
	 * @param multipleFlag
	 * @param CQ_URL
	 * @return
	 * @throws RestClientException
	 * @throws ParseException
	 */
	List<CodeQuality> getCodeQuality(String sonarUrl, String user, String pass, String projectName, String projectId,
			Boolean multipleFlag) throws SonarCollectorException;

	List<CodeQuality> getCodeQualityLatest(String instanceURL, String userName, String password, String projectName,
			String projectCode, Boolean multipleFlag);

}
