package com.bolt.dashboard.sonar;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.DateTimeFormatterBuilder;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.core.model.CodeQualityComponents;
import com.bolt.dashboard.core.model.CodeQualityComponentsMetrics;
import com.bolt.dashboard.core.model.CodeQualityMetric;
import com.bolt.dashboard.core.repository.CodeQualityRep;
import com.bolt.dashboard.util.RestClient;
import com.mysema.query.types.Constant;


public class SonarClientImplementation implements SonarClient {

	private static final Logger LOGGER = LogManager.getLogger(SonarClientImplementation.class);
	AnnotationConfigApplicationContext ctx = null;
	CodeQuality tool = null;
	String projectName = null;
	String projectCode = null;
	RestClient restClient=null;
	private Set<CodeQualityComponents> componentsData = null;
	private List<CodeQuality> codeQualityList=null;

	public RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000);
		requestFactory.setReadTimeout(20000);
		return new RestTemplate(requestFactory);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.bolt.dashboard.Client.SonarClient#getCodeQuality(java.lang.String)
	 */
	public List<CodeQuality> getCodeQuality(String sonarUrl, String user, String pass, String projectName,
			String projectId, Boolean multipleFlag) throws SonarCollectorException {
		this.projectName = projectName;
		this.projectCode = projectId;
		LOGGER.info("getCodeQuality Url   " );
		String componentUrl = sonarUrl + "&depth=1" + ConstantVariable.SONAR_METRICS;

		String fnsonarUrl = sonarUrl + ConstantVariable.SONAR_METRICS;

		int dataSize = getCount(projectName, multipleFlag);
		JSONArray jsonArray = null;
		List<CodeQuality> collectionData = new ArrayList<>();
		if (dataSize == 0) {
			String historyUrl = fnsonarUrl.replace("resources", "timemachine")
					+ "&fromDateTime=2012-01-01T00:00:00+0100";
			LOGGER.info("history Url   " + historyUrl);
			historyUrl = String.format(historyUrl);
			ResponseEntity<String> historyResponse = makeRestCall(historyUrl, user, pass);

			jsonArray = parseAsNewArray(historyResponse);
			componentUrl = String.format(componentUrl);
			LOGGER.info("componentUrl Url   " + componentUrl);
			ResponseEntity<String> componentResponse = makeRestCall(componentUrl, user, pass);
			JSONArray componentJsonArray = parseAsNewArray(componentResponse);
			collectionData = getCodeQualityHistory(jsonArray, componentJsonArray);
			return collectionData;

		} else {
			String url = String.format(fnsonarUrl);
			LOGGER.info("componentUrl else Url   " + url);
			ResponseEntity<String> response = makeRestCall(url, user, pass);

			jsonArray = parseAsNewArray(response);
			componentUrl = String.format(componentUrl);
			LOGGER.info("componentUrl else 2 Url   " + componentUrl);
			ResponseEntity<String> componentResponse = makeRestCall(componentUrl, user, pass);
			JSONArray componentJsonArray = parseAsNewArray(componentResponse);
			collectionData = getCodeQualityInfo(jsonArray, componentJsonArray);
			return collectionData;
		}

	}

	public List<CodeQuality> getCodeQualityInfo(JSONArray jsonArray, JSONArray componentJsonArray)
			throws SonarCollectorException {
		List<CodeQuality> codeQuality = new ArrayList<>();

		try {
			tool = new CodeQuality();
			tool.setName(projectName);
			if (jsonArray.length() == 0 || componentJsonArray.length() == 0) {
				return codeQuality;
			}
			addMetric(jsonArray);
			addComponent(componentJsonArray);

			codeQuality.add(tool);
			return codeQuality;
		}

		catch (RestClientException rce) {
			LOGGER.error(rce);
			throw new SonarCollectorException();
		}
	}

	public void addMetric(JSONArray jsonArray) {
		for (int i = 0; i < jsonArray.length(); i++) {
			JSONObject jsonNewLoopObject = (JSONObject) jsonArray.get(i);

			tool.setTimestamp(ConstantVariable.timestamp(jsonNewLoopObject.get("date"),projectName));
			tool.setVersion(this.projectCode);
			tool.setInstance(jsonNewLoopObject.getString("name"));
			for (int j = 0; j < ((JSONArray) jsonNewLoopObject.get("msr")).length(); j++) {
				JSONObject metricJsonNew = (JSONObject) ((JSONArray) jsonNewLoopObject.get("msr")).get(j);

				CodeQualityMetric metricNew = new CodeQualityMetric(metricJsonNew.getString("key"));

				String value = metricJsonNew.get(ConstantVariable.VALUE).toString();
				if (!("null".equals(value))) {
					metricNew.setValue(value);
					metricNew.setFormattedValue(metricJsonNew.get(ConstantVariable.FORMATTED_VALUE).toString());
				}

				if (metricJsonNew.has(ConstantVariable.ALERT_TEXT))
					metricNew.setStatusMessage(metricJsonNew.getString(ConstantVariable.ALERT_TEXT));
				tool.getMetrics().add(metricNew);
			}
		}
	}

	public void addComponent(JSONArray componentJsonArray) {
		for (int i = 0; i < componentJsonArray.length() - 1; i++) {
			JSONObject componentJsonObject = (JSONObject) componentJsonArray.get(i);
			String componentName = componentJsonObject.getString("name");
			CodeQualityComponents components = new CodeQualityComponents();
			components.setComponentName(componentName);
			Set<CodeQualityComponentsMetrics> componentMetrics = new HashSet<>();
			if (!componentJsonObject.toString().contains("msr")) {
				continue;
			}

			for (int j = 0; j < ((JSONArray) componentJsonObject.get("msr")).length(); j++) {

				JSONObject metricJsonNew = (JSONObject) ((JSONArray) componentJsonObject.get("msr")).get(j);
				CodeQualityComponentsMetrics metricNew = new CodeQualityComponentsMetrics(
						metricJsonNew.getString("key"));

				String value = metricJsonNew.get(ConstantVariable.VALUE).toString();
				if (!("null".equals(value))) {
					metricNew.setValue(value);
					metricNew.setFormattedValue(metricJsonNew.get(ConstantVariable.FORMATTED_VALUE).toString());
				}

				componentMetrics.add(metricNew);

				components.setComponents(componentMetrics);

			}
			tool.getComponents().add(components);
		}
	}

	public ResponseEntity<String> makeRestCall(String url, String userId, String password)
			throws SonarCollectorException {
		// Basic Auth only.

		if (!"".equals(userId) && !"".equals(password)) {
			return get().exchange(url, HttpMethod.GET, new HttpEntity<>(createHeaders(userId, password)), String.class);

		} else {
			return get().exchange(url, HttpMethod.GET, null, String.class);
		}

	}

	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}

	private JSONArray parseAsNewArray(ResponseEntity<String> response) {
		return (JSONArray) new JSONTokener(response.getBody()).nextValue();
	}
	private JSONObject parseAsNewObject(ResponseEntity<String> response) {
		return (JSONObject) new JSONTokener(response.getBody()).nextValue();
	}

	@SuppressWarnings("unused")
	private Integer integer(JSONObject json, String key) {
		Object obj = json.get(key);
		return obj == null ? null : (Integer) obj;
	}

	@SuppressWarnings("unused")
	private BigDecimal decimal(JSONObject json, String key) {
		Object obj = json.get(key);
		return obj == null ? null : new BigDecimal(obj.toString());
	}

	@SuppressWarnings("unused")
	private Boolean bool(JSONObject json, String key) {
		Object obj = json.get(key);
		return obj == null ? null : Boolean.valueOf(obj.toString());
	}

	/**/
	public int getCount(String projectName, Boolean multipleFlag) {
		ctx = DataConfig.getContext();
		CodeQualityRep repo = ctx.getBean(CodeQualityRep.class);
		Iterable<CodeQuality> data = null;
		if (multipleFlag) {
			data = repo.findByNameAndVersion(projectName, this.projectCode);
		} else {
			data = repo.findByName(projectName);
		}

		Collection<CodeQuality> list = new ArrayList<CodeQuality>();
		for (CodeQuality item : data) {
			list.add(item);
		}

		LOGGER.info("size  " + list.size());

		return list.size();
	}

	public List<CodeQuality> getCodeQualityHistory(JSONArray historyJsonArray, JSONArray componentsJsonArary) {
		List<CodeQuality> codeQuality = new ArrayList<>();

		try {

			if (historyJsonArray.length() == 0) {
				return codeQuality;
			}
			if (componentsJsonArary.length() == 0) {
				return codeQuality;
			}

			for (int i = 0; i < historyJsonArray.length(); i++) {
				JSONObject historyJsonObject = (JSONObject) historyJsonArray.get(i);
				JSONArray colsJsonArray = historyJsonObject.getJSONArray("cols");
				JSONArray cellsJsonArary = historyJsonObject.getJSONArray("cells");
				codeQuality = getCellsInformation(cellsJsonArary, colsJsonArray, componentsJsonArary);
			}
		} catch (Exception e) {
			LOGGER.info(e);
		}
		codeQuality.add(tool);
		return codeQuality;

	}

	public List<CodeQuality> getCellsInformation(JSONArray cellsJsonArary, JSONArray colsJsonArray,
			JSONArray componentsJsonArary) {
		List<CodeQuality> codeQuality = new ArrayList<>();
		for (int i = 0; i < cellsJsonArary.length(); i++) {
			JSONObject cellsJsonObject = (JSONObject) cellsJsonArary.get(i);
			tool = new CodeQuality();
			tool.setName(projectName);
			tool.setVersion(this.projectCode);
			tool.setTimestamp(ConstantVariable.timestamp(cellsJsonObject.getString("d"),projectName));

			JSONArray valuesArary = cellsJsonObject.getJSONArray("v");
			for (int j = 0; j < colsJsonArray.length(); j++) {
				String name = ((JSONObject) colsJsonArray.get(j)).getString("metric");

				CodeQualityMetric metricNew = new CodeQualityMetric(name);
				String value = valuesArary.get(j).toString();
				if (!("null".equals(value))) {
					metricNew.setValue(value);
					metricNew.setFormattedValue(value);
				}
				tool.getMetrics().add(metricNew);

			}
			for (int m = 0; m < componentsJsonArary.length(); m++) {
				JSONObject componentJsonObject = (JSONObject) componentsJsonArary.get(m);
				String componentName = componentJsonObject.getString("name");
				String key = componentJsonObject.getString("key").split(Pattern.quote(":"))[0];
				tool.setInstance(key);
				CodeQualityComponents components = new CodeQualityComponents();
				components.setComponentName(componentName);
				long moduleCreatedDate = ConstantVariable.timestamp(componentJsonObject.getString("creationDate"),projectName);
				components.setModuleCreatedDate(moduleCreatedDate);

				Set<CodeQualityComponentsMetrics> componentMetrics = new HashSet<>();

				for (int j = 0; j < ((JSONArray) componentJsonObject.get("msr")).length(); j++) {
					JSONObject metricJsonNew = (JSONObject) ((JSONArray) componentJsonObject.get("msr")).get(j);
					CodeQualityComponentsMetrics metricNew = new CodeQualityComponentsMetrics(
							metricJsonNew.getString("key"));
					String value = metricJsonNew.get(ConstantVariable.VALUE).toString();
					if (!("null".equals(value))) {
						metricNew.setValue(value);
						metricNew.setFormattedValue(value);
					}

					componentMetrics.add(metricNew);
					components.setComponents(componentMetrics);

				}
				tool.getComponents().add(components);
			}

			codeQuality.add(tool);
		}

		return codeQuality;
	}

	@Override
	public List<CodeQuality> getCodeQualityLatest(String instanceURL, String userName, String password,
			String projectName, String projectCode, Boolean multipleFlag) {
		
		 componentsData = new HashSet<CodeQualityComponents>();
		this.projectName = projectName;
		
		this.projectCode = projectCode;
		String historyUrl=instanceURL;
		String componentUrl=instanceURL;
		
		int dataSize = getCount(projectName, multipleFlag);
		JSONArray jsonArray = null;
		JSONObject historyJsonObject=null;
		JSONObject componentJsonObject1=null;
		JSONObject componentJsonObject2=null;
		JSONObject componentJsonObject3=null;
		List<CodeQuality> collectionData = new ArrayList<>();
		LOGGER.info("getCodeQualityLatest   " );
		if (dataSize == 0) {
		//	String date[]=ConstantVariable.SONARCOLLECTORSTARTDATE.split("\\+");
			
			 
			historyUrl=historyUrl+"/api/measures/search_history?component="+this.projectCode+"&metrics="+
		      ConstantVariable.SONAR_METRICS_NEW1+","+ConstantVariable.SONAR_METRICS_NEW2+","+ConstantVariable.SONAR_METRICS_NEW3
		        +"&from="+ConstantVariable.SONARCOLLECTORSTARTDATE;
		}
		else {
			ctx = DataConfig.getContext();
			CodeQualityRep repo = ctx.getBean(CodeQualityRep.class);
//			CodeQuality lastAddedCodeQuality= repo.findOneByNameAndVersionOrderByTimestamp(this.projectName,this.projectCode);
//			long lastCollectedDate=lastAddedCodeQuality.getTimestamp();
//			
//			DateTimeZone zoneUTC = DateTimeZone.forOffsetHours(+0);
//			DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ssZ");
//		    DateTime tempDate=new DateTime(lastCollectedDate,zoneUTC);
//		    String lastRunDate=tempDate.toString(fmt);

		   
		    historyUrl=historyUrl+"/api/measures/search_history?component="+this.projectCode+"&metrics="+
		    		ConstantVariable.SONAR_METRICS_NEW1+","+ConstantVariable.SONAR_METRICS_NEW2+ ","+ConstantVariable.SONAR_METRICS_NEW3
			        +"&from="+ConstantVariable.SONARCOLLECTORSTARTDATE;
			
		}
		String componentUrl1 =componentUrl+"/api/measures/component_tree?component="
		          +this.projectCode+"&metricKeys="
		                +ConstantVariable.SONAR_METRICS_NEW1+"&qualifiers=DIR&additionalFields=periods";
		
		String componentUrl2 = componentUrl+"/api/measures/component_tree?component="
		        +this.projectCode+"&metricKeys="
		              +ConstantVariable.SONAR_METRICS_NEW2+"&qualifiers=DIR&additionalFields=periods";
		String componentUrl3 = componentUrl+"/api/measures/component_tree?component="
		        +this.projectCode+"&metricKeys="
		              +ConstantVariable.SONAR_METRICS_NEW3+"&qualifiers=DIR&additionalFields=periods";
		this.restClient=new RestClient();
		LOGGER.info("componentUrl1 Url   " + componentUrl1);
		ResponseEntity<String> jsonResponseString=this.restClient.makeGetRestCall(componentUrl1+"&ps=1", userName, password);
		componentJsonObject1=parseAsNewObject(jsonResponseString);
		JSONObject page=componentJsonObject1.getJSONObject("paging");
		double totalRecords= page.getDouble("total");
		int startPage=1;
		
		 
		
	    int pages=(int)Math.ceil(totalRecords/100);
	   
	    while(startPage<=pages) {
	    	jsonResponseString=this.restClient.makeGetRestCall(componentUrl1+"&p="+startPage, userName, password);
	    	componentJsonObject1=parseAsNewObject(jsonResponseString);
	    	jsonResponseString=this.restClient.makeGetRestCall(componentUrl2+"&p="+startPage, userName, password);
	    	componentJsonObject2=parseAsNewObject(jsonResponseString);
	    	jsonResponseString=this.restClient.makeGetRestCall(componentUrl3+"&p="+startPage, userName, password);
	    	componentJsonObject3=parseAsNewObject(jsonResponseString);
	    	processComponentsData(componentJsonObject1,componentJsonObject2,componentJsonObject3);
	    	startPage++;
	    	
	    }
		
		
				jsonResponseString=this.restClient.makeGetRestCall(historyUrl+"&ps=1", userName, password);
		
		historyJsonObject=parseAsNewObject(jsonResponseString);
		 page=historyJsonObject.getJSONObject("paging");
		 totalRecords= page.getDouble("total");
		 startPage=1;
	     pages=(int)Math.ceil(totalRecords/100);
	    while(startPage<=pages) {
	    	jsonResponseString=this.restClient.makeGetRestCall(historyUrl+"&p="+startPage, userName, password);
			historyJsonObject=parseAsNewObject(jsonResponseString);
        processHistoryData(historyJsonObject);
        startPage++;
	    }
		return codeQualityList;
	}

	private void processComponentsData(JSONObject componentJsonObject1, JSONObject componentJsonObject2, JSONObject componentJsonObject3) {
		
		
		
		JSONArray tempArray=componentJsonObject1.getJSONArray("periods");
		 long periodDateMilis=0;
		 if(tempArray!=null) {
			 JSONObject period=tempArray.getJSONObject(0);
			 String periodDate=period.getString("date");
			 DateTimeZone zoneUTC = DateTimeZone.forOffsetHours(+0);
				DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ssZ");
			    DateTime tempDate=new DateTime(periodDate,zoneUTC);
			    periodDateMilis=tempDate.getMillis();
			 
		 }
		
		for(int i=0;i<=2;i++) {
			if(i==1) {
				componentJsonObject1=componentJsonObject2;
			}else if(i==2) {
				componentJsonObject1=componentJsonObject3;
			}
		   // componentJsonObject1=i==0?componentJsonObject1:componentJsonObject2;
		    boolean firstFlag=i==0?true:false;
		    
			
			tempArray=componentJsonObject1.getJSONArray("components");
			
			if(tempArray!=null && tempArray.length()>0) {
			for(int j=0;j<tempArray.length();j++) {
			   JSONObject tempJson= tempArray.getJSONObject(j);
			   CodeQualityComponents component=null;
			    if(firstFlag) {
			component=new CodeQualityComponents();
			   component.setComponentName(tempJson.getString("name"));
			   componentsData.add(component);
			    }else {
			    	component = componentsData.stream().
			    			filter(p -> p.getComponentName().equals(tempJson.getString("name"))).
			    			findAny().orElse(null);
			    	
			    }
			    if(component!=null) {
			    	component.setModuleCreatedDate(periodDateMilis);
			    }
			    
			    JSONArray measureArray= tempJson.getJSONArray("measures");
			     for(int k=0;k<measureArray.length();k++) {
			    	 JSONObject measure=measureArray.getJSONObject(k);
			    	 CodeQualityComponentsMetrics tempComponentMetric=new CodeQualityComponentsMetrics(measure.optString("metric"));
			    	   tempComponentMetric.setValue(measure.opt("value"));
			    	   if(component!=null)
			    		   component.getComponents().add(tempComponentMetric);
			     }
			    
			   
			 }
			}
		}
		
		
		
		
	}

	private void processHistoryData(JSONObject historyJsonObject) {
		
		codeQualityList=new ArrayList<CodeQuality>();
		
		JSONArray measuresArray=historyJsonObject.getJSONArray("measures");
		
		for(int i=0;i<measuresArray.length();i++) {
			JSONObject metricObj=measuresArray.getJSONObject(i);
			String metricName=metricObj.getString("metric");
			
			JSONArray historyArray=metricObj.getJSONArray("history");
			for(int j=0;j<historyArray.length();j++) {
				JSONObject historyObj=historyArray.getJSONObject(j);
				 String historyDate=historyObj.getString("date");
				 DateTimeZone zoneUTC = DateTimeZone.forOffsetHours(+0);
					DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ssZ");
				    DateTime tempDate=new DateTime(historyDate,zoneUTC);
				    long historyDateMils=tempDate.getMillis();
				CodeQuality codeQuality=null;
				if(i==0) {
					codeQuality =new CodeQuality();
			        codeQualityList.add(codeQuality);
			        codeQuality.setTimestamp(historyDateMils);
			        codeQuality.setComponents(componentsData);
				}else {
					
					codeQuality = codeQualityList.stream().
			    			filter(p -> p.getTimestamp()==historyDateMils).
			    			findAny().orElse(null);
				}
				
			
				
				if(historyObj.has("value")) {
				
				CodeQualityMetric metrics=new CodeQualityMetric(metricName);
				metrics.setValue(historyObj.optDouble("value"));
				if(codeQuality !=null) {
					codeQuality.getMetrics().add(metrics);
					codeQuality.setName(this.projectName);
					codeQuality.setVersion(this.projectCode);
				}
				
				}
				else {
					
				}
			}
			
			
			
		}
	}
}


