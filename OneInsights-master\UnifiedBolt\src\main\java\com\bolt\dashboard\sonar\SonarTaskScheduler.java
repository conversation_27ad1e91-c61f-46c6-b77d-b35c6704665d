package com.bolt.dashboard.sonar;


import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;

import com.bolt.dashboard.core.ProjectCollector;

public abstract class SonarTaskScheduler extends QuartzJobBean {
	private static final Logger LOGGER = LogManager.getLogger(SonarApplication.class);
	private ProjectCollector task;

	@Override
	protected void executeInternal(JobExecutionContext arg0) throws JobExecutionException {

		try {
			task.printMessage();
		} catch (Exception e) {
			LOGGER.info(e);
			throw new JobExecutionException(e);
		}
	}

}
