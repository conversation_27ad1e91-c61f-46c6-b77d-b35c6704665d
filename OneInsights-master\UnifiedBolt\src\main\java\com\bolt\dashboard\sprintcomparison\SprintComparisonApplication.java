package com.bolt.dashboard.sprintcomparison;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.config.MongoAggregate;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.model.CodeQuality;
import com.bolt.dashboard.core.model.CodeQualityMetric;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.model.SmartTestDefectTool;
import com.bolt.dashboard.core.model.SprintComparison;
import com.bolt.dashboard.core.model.SprintComparisonMetrics;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.CodeQualityRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.DefectRepo;
import com.bolt.dashboard.core.repository.ProjectIterationRepo;
import com.bolt.dashboard.core.repository.SCMToolRepository;
import com.bolt.dashboard.core.repository.SprintComparisonRepo;
import com.bolt.dashboard.core.repository.TestManagementRepo;
import com.bolt.dashboard.projectHealth.ProjectHealthSprintProgress;

/**
 * <AUTHOR>
 *
 */
public class SprintComparisonApplication {
	private static final Logger LOGGER = LogManager.getLogger(SprintComparisonApplication.class);
    
    public static final String ALM_STORY = "Story";
    public static final String ALM_BUG = "Bug";
    public static final String ALM_DEFECT = "Defect";
    public static final String ALM_PRODUCT_BACKLOG = "Product Backlog Item";
    public static final String CQ_TD = "sqale_index";

    public static final String ALM_TASK = "Task";
    public static final String ALM_SUBTASK = "Sub-task";
    public static final String DONE_STATE = "Done";
    public static final String CLOSED_STATE = "Closed";
    public static final String FAIL_STATE = "FAILURE";
    public String sprintWise = "Sprint Wise";
    String result = "SUCCESS";

    /**
     * Private constructor
     */
    public SprintComparisonApplication() {

    }

    private ProjectIterationRepo iterationRepo;
    String projectType = null;
    int defectsCount;
    double efficiency;
    double totalStoryPoints;
    double storyPoints;
    String projectName = null;
    AnnotationConfigApplicationContext ctx = null;
    SprintComparisonRepo repo = null;
    TestManagementRepo testmgmtRepo = null;
    DefectRepo defectRepo;
    SCMToolRepository scmRepo = null;

    Set<String> assignedToSet = null;
    BuildToolRep buildRepo = null;
    CodeQualityRep codeQualityRepo = null;
    String almType = "";
    String defectType = null;
    String buildType = "";
    String scmType = "";
    int filesAdded;
    int filesDeleted;
    int filesModified;
    String technicalDebt;
    SprintComparison sprintComparison = null;
    SprintComparisonMetrics metrics = null;
    Set<SprintComparisonMetrics> metricsList = new LinkedHashSet<>();
    List<SmartTestDefectTool> defectList;
    String name;
    long[] datesArray;

    public void sprintComparisonMain(String name) {

    	LOGGER.info("Sprint Comparsion Main   started for " + name);
	this.projectName = name;

	ctx = DataConfig.getContext();
	repo = ctx.getBean(SprintComparisonRepo.class);

	ConfigurationSettingRep configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
	ConfigurationSetting configurationColection = configurationRepo.findByProjectName(projectName).get(0);
	if (configurationColection != null) {
	    projectType = configurationColection.getProjectType();

	    try {
		if (projectType.equals(sprintWise)) {
		    getSprintWiseData();
		    ConstantVariable.getLastRun(name, "Sprint comparison", new Date().getTime(), result);
		    LOGGER.info("Sprint Comparsion Collector ended for " + name);
		} else {
			LOGGER.info("Sprint Comparision N/A for  " + name + " as it is a " + projectType);
		    ConstantVariable.getLastRun(name, "Sprint comparison", new Date().getTime(), result);
		    cleanObject();
		}
	    } catch (SprintComparisonException e) {
		result = "FAIL";
		ConstantVariable.getLastRun(name, "Sprint comparison", new Date().getTime(), result);
		cleanObject();
		LOGGER.info("Sprint Comparsion Collector Failed for " + name);
	    }
	} else {
		LOGGER.error("NO Data found for project  " + projectName + " in configuration collection..");
	}

	cleanObject();

    }

    public void getSprintWiseData() throws SprintComparisonException {
	iterationRepo = ctx.getBean(ProjectIterationRepo.class);
	almType = getAlmType(projectName);
	testmgmtRepo = ctx.getBean(TestManagementRepo.class);
	scmRepo = ctx.getBean(SCMToolRepository.class);
	defectRepo = ctx.getBean(DefectRepo.class);
	defectList = defectRepo.findByProjectNameAndDefectType(projectName, defectType);
	buildRepo = ctx.getBean(BuildToolRep.class);
	codeQualityRepo = ctx.getBean(CodeQualityRep.class);

	SprintComparison comparison = getIterationDates(projectName);
	repo.save(comparison);
    }

    public SprintComparison getIterationDates(String projectName) throws SprintComparisonException {
	this.name = projectName;

	MongoAggregate agg = new MongoAggregate();

	List<IterationOutModel> dataModel = iterationRepo.findByPName(projectName);
	if (dataModel == null || dataModel.size() < 1) {
	    agg.aggregate(projectName,almType);
	    dataModel = iterationRepo.findByPName(projectName);
	}
	sprintComparison = new SprintComparison();
	sprintComparison.setProjectName(projectName);
	sprintComparison.setTimeStamp(new Date().getTime());

	IterationOutModel nextIteration;

	for (int i = 0; i < dataModel.size(); i++) {
	    IterationOutModel currentIteration = dataModel.get(i);
	    if (currentIteration.getStDate() == null) {
		continue;
	    }
	    long[] datesArray = new long[3];
	    datesArray[0] = currentIteration.getStDate();
	    datesArray[1] = currentIteration.getCompletedDate();
	    metrics = new SprintComparisonMetrics();
	    filesAdded = 0;
	    filesDeleted = 0;
	    filesModified = 0;
	    technicalDebt = "";
	    storyPoints = 0;
	    totalStoryPoints = 0;
	    efficiency = 0;
	    defectsCount = 0;
	    assignedToSet = new HashSet<>();

	    if (datesArray[0] == 0) {
		continue;
	    }
	    long nextIterationStartDate = 0;
	    if (i < dataModel.size() - 1) {
		nextIteration = dataModel.get(i + 1);
		if (!(nextIteration.getStDate() == null || nextIteration.getStDate() == 0)) {
		    nextIterationStartDate = nextIteration.getStDate();
		} else {
		    nextIterationStartDate = datesArray[1];
		}
	    }
	    metrics.setItaretionStartDate(datesArray[0]);
	    metrics.setIterationEndDate(datesArray[1]);
	    metrics.setIterationName(currentIteration.getsName());
	    metrics.setVelocity((int) currentIteration.getVelocity());

	    getCapacity(datesArray[0], datesArray[1], currentIteration);
	    getBuildInfo(datesArray[0], nextIterationStartDate);
	    // check for all Projects
	    getCommitInfo(scmType, datesArray[0], nextIterationStartDate);
	    getCodeQualityInfo(datesArray[0], nextIterationStartDate);
	    metricsList.add(metrics);

	}
	sprintComparison.setMetrics(metricsList);
	return sprintComparison;
    }

    public void getCommitInfo(String scmType, long pastIterationStartDate, long ppastIterationStartDate) {
	Iterable<SCMTool> scmToolList = scmRepo.findByScTypeAndProjectNameAndCommitTSBetween(scmType, name,
		pastIterationStartDate, ppastIterationStartDate);
	Iterator<SCMTool> scmToolIterator = scmToolList.iterator();
	while (scmToolIterator.hasNext()) {

	    SCMTool scmTool = scmToolIterator.next();
	    filesAdded = filesAdded + scmTool.getAddition();
	    filesDeleted = filesDeleted + scmTool.getDeletion();
	    filesModified = filesModified + scmTool.getModification();

	}
	metrics.setFilesAdded(filesAdded);
	metrics.setFilesDeleted(filesDeleted);
	metrics.setFilesModified(filesModified);
    }

    public String getAlmType(String projectName) {

	ctx = DataConfig.getContext();
	ConfigurationSettingRep configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
	ConfigurationSetting configurationColection = configurationRepo.findByProjectName(projectName).get(0);

	Set<ConfigurationToolInfoMetric> metric = configurationColection.getMetrics();
	Iterator<ConfigurationToolInfoMetric> iter = metric.iterator();

	while (iter.hasNext()) {
	    Object configuration1 = iter.next();
	    ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
	    if ("Jira".equals(metric1.getToolName())) {
		almType = "JIRA";

	    }
	    if ("BIT Server".equals(metric1.getToolName()) || "BITBUCKET".equals(metric1.getToolName())) {
		scmType = "BITBUCKET";
	    }
	    if ("SVN".equals(metric1.getToolName())) {
		scmType = "SVN";
	    }
	    if ("Jenkins".equals(metric1.getToolName())) {
		buildType = "JENKINS";
	    }
	    if ("Jira-kanban".equals(metric1.getToolName())) {
		almType = "JIRA-KANBAN";
	    }
	    if ("Smart Test Defect".equals(metric1.getToolName())) {
		defectType = "Smart Test Defect";
	    }
	    if ("Jira Defects".equals(metric1.getToolName()) || "Jira".equals(metric1.getToolName())) {
		defectType = "JIRA";
	    }
	    if ("TFS".equals(metric1.getToolName())) {
		almType = "TFS";

	    }
	    if ("TFS".equals(metric1.getToolName())) {
		scmType = "TFS";
	    }
	    if ("TFS Defects".equals(metric1.getToolName())) {
		defectType = "TFS";
	    }

	}

	return almType;

    }

    public void getBuildInfo(long startDate, long endDate) {
	long buildTimeStamp = 0;
	String buildResult = "";
	int failureCount = 0;
	double successCount = 0;
	double totalCount = 0;
	Set<BuildTool> buildToolSet = buildRepo.findByBuildTypeAndNameAndTimestampBetween(buildType, name, startDate,
		endDate);
	Iterator<BuildTool> buildToolIterator = buildToolSet.iterator();
	while (buildToolIterator.hasNext()) {
	    BuildTool buildTool = buildToolIterator.next();
	    Set<BuildToolMetric> metricSet = buildTool.getMetrics();
	    Iterator<BuildToolMetric> iterator = metricSet.iterator();
	    while (iterator.hasNext()) {
		BuildToolMetric metric = iterator.next();
		if (metric.getName().equals("timestamp")) {
		    buildTimeStamp = Long.parseLong(metric.getValue().toString());
		}
		if (metric.getName().equals("result")) {
		    buildResult = metric.getValue().toString();
		}

		if (startDate < buildTimeStamp && buildTimeStamp < endDate)
		    if (buildResult.equals(FAIL_STATE))
			failureCount++;
		    else
			successCount++;
	    }

	}
	totalCount = failureCount + successCount;
	metrics.setBuildFailure(failureCount);
	if (totalCount != 0)
	    metrics.setSuccessRate((successCount / totalCount) * 100);
    }

    public void getCodeQualityInfo(long startDate, long endDate) {
	Iterable<CodeQuality> codeQaulityIterator = codeQualityRepo.findByNameAndTimestampBetween(name, startDate,
		endDate);
	// codeQaulityIterator
	Collection<CodeQuality> list = new ArrayList<CodeQuality>();
	for (CodeQuality item : codeQaulityIterator) {
	    list.add(item);
	}
	if (!list.isEmpty()) {
	    CodeQuality codeQuality = ((ArrayList<CodeQuality>) list).get(list.size() - 1);
	    Set<CodeQualityMetric> metricsSet = codeQuality.getMetrics();
	    Iterator<CodeQualityMetric> iterator = metricsSet.iterator();
	    while (iterator.hasNext()) {
		CodeQualityMetric codeQualityMetric = iterator.next();
		if (codeQualityMetric.getName().equals(CQ_TD)) {
		    technicalDebt = codeQualityMetric.getFormattedValue();
		    break;
		}
	    }
	}
	metrics.setTechnicalDebt(technicalDebt);
    }

    public void getCapacity(Long startDate, Long endDate, IterationOutModel currentIteration) {

	List<MonogOutMetrics> metricList = currentIteration.getMetrics();
	Iterator<MonogOutMetrics> almtoolMetricIterator = metricList.iterator();
	while (almtoolMetricIterator.hasNext()) {
	    MonogOutMetrics metric = almtoolMetricIterator.next();
	    if ((metric.getType().equals(ALM_STORY) || metric.getType().equals(ALM_PRODUCT_BACKLOG))
		    && (!(null == metric.getStoryPoints()))) {
		totalStoryPoints = totalStoryPoints + (double) metric.getStoryPoints().values().toArray()[0];
	    }

	    if (metric.getType().equals(ALM_TASK) || metric.getType().equals(ALM_SUBTASK)) {
		assignedToSet.add(metric.getAssgnTo());
	    }

	    if ((metric.getType().equals(ALM_STORY) || metric.getType().equals(ALM_PRODUCT_BACKLOG))
		    && metric.getState().equals(DONE_STATE) && (!(null == metric.getStoryPoints()))) {
		storyPoints = storyPoints + (double) metric.getStoryPoints().values().toArray()[0];
	    }
	}
	if (storyPoints == 0 && totalStoryPoints == 0) {
	    metrics.setEfficiency(0);
	} else {
	    efficiency = (storyPoints / totalStoryPoints) * 100;
	    metrics.setEfficiency(efficiency);
	}

	int capacity = (int) currentIteration.getTeamSize();
	double daysBetween = new ProjectHealthSprintProgress().getWorkingDaysBetweenTwoDates(new Date(startDate),
		new Date(endDate));
	metrics.setTeamCapacity(capacity * 6.5 * daysBetween);
	metrics.setDefects((int) currentIteration.getTotDefects());

    }

    public void cleanObject() {

	repo = null;
	testmgmtRepo = null;
	defectRepo = null;
	scmRepo = null;
	buildRepo = null;
	codeQualityRepo = null;

    }
}