package com.bolt.dashboard.svn;

import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.SCMToolRepository;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class SVNApplication {

	private static final Logger LOGGER = LogManager.getLogger(SVNApplication.class);
	AnnotationConfigApplicationContext ctx = null;
	SCMToolRepository repo = null;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configurationColection = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	ConfigurationToolInfoMetric metric1 = null;
	
	/**
	 * Public Constructor
	 */
	public SVNApplication() {

	}


	public void svnMain(String projectName) {
		LOGGER.info("SVN Collector started for " + projectName);
		ctx = DataConfig.getContext();
		repo = ctx.getBean(SCMToolRepository.class);
		String instanceURL = "";
		String username = "";
		String password = null;
		String result = "SUCCESS";
		String type = "SVN";


		SVNClientImplementation scmToolMetricsimpl = new SVNClientImplementation();

		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configurationColection = configurationRepo.findByProjectName(projectName).get(0);

		metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOGGER.info("Tool name  " + metric1.getToolName());
			if ("SVN".equals(metric1.getToolName())) {
				LOGGER.info("URL  " + metric1.getUrl());
				instanceURL = metric1.getUrl();
				username = metric1.getUserName();
				
					password=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				break;
			}

		}

		try {

			List<SCMTool> scmTool = scmToolMetricsimpl.getCommits(instanceURL, repo, username, password, projectName);
			if (!scmTool.isEmpty()) {
				repo.save(scmTool);
				cleanObject();
			} else {
				cleanObject();
				LOGGER.info("No commits to be stored...............");
			}
			ConstantVariable.getLastRun(projectName, type, new Date().getTime(), result);
		}  catch (Exception e) {
			result = "FAIL";
			ConstantVariable.getLastRun(projectName, type, new Date().getTime(), result);
			LOGGER.error(e.getMessage());
			LOGGER.info(e.getStackTrace());
			LOGGER.info(e);
			LOGGER.info("SVN Collector failed for " + projectName);
		}
		LOGGER.info("SVN Collector ended for " + projectName);
	}

	public void cleanObject() {
		repo = null;
		configurationRepo = null;
		configurationColection = null;
		metric = null;
		metric1 = null;

	}
}
