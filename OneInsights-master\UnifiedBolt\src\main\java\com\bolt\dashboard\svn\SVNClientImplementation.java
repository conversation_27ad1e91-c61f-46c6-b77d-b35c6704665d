package com.bolt.dashboard.svn;

import java.sql.Date;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.tmatesoft.svn.core.SVNException;
import org.tmatesoft.svn.core.SVNLogEntry;
import org.tmatesoft.svn.core.SVNLogEntryPath;
import org.tmatesoft.svn.core.SVNURL;
import org.tmatesoft.svn.core.auth.ISVNAuthenticationManager;
import org.tmatesoft.svn.core.internal.io.dav.DAVRepositoryFactory;
import org.tmatesoft.svn.core.io.SVNRepository;
import org.tmatesoft.svn.core.io.SVNRepositoryFactory;
import org.tmatesoft.svn.core.wc.SVNWCUtil;

import com.bolt.dashboard.core.model.DeletedFileDetails;
import com.bolt.dashboard.core.model.FileDetails;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.SCMToolRepository;

public class SVNClientImplementation implements SVNClient {
	private static final Logger LOGGER = LogManager.getLogger(SVNClientImplementation.class);

	private String userName;
	private String passWord;
	private long startRevision = 0;
	String projectName = null;
	SCMToolRepository scmToolRepository = null;
	String url = null;
	List<SCMTool> scmtool = new ArrayList<>();

	public SVNClientImplementation() {
		DAVRepositoryFactory.setup();
	}

	@SuppressWarnings("rawtypes")
	public List<SCMTool> getCommits(String svnURL, SCMToolRepository repo, String userName, String password,
			String projectName) throws SVNExceptions {
		this.userName = userName;
		this.passWord = password;
		this.projectName = projectName;
		this.scmToolRepository = repo;
		this.url = svnURL;

		long lastCommitTs = getLastCommitTS();
		Collection logEntry = getHistory(svnURL, startRevision);
		for (Iterator entries = logEntry.iterator(); entries.hasNext();) {

			SVNLogEntry svnEntry = (SVNLogEntry) entries.next();

			if (lastCommitTs < svnEntry.getDate().getTime()) {
				getSVNCommitInfo(svnEntry);
			}

		}
		return scmtool;

	}

	private FileDetails collectFileDetails(FileDetails fileDetails, String fileName, SCMTool commit) {
		String path = getFilePath(fileName);
		if (!(path == null)) {
			fileDetails.setFilename(path);
		}
		return fileDetails;

	}

	public long getRevisionClosestTo(String url, Date revisionDate) throws SVNExceptions {
		try {
			return getSvnRepository(url).getDatedRevision(revisionDate);
		} catch (SVNException svne) {
			LOGGER.error("Subversion repo: " + svne.getMessage());
			throw new SVNExceptions(svne);
		}
	}

	@SuppressWarnings("unchecked")
	private Collection<SVNLogEntry> getHistory(String url, long startRevision) {
		long endRevision = -1; // HEAD (the latest) revision

		try {
			LOGGER.info("startRevision   " + startRevision);

			return getSvnRepository(url).log(new String[] { "" }, null, startRevision, endRevision, true, true);
		} catch (SVNException svne) {
			LOGGER.error("Subversion repo: " + svne.getMessage());
			LOGGER.info(svne.getErrorMessage(), svne);
			return Collections.emptySet();
		}
	}

	private SVNRepository getSvnRepository(String url) throws SVNException {
		SVNRepository repository = SVNRepositoryFactory.create(SVNURL.parseURIEncoded(url));
		ISVNAuthenticationManager authManager = SVNWCUtil.createDefaultAuthenticationManager(userName, passWord);
		repository.setAuthenticationManager(authManager);
		return repository;
	}

	public String getFilePath(String filePath) {
		String path = null;
		String yPath[] = null;
		String xPath[] = null;
		if (filePath.contains("/")) {
			xPath = filePath.split(Pattern.quote("/"));
			String zPath = xPath[xPath.length - 1];
			if (xPath[xPath.length - 1].contains(".")) {

				yPath = xPath[xPath.length - 1].split(Pattern.quote("."));

				if (!(yPath == null) && !(yPath[0].equals("")) && !(yPath[yPath.length - 1].equals(""))) {
					boolean fileStatus = testExtension(yPath[yPath.length - 1]);
					if (fileStatus) {
						path = xPath[xPath.length - 1];
					}
					return path;
				}

			}

		}
		return path;

	}

	public long getLastCommitTS() {
		long timeStamp = 0;
		List<SCMTool> scmToolList = scmToolRepository.findByScTypeAndProjectName("SVN", projectName);
		if (!scmToolList.isEmpty()) {
			timeStamp = scmToolList.get(scmToolList.size() - 1).getCommitTS();
		}
		return timeStamp;

	}

	public void getSVNCommitInfo(SVNLogEntry svnEntry) {
		int totalAdditions = 0;
		int totalDeletions = 0;
		int totalModifications = 0;
		SCMTool commit = new SCMTool();
		commit.setScType("SVN");
		commit.setTimestamp(System.currentTimeMillis());
		commit.setUrl(url);
		commit.setRevisionNo(String.valueOf(svnEntry.getRevision()));
		commit.setCommiter(svnEntry.getAuthor());
		commit.setCommitLog(svnEntry.getMessage());
		commit.setCommitTS(svnEntry.getDate().getTime());
		commit.setProjectName(projectName);
		List<DeletedFileDetails> deletedFileDetailsList = new ArrayList<DeletedFileDetails>();
		List<FileDetails> fileDetails = new ArrayList<FileDetails>();
		LOGGER.info("Get changed path   " + svnEntry.getChangedPaths().isEmpty());
		if (!svnEntry.getChangedPaths().isEmpty()) {
			Set changedPathsSet = svnEntry.getChangedPaths().keySet();
			for (Iterator changedPaths = changedPathsSet.iterator(); changedPaths.hasNext();) {
				DeletedFileDetails deletedFileInfo = new DeletedFileDetails();
				FileDetails fileDetailsData = new FileDetails();
				SVNLogEntryPath entryPath = (SVNLogEntryPath) svnEntry.getChangedPaths().get(changedPaths.next());
				char type = entryPath.getType();
				String filePath = entryPath.getPath();
				String typeString = Character.toString(type);
				if ("D".equalsIgnoreCase(typeString)) {
					totalDeletions = totalDeletions + 1;
					String path = getFilePath(filePath);
					if (!(path == null)) {
						deletedFileInfo.setFileName(path);
						deletedFileInfo.setDeletedDateTime(commit.getCommitTS());
						deletedFileInfo.setCommitter(commit.getCommiter());
						deletedFileDetailsList.add(deletedFileInfo);
					}

				}
				if ("A".equalsIgnoreCase(typeString)) {
					totalAdditions = totalAdditions + 1;

				}
				if ("M".equalsIgnoreCase(typeString)) {
					totalModifications = totalModifications + 1;

				}
				fileDetails.add(collectFileDetails(fileDetailsData, filePath, commit));

			}
		}
		LOGGER.info("Total Additions   " + totalAdditions);
		LOGGER.info("Total Modifications per Revision " + totalModifications);
		LOGGER.info("Total Deletions per Revision " + totalDeletions);
		LOGGER.info("Total Changes per Revision " + (totalAdditions + totalDeletions + totalModifications));
		commit.setAddition(totalAdditions);
		commit.setDeletion(totalDeletions);
		commit.setModification(totalModifications);
		commit.setNoOfChanges(totalAdditions + totalDeletions + totalModifications);
		commit.setFileDetails(fileDetails);
		if (totalDeletions > 0) {
			commit.setDeletedFileDetails(deletedFileDetailsList);
		}

		scmtool.add(commit);
	}

	public boolean testExtension(String extension) {
		boolean status = true;
		for (char c : extension.toCharArray()) {
			if (Character.isDigit(c)) {
				status = false;
			}
		}
		return status;

	}
}