package com.bolt.dashboard.teamcity;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.ResponseEntity;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class TeamcityApplication {
	private static final Logger LOGGER = LogManager.getLogger(TeamcityApplication.class);
	

	/**
	 * Private Constructor
	 */
	public TeamcityApplication() {

	}

	public void teamCityMain(String projectName) {
		LOGGER.info("Team City Collector started for " + projectName);
		AnnotationConfigApplicationContext ctx = DataConfig.getContext();
		BuildToolRep repo = ctx.getBean(BuildToolRep.class);
		String instanceURL = "";
		String username = "";
		String password = null;
		String projectCode = "";
		Collection<BuildTool> buildToolSet = null;
		int buildCount = 0;
		TeamcityClientImplementation buildToolMetrics = new TeamcityClientImplementation();
		ConfigurationSettingRep configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		ConfigurationSetting configurationColection = configurationRepo.findByProjectName(projectName).get(0);

		Set<ConfigurationToolInfoMetric> metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOGGER.info("Tool name  " + metric1.getToolName());
			if ("TeamCity".equals(metric1.getToolName())) {
				LOGGER.info("URL  " + metric1.getUrl());
				instanceURL = metric1.getUrl();
				username = metric1.getUserName();
				
					password=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				projectCode = metric1.getProjectCode();

				break;
			}

		}
		try {

			instanceURL = instanceURL + "/httpAuth/app/rest/projects/";

			ResponseEntity<String> idResponse = buildToolMetrics.makeRestCall(instanceURL, username, password);
			String idResponseBody = idResponse.getBody().toString();
			JSONObject jsonIdObject = new JSONObject(idResponseBody);
			String projectId = getProjectId(jsonIdObject, projectCode);
			instanceURL = instanceURL + projectId;
			String buildTypeUrl = instanceURL + "/buildTypes/";
			ResponseEntity<String> buildResponse = buildToolMetrics.makeRestCall(buildTypeUrl, username, password);
			String buildResponseBody = buildResponse.getBody().toString();
			JSONObject buildJson = new JSONObject(buildResponseBody);
			List<String> buildsArray = getBuildArray(buildJson);

			Iterator<String> bIterator = buildsArray.iterator();
			while (bIterator.hasNext()) {
				String jobName = bIterator.next().toString();
				String newUrl = buildTypeUrl + jobName + "/builds/";

				Set<BuildTool> collection = repo.findByNameAndJobName(projectName, jobName);
				List<BuildTool> list = new ArrayList<BuildTool>(collection);
				if (list != null && !list.isEmpty()) {
					BuildTool buildTool = list.get(list.size() - 1);
					buildCount = buildTool.getBuildID();
				}

				buildToolSet = buildToolMetrics.getBuildTool(newUrl, username, password, repo, projectName, buildCount);

			}
			List list = new ArrayList<BuildTool>(buildToolSet);
			Collections.sort(list);
			repo.save(list);

		} catch (Exception e) {

			LOGGER.error(e.getMessage());
			LOGGER.info(e.getStackTrace());
			LOGGER.info(e);
			LOGGER.info("Team City Collector failed for " + projectName);
		}
		LOGGER.info("Team City Collector ended for " + projectName);
	}

	public String getProjectId(JSONObject json, String projectCode) {

		String projectId = null;
		JSONArray projectsArray = json.getJSONArray("project");
		for (int i = 0; i < projectsArray.length(); i++) {
			JSONObject projectJson = (JSONObject) projectsArray.get(i);
			if (projectCode.equals(projectJson.getString("name"))) {
				projectId = projectJson.getString("id");
			}
		}
		return projectId;

	}

	public List getBuildArray(JSONObject json) {
		List<String> buildList = new ArrayList();
		JSONArray buildTypeJsonArray = json.getJSONArray("buildType");
		for (int i = 0; i < buildTypeJsonArray.length(); i++) {
			String buildName = ((JSONObject) buildTypeJsonArray.get(i)).getString("id");
			buildList.add(buildName);
		}

		return buildList;

	}
}
