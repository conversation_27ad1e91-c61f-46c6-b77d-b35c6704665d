package com.bolt.dashboard.teamcity;

import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.StringTokenizer;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.repository.BuildToolRep;

public class TeamcityClientImplementation implements TeamcityClient {
	private static final Logger LOGGER = LogManager.getLogger(TeamcityClientImplementation.class);

	int id1 = 0, k;
	ResponseEntity<String> response;
	BuildTool tool = null;
	String projectName = null;
	BuildToolRep repo = null;
	Set<BuildTool> toolSet = new HashSet<BuildTool>();

	public ResponseEntity<String> makeRestCall(String url, String userId, String password) {
		// Basic Auth only.
		if (!"".equals(userId) && !"".equals(password)) {
			return get().exchange(url, HttpMethod.GET, new HttpEntity<>(createHeaders(userId, password)), String.class);

		} else {
			return get().exchange(url, HttpMethod.GET, null, String.class);
		}

	}

	private static HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}

	private static RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000);
		requestFactory.setReadTimeout(20000);
		return new RestTemplate(requestFactory);
	}

	public Set<BuildTool> getBuildTool(String teamcityUrl, String user, String pass, BuildToolRep repo, String pName,
			int buildCount) throws TeamcityException {
		this.projectName = pName;
		this.repo = repo;
		JSONArray countArray = null;

		try {
			response = makeRestCall(teamcityUrl, user, pass);
			String responseBody = response.getBody();
			JSONObject jsonObj = new JSONObject(responseBody);
			int count = jsonObj.getInt("count");

			if (count > 0) {
				countArray = jsonObj.getJSONArray("build");
				for (int i = countArray.length() - 1; i >= 0; i--) {
					int id = countArray.getJSONObject(i).getInt("id");
					String buildUrl = teamcityUrl + "id:" + id;
					boolean updateResponse = callBuildUpdate(id, buildCount);
					if (updateResponse) {
						ResponseEntity<String> idResponse = makeRestCall(buildUrl, user, pass);
						String idResponseBody = idResponse.getBody().toString();
						JSONObject jsonIdObject = new JSONObject(idResponseBody);
						addDataToCollection(jsonIdObject);
					}
				}
			}

			return toolSet;
		} catch (Exception e) {
			throw new TeamcityException(e);
		}

	}

	@SuppressWarnings({})
	public int getNextChangeSetID(JSONObject json) {
		id1 = (int) json.get("id");
		return id1;

	}

	public boolean callBuildUpdate(int id, int buildCount) {
		if (id > buildCount) {
			return true;
		} else {
			return false;
		}

	}

	public long dateformat(String buildDate) {

		String finalDate = null;
		try {
			StringTokenizer stringtokens = new StringTokenizer(buildDate, "T,+");
			String firsttoken = "";
			String secondtoken = "";
			String lasttoken = "";

			firsttoken = (String) stringtokens.nextElement();
			secondtoken = (String) stringtokens.nextElement();
			if(stringtokens.hasMoreTokens()){
			lasttoken = (String) stringtokens.nextElement();
			}
			secondtoken = secondtoken.substring(0, 2).concat(":") + secondtoken.substring(2, 4).concat(":")
					+ secondtoken.substring(4, 6).concat(".") + lasttoken;
			SimpleDateFormat originalFormat = new SimpleDateFormat("yyyyMMdd");
			Date date = originalFormat.parse(firsttoken);
			finalDate = new SimpleDateFormat("yyyy-MM-dd").format(date).concat("T").concat(secondtoken);
		} catch (ParseException e) {

		}
		return ConstantVariable.timestamp(finalDate,projectName);

	}

	public void addDataToCollection(JSONObject jsonIdObject) {
		tool = new BuildTool();
		tool.setBuildID(jsonIdObject.getInt("id"));
		tool.setTimestamp(new Date().getTime());
		tool.setJobName(jsonIdObject.getString("buildTypeId"));
		Set<BuildToolMetric> metricSet = new HashSet<BuildToolMetric>();
		JSONObject idBuildAgentObject = (JSONObject) jsonIdObject.get("agent");
		String version = idBuildAgentObject.getString("name");
		String idUrl = jsonIdObject.getString("webUrl");
		tool.setUrl(idUrl);
		BuildToolMetric resultmetric = new BuildToolMetric("result");
		String result = jsonIdObject.getString("status");
		resultmetric.setValue(result);
		metricSet.add(resultmetric);
		tool.getMetrics().add(resultmetric);
		String buildStartDate = jsonIdObject.get("startDate").toString();
		BuildToolMetric startmetric = new BuildToolMetric("StartTime");
		long startdate = dateformat(buildStartDate);
		startmetric.setValue(startdate);
		metricSet.add(startmetric);
		tool.getMetrics().add(startmetric);
		String buildEndDate = jsonIdObject.get("finishDate").toString();
		BuildToolMetric finishmetric = new BuildToolMetric("FinishTime");
		long enddate = dateformat(buildEndDate);
		finishmetric.setValue(enddate);
		metricSet.add(finishmetric);
		tool.getMetrics().add(finishmetric);
		BuildToolMetric durationMetric = new BuildToolMetric("duration");
		long duration = enddate - startdate;
		durationMetric.setValue(duration);
		metricSet.add(durationMetric);
		tool.getMetrics().add(durationMetric);
		BuildToolMetric buildTimeStamp = new BuildToolMetric("queuedDate");
		String queueDate = jsonIdObject.get("queuedDate").toString();
		long buildDate = dateformat(queueDate);
		buildTimeStamp.setValue(buildDate);
		metricSet.add(buildTimeStamp);
		tool.getMetrics().add(buildTimeStamp);
		tool.setName(projectName);
		tool.setVersion(version);
		tool.setBuildType("TEAMCITY");
		toolSet.add(tool);
	}
}