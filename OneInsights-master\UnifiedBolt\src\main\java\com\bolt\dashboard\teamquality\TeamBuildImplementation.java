package com.bolt.dashboard.teamquality;

import java.util.List;
import java.util.Map;

import org.springframework.data.mongodb.core.MongoTemplate;

import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.TeamBuildMetrics;
import com.bolt.dashboard.core.model.TeamCommitMetrics;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.TeamQualityRepo;

public class TeamBuildImplementation {
	String pName;
	MongoTemplate mongoTemplate;
	TeamQualityRepo teamQualityRepo;
	MetricRepo metricRepo;
	List<IterationModel> sprints;
	long timestamp;
	TeamQualityApplication app=new TeamQualityApplication();
	Map<String,List<String>> repoTeamMapping;
	
	public List<TeamBuildMetrics> getTeamBuildData(String pName, MongoTemplate mongoTemplate,
			TeamQualityRepo teamQualityRepo, long timestamp, MetricRepo metricRepo, List<IterationModel> sprints) {
		
		this.pName = pName;
		this.mongoTemplate = mongoTemplate;
		this.teamQualityRepo=teamQualityRepo;
		this.timestamp=timestamp;
		this.metricRepo=metricRepo;
		this.sprints=sprints;
		return null;
		
	}
}
