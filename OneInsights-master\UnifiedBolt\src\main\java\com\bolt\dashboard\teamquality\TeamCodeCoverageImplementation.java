package com.bolt.dashboard.teamquality;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import com.bolt.dashboard.core.model.GoalMetric;
import com.bolt.dashboard.core.model.GoalSetting;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.ProjectCoverageDetails;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.model.TeamCodeCoverageMetrics;
import com.bolt.dashboard.core.model.TeamMember;
import com.bolt.dashboard.core.model.TeamQuality;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.ProjectCoverageDetailsRepo;
import com.bolt.dashboard.core.repository.TeamQualityRepo;
import com.bolt.dashboard.util.TeamQualityUtils;

public class TeamCodeCoverageImplementation {

	private ProjectCoverageDetailsRepo projectCoverageDetailsRepo;
	MongoTemplate mongoTemplate = null;
	TeamQualityRepo teamQualityRepo = null;
	String pName = null;
	private static final Logger LOGGER = LogManager.getLogger(TeamCodeCoverageImplementation.class);
	List<IterationModel> sprints;
	long timestamp;
	GoalMetric coverageGoalMetric;
	TeamQualityUtils teamQualityUtils = new TeamQualityUtils();
	Map<String, List<ProjectCoverageDetails>> coveredRepos;
	MetricRepo metricRepo;
	List<TeamQuality> calulatedSPrints=new ArrayList<TeamQuality>();
	TeamQualityApplication app;
	private String environment;
	
	public List<TeamCodeCoverageMetrics> getTeamCoveragerData(String pName, MongoTemplate mongoTemplate,
			TeamQualityRepo teamQualityRepo, long timestamp, MetricRepo metricRepo, List<IterationModel> sprints, String environment ) {

		this.pName = pName;
		this.mongoTemplate = mongoTemplate;
		this.teamQualityRepo = teamQualityRepo;
		this.timestamp = timestamp;
		this.metricRepo = metricRepo;
		this.sprints = sprints;
		this.environment = environment;
		app=new TeamQualityApplication(mongoTemplate,metricRepo,this.pName);
		
		coverageGoalMetric = new GoalMetric("coverage");
		coverageGoalMetric.setDisplayName("coverage");
		coverageGoalMetric.setGoal("80");
		coverageGoalMetric.setSelected("true");
		coverageGoalMetric.setOperator(">");
		
		getCoverageGoalMetric();

		calculateCoverageSprintWise();
		addPastPerformance();
		saveResults();
		return null;
	}

	private void saveResults() {
		calulatedSPrints.stream().forEach(ele ->{
			teamQualityRepo.save(ele);
		});
		
	}

	private List<String> getCoveredRepos(long start, long end) {
		List<String> repos = new ArrayList<String>();
		Query q = new Query();
		q.addCriteria(Criteria.where("created_timestamp").gte(start).lt(end));
		coveredRepos = mongoTemplate.find(q, ProjectCoverageDetails.class, "ProjectCodeCoverage").stream()
				.collect(Collectors.groupingBy(ProjectCoverageDetails::getRepoName));
		for (Map.Entry<String, List<ProjectCoverageDetails>> entry : coveredRepos.entrySet()) {
			repos.add(entry.getKey());
		}

		return repos;
	}

	// get projectCovarage filteredby startTimeStamp and list of developers
	public List<ProjectCoverageDetails> getCoverageSummary(long start, long end, String brach, List<String> members) {
		Query query = new Query();
		query.addCriteria(Criteria.where("branchName").is(brach).and("started_timestamp").gte(start).lt(end)
				.and("commiter").in(members));
		List<ProjectCoverageDetails> coveageSummay = mongoTemplate.find(query, ProjectCoverageDetails.class,
				"ProjectCodeCoverage");
		return coveageSummay;
	}

	public List<TeamQuality> calculateCoverageSprintWise(String component, List<String> members) {
		List<TeamQuality> teamQuality = new ArrayList<TeamQuality>();
		Map sprintwiseCoverage = new HashMap();
		for (IterationModel sprint : sprints) {
			List<ProjectCoverageDetails> coveageSummay = getCoverageSummary(sprint.getStDate(),
					sprint.getCompletedDate(), environment, members);

			if (coveageSummay.size() > 0) {

				// group by repo
				Map<String, List<ProjectCoverageDetails>> repos = coveageSummay.stream()
						.collect(Collectors.groupingBy(ProjectCoverageDetails::getRepoName));

				// looping on repos to add up coverage of all repos
				double totalCoverage = 0;
				for (Map.Entry<String, List<ProjectCoverageDetails>> entry : repos.entrySet()) {

					// sortby started_timestamp
					List<ProjectCoverageDetails> coverage = entry.getValue().stream()
							.sorted(Comparator.comparingLong(ProjectCoverageDetails::getStarted_timestamp))
							.collect(Collectors.toList());

					totalCoverage = totalCoverage
							+ coverage.get(coverage.size() - 1).getCovered_statements_percentage();
				}
				double average = totalCoverage / repos.size();
				// points calculations
				double diff = teamQualityUtils.calclulateAbsDiff(average, Double.parseDouble(coverageGoalMetric.getGoal()),
						coverageGoalMetric.getOperator());
				double points = teamQualityUtils.calculatePoints(diff,
						Double.parseDouble(coverageGoalMetric.getGoal()));

				TeamQuality team = new TeamQuality();
				team.setComponentName(component);
				TeamCodeCoverageMetrics metrics = new TeamCodeCoverageMetrics();
				metrics.setTeamCodeCoveragePercentage(average);
				metrics.setTeamScores(points);
				team.getCoverageMetrics().add(metrics);
				team.setProjectName(pName);
				team.setSprintName(sprint.getsName());
				team.setTimeStamp(timestamp);
				team.setSprintStart(sprint.getStDate());
				team.setSprintEnd(sprint.getCompletedDate());
				calulatedSPrints.add(team);
				teamQualityRepo.save(team);
				teamQuality.add(team);
			}
		}
		return teamQuality;
		
	}

	public void calculateCoverageSprintWise() {
		for (IterationModel sprint : sprints) {
			if(sprint.getsName().contains("NP PI-3")) {
				LOGGER.info("debugging...!"+sprint.getsName());
			}
			// Step 1 : Getting only the Code Coverage Data available Repo between sprint Dates
			List<String> coveredRepoList = getCoveredRepos(sprint.getStDate(), sprint.getCompletedDate());
			
			// Step 2 : Map the Team to Repo
			Map<String, List<String>> repoToTeam = app.getRepoToTeamMapping(sprint.getStDate(), sprint.getCompletedDate(),
					coveredRepoList);

			for (Map.Entry<String, List<String>> entry : repoToTeam.entrySet()) {
				Map<String, Double> repos = new HashMap<String, Double>();
				double total = 0;
				for (String repoName : entry.getValue()) {
					Query q = new Query();
					q.addCriteria(Criteria.where("started_timestamp").gte(sprint.getStDate())
							.lt(sprint.getCompletedDate()).and("repoName").is(repoName).and("branchName").is(environment));
					List<ProjectCoverageDetails> coverageData = mongoTemplate.find(q, ProjectCoverageDetails.class,
							"ProjectCodeCoverage");
					if (coverageData != null && coverageData.size() > 0) {
						double cov = coverageData.get(coverageData.size() - 1).getCovered_statements_percentage();
						repos.put(repoName, cov);
						total = total +cov;				
					}

				}
				if(repos.size()>0) {
					double avg = total / repos.size();
					double diff = teamQualityUtils.calclulateAbsDiff(avg,
							Double.parseDouble(coverageGoalMetric.getGoal()), coverageGoalMetric.getOperator());
					double points = teamQualityUtils.calculatePoints(diff,
							Double.parseDouble(coverageGoalMetric.getGoal()));

					TeamQuality team = new TeamQuality();
					team.setComponentName(entry.getKey());
					TeamCodeCoverageMetrics metrics = new TeamCodeCoverageMetrics();
					metrics.setTeamCodeCoveragePercentage(avg);
					metrics.setTeamScores(points);
					metrics.setRepos(repos);
					team.getCoverageMetrics().add(metrics);
					team.setProjectName(pName);
					team.setSprintName(sprint.getsName());
					team.setTimeStamp(timestamp);
					team.setSprintStart(sprint.getStDate());
					team.setSprintEnd(sprint.getCompletedDate());
					team.setEnvironment(environment);
					calulatedSPrints.add(team);
					teamQualityRepo.save(team);
					
				}
				
			}

		}
		
	}

	
	public void calculateCovergeComponenetWise() {
		Query query = new Query();
		query.addCriteria(Criteria.where("projectName").is(pName));
		List<TeamMember> teamMember = mongoTemplate.find(query, TeamMember.class, "TeamMember");
		Map<String, List<TeamMember>> components = teamMember.stream()
				.collect(Collectors.groupingBy(TeamMember::getComponent));

		// group by component
		Map componentWiseCoverage = new HashMap();

		components.forEach((component, memberList) -> {

			// building List<string> of teamMembers
			List<String> members = new ArrayList<String>();
			for (TeamMember t : memberList) {
				members.add(t.getName());
			}

			List<TeamQuality> sprintwiseCoverage = calculateCoverageSprintWise(component, members);
			if (sprintwiseCoverage != null && sprintwiseCoverage.size() > 0) {

				teamQualityRepo.save(sprintwiseCoverage);
			}

		});

	}



	public void getCoverageGoalMetric() {
		Query query = new Query();
		query.addCriteria(Criteria.where("projectName").is(pName));
		List<GoalSetting> goalSettings = mongoTemplate.find(query, GoalSetting.class, "Goal");

		List<GoalSetting> codeQualityGoals = goalSettings.stream().filter(ele -> {
			return ele.getName().equalsIgnoreCase("CodeQuality");
		}).collect(Collectors.toList());

		if (codeQualityGoals != null && codeQualityGoals.size() > 0) {
			List<GoalMetric> coverage = codeQualityGoals.get(codeQualityGoals.size() - 1).getMetrics().stream()
					.filter(ele -> {
						return ele.getName().equalsIgnoreCase("coverage");
					}).collect(Collectors.toList());
			if (coverage != null && coverage.size() > 0) {
				coverageGoalMetric = coverage.get(0);
			}
		}
	}

	public void addPastPerformance() {
		List<TeamQuality> sorted = calulatedSPrints.stream().sorted(Comparator.comparingLong(TeamQuality::getSprintEnd).reversed()).collect(Collectors.toList());
		for(int i=0;i<sorted.size()-1;i++) {
			TeamQuality current = sorted.get(i);
			TeamQuality past = getPastSprintData(current);
			if(past != null) {
				double currentCoverage = current.getCoverageMetrics().iterator().next().getTeamCodeCoveragePercentage();
				double pastCoverage = past.getCoverageMetrics().iterator().next().getTeamCodeCoveragePercentage();
				double diff = teamQualityUtils.calclulateDiff(currentCoverage, pastCoverage, coverageGoalMetric.getOperator());
				double points = teamQualityUtils.calculateAbsPoints(diff, pastCoverage);
				double currentPoints = current.getCoverageMetrics().iterator().next().getTeamScores();
				current.getCoverageMetrics().iterator().next().setTeamScores(currentPoints+points);
			}
		}
	}

	private TeamQuality getPastSprintData(TeamQuality current) {
		Query query =new  Query();
		
		query.addCriteria(Criteria.where("componentName").is(current.getComponentName()).and("sprintEnd").lt(current.getSprintEnd()));
		query.with(new Sort(new Order(Direction.DESC, "sprintEnd")));
		query.limit(1);
		List<TeamQuality> temp =  mongoTemplate.find(query, TeamQuality.class,"TeamQuality");
		if(temp!=null && temp.size()>0) {
			return temp.get(0);
			
		}
		return null;
	}
	
}
