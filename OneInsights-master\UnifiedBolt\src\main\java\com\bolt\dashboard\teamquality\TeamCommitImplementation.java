package com.bolt.dashboard.teamquality;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import com.bolt.dashboard.core.model.GoalMetric;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.ProjectCoverageDetails;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.model.TeamCodeCoverageMetrics;
import com.bolt.dashboard.core.model.TeamCommitMetrics;
import com.bolt.dashboard.core.model.TeamMember;
import com.bolt.dashboard.core.model.TeamQuality;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.TeamQualityRepo;
import com.bolt.dashboard.util.TeamQualityUtils;

public class TeamCommitImplementation {

	String pName;
	MongoTemplate mongoTemplate;
	TeamQualityRepo teamQualityRepo;
	MetricRepo metricRepo;
	List<IterationModel> sprints;
	long timestamp;
	TeamQualityApplication app=new TeamQualityApplication();
	Map<String,List<String>> repoTeamMapping;
	
	public List<TeamCommitMetrics> getTeamCommitData(String pName, MongoTemplate mongoTemplate,
			TeamQualityRepo teamQualityRepo, long timestamp, MetricRepo metricRepo, List<IterationModel> sprints) {
		this.pName = pName;
		this.mongoTemplate=mongoTemplate;
		this.teamQualityRepo = teamQualityRepo;
		this.timestamp = timestamp;
		this.metricRepo = metricRepo;
		this.sprints = sprints;
		app=new TeamQualityApplication(mongoTemplate,metricRepo,this.pName);
		calculateCommitScore();
		return null;
	}
	
	public void calculateCommitScore() {
		sprints.stream().forEach(sprint -> {
			
			repoTeamMapping = app.getRepoToTeamMapping(sprint.getStDate(), sprint.getCompletedDate());
			
			for(Map.Entry<String, List<String>> entry:repoTeamMapping.entrySet()) {
				
				String componentName = entry.getKey();
				List<String> repos = entry.getValue();     
				TeamCommitMetrics commitMetrics = new TeamCommitMetrics();
				Map<String,List<SCMTool>> developerAndCommits = new HashMap<String, List<SCMTool>>();
				Map<String,Integer> developerAndCommitFreq = new HashMap<String,Integer>();
				
				
				for(String repo:repos) {
					
					List<SCMTool> commits = getComits(sprint.getStDate(),sprint.getCompletedDate(),repo);
					List<SCMTool> filteredCommits = commits.stream().filter(commit -> {
						return app.getIssueNumber(commit) > 0 && !commit.getCommitLog().toLowerCase().contains("merge");
					}).collect(Collectors.toList());
					
					Map<String,List<SCMTool>> grouped = filteredCommits.stream().collect(Collectors.groupingBy(commit-> commit.getCommiter().toLowerCase()));
					
					   
					for(Map.Entry<String, List<SCMTool>> developer:grouped.entrySet()) {
						String developerName = developer.getKey();
						developerName = developerName.replaceAll("[.]", " ");
						int commitCount = developer.getValue().size();
						
						if(developerAndCommits.containsKey(developerName)) {
							developerAndCommits.get(developerName).addAll(developer.getValue());
						}else {
							List<SCMTool> commitTimeStamps = new ArrayList<SCMTool>();
							commitTimeStamps.addAll( developer.getValue());
							developerAndCommits.put(developerName, commitTimeStamps);
						}
						
					}
					
					
					
				}
				
				for(Map.Entry<String, List<SCMTool>> devEntry:developerAndCommits.entrySet()) {
					String dName= devEntry.getKey();
					int commitedDays= getCommitFreq(devEntry.getValue());
					long difference = sprint.getCompletedDate()- sprint.getStDate();
					double sprintDays =  (difference / (1000*60*60*24));
					int commitFreq = (int) ((commitedDays/sprintDays)*100);
					developerAndCommitFreq.put(dName,commitFreq);
				}
				
				int avgCommitFreq = 0;
				for(Map.Entry<String, Integer> entry1:developerAndCommitFreq.entrySet()) {
					avgCommitFreq = avgCommitFreq+entry1.getValue();
				}
				avgCommitFreq = avgCommitFreq/developerAndCommitFreq.size();
				TeamQuality teamQuality=teamQualityRepo.findBySprintNameAndComponentName(sprint.getsName(), componentName);
				if(teamQuality !=null) {
					commitMetrics.setCommits(developerAndCommitFreq);
					commitMetrics.setTeamCommitFrequency(avgCommitFreq);
					teamQuality.getCommitMetrics().add(commitMetrics);
					teamQualityRepo.save(teamQuality);
				}
			}
			
		});
	}
	
	private int getCommitFreq(List<SCMTool> value) {
		
		
		Map<Integer,List<SCMTool>>grouped=value.stream().collect(Collectors.groupingBy(commit-> new Date(commit.getCommitTS()).getDay() ));
		
		return grouped.size();
	}

	List<SCMTool> getComits(long start,long end,String repoName){
		Query query = new Query();
		query.addCriteria(Criteria.where("commitTS").gte(start).lt(end).and("repoName").is(repoName));
		List<SCMTool> scmData = mongoTemplate.find(query, SCMTool.class, "SCM");
		return scmData;
	}
	
	
}
