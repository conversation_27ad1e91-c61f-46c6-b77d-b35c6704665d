package com.bolt.dashboard.teamquality;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;


import org.springframework.data.mongodb.core.MongoTemplate;

import com.atlassian.jira.rest.client.api.domain.Issue;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.ComponentVelocityList;
import com.bolt.dashboard.core.model.IssueList;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.TeamCodeCoverageMetrics;
import com.bolt.dashboard.core.model.TeamDefectsClosedMetrics;
import com.bolt.dashboard.core.model.TeamQuality;
import com.bolt.dashboard.core.model.VelocityList;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.TeamQualityRepo;

//commented while velocity code movement from move to migration

/*import com.bolt.dashboard.jira.ChartCalculations;

public class TeamDefectsClosedImplementation {
	
	String pName;
	MongoTemplate mongoTemplate;
	TeamQualityRepo teamQualityRepo;
	MetricRepo metricRepo;
	List<IterationModel> sprints;
	long timeStamp;
	ChartCalculations almChartCalculations;
	List<ComponentVelocityList> velcityChartData;
	ALMConfiguration almConfiguration;
	List<String> almCritical;
	List<String> almHigh;
	List<String> almMedium;
	List<String> almLow;

	public List<TeamCodeCoverageMetrics> getTeamDefectsClosedData(String pName, MongoTemplate mongoTemplate,
		TeamQualityRepo teamQualityRepo, long timestamp,List<IterationModel> sprints, ChartCalculations almChartCalculations,MetricRepo metricRepo) {
		this.pName = pName;
		this.mongoTemplate = mongoTemplate;
		this.teamQualityRepo = teamQualityRepo;
		this.metricRepo = metricRepo;
		this.timeStamp = timestamp;
		this.sprints = sprints;
		this.almChartCalculations = almChartCalculations;
		velcityChartData= this.almChartCalculations.getComponentVelocity(this.pName,true);
		
		Query query=new Query();
		query.addCriteria(Criteria.where("projectName").is(pName));
		
		almConfiguration = mongoTemplate.find(query, ALMConfiguration.class,"ALMConfiguration").get(0);
		almCritical = Arrays.asList(almConfiguration.getCriticalPriority());
		almHigh = Arrays.asList(almConfiguration.getHighPriority());
		almMedium = Arrays.asList(almConfiguration.getMedPriority());
		almLow = Arrays.asList(almConfiguration.getLowPriority());
		
		addDefectData();
		return null;
	}
	
	public void addDefectData() {
		Iterator components = velcityChartData.iterator();
		while(components.hasNext()) {
			ComponentVelocityList comp = (ComponentVelocityList) components.next();
			String componentName =  comp.getComponent();
			VelocityList componentVelocity = comp.getVelocityList();
			
			for(int i=0;i<componentVelocity.catagories.size();i++) {
				String sprintName = componentVelocity.catagories.get(i);
				TeamQuality teamQuality;
//				Query query = new Query();
//				query.addCriteria(Criteria.where("sprintName").is(sprintName).and("componentName").is(componentName));
//				
//				List<TeamQuality> teamQualityList = mongoTemplate.find(query,TeamQuality.class,"TeamQuality");
//				boolean found=false;
//				if(teamQualityList!=null && teamQualityList.size()>0) {
//					teamQuality = teamQualityList.get(0);
//					found = true;
//				}
				teamQuality = teamQualityRepo.findBySprintNameAndComponentName(sprintName, componentName);
				if(teamQuality !=null) {
					TeamDefectsClosedMetrics defectsData=new TeamDefectsClosedMetrics();
					int low=0, medium=0,high=0,critical=0;
					for(int j=0;j<componentVelocity.chartIssuesComp.get(i).size();j++) {
						IssueList issue = componentVelocity.chartIssuesComp.get(i).get(j);
						MetricsModel metric = metricRepo.findByWId(issue.getwId());
						if (metric.getType().equals(almConfiguration.getStoryName())) {

							if(almCritical.contains(metric.getPriority())) {
								critical++;
							} else if(almHigh.contains(metric.getPriority())) {
								high++;
							} else if(almMedium.contains(metric.getPriority())) {
								medium++;
							} else {
								low++;
							}

						}
					}
					int points = critical*10+high*8+medium*6+low*4;
					defectsData.setTeamScores(points);
					defectsData.setCritical(critical);
					defectsData.setHigh(high);
					defectsData.setMedium(medium);
					defectsData.setLow(low);
					teamQuality.getDefectsClosedMetrics().clear();
					teamQuality.getDefectsClosedMetrics().add(defectsData);
					teamQualityRepo.save(teamQuality);
				}
			}
			
		}
	}
}*/
