package com.bolt.dashboard.teamquality;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONTokener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import com.atlassian.jira.rest.client.api.RestClientException;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.PortfolioViewConfig;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.model.TeamQuality;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.IterationRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.PortfolioViewConfigRepo;
import com.bolt.dashboard.core.repository.ProjectCoverageDetailsRepo;
import com.bolt.dashboard.core.repository.TeamQualityRepo;

//commented while velocity code movement from move to migration

//import com.bolt.dashboard.jira.ChartCalculations;

public class TeamQualityApplication {

	private static final Logger LOGGER = LogManager.getLogger(TeamQualityApplication.class.getName());
	AnnotationConfigApplicationContext applicationContext = null;
	@Autowired
	PortfolioViewConfigRepo portfolioViewConfigRepo;
	AnnotationConfigApplicationContext ctx = null;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configuration = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	ConfigurationToolInfoMetric almTool;
	ConfigurationToolInfoMetric metric1 = null;
	private ProjectCoverageDetailsRepo projectCoverageDetailsRepo;
	MongoTemplate mongoTemplate = null;
	IterationRepo iterationRepo = null;
	TeamQualityRepo teamQualityRepo = null;
	MetricRepo metricRepo = null;
	//ChartCalculations almChartCalculations;
	long timestamp;
	List<IterationModel> sprints;
	PortfolioViewConfig pConfig= null;
	
	
	
	// public static void main(String[] args) {
	// 	new TeamQualityApplication().teamQualityMain("Network Personalization ART");
	// }
	
	public TeamQualityApplication() {
		
	}
	
	public TeamQualityApplication(MongoTemplate mongoTemplate,MetricRepo metricRepo,String projectName) {
		this.mongoTemplate = mongoTemplate;
		this.metricRepo = metricRepo;
		ctx = DataConfig.getContext();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		portfolioViewConfigRepo = ctx.getBean(PortfolioViewConfigRepo.class);
		configuration = configurationRepo.findByProjectName(projectName).get(0);
		metric = configuration.getMetrics();
		Iterator it = metric.iterator();
		while(it.hasNext()) {
			ConfigurationToolInfoMetric specific = (ConfigurationToolInfoMetric) it.next();
			if(specific.getToolName().equalsIgnoreCase("Jira") || specific.getToolName().equalsIgnoreCase("Azure Board") ) {
				almTool = specific;
			}
		}
	}


	public void teamQualityMain(String projectName) throws RestClientException {
		LOGGER.info("TeamQuality Collector started for " + projectName);
		ctx = DataConfig.getContext();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		portfolioViewConfigRepo = ctx.getBean(PortfolioViewConfigRepo.class);
		configuration = configurationRepo.findByProjectName(projectName).get(0);
		metric = configuration.getMetrics();
		metricRepo = ctx.getBean(MetricRepo.class);
		Iterator it = metric.iterator();
		while(it.hasNext()) {
			ConfigurationToolInfoMetric specific = (ConfigurationToolInfoMetric) it.next();
			if(specific.getToolName().equalsIgnoreCase("Jira") || specific.getToolName().equalsIgnoreCase("Azure Board") ) {
				almTool = specific;
			}
		}
		try {
			mongoTemplate = DataConfig.getInstance().mongoTemplate();
		} catch (Exception e) {
			LOGGER.info("Mongo Template exception");
		}
		projectCoverageDetailsRepo = ctx.getBean(ProjectCoverageDetailsRepo.class);
		iterationRepo = ctx.getBean(IterationRepo.class);
		teamQualityRepo = ctx.getBean(TeamQualityRepo.class);
	//	almChartCalculations = new ChartCalculations();
		timestamp =System.currentTimeMillis();
	//	teamQualityRepo.deleteByProjectName(projectName);
		pConfig = portfolioViewConfigRepo.findByProjectName(projectName);
		
		TeamCodeCoverageImplementation teamCodeCoverageImplementation = new TeamCodeCoverageImplementation();
	//	TeamDefectsClosedImplementation teamDefectsClosedImplementation= new TeamDefectsClosedImplementation();
		TeamCommitImplementation teamCommitImplementation=new TeamCommitImplementation();
		try {
			getDeltaSprints(projectName);
			teamCodeCoverageImplementation.getTeamCoveragerData(projectName,mongoTemplate,teamQualityRepo,timestamp,metricRepo,sprints, pConfig.getEnvironment());
	//		teamDefectsClosedImplementation.getTeamDefectsClosedData(projectName, mongoTemplate, teamQualityRepo, timestamp, sprints,almChartCalculations,metricRepo);
			teamCommitImplementation.getTeamCommitData(projectName, mongoTemplate, teamQualityRepo, timestamp, metricRepo, sprints);
			LOGGER.info("TemQuality Coverage Calculations Completed for"+projectName);
		}
		catch(Exception e) {
			LOGGER.info(e);
			LOGGER.info("Issue in TemQuality Implemantation");
		}
		cleanObject();
		LOGGER.info("TeamQuality Collector ended for " + projectName);
		System.out.println("TeamQuality Collector ended for " + projectName);
	}

	public void cleanObject() {
//		gitlabPipelineMetrics = null;
	}

	private JSONArray parseAsArray(ResponseEntity<String> response) {
		return (JSONArray) new JSONTokener(response.getBody()).nextValue();
	}


	private ResponseEntity<String> checkProject(String instanceURL, String projectCode, String password) {
		
		instanceURL = instanceURL + "/api/v4/projects/" + projectCode + "?private_token=" + password;
		LOGGER.info(instanceURL);
		ResponseEntity<String> response = makeRestCall(instanceURL);

		return response;
	}

	private ResponseEntity<String> makeRestCall(String url) {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000000);
		requestFactory.setReadTimeout(20000000);
		RestTemplate rest = new RestTemplate(requestFactory);

		try {
			ResponseEntity<String> response = rest.exchange(url, HttpMethod.GET, null, String.class);
			return response;
		} catch (HttpClientErrorException e) {
			LOGGER.info(e);
			LOGGER.error("Team Quality Error in Network call ", e.getCause());
			if (e.getMessage().contains("404"))
				return new ResponseEntity<String>(HttpStatus.NOT_FOUND);
			else if (e.getMessage().contains("401"))
				return new ResponseEntity<String>(HttpStatus.UNAUTHORIZED);
		} catch (Exception e) {
			// TODO: handle exception
			LOGGER.info(e);
			LOGGER.info(e.getMessage());
			return new ResponseEntity<String>(HttpStatus.NOT_FOUND);
		}
		return null;

	}
	
	public void getDeltaSprints(String pName) {
		List<TeamQuality> calculatedList = teamQualityRepo.findByProjectNameAndEnvironment(pName, pConfig.getEnvironment());
		List<String> calculatedSprints = new ArrayList<String>();
		if (calculatedList != null && calculatedList.size() > 0) {
			calculatedList.stream().forEach(ele -> {
				calculatedSprints.add(ele.getSprintName());
			});
		}

		Query query = new Query();
		List<String> closed = Arrays.asList("CLOSED", "closed", "CLOSE", "close");
		query.addCriteria(
				Criteria.where("pName").is(pName).and("state").in(closed).and("sName").nin(calculatedSprints));
		sprints = mongoTemplate.find(query, IterationModel.class, "Iterations");
		

	}
	
	
	
	
	Map<String, List<String>> getRepoToTeamMapping(long stDate, long completedDate,
			List<String> coveredRepoList) {
		Map<String, List<String>> repoToTeam = new HashMap<String, List<String>>();
		// Step 3 : Map the Team to Repo
		Query query = new Query();
		query.addCriteria(Criteria.where("commitTS").gte(stDate).lt(completedDate).and("repoName").in(coveredRepoList));
		query.with(new Sort(Sort.Direction.DESC, "commitTS"));
		List<SCMTool> scmData = mongoTemplate.find(query, SCMTool.class, "SCM");
		// step 4: filter from merge Commit
		List<SCMTool> filteredCommits = scmData.stream().filter(commit -> {
			return !commit.getCommitLog().toLowerCase().contains("merge");
		}).collect(Collectors.toList());
		Map<String, List<SCMTool>> scmgrouped = filteredCommits.stream().collect(Collectors.groupingBy(SCMTool::getRepoName));

		for (Map.Entry<String, List<SCMTool>> entry : scmgrouped.entrySet()) {
			String repoName = entry.getKey();
			int issueNum = getIssueNum(entry.getValue());
			String componentName;
			if (issueNum > 0) {
				MetricsModel temp = metricRepo.findByWId(almTool.getProjectCode()+"-" + issueNum);
				if (temp != null) {
					List<String> components = temp.getComponents();
					if (components != null && components.size() > 0) {
						componentName = components.get(0);
						if (!repoToTeam.containsKey(componentName)) {
							repoToTeam.put(componentName, new ArrayList<String>());
						}
						repoToTeam.get(componentName).add(repoName);
					}
				}

			}

		}

		return repoToTeam;
	}
	
	public Map<String, List<String>> getRepoToTeamMapping(Long stDate, long completedDate) {
		Map<String, List<String>> repoToTeam = new HashMap<String, List<String>>();
		Query query = new Query();
		query.addCriteria(Criteria.where("projectName").is("SP 1").and("commitTS").gte(stDate).lt(completedDate)).getSortObject();
		query.with(new Sort(Sort.Direction.DESC, "commitTS"));
		List<SCMTool> scmData = mongoTemplate.find(query, SCMTool.class, "SCM");
		List<SCMTool> filteredCommits = scmData.stream().filter(commit -> {
			return !commit.getCommitLog().toLowerCase().contains("merge") &&commit.getRepoName()!=null;
		}).collect(Collectors.toList());
		Map<String, List<SCMTool>> scmgrouped = filteredCommits.stream().collect(Collectors.groupingBy(SCMTool::getRepoName));

		for (Map.Entry<String, List<SCMTool>> entry : scmgrouped.entrySet()) {
			String repoName = entry.getKey();
			int issueNum = getIssueNum(entry.getValue());
			String componentName;
			if (issueNum > 0) {
				MetricsModel temp = metricRepo.findByWId(almTool.getProjectCode()+"-" + issueNum);
				if (temp != null) {
					List<String> components = temp.getComponents();
					if (components != null && components.size() > 0) {
						componentName = components.get(0);
						if (!repoToTeam.containsKey(componentName)) {
							repoToTeam.put(componentName, new ArrayList<String>());
						}
						repoToTeam.get(componentName).add(repoName);
					}
				}

			}

		}

		return repoToTeam;
	}

	public int getIssueNum(List<SCMTool> value) {
		List<SCMTool> Commits = value;
		int issueNum = 0;
		boolean found = false;
		int count = 0;
		while (!found && count <= value.size()-1) {
			SCMTool lastCommit = Commits.get(count);
			String log = lastCommit.getCommitLog();
			String projectCode = almTool.getProjectCode()+"-";
			int index = log.indexOf(projectCode);

			if (index > 0) {
				index = index + projectCode.length();
				int count1 = 0;
				String num = "";
				char temp = log.charAt(index);
				while (index<=log.length()-1 && log.charAt(index) >= '0' && log.charAt(index) <= '9') {
					num = num + log.charAt(index);
					index++;
				}
				if (!num.equals("")) {
					issueNum = Integer.parseInt(num);
				}

			}
			if (issueNum > 0) {
				found = true;
			} else {
				count++;
			}

		}
		return issueNum;
	}
	
	public int getIssueNumber(SCMTool commit) {
		int issueNum=0;
		
		String log = commit.getCommitLog();
		String projectCode = almTool.getProjectCode()+"-";
		int index = log.indexOf(projectCode);

		if (index > 0) {
			index = index + projectCode.length();
			int count1 = 0;
			String num = "";
			char temp = log.charAt(index);
			while (index<=log.length()-1 && log.charAt(index) >= '0' && log.charAt(index) <= '9') {
				num = num + log.charAt(index);
				index++;
			}
			if (!num.equals("")) {
				issueNum = Integer.parseInt(num);
			}

		}
		return issueNum;
	}
	
   
	

}
