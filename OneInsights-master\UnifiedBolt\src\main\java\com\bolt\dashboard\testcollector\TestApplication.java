package com.bolt.dashboard.testcollector;

import java.util.Iterator;
import java.util.List;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.TestTool;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.TestRepository;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class TestApplication {
	private static final Logger LOGGER = LogManager.getLogger(TestApplication.class);
	
	AnnotationConfigApplicationContext ctx = null;
	TestRepository repo = null;
	TestClientImplementation testClientImpl = null;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configurationColection = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	ConfigurationToolInfoMetric metric1 = null;

	public TestApplication() {
	}

	public void testCollectorMain(String projectName) {
		LOGGER.info("Junit Collector failed for " + projectName);
		ctx = DataConfig.getContext();
		repo = ctx.getBean(TestRepository.class);
		String instanceURL = "";
		String userName = "";
		String password = null;
		testClientImpl = new TestClientImplementation();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configurationColection = configurationRepo.findByProjectName(projectName).get(0);

		metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOGGER.info("Tool name  " + metric1.getToolName());
			if ("Junit".equals(metric1.getToolName())) {
				LOGGER.info("URL  " + metric1.getUrl());
				instanceURL = metric1.getUrl();
				userName = metric1.getUserName();
				
					password=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				break;
			}

		}
		String appendUrl = "/view/Dashboard/job/Automation_Testing/api/json";
		try {
			instanceURL = instanceURL + appendUrl;
			List<TestTool> testToolList = testClientImpl.getTestResultJSONObject(instanceURL, repo, userName, password,
					projectName);
			repo.save(testToolList);
			cleanObject();
		} catch (Exception e) {
			cleanObject();
			LOGGER.error(e.getMessage());
			LOGGER.info(e.getStackTrace());
			LOGGER.info(e);
			LOGGER.info("Junit Collector failed for " + projectName);

		}
		LOGGER.info("Junit Collector ended for " + projectName);
	}

	public void cleanObject() {

		repo = null;
		testClientImpl = null;
		configurationRepo = null;
		configurationColection = null;
		metric = null;
		metric1 = null;
	}
}
