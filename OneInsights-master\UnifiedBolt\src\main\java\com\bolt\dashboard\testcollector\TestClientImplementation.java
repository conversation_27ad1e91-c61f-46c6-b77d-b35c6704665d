package com.bolt.dashboard.testcollector;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Pattern;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.model.TestTool;
import com.bolt.dashboard.core.repository.TestRepository;

public class TestClientImplementation implements TestClient {
	private static final Logger LOGGER = LogManager.getLogger(TestClientImplementation.class);
    private RestOperations rest;
    private static final String URLFORMAT = "/api/json";
    String pName = "";

    public TestClientImplementation() {
        this.rest = get();
    }

    private RestOperations get() {
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout(20000);
        requestFactory.setReadTimeout(20000);
        return new RestTemplate(requestFactory);
    }

    private JSONObject parseAsArray(String url) throws RestClientException, org.json.simple.parser.ParseException {
        return (JSONObject) new JSONParser().parse(rest.getForObject(url, String.class));
    }

    @Override
    public List<TestTool> getTestResultJSONObject(String testURL, TestRepository repo, String username, String password,
            String projectName) throws TestExceptions {
        pName = projectName;
        List<TestTool> toolList = new ArrayList<>();

        String url = String.format(testURL);
        LOGGER.info("URL  " + url);
        JSONObject jsonObject;
        try {
            jsonObject = parseAsArray(url);
            if (!jsonObject.isEmpty()) {
                JSONObject testResultJSONObject = (JSONObject) new JSONParser().parse(jsonObject.toJSONString());

                int nextBuildNumber = getNextBuildNumber(testResultJSONObject, repo, url, toolList);
            }
        } catch (RestClientException | ParseException e) {
        	LOGGER.error(e.getMessage());
            throw new TestExceptions(e);
        }
        return toolList;

    }

    private int getNextBuildNumber(JSONObject object, TestRepository repo, String url, List<TestTool> toolList)
            throws TestExceptions {
        int nextBuildNumber = Integer.parseInt(object.get("nextBuildNumber").toString());
        List<TestTool> toolListn = new ArrayList<>();
        int countFromDB = (int) repo.count();
        LOGGER.info("Count from DB " + countFromDB);
        if (countFromDB >= (nextBuildNumber - 1)) {

        	LOGGER.info("No records to be stored    ");
        } else {
            for (++countFromDB; countFromDB <= nextBuildNumber - 1; countFromDB++) {
                String baseUrl = "";

                if (url.contains(URLFORMAT)) {
                    baseUrl = url.replaceAll(URLFORMAT, "") + "/" + countFromDB + URLFORMAT;
                    toolListn = getTestReport(baseUrl, repo, toolList);
                }
            }
        }
        return countFromDB;

    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    private List<TestTool> getTestReport(String url, TestRepository repo, List<TestTool> toolList)
            throws TestExceptions {
        TestTool testTool = new TestTool();

        testTool.setTimestamp(new Date().getTime());

        try {
            String testUrlurl = String.format(url);
            JSONObject object1 = parseAsArray(testUrlurl);

            JSONObject object = (JSONObject) new JSONParser().parse(object1.toJSONString());
            String projectName = object.get("fullDisplayName").toString();
            String[] projetNameArray = null;
            if (projectName.contains("#")) {
                projetNameArray = projectName.split(Pattern.quote("#"));
                projectName = projetNameArray[0];
                testTool.setName(pName);
            }

            String result = object.get("result").toString();
            testTool.setResult(result);
            String testngUrl = object.get("url").toString();
            testTool.setUrl(testngUrl);
            String duration = object.get("duration").toString();
            testTool.setDuration(Long.parseLong(duration));
            String estimatedDuration = object.get("estimatedDuration").toString();
            testTool.setEstimatedDuration(Long.parseLong(estimatedDuration));
            Object trstngTimestamp = object.get("timestamp");
            testTool.setTestTimestamp(Long.parseLong(trstngTimestamp.toString()));
            JSONObject obj = (JSONObject) object.get("changeSet");
            if (obj != null) {
                List<Object> pathsOutput = (ArrayList<Object>) obj.get("items");
                getPath(pathsOutput, testTool);
            }
            List<Object> objOutput = (ArrayList<Object>) object.get("actions");
            for (Object objOutputJson : objOutput) {

                Iterator<Entry<Object, Object>> keySetIterator = ((Map<Object, Object>) objOutputJson).entrySet()
                        .iterator();

                while (keySetIterator.hasNext()) {
                    Entry hm = (Entry) keySetIterator.next();

                    if ("failCount".equalsIgnoreCase(hm.getKey().toString()) && hm.getValue() != null) {
                        testTool.setFailCount(Integer.parseInt(hm.getValue().toString()));
                    }
                    if ("skipCount".equalsIgnoreCase(hm.getKey().toString()) && hm.getValue() != null) {
                        testTool.setSkipcount(Integer.parseInt(hm.getValue().toString()));
                    }
                    if ("totalCount".equalsIgnoreCase(hm.getKey().toString()) && hm.getValue() != null) {
                        testTool.setTotalcount(Integer.parseInt(hm.getValue().toString()));
                    }

                }

            }
            toolList.add(testTool);

        } catch (RestClientException e) {
        	LOGGER.error(e.getMessage());
            throw new TestExceptions(e);
        } catch (ParseException e) {
        	LOGGER.error(e.getMessage());
            throw new TestExceptions(e);
        }
        return toolList;
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    private void getPath(List<Object> pathsOutput, TestTool testTool) {
        for (Object objOutputJson : pathsOutput) {

            Iterator<Entry<Object, Object>> keySetIterator = ((Map<Object, Object>) objOutputJson).entrySet()
                    .iterator();

            while (keySetIterator.hasNext()) {
                Entry hm = (Entry) keySetIterator.next();

                if ("author".equalsIgnoreCase(hm.getKey().toString()) && hm.getValue() != null) {
                    JSONObject authorObject = (JSONObject) hm.getValue();
                    String authorName = authorObject.get("fullName").toString();
                    testTool.setAuthor(authorName);
                }
                if ("paths".equalsIgnoreCase(hm.getKey().toString()) && hm.getValue() != null) {
                    List<Object> objOutput = (ArrayList<Object>) hm.getValue();
                    getTestPath(objOutput, testTool);

                }

            }

        }
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    private void getTestPath(List<Object> objOutput, TestTool testTool) {
        for (Object objOutputJson : objOutput) {

            Iterator<Entry<Object, Object>> keySetIterator = ((Map<Object, Object>) objOutputJson).entrySet()
                    .iterator();

            while (keySetIterator.hasNext()) {
                Entry hm = (Entry) keySetIterator.next();
                if ("file".equalsIgnoreCase(hm.getKey().toString()) && hm.getValue() != null) {
                    testTool.setAffectedPaths(hm.getValue().toString());

                }
            }

        }
    }

}
