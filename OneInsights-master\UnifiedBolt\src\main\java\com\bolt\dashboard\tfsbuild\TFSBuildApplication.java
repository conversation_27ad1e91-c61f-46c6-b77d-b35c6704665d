package com.bolt.dashboard.tfsbuild;

import java.util.Date;
import java.util.Iterator;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class TFSBuildApplication {
	private static final Logger LOGGER = LogManager.getLogger(TFSBuildApplication.class);

	AnnotationConfigApplicationContext ctx = null;
	BuildToolRep repo = null;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configurationColection = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	ConfigurationToolInfoMetric metric1 = null;

//	public static void main(String[] args) {
//		new TFSBuildApplication().tfsbuildMain("BrillioOne");
//	}
	
	public TFSBuildApplication() {
	
	}

	@SuppressWarnings("resource")
	public void tfsbuildMain(String projectName) {
		LOGGER.info("TFS BUILD Collector started for " + projectName);
		String instanceURL = "";
		String username = "";
		String password = null;
		
		//It will be definition Id for each Pipeline
		String projectCode="";
		ctx = DataConfig.getContext();
		repo = ctx.getBean(BuildToolRep.class);
		TFSBuildClientImplementation client = new TFSBuildClientImplementation();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configurationColection = configurationRepo.findByProjectName(projectName).get(0);

		metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOGGER.info("Tool name  " + metric1.getToolName());
			if ("TFS Build".equals(metric1.getToolName())) {
				LOGGER.info("URL  " + metric1.getUrl());
				instanceURL = metric1.getUrl();
				username = metric1.getUserName();
				  projectCode=metric1.getProjectCode();
					password=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				break;
			}

		}
		String urls[]= instanceURL.split(",");
       String definitions[]=projectCode.split(",");
       
      
		try {
			 
			if(urls.length>0) {
				 for(int i=0;i<urls.length;i++) {
					 String url=urls[i].replace("?definitions=","/_apis/build/builds?definitions=" );
					 String definition= url.split("=")[1];
					 client.getBuildTool(url, repo, username, password, projectName, definition);
					 
				 }
				
			}else {
			
			 for(int i=0;i<definitions.length;i++) {
					String appendUrl = "/_apis/build/builds?definitions="+definitions[i];
			//instanceURL = instanceURL + appendUrl;
			client.getBuildTool(instanceURL+appendUrl, repo, username, password, projectName, definitions[i]);
			 }
			 }
			cleanObject();
			LOGGER.info("TFS BUILD Collector Successfull for " + projectName);
			ConstantVariable.getLastRun(projectName, "TFSBUILD", new Date().getTime(), ConstantVariable.COLLECTOR_STATUS_SUCCESS);
		} catch (Exception e) {
			
			//e.printStackTrace();
			cleanObject();
			LOGGER.error(e.getMessage());
			LOGGER.info(e.getStackTrace());
			LOGGER.info("TFS BUILD Collector failed for " + projectName);
			ConstantVariable.getLastRun(projectName, "TFSBUILD", new Date().getTime(), ConstantVariable.COLLECTOR_STATUS_FAILURE);
		}
		LOGGER.info("TFS BUILD Collector ended for " + projectName);

	}

	public void cleanObject() {
		repo = null;
		configurationRepo = null;
		configurationColection = null;
		metric = null;
		metric1 = null;

	}
}
