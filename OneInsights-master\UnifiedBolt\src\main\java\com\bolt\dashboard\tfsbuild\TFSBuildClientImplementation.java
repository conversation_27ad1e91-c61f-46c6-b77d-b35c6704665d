package com.bolt.dashboard.tfsbuild;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.BuildFailurePatternForProjectInJenkinsModel;
import com.bolt.dashboard.core.model.BuildFailurePatternMetrics;
import com.bolt.dashboard.core.model.BuildSteps;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.repository.BuildFailurePatternForProjectRepo;
import com.bolt.dashboard.core.repository.BuildToolRep;

public class TFSBuildClientImplementation implements TFSBuildClient {
	private static final Logger LOGGER = LogManager.getLogger(TFSBuildClientImplementation.class);
    long startTimming;
    long finishTimming;
    String pName = "";
    BuildTool tool = null;
    String toolName="TFS Build";
    BuildFailurePatternForProjectRepo failurePatternRepo = null;
    AnnotationConfigApplicationContext ctx = null;
    public TFSBuildClientImplementation() {
    }

    @SuppressWarnings({ "unchecked", "rawtypes", "unused" })
    @Override
    public Set<BuildTool> getBuildTool(String tfsURL, BuildToolRep repo, String userName, String pasword,
            String projectName, String definitionId) throws TFSBuildExceptions {
        pName = projectName;
        String buildURL = tfsURL;
        Set<BuildTool> toolSet = new HashSet<BuildTool>();

        Set<BuildToolMetric> metricSet = new HashSet<BuildToolMetric>();
        ResponseEntity<String> buildResponse=makeRestCall(tfsURL, userName, pasword);
        JSONObject jsonObject = parseAsObject(buildResponse);
        JSONArray objOutput = jsonObject.getJSONArray("value");
        JSONArray newJsonArray = new JSONArray();
        		for (int i = objOutput.length()-1; i>=0; i--) {
        		    newJsonArray.put(objOutput.get(i));
        		}
        
        List<BuildTool> lastBuild= repo.findByNameAndDefinitionId(projectName,definitionId);
       int firstIndex=newJsonArray.length();
       int lastIndex=0;
        if(lastBuild!=null) {
        	lastIndex=lastBuild.size();
        			}
        
        try {
        
                for (;firstIndex>lastIndex;firstIndex--) {
                	try {
                		JSONObject buildObj=newJsonArray.getJSONObject(firstIndex-1);
                    	tool= new BuildTool();
                    	tool.setBuildID(buildObj.getInt("id"));
                    	if(tool.getBuildID()==99) {
                    		System.out.println("Stop");
                    	}
                    	long duration=0;
                    	long finishTime=getTimeInMiliseconds(buildObj.getString("queueTime"));
                    	if(buildObj.has("startTime")) {
                    	long startTime=getTimeInMiliseconds(buildObj.getString("startTime"));
                    	 finishTime=getTimeInMiliseconds(buildObj.getString("finishTime"));
                    	 duration= (finishTime- startTime)/1000;
                    	}
                    	BuildToolMetric durationMetric = new BuildToolMetric("duration");
                            
                    	durationMetric.setValue(duration);
    					BuildToolMetric resultMetric = new BuildToolMetric("result");
    					String result="Not Started";
    					if(buildObj.has("result")) {
    					 result=buildObj.getString("result");
    					if(result.equalsIgnoreCase("SUCCEEDED")) {
                    		result="SUCCESSFUL";
                    	 }
    					}
    					
    					resultMetric.setValue(result.toUpperCase());
    					BuildToolMetric timestampMetric = new BuildToolMetric("timestamp");
    					
    					timestampMetric.setValue(finishTime);
                        tool.setTimestamp(finishTime);
    					tool.getMetrics().add(resultMetric);
    					tool.getMetrics().add(durationMetric);
    					tool.getMetrics().add(timestampMetric);
                    	tool.setBranchName(buildObj.getString("sourceBranch"));
                    	tool.setDefinitionId(definitionId);
                    	JSONObject definition=buildObj.getJSONObject("definition");
                    	tool.setRepoName(definition.getString("name"));
                    	JSONObject buildFor= buildObj.getJSONObject("requestedFor");
                    	
                    	tool.setCreatedBy(buildFor.getString("displayName"));
                    	
                    	JSONObject buildBy=buildObj.getJSONObject("requestedBy");
                    	String requestedBy= buildBy.getString("displayName");
                    	 tool.setTriggerType("PUSH");
                    	if(requestedBy.equals(tool.getCreatedBy())) {
                    		tool.setTriggerType("MANUAl");
                    	  }
                    
                    	tool.setName(projectName);
                    	tool.setBuildType(toolName);
                    	
                    	processSteps(buildObj.getJSONObject("_links"), userName, pasword);
                    	
                    	//Build Failure Reasons Pending
                    	patternMatcher(buildObj, userName, pasword, projectName);
                    	
                    	toolSet.add(tool);
	                }catch(Exception e) {
	    				LOGGER.info(e);
	    			}
                	
                }
        }catch (Exception e) {
			// TODO: handle exception
        	//e.printStackTrace();
        	LOGGER.info(e.getMessage());
        	 repo.save(toolSet);
        	
		}
        
//        for (Object validationResultJson : objOutput) {
//            BuildTool tool = new BuildTool();
//            Iterator<Entry<Object, Object>> keySetIterator = ((Map<Object, Object>) validationResultJson).entrySet()
//                    .iterator();
//            while (keySetIterator.hasNext()) {
//                Entry hm = (Entry) keySetIterator.next();
//                if ("id".equalsIgnoreCase(hm.getKey().toString())) {
//                    String buildID = hm.getValue().toString();
//                    String authURL = buildURL + "/" + buildID;
//                   // JSONObject buildUrlJSONObject = makeRestCall(authURL, userName, pasword);
//                    getDuration(authURL, userName, pasword, metricSet, tool);
//                    String version = (String) buildUrlJSONObject.get("sourceVersion");
//                    tool.setVersion(version);
//                    String buildUrl = (String) buildUrlJSONObject.get("url");
//                    tool.setUrl(buildUrl);
//                    tool.setBuildType("TFSBUILD");
//                    tool.setTimestamp(new Date().getTime());
//                }
//
//                if ("validationResults".equalsIgnoreCase(hm.getKey().toString())) {
//                    List<Object> validationList = (ArrayList<Object>) hm.getValue();
//                    for (Object resultJson : validationList) {
//                        Iterator<Entry<Object, Object>> iterator = ((Map<Object, Object>) resultJson).entrySet()
//                                .iterator();
//                        while (iterator.hasNext()) {
//
//                            Entry entry = (Entry) iterator.next();
//                            Object entrykey = entry.getKey();
//
//                            String entryKeyString = entrykey.toString();
//                            BuildToolMetric buildtoolMetricObject = new BuildToolMetric(entryKeyString);
//                        }
//                    }
//                }
//                if ("definition".equalsIgnoreCase(hm.getKey().toString())) {
//
//                    Map<Object, Object> validationOutput = (Map<Object, Object>) hm.getValue();
//
//                    Iterator<Entry<Object, Object>> iterator = validationOutput.entrySet().iterator();
//                    while (iterator.hasNext()) {
//
//                        Entry entry = (Entry) iterator.next();
//                        if ("project".equalsIgnoreCase(entry.getKey().toString())) {
//                            Map<Object, Object> mapObject = (Map<Object, Object>) entry.getValue();
//                            Iterator<Entry<Object, Object>> iteratorMap = mapObject.entrySet().iterator();
//                            while (iteratorMap.hasNext()) {
//                                Entry entryMap = (Entry) iteratorMap.next();
//                                if ("name".equalsIgnoreCase(entryMap.getKey().toString())) {
//                                    tool.setName(pName);
//                                }
//                            }
//
//                        }
//
//                    }
//
//                }
//            }
//            toolSet.add(tool);
//            repo.save(tool);
//        }
  
        
        repo.save(toolSet);
        return toolSet;
    }

    private void patternMatcher(JSONObject buildObj, String userName, String password, String projectName) {
//    	System.out.println(buildObj);
//    	JSONObject logDetails=buildObj.getJSONObject("logs");
    	ctx = DataConfig.getContext();
		failurePatternRepo = ctx.getBean(BuildFailurePatternForProjectRepo.class);
		List<BuildFailurePatternForProjectInJenkinsModel> failurePattern = failurePatternRepo
				.findByProjectName(projectName);
    	if(buildObj.has("result") && buildObj.getString("result").equalsIgnoreCase("failed") && failurePattern.size()>0) {
    		
    		JSONObject logDetails=buildObj.getJSONObject("logs");
    		System.out.println(logDetails.getString("url"));
    		ResponseEntity<String> logsResponse = makeRestCall(logDetails.getString("url"),userName,password);
    		JSONObject logsObject = parseAsObject(logsResponse);
    		JSONArray logsOutput = logsObject.getJSONArray("value");
    		
    		
    		BuildFailurePatternForProjectInJenkinsModel tempBuildFailure = failurePattern.get(0);
			List<BuildFailurePatternMetrics> failureMetrics = tempBuildFailure.getPatternMetrics();
			List<String> allLogs = new ArrayList<String>();
			for(int i=0;i<logsOutput.length();i++) {
    			
    			try {
    				JSONObject log=logsOutput.getJSONObject(i);
        			ResponseEntity<String> logResponse = makeRestCall(log.getString("url"),userName,password);
        			String failureLog = logResponse.getBody();
        			
        			allLogs.add(failureLog);
    			}catch(Exception e) {
    				LOGGER.info(e);
    			}

    		}
			for (BuildFailurePatternMetrics temp : failureMetrics) {
				
				for(String log:allLogs) {
	    			if(log.contains(temp.getPatternDefined())) {
						temp.setPatternCount(temp.getPatternCount()+1);
					}
	    		}
			}

			failurePatternRepo.save(tempBuildFailure);
    		
    	}
    	
    }
    
	private void processSteps(JSONObject linksObj, String userName, String pasword) {
		
		List<BuildSteps> stepData = new ArrayList<BuildSteps>();
		String timelineURL="";
		//tool.setStepsList(stepData);
		try {
		timelineURL= linksObj.getJSONObject("timeline").getString("href");
		
		  ResponseEntity<String> timelineResponse=makeRestCall(timelineURL, userName, pasword);
		  if(timelineResponse.getStatusCode()==HttpStatus.OK) {
	        JSONObject timelineObj = parseAsObject(timelineResponse);
     		JSONArray records= timelineObj.getJSONArray("records");
     		
     		for(int i=0;i<records.length();i++) {
     			JSONObject record= records.getJSONObject(i);
     			
     			if(record.getString("type").equals("Job")) {
     				if(!record.getString("name").equalsIgnoreCase("Finalize build")) {
     				BuildSteps stepObj = new BuildSteps();
     				stepObj.setStepName(record.getString("name"));
     				
     				long startTime=getTimeInMiliseconds(record.getString("startTime"));
                	long finishTime=getTimeInMiliseconds(record.getString("finishTime"));
                	long duration = (finishTime- startTime)/1000;
                	stepObj.setDuration(duration);
                	String result=record.getString("result").toUpperCase();
                	if(record.getString("result").equalsIgnoreCase("SUCCEEDED")) {
                		result="SUCCESSFUL";
                	}
                	stepObj.setResult(result.toUpperCase());
                    stepObj.setStartedTime(startTime);  
                    stepObj.setCompletedTime(finishTime);
                    stepData.add(stepObj);
     				}
     			}	
     		}
     		if(stepData.size()>0) {
     	   stepData= stepData.stream()
     		.sorted(Comparator.comparingLong(BuildSteps::getStartedTime))
            .collect(Collectors.toList());
     		tool.setStepsList(stepData);
     		}
		  }
		}catch(Exception ex) {
			
			LOGGER.info(ex);
		}
	}

	private long getTimeInMiliseconds(String temp_date) {
		
		String[] splitDate = temp_date.split("T");
		String[] temp = splitDate[1].split("\\.");
		temp = temp[0].split("Z");
		// temp=temp_date.split(".");
		String tempTime = splitDate[0] + " " + temp[0];

		DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
		DateTime createdDate = fmt.parseDateTime(tempTime);
		return createdDate.getMillis();
	}
    
    
//    private JSONObject makeRestCall(String url, String userId, String password) {
//        if (!"".equals(userId) && !"".equals(password)) {
//
//            com.sun.jersey.api.client.Client restClient = com.sun.jersey.api.client.Client.create();
//            String authString = userId + ":" + password;
//            String authStringEnc = new Base64().encode(authString.getBytes()).toString();
//            WebResource webResource = restClient.resource(url);
//            ClientResponse resp = webResource.type(MediaType.APPLICATION_OCTET_STREAM_VALUE)
//                    .accept(MediaType.APPLICATION_JSON_UTF8_VALUE).header("Authorization", "Basic " + authStringEnc)
//                    .get(ClientResponse.class);
//            if (resp.getStatus() != 200) {
//            	LOGGER.info("Unable to connect to the server");
//            }
//
//            return resp.getEntity(JSONObject.class);
//
//        }
//        return null;
//
//    }
    
    private ResponseEntity<String> makeRestCall(String url, String userId, String password) {
		// Basic Auth only.
		 if (!"".equals(userId) && !"".equals(password)) {
		 return get().exchange(url, HttpMethod.GET, new
		 HttpEntity<>(createHeaders(userId, password)), String.class);
		
		 } else {
		 return get().exchange(url, HttpMethod.GET, null, String.class);
		 }
		
	}

	private JSONObject parseAsObject(ResponseEntity<String> staticsticResponse) {
		
		return (JSONObject) new JSONTokener(staticsticResponse.getBody()).nextValue();
	}
    private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000000);
		requestFactory.setReadTimeout(20000000);
		return new RestTemplate(requestFactory);
	}
    
	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}
    private long timestamp(Object obj) {
        if (obj != null) {
            String string = obj.toString();
            DateTime dt = new DateTime(string, DateTimeZone.UTC);
            return dt.getMillis();
        }
        return 0;
    }

//    private void getDuration(String url, String userName, String password, Set<BuildToolMetric> metricSet,
//            BuildTool tool) throws TFSBuildExceptions {
//
//        String authUrl = url + "/Timeline";
//        JSONObject buildUrlJSONObject = makeRestCall(authUrl, userName, password);
//        Object lastChangedOn = buildUrlJSONObject.get("lastChangedOn");
//        long lastChange = timestamp(lastChangedOn);
//        BuildToolMetric changeMetric = new BuildToolMetric("timestamp");
//        if (!(buildUrlJSONObject.get("lastChangedOn") == null)) {
//            changeMetric.setValue(lastChange);
//            metricSet.add(changeMetric);
//            tool.getMetrics().add(changeMetric);
//        } else {
//            changeMetric.setValue(0);
//            metricSet.add(changeMetric);
//            tool.getMetrics().add(changeMetric);
//        }
//
//        @SuppressWarnings("unchecked")
//        List<Object> buildIdOutput = (ArrayList<Object>) buildUrlJSONObject.get("records");
//
//        for (Object resultJson : buildIdOutput) {
//            @SuppressWarnings("unchecked")
//            Iterator<Entry<Object, Object>> iterator = ((Map<Object, Object>) resultJson).entrySet().iterator();
//            while (iterator.hasNext()) {
//
//                @SuppressWarnings("rawtypes")
//                Entry entry = (Entry) iterator.next();
//                Object entrykey = entry.getKey();
//                String entryValueString = entrykey.toString();
//                BuildToolMetric buildToolMetricObject = new BuildToolMetric(entryValueString);
//                long duration;
//
//                if ("startTime".equalsIgnoreCase(entry.getKey().toString())) {
//                    if (entry.getValue() != null) {
//
//                        startTimming = timestamp(entry.getValue());
//                        buildToolMetricObject.setValue(startTimming);
//                        metricSet.add(buildToolMetricObject);
//                        tool.getMetrics().add(buildToolMetricObject);
//
//                    } else {
//                        buildToolMetricObject.setValue(0);
//                        metricSet.add(buildToolMetricObject);
//                        tool.getMetrics().add(buildToolMetricObject);
//                    }
//
//                }
//                if ("finishTime".equalsIgnoreCase(entry.getKey().toString())) {
//                    if (entry.getValue() != null) {
//
//                        finishTimming = timestamp(entry.getValue());
//                        buildToolMetricObject.setValue(finishTimming);
//                        metricSet.add(buildToolMetricObject);
//                        tool.getMetrics().add(buildToolMetricObject);
//                        BuildToolMetric durationMetric = new BuildToolMetric("duration");
//                        duration = (finishTimming - startTimming);
//                        durationMetric.setValue(duration);
//                        metricSet.add(durationMetric);
//                        tool.getMetrics().add(durationMetric);
//
//                    } else {
//                        long finishTime = 0;
//                        buildToolMetricObject.setValue(finishTime);
//                        metricSet.add(buildToolMetricObject);
//                        tool.getMetrics().add(buildToolMetricObject);
//                        BuildToolMetric durationMetric = new BuildToolMetric("duration");
//                        durationMetric.setValue(0);
//                        metricSet.add(durationMetric);
//                        tool.getMetrics().add(durationMetric);
//                    }
//
//                }
//
//                if ("result".equalsIgnoreCase(entry.getKey().toString())) {
//                    if (entry.getValue() == null) {
//                        buildToolMetricObject.setValue("");
//                        metricSet.add(buildToolMetricObject);
//                    } else {
//                        buildToolMetricObject.setValue(entry.getValue().toString());
//                        metricSet.add(buildToolMetricObject);
//                    }
//
//                    tool.getMetrics().add(buildToolMetricObject);
//
//                }
//            }
//
//        }
//    }

}
