package com.bolt.dashboard.tfscollector;

import java.util.Date;
import java.util.Iterator;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.jira.JIRAApplication;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class TFSApplication {
	private static final Logger LOGGER = LogManager.getLogger(TFSApplication.class);
	AnnotationConfigApplicationContext ctx = null;
	
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configurationColection = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	ConfigurationToolInfoMetric metric1 = null;

	public TFSApplication() {
	}

//	public static void main(String[] args) {
//	new TFSApplication().tfscollectorMain("Team A");
//}

	public void tfscollectorMain(String projectName) {
		
		String instanceURL = "";
		String username = "";
		String password = null;
		String projectCode = "";
		ctx = DataConfig.getContext();
		
		TFSClientImplementation client = new TFSClientImplementation();
		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configurationColection = configurationRepo.findByProjectName(projectName).get(0);
		
		metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			metric1 = (ConfigurationToolInfoMetric) configuration1;
		
			if ("Azure Board".equals(metric1.getToolName())) {
			
				instanceURL = metric1.getUrl();
				if (instanceURL.charAt(instanceURL.length() - 1) == '/') {
					instanceURL = instanceURL.substring(0, instanceURL.length() - 1);
				}
				username = metric1.getUserName();
				password =  EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				projectCode = metric1.getProjectCode();

				break;
			}

		}

		try {
			//    client.getTFSData(username, password,instanceURL, projectName, projectCode);
			client.getTfsData(username, password,instanceURL, projectCode, projectName);
			ConstantVariable.getLastRun(projectName, "Azure Board", new Date().getTime(), "SUCCESS");
			LOGGER.info("Azure Board Collector ended for " + projectName);
			cleanObject();
		} catch (Exception e) {
			
			LOGGER.info(e.getMessage());
			LOGGER.info("Azure Board Collector failed for " + projectName);
			ConstantVariable.getLastRun(projectName, "Azure Board", new Date().getTime(), "FAIL");
			LOGGER.info(e);
			cleanObject();
			
		}
	
	}

	public void cleanObject() {
		
		configurationRepo = null;
		configurationColection = null;
		metric = null;
		metric1 = null;
	}
}
