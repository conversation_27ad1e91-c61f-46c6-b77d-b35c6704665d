package com.bolt.dashboard.tfscollector;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.config.MongoAggregate;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.ChangeHistoryModel;
import com.bolt.dashboard.core.model.EffortHistoryModel;
import com.bolt.dashboard.core.model.IterationModel;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.ProjectModel;
import com.bolt.dashboard.core.model.TransitionModel;
import com.bolt.dashboard.core.model.defectCount;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.ChangeHisortyRepo;
import com.bolt.dashboard.core.repository.EffortHistoryRepo;
import com.bolt.dashboard.core.repository.IterationRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.ProjectIterationRepo;
import com.bolt.dashboard.core.repository.ProjectRepo;
import com.bolt.dashboard.core.repository.TransitionRepo;
import com.bolt.dashboard.jira.EffortAndChangeItemInfo;
import com.bolt.dashboard.jira.IterationInfo;
import com.bolt.dashboard.jira.MetricsInfo;
import com.bolt.dashboard.jira.SprintWiseCalculation;
import com.bolt.dashboard.jira.TransitionInfo;
import com.bolt.dashboard.util.AzureDevOpsUtils;
import com.mongodb.BasicDBObject;

public class TFSClientImplementation {
	private static final Logger LOGGER = LogManager.getLogger(TFSApplication.class);
	public static final String ITERATION = "System.IterationPath";
	public static final String ASSIGNEDTO = "System.AssignedTo";
	public static final String PRIORITY = "Microsoft.VSTS.Common.Priority";
	public static final String WORKITEMSTATE = "System.State";
	public static final String EFFORT = "Microsoft.VSTS.Scheduling.Effort";
	public static final String WORKITEMTYPE = "System.WorkItemType";
	public static final String SEVERITY = "Microsoft.VSTS.Common.Severity";
	public static final String DESCRIPTION = "System.Title";
	public static final String REMAININGWORK = "Microsoft.VSTS.Scheduling.RemainingWork";
	public static final String CREATEDDATE = "System.CreatedDate";
	public static final String CLOSEDDATE = "Microsoft.VSTS.Common.ClosedDate";
	public static final String CHANGEDDATE = "System.ChangedDate";
	public static final String STORYPOINTS = "Microsoft.VSTS.Scheduling.StoryPoints";
	public static final String ESTIMATE = "Microsoft.VSTS.Scheduling.OriginalEstimate";
	ProjectModel almProj = null;
	private List<MetricsModel> backlogIssues = new ArrayList<>();
	String projectId = null;
	// ALMRepository repository = null;
	String pName = "";
	// String projectName = null;
	long startDate = 0;
	long finishDate = 0;
	// String iteration = "";
	String oldIterationName = "";
	String url = "";
	String appendIterationUrl = "/_apis/work/teamsettings/iterations?api-version=v2.0-preview";
	String projectCode = "";
	Map<String, String> datesMap = new LinkedHashMap<>();
//	String sprintName = null;
	String attributeValue = null;
	AzureDevOpsUtils utils = new AzureDevOpsUtils();
	ChangeHisortyRepo changeHisortyRepo = null;
	List<ChangeHistoryModel> changeLogs = null;
	ALMConfigRepo almConfigRepo = null;
	ALMConfiguration almConfig = null;
	long creationTime;
	private AnnotationConfigApplicationContext ctx = null;
	EffortHistoryRepo effortHistoryRepo = null;
	EffortAndChangeItemInfo effortInfo = null;
	List<EffortHistoryModel> efforts = null;
	JSONObject fieldsOfJSONData = null;
	JSONObject historyjsonOutput = null;
	JSONArray historyArray = null;
	IterationInfo iterationInfo = null;
	IterationRepo iterationRepo = null;
	Set<IterationModel> iterationSet;
	MetricRepo metricRepo = null;
	MetricsModel metrics = null;
	MetricsInfo metricsInfo = null;
	Set<MetricsModel> metricsSet = new LinkedHashSet<>();
	ProjectIterationRepo authoreRepo = null;
	double origEst = 0;
	String projectName = "";
	ProjectRepo projectRepo = null;
	int sprintId;
	String sprintName = "";
	List<Integer> multipleSprints = new ArrayList<>();
	SprintWiseCalculation sprintWiseCalculation = null;
	Set<String> subTaskStateList = new LinkedHashSet<>();
	List<TransitionModel> taskDetailsList = null;
	long taskEffort;
	String taskFirstState = null;

	String taskLastState = null;
	List<String> taskList = null;
	Set<String> taskSet = null;
	TransitionInfo transitionInfo = null;
	TransitionRepo transitionRepo = null;

	public TFSClientImplementation() {

	}

	public RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000);
		requestFactory.setReadTimeout(20000);
		return new RestTemplate(requestFactory);
	}

//    public void getData( String iteration,  ALMRepository repo, String userName, String password,
//            Iteration toolMetrics
//                                  * , String iteratinUrl
//                                  ) throws TFSExceptions {
//        JSONObject justforCheck = null;
//        String newUrl = url + "/" + projectCode + "/" + appendIterationUrl;
//
//        justforCheck = makeRestCall(newUrl, userName, password);
//
//        ArrayList<Object> list = (ArrayList<Object>) justforCheck.get("value");
//        Iterator<Object> listIterator = list.iterator();
//        while (listIterator.hasNext()) {
//            Map<Object, Object> objOutput = (Map<Object, Object>) listIterator.next();
//            if (!objOutput.isEmpty()) {
//                Iterator<Entry<Object, Object>> keySetIterator = objOutput.entrySet().iterator();
//                getDatesArray(keySetIterator);
//
//            }
//        }
//
//    }

	public Map<String, String> getDatesArray(Iterator<Entry<Object, Object>> keySetIterator) {

		while (keySetIterator.hasNext()) {
			@SuppressWarnings("rawtypes")
			Entry hm = (Entry) keySetIterator.next();

			if (hm.getKey().toString().contains("name")) {
				sprintName = hm.getValue().toString();

			}

			if (hm.getValue().toString().contains("startDate") && hm.getValue().toString().contains("finishDate")) {
				attributeValue = hm.getValue().toString();
			}

		}
		datesMap.put(sprintName, attributeValue);
		return datesMap;
	}

	@SuppressWarnings("unused")
	private String str(JSONObject json, String key) {
		Object obj = json.get(key);
		return obj == null ? null : obj.toString();
	}

//    public JSONObject makeRestCall(String url, String userId, String password) {
//
//        if (!"".equals(userId) && !"".equals(password)) {
//
//            com.sun.jersey.api.client.Client restClient = com.sun.jersey.api.client.Client.create();
//            String authString = userId + ":" + password;
//            String authStringEnc = new Base64().encode(authString.getBytes()).toString();
//            WebResource webResource = restClient.resource(url);
//            ClientResponse resp = webResource.accept("application/json")
//                    .header("Authorization", "Basic " + authStringEnc).get(ClientResponse.class);
//            if (resp.getStatus() != 200) {
//              
//            }
//            return resp.getEntity(JSONObject.class);
//
//        }
//        return null;
//
//    }

//    public ALMProject getWorkItemDetails(String userName, String password, ALMRepository repo,
//            WorkItemCollection workItems, ALMProject toolList) throws TFSExceptions {
//
//        String currentIte = "";
//        Iteration almTool = new Iteration();
//        getData(repo, userName, password, almTool);
//        for (int i = 0; i < workItems.size(); i++) {
//            WorkItem workItem = workItems.getWorkItem(i);
//
//            FieldCollection collection = workItem.getFields();
//            toolList.setAlmType("TFS");
//            toolList.setProjectName(pName);
//
//            ALMToolMetric tool = new ALMToolMetric();
//            if (collection.getField(ITERATION).getValue() != null) {
//                String iterationPath = (String) collection.getField(ITERATION).getValue();
//
//                String[] separationString = iterationPath.split(Pattern.quote("\\"));
//                if (separationString.length == 2) {
//                    projectName = separationString[0];
//                    iteration = separationString[1];
//                    tool.setIteration(iteration);
//
//                } else {
//                    iteration = "Backlog";
//                    tool.setIteration(iteration);
//                }
//            }
//            if (!currentIte.equalsIgnoreCase(tool.getIteration())) {
//
//                if (currentIte != "") {
//                    almTool.setIterationName(currentIte);
//                    long[] dates = iterationDates(currentIte);
//                    almTool.setStartDate(dates[0]);
//                    almTool.setEndDate(dates[1]);
//                    toolList.getIteration().add(almTool);
//                }
//
//                almTool = new Iteration();
//                currentIte = tool.getIteration();
//
//            }
//            if (collection.contains(DESCRIPTION)) {
//                getDescription(collection, tool);
//            }
//            if (collection.contains(SEVERITY)) {
//                getSeverity(collection, tool);
//            }
//
//            if (collection.contains(PRIORITY)) {
//                getPriority(collection, tool);
//            }
//            if (collection.contains(CREATEDDATE)) {
//                getCreatedDate(collection, tool);
//            }
//            if (collection.contains(WORKITEMTYPE)) {
//                getWorkItemType(collection, tool);
//            }
//            if (collection.contains(REMAININGWORK)) {
//                getRemainingWork(collection, tool);
//            }
//            if (collection.contains(ASSIGNEDTO)) {
//                getAssignedTo(collection, tool);
//            }
//            if (collection.contains(WORKITEMSTATE)) {
//                getWorkItemState(collection, tool);
//            }
//            if (collection.contains(EFFORT)) {
//                getEffort(collection, tool);
//            }
//
//            if (Integer.toString(collection.getID()) != null) {
//                tool.setWorkItemID(Integer.toString(workItem.getID()).toString());
//            }
//            if (collection.contains(CREATEDDATE)) {
//                getCreatedDate(collection, tool);
//            }
//            if (collection.contains(CLOSEDDATE)) {
//                getClosedDate(collection, tool);
//            }
//
//            almTool.getMetrics().add(tool);
//            if (i == workItems.size() - 1) {
//
//                almTool.setIterationName(currentIte);
//                long[] dates = iterationDates(currentIte);
//                almTool.setStartDate(dates[0]);
//                almTool.setEndDate(dates[1]);
//                toolList.getIteration().add(almTool);
//            }
//        }
//        return toolList;
//    }

//    @Override
//    public ALMProject getTFSData(String userName, String password,
//            ALMRepository repo , String iterationUrl , String instanceUrl, String projectName, String projectId)
//            throws TFSExceptions {
//        this.projectCode = projectId;
//        pName = projectName;
//        this.url = instanceUrl;
//        String wiql = "select [System.Id],[System.AssignedTo], [Microsoft.VSTS.Common.Priority], [System.WorkItemType], [System.State], [System.Title],"
//                + "[System.IterationPath],[System.IterationId],[Microsoft.VSTS.Scheduling.Effort], [System.WorkItemType],[Microsoft.VSTS.Scheduling.RemainingWork],"
//                + "[System.CreatedDate],[Microsoft.VSTS.Scheduling.RemainingWork],[Microsoft.VSTS.Common.ClosedDate] from WorkItems  where [System.TeamProject]='"
//                + projectId + "' order by [System.IterationPath]";
//
//        ALMProject toolList = new ALMProject();
//        toolList.setTimestamp(new Date().getTime());
//
//        TFSTeamProjectCollection tpc = null;
//        java.net.URI url;
//        try {
//            Credentials credentials = new UsernamePasswordCredentials(userName, password);
//
//            url = new java.net.URI(instanceUrl);
//
//            tpc = new TFSTeamProjectCollection(url, credentials);
//            tpc.authenticate();
//            WorkItemClient workItemClient = tpc.getWorkItemClient();
//            WorkItemCollection workItems = workItemClient.query(wiql);
//            LOG.info("Found " + workItems.size() + " work items.");
//            toolList = getWorkItemDetails(userName, password,
//                    repo , iterationUrl , workItems, toolList);
//
//        } catch (URISyntaxException e) {
//            LOG.error(e.getMessage());
//            throw new TFSExceptions(e);
//        }
//        repo.save(toolList);
//        return toolList;
//
//    }

	public String getIterationDetails(String iteration) {

		if (iteration.contains(" ")) {
			return iteration.replace(" ", "%20");
		}
		return iteration;

	}

	public String getSeverityInfo(String priority) {
		if (priority.contains(" ")) {
			String[] separationString = priority.split(Pattern.quote(" "));
			priority = separationString[separationString.length - 1];
		}
		return priority;
	}

	public String getPriorityInfo(int priority) {
		String priorityType = null;
		if (priority == 1)
			priorityType = "Critical";
		if (priority == 2)
			priorityType = "High";
		if (priority == 1)
			priorityType = "Medium";
		if (priority == 1)
			priorityType = "Low";
		return priorityType;
	}

	public long getDate(Object crtDt) {
		long milliSeconds = 0;

		SimpleDateFormat dtformat = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy");
		try {
			Date date = (Date) dtformat.parse(crtDt.toString());
			milliSeconds = date.getTime();

		} catch (ParseException e) {
             
		}

		return milliSeconds;

	}

	public long[] iterationDates(String iterationName) {
		String[] dateArray = null;
		String[] equelsSplitArray = null;
		long[] dates = new long[2];
		Set<Entry<String, String>> mapSet = datesMap.entrySet();
		Iterator<Entry<String, String>> mapIterator = mapSet.iterator();
		while (mapIterator.hasNext()) {
			Entry<String, String> mapEntry = mapIterator.next();
			String keyValue = mapEntry.getKey();
			String value = mapEntry.getValue();
			if (iterationName.equals(keyValue)) {
				dateArray = value.split(Pattern.quote(","));
				equelsSplitArray = dateArray[0].split(Pattern.quote("="));
				if (!"null".equals(equelsSplitArray[1])) {
					dates[0] = getTimeStamp(equelsSplitArray[1].toString());
				}

				equelsSplitArray = dateArray[1].split(Pattern.quote("="));
				if (!"null".equals(equelsSplitArray[1])) {
					dates[1] = getTimeStamp(
							equelsSplitArray[1].substring(0, equelsSplitArray[1].length() - 1).toString());
				}
			}
		}

		return dates;

	}

	public long getTimeStamp(String date) {
		Long time = null;
		String[] dArray = date.split(Pattern.quote("T"));
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		Date dt;
		try {
			dt = df.parse(dArray[0]);
			time = dt.getTime();
		} catch (ParseException e1) {
			//e1.printStackTrace();
			LOGGER.info("Date Parse Error");
			LOGGER.error(e1.getMessage());
		}
		return time;

	}

	public void getTfsData(String username, String password, String instanceURL, String projetcCode,
			String projectName) {
		init();
		almConfig = almConfigRepo.findByProjectName(projectName).get(0);

		List<MetricsModel> metricsListNew = new ArrayList<MetricsModel>();

		// String query = "{ \"query\": \"Select [System.Id], [System.Title],
		// [System.State] From WorkItems\" }";
		List<ProjectModel> almProjects = projectRepo.findByProjectName(projectName);
		Map<String, String> query = new HashMap<String, String>();
		query.put("query", "Select [System.Id], [System.Title], [System.State], [System.IterationID] From WorkItems");
		almProj = new ProjectModel();
		if (almProjects.size() > 0) {
			long lastRunTimestamp = almProjects.get(0).getcRuns();
			DateTime lastRunDate = new DateTime(lastRunTimestamp, DateTimeZone.forID(almConfig.getTimeZone()));
			DateTime oneDayBeforeLastUpdate = lastRunDate.minusDays(2);
			String queryDate = oneDayBeforeLastUpdate.getMonthOfYear() + "/" + oneDayBeforeLastUpdate.getDayOfMonth()
					+ "/" + oneDayBeforeLastUpdate.getYear();
			query.put("query",
					"Select [System.Id], [System.Title], [System.State], [System.IterationID] From WorkItems Where [System.ChangedDate] > '"
							+ queryDate + "'");
			almProj = almProjects.get(0);
		}
		almProj.setAlmType("Azure Board");
		almProj.setcRuns(new DateTime(DateTimeZone.forID(almConfig.getTimeZone())).getMillis());
		almProj.setProjectName(projectName);
		String wiqlURL = instanceURL + "/" + projetcCode + "/_apis/wit/wiql?api-version=6.0";
		ResponseEntity<String> response = utils.makePostRestCall(wiqlURL, username, password, query);

		if (response != null) {
			JSONObject responseObject = utils.parseAsObject(response);
			JSONArray workItemArray = responseObject.getJSONArray("workItems");
			for (int i = 0; i < workItemArray.length(); i++) {
				JSONObject workItemObject = workItemArray.getJSONObject(i);
				String workItemUrl = workItemObject.getString("url");
				String workItemId = String.valueOf(workItemObject.getInt("id"));
				MetricsModel workItemMetric = getWorkItemDetails(workItemId, workItemUrl + "?$expand=all", username,
						password, projectName);
				metricsListNew.add(workItemMetric);
			}
		}

		metricRepo.save(metricsListNew);

		// Iteration data
		String iterationURL = instanceURL + "/" + projetcCode + appendIterationUrl;

		ResponseEntity<String> iterationResponse = utils.makeRestCall(iterationURL, username, password);
		if (iterationResponse != null) {
			JSONObject iterationObject = utils.parseAsObject(iterationResponse);
			processIterations(metricsListNew, projectName, iterationObject);
		}

		// Backlog

		if (backlogIssues.size() > 0) {

			List<MetricsModel> metricsFiltered = backlogIssues.stream().filter(distinctByKey(p -> p.getsId()))
					.collect(Collectors.toList());
			List<IterationModel> backlogIterations = new ArrayList<IterationModel>();
			for (int index = 0; index < metricsFiltered.size(); index++) {

				MetricsModel tempMetric = metricsFiltered.get(index);

				IterationModel backlogIteration = iterationRepo.findBySIdAndPName(tempMetric.getsId(), projectName);

				if (backlogIteration == null) {
					backlogIteration = new IterationModel();
					backlogIteration.setsId(tempMetric.getsId());
					backlogIteration.setState("Backlog");
					backlogIteration.setsName("Backlog");
					backlogIteration.setpAlmType("Azure Board");
					backlogIteration.setpName(projectName);
					iterationRepo.save(backlogIteration);
				}
			}

			// List<IterationModel> backlog = iterationRepo.find
		}

		new MongoAggregate().aggregate(projectName, "Azure Board");
		// ALM Project for Defect Count
		processDefectCount(almProj, projectName);

		projectRepo.save(almProj);

	}

	private void processDefectCount(ProjectModel almProj2, String projectName2) {
		

		List<defectCount> defectCount = new ArrayList<defectCount>();
		almProj2.setDefectCount(defectCount);

		List<IterationOutModel> activeSprints = authoreRepo.findByPNameAndState(projectName2, "ACTIVE");

		// List<MetricsModel> metrics=
		// metricRepo.findByPNameAndSNameAndType(projectName2, projectName2,
		// almConfig.getDefectName());
		Map<String, Integer> count = new HashMap<String, Integer>();
		for (int index = 0; index < activeSprints.size(); index++) {
			List<MonogOutMetrics> metrics = activeSprints.get(index).getMetrics();
			for (int index2 = 0; index2 < metrics.size(); index2++) {
				MonogOutMetrics temp = metrics.get(index2);

				if (temp.getType().equalsIgnoreCase(almConfig.getDefectName()) && temp.getSeverity() != null) {
					String defectId = getAlmProjIdBySeverity(metrics.get(index2).getSeverity());
					int val = 1;
					if (count.containsKey(defectId)) {
						val = count.get(defectId).intValue() + 1;
					}
					count.put(defectId, val);
				}

			}
		}
		for (Map.Entry<String, Integer> entry : count.entrySet()) {
			defectCount temp = new defectCount();
			temp.setCount(entry.getValue());
			temp.setType(entry.getKey());
			defectCount.add(temp);
		}

	}

	private String getAlmProjIdBySeverity(String severity2) {
		

		if (severity2.equalsIgnoreCase("1 - Critical")) {

			return "Critical";
		} else if (severity2.equalsIgnoreCase("3 - Medium")) {

			return "Medium";
		} else if (severity2.equalsIgnoreCase("2 - High")) {

			return "High";
		}

		return "Low";
	}

	static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
		Map<Object, Boolean> seen = new ConcurrentHashMap<>();
		return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
	}

	private void processIterations(List<MetricsModel> metricsList, String projectName2, JSONObject iterationObject) {
		

		List<IterationModel> iterations = new ArrayList<IterationModel>();

		JSONArray iterationValues = iterationObject.getJSONArray("value");

		for (int index = 0; index < iterationValues.length(); index++) {
			JSONObject iterationObj = iterationValues.getJSONObject(index);
			List<MetricsModel> metrcisData = metricRepo.findByPNameAndSName(projectName2,
					iterationObj.getString("name"));

			if (metrcisData.size() > 0) {
				// IterationModel iter
				// Replace with filter to save time?
				IterationModel iter = iterationRepo.findBySNameAndPName(iterationObj.getString("name"), projectName2);
				if (iter == null) {
					iter = new IterationModel();
					
				}
				iterations.add(iter);
				iter.setpAlmType("Azure Board");

				iter.setsName(iterationObj.getString("name"));
				iter.setsId(metrcisData.get(0).getsId());
				iter.setpName(projectName2);
				JSONObject attributesObj = iterationObj.getJSONObject("attributes");
				String startDate=null;
				String finsihDate =null;
				
					try {
				 startDate = attributesObj.getString("startDate");
				 finsihDate = attributesObj.getString("finishDate");
					}catch (Exception e) {
						// TODO: handle exception
						LOGGER.error("Date Problem In Azure Board Iteration");
						LOGGER.info(e.getMessage());
						
					}
				
				
				
				
				if (startDate != null) {
					iter.setStDate(utils.getTimeInMiliseconds(startDate));
				}
				if (finsihDate != null) {
					iter.setEndDate(utils.getTimeInMiliseconds(finsihDate));
					iter.setCompletedDate(utils.getTimeInMiliseconds(finsihDate));
				}
				String state = attributesObj.getString("timeFrame");
				iter.setState(state.toUpperCase());
				if (state.equalsIgnoreCase("current")) {
					iter.setState("ACTIVE");
				} else if (state.equals("past")) {
					iter.setState("CLOSED");
				}
               processTeamSize(iter);
               getPlannedStoryPoint(iter);
			}

		}
          
		iterationRepo.save(iterations);

	}

	
    public void getPlannedStoryPoint(IterationModel mainModel) {
	int defectsCount = 0;
	int closedDefectsCount = 0;
	int openedDefectsCount = 0;
	String defectName;
	List<MetricsModel> modelList = DataConfig.getContext().getBean(MetricRepo.class)
		.findByPNameAndSName(mainModel.getpName(), mainModel.getsName());
	String[] closedState = almConfig.getCloseState();
	defectName = almConfig.getDefectName();

	String str = Arrays.toString(closedState);

	int velocitySprint = 0;
	int plannedStoryPoints = 0;
	// list of Metric model
	for (MetricsModel currentSprintData : modelList) {
	    if (defectName.equalsIgnoreCase(currentSprintData.getType())) {
		defectsCount++;
		if (str.contains(currentSprintData.getState())) {
		    closedDefectsCount++;
		} else {
		    openedDefectsCount++;
		 }
	    }
	}
	mainModel.setVelocity(velocitySprint);
	mainModel.setTotalStoryPoints(plannedStoryPoints);
	mainModel.setTotDefects(defectsCount);
	mainModel.setTotClosedDefects(closedDefectsCount);
	mainModel.setTotOpenDefetct(openedDefectsCount);
	

    }
	
	private void processTeamSize(IterationModel iter) {
		

			List<String> list = new ArrayList<String>();

			try {
			    list = DataConfig.getInstance().mongoTemplate().getCollection("Metrics").distinct("assgnTo",
				    new BasicDBObject("sName", iter.getsName()).append("pName", iter.getpName()));
			} catch (Exception e) {
				LOGGER.info(e);
			}
			iter.setTeamSize(list.size());

		    
	}

	private MetricsModel getWorkItemDetails(String workItemId, String workItemUrl, String username, String password,
			String pName) {
		ResponseEntity<String> workItemResponse = utils.makeRestCall(workItemUrl, username, password);
		MetricsModel metrics = new MetricsModel();
		if (workItemResponse != null) {
			JSONObject workItem = utils.parseAsObject(workItemResponse);
			if (workItem.has("fields")) {

				MetricsModel temp = metricRepo.findByWIdAndPName(workItemId, pName);
				boolean newUpdates = true;
				JSONObject filelds = workItem.getJSONObject("fields");
				
				if (temp != null) {
					long updateDate = utils.getTimeInMiliseconds(filelds.getString("System.ChangedDate"));
					long prevUpdateDate = temp.getUpdatedDate();
					if (updateDate == prevUpdateDate) {
						newUpdates = false;
					}
					metrics = temp;
				}
				
				if (workItem.has("relations")) {
					JSONArray relations = workItem.getJSONArray("relations");

					processLinks(relations, metrics, pName, username, password);

				}

				if (newUpdates) {
					metrics.setpName(pName);
					metrics.setType(filelds.getString("System.WorkItemType"));
					metrics.setState(filelds.getString("System.State"));
					metrics.setCreateDate(utils.getTimeInMiliseconds(filelds.getString("System.CreatedDate")));
					metrics.setwId(workItemId);
					metrics.setUpdatedDate((utils.getTimeInMiliseconds(filelds.getString("System.ChangedDate"))));
					if (filelds.has(ASSIGNEDTO)) {
						metrics.setAssgnTo(filelds.getJSONObject(ASSIGNEDTO).getString("displayName"));
					}
					if (filelds.has("System.Title")) {
						metrics.setSumm(filelds.getString("System.Title"));
					}
					

					metrics.setsId(filelds.getInt("System.IterationId"));
					if (filelds.has("System.IterationLevel2")) {
						metrics.setsName(filelds.getString("System.IterationLevel2"));

					} else {
						backlogIssues.add(metrics);
						metrics.setsName("Backlog");
					}
					if (filelds.has("Microsoft.VSTS.Common.ClosedDate")) {
						metrics.setDoneDate(
								utils.getTimeInMiliseconds(filelds.getString("Microsoft.VSTS.Common.ClosedDate")));
					}
					if (filelds.has("Microsoft.VSTS.Scheduling.CompletedWork")) {
						metrics.setEffort(filelds.getDouble("Microsoft.VSTS.Scheduling.CompletedWork"));
					}
                    if(filelds.has(ESTIMATE)) {
                    	metrics.setOrgEst(filelds.getDouble(ESTIMATE));
                    }
                    
                    if(filelds.has(REMAININGWORK)) {
                    	metrics.setRemTime(filelds.getDouble(REMAININGWORK));
                    }
                    
                    setActEst(metrics);
					metrics.setpAlmType("Azure Board");

					if (filelds.has(PRIORITY)) {

						int priority = filelds.getInt(PRIORITY);
						metrics.setPriority(getPriorityInfo(priority));
					}
					if (filelds.has(SEVERITY)) {
						metrics.setSeverity(filelds.getString(SEVERITY));
					}

					JSONObject linksObject = workItem.getJSONObject("_links");

					String updatesURL = linksObject.getJSONObject("workItemUpdates").getString("href");

					ResponseEntity<String> workItemUpdateResponse = utils.makeRestCall(updatesURL, username, password);
					if (workItemUpdateResponse != null) {
						JSONObject workItemUpdate = utils.parseAsObject(workItemUpdateResponse);
						JSONArray updateValues = workItemUpdate.getJSONArray("value");

						processUpdateValues(updateValues, metrics);

					}

				}

			}
		}
		return metrics;

	}

	
	
	public void setActEst(MetricsModel metrics) {
		Double timeSpent = metrics.getEffort() != null ? metrics.getEffort() : 0.0;
		Double originalEstimate = metrics.getOrgEst() != null ? metrics.getOrgEst() : 0.0;
		Double deviation = timeSpent - originalEstimate;
		if (deviation > 0.0) {
			metrics.setActEst(originalEstimate + deviation);
			metrics.setExtEffort(deviation);
		} else {
			metrics.setActEst(originalEstimate);
			metrics.setExtEffort(0.0);
		}
	}
	
	
	
	private void processLinks(JSONArray relations, MetricsModel metrics2, String pName2, String username,
			String password) {
		
		List<String> inwardLinks = new ArrayList<String>();
        
		List<String> outwardIssue = new ArrayList<String>();
		List<String> taskList = new ArrayList<String>();
		List<String> subTaskList = new ArrayList<String>();

		for (int index = 0; index < relations.length(); index++) {

			JSONObject relation = relations.getJSONObject(index);
			String url[] = relation.getString("url").split("_apis/wit/workItems/");
			String relationName = relation.getJSONObject("attributes").getString("name");
			if (relationName.equals("Child") || relationName.equals("Parent")) {
				MetricsModel temp = metricRepo.findByPNameAndWId(pName2, url[1]);
				String type = "";
				if (temp == null) {
					ResponseEntity<String> workItemResp = utils.makeRestCall(relation.getString("url"), username,
							password);
					JSONObject workItem = utils.parseAsObject(workItemResp);
					
					type= workItem.getJSONObject("fields").getString("System.WorkItemType");
				} else {
					type = temp.getType();

				}

				if (relationName.equals("Parent")) {
					if(type.equalsIgnoreCase("Epic")) {
						metrics2.setEpicLink(url[1]);
					}else{
						inwardLinks.add(url[1]);
					}
				}
				if (relationName.equals("Child")) {
					if(type.equalsIgnoreCase("Task")) {
						taskList.add(url[1]);
					}else { 
						outwardIssue.add(url[1]);
					}
				}

			}

		}

		metrics2.setInWardIssueLink(inwardLinks);
		metrics2.setOutWardIssueLink(outwardIssue);
		metrics2.setTaskList(taskList);
	}

	public void init() {
		ctx = DataConfig.getContext();
		transitionRepo = ctx.getBean(TransitionRepo.class);
		// ALMProject
		projectRepo = ctx.getBean(ProjectRepo.class);
		authoreRepo = ctx.getBean(ProjectIterationRepo.class);
		metricRepo = ctx.getBean(MetricRepo.class);
		iterationRepo = ctx.getBean(IterationRepo.class);
		effortHistoryRepo = ctx.getBean(EffortHistoryRepo.class);
		changeHisortyRepo = ctx.getBean(ChangeHisortyRepo.class);
		changeLogs = new ArrayList<>();
		efforts = new ArrayList<>();
		taskDetailsList = new ArrayList<>();
		iterationSet = new LinkedHashSet<>();
		metricsSet = new LinkedHashSet<>();
		almConfigRepo = ctx.getBean(ALMConfigRepo.class);

	}

	private void processUpdateValues(JSONArray updateValues, MetricsModel metrics) {
		
		Map<Long, Double> storyPoints = new HashMap<Long, Double>();
		Map<Long, String> allocatedDate = new HashMap<Long, String>();
		List<TransitionModel> newTransitions = new ArrayList<>();
		List<TransitionModel> transitions = transitionRepo.findByWId(metrics.getwId());
		List<EffortHistoryModel> effortData = new ArrayList<EffortHistoryModel>();
		// Remove older transitions for removing duplicate
		effortHistoryRepo.delete(effortHistoryRepo.findByWId(metrics.getwId()));
		transitionRepo.delete(transitions);
		metrics.setStoryPoints(storyPoints);
		metrics.setAllocatedDate(allocatedDate);

		for (int index = 0; index < updateValues.length(); index++) {
			JSONObject updateValue = updateValues.getJSONObject(index);

			if (updateValue.has("fields")) {
				JSONObject updatedFields = updateValue.getJSONObject("fields");

				long updatedTime = utils
						.getTimeInMiliseconds(updatedFields.getJSONObject(CHANGEDDATE).getString("newValue"));

				// StoryPoints
				if (updatedFields.has(STORYPOINTS)) {
					storyPoints.put(updatedTime, updatedFields.getJSONObject(STORYPOINTS).getDouble("newValue"));

				}
				// Allocations
				if (updatedFields.has("System.IterationId")) {
					allocatedDate.put(updatedTime,
							String.valueOf(updatedFields.getJSONObject("System.IterationId").getInt("newValue")));

				}
				
				//Effort History
				if(updatedFields.has("Microsoft.VSTS.Scheduling.CompletedWork") || updatedFields.has(ESTIMATE) ) {
					
					EffortHistoryModel effort = new EffortHistoryModel();
					effortData.add(effort);
					effort.setwId(metrics.getwId());
                    effort.setpName(metrics.getpName());
                    effort.setLogDate(updatedTime);
                    
                    if(updatedFields.has("Microsoft.VSTS.Scheduling.RemainingWork"))
                    effort.setRemainingWork(updatedFields.getJSONObject("Microsoft.VSTS.Scheduling.RemainingWork").getDouble("newValue"));
                    
                    effort.setLoggedBy(updateValue.getJSONObject("revisedBy").getString("displayName"));
                    effort.setInitialWork(metrics.getOrgEst());
                	if(updatedFields.has(ESTIMATE)) {
                		double oldVal=0, newVal=0;
                		  if(updatedFields.getJSONObject(ESTIMATE).has("oldValue"))
    					 oldVal =updatedFields.getJSONObject(ESTIMATE).getDouble("oldValue");
                		  if(updatedFields.getJSONObject(ESTIMATE).has("newValue"))
    					 newVal= updatedFields.getJSONObject(ESTIMATE).getDouble("newValue");
                		  
                		  if(newVal>oldVal) {
                			  effort.setRemainingWorkInc(newVal-oldVal);
                		  }else {
                			  effort.setRemainingWorkDec(oldVal-newVal);
                		  }
                		  
                		  
    					
    				}
                	
                	if(updatedFields.has("Microsoft.VSTS.Scheduling.CompletedWork")) {
                	JSONObject completedWork=updatedFields.getJSONObject("Microsoft.VSTS.Scheduling.CompletedWork");
                	 
                	double oldCompletion=0;
                	double newCompletion=0;
                	
                	
                	if(completedWork.has("oldValue")) {
                		oldCompletion=completedWork.getDouble("oldValue");
                	}
                	if(completedWork.has("newValue")) {
                		newCompletion=completedWork.getDouble("newValue");
                	}
					
                	effort.setTimeSpent((int) ((int)newCompletion-oldCompletion));
                	
				 }
				}
			
				
				

				// Transitions
				if (updatedFields.has("System.State")) {
					JSONObject stateObj = updatedFields.getJSONObject("System.State");

					if (stateObj.has("oldValue")) {

						TransitionModel transition = new TransitionModel();
						transition.setwId(metrics.getwId());
						transition.setCreateTime(metrics.getCreateDate());
						transition.setCrState(stateObj.getString("newValue"));
						transition.setFrmState(stateObj.getString("oldValue"));
						transition.setpName(metrics.getpName());
						transition.setMdfDate(updatedTime);
						long lastUpdatedTime = utils
								.getTimeInMiliseconds(updatedFields.getJSONObject(CHANGEDDATE).getString("oldValue"));

						transition.setWaitTime(updatedTime - lastUpdatedTime);
						transition.setPreStateWaitTime(updatedTime - metrics.getCreateDate());
						newTransitions.add(transition);
					}

				}
				
				

			}

		}

		transitionRepo.save(newTransitions);
        effortHistoryRepo.save(effortData);
	}
}