package com.bolt.dashboard.tfsversion;

import java.util.Iterator;
import java.util.List;
import java.util.Set;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.SCMToolRepository;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

public class TFSVersionControllerApplication {

	private static final Logger LOGGER = LogManager.getLogger(TFSVersionControllerApplication.class);
	AnnotationConfigApplicationContext ctx = null;
	SCMToolRepository repo = null;
	ConfigurationSettingRep configurationRepo = null;
	ConfigurationSetting configurationColection = null;
	Set<ConfigurationToolInfoMetric> metric = null;
	List<SCMTool> toolList = null;

	public TFSVersionControllerApplication() {
	}

	@SuppressWarnings("resource")
	public void tfsversionMain(String projectName) {
		LOGGER.info("TFS Version Control Collector started for " + projectName);
		String instanceURL = null;
		String username = null;
		String password = null;

		ctx = DataConfig.getContext();
		repo = ctx.getBean(SCMToolRepository.class);
		TFSVersionControllerClientImplementation client = new TFSVersionControllerClientImplementation();

		configurationRepo = ctx.getBean(ConfigurationSettingRep.class);
		configurationColection = configurationRepo.findByProjectName(projectName).get(0);

		metric = configurationColection.getMetrics();
		Iterator iter = metric.iterator();
		while (iter.hasNext()) {
			Object configuration1 = iter.next();
			ConfigurationToolInfoMetric metric1 = (ConfigurationToolInfoMetric) configuration1;
			LOGGER.info("Tool name  " + metric1.getToolName());
			if ("TFS".equals(metric1.getToolName())) {
				LOGGER.info("URL  " + metric1.getUrl());
				instanceURL = metric1.getUrl();
				username = metric1.getUserName();
				
					password=EncryptionDecryptionAES.decrypt(metric1.getPassword(), ConstantVariable.SECRET_KEY);
				break;
			}

		}
		String appendUrl = "/_apis/tfvc/changesets/";
		try {
			instanceURL = instanceURL + appendUrl;
			toolList = (List<SCMTool>) client.getCommits(instanceURL, username, password, projectName);
			repo.save(toolList);
			cleanObject();
			LOGGER.info("End of TFSVersion Main");

		} catch (Exception e) {

			cleanObject();
			LOGGER.error(e.getMessage());
			LOGGER.info(e.getStackTrace());
			LOGGER.info(e);
			LOGGER.info("TFS Version Control Collector failed for " + projectName);


		}

		LOGGER.info("TFS Version Control Collector ended for " + projectName);
	}

	public void cleanObject() {
		repo = null;
		configurationRepo = null;
		configurationColection = null;
		metric = null;
		toolList = null;
	}
}
