package com.bolt.dashboard.tfsversion;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Pattern;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.simple.JSONObject;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.DeletedFileDetails;
import com.bolt.dashboard.core.model.FileDetails;
import com.bolt.dashboard.core.model.SCMTool;
import com.bolt.dashboard.core.repository.SCMToolRepository;
import com.sun.jersey.api.client.ClientResponse;
import com.sun.jersey.api.client.WebResource;

public class TFSVersionControllerClientImplementation implements TFSVersionControllerClient {

	private static final Logger LOGGER = LogManager.getLogger(TFSVersionControllerClientImplementation.class);
	int fromId = 0;
	int toId = 99;
	int lasstChangeSetId = 0;
	String commitUrl;
	String uName;
	String password;
	String projectName;
	int lastRecord = 0;
	List<FileDetails> filesDetailsList;
	List<SCMTool> toolList = new ArrayList<SCMTool>();
	String committer = null;
	String deletedFileName = null;
	long deletedTimeStamp = 0;
	List<DeletedFileDetails> list;

	@SuppressWarnings({ "unchecked", "rawtypes", "unused" })
	public List<SCMTool> getCommits(String url, String userName, String password, String projectName)
			throws TFSVersionControllerExceptions {
		this.commitUrl = url;
		this.uName = userName;
		this.password = password;
		this.projectName = projectName;

		JSONObject jsonObject;
		jsonObject = makeRestCall(url, userName, password);
		List<Object> objOutput = (ArrayList<Object>) jsonObject.get("value");

		Object lastChangeSetObject = objOutput.get(0);
		Iterator<Entry<Object, Object>> keySetIterator = ((Map<Object, Object>) lastChangeSetObject).entrySet()
				.iterator();
		while (keySetIterator.hasNext()) {
			Entry hm = (Entry) keySetIterator.next();
			Object key = hm.getKey();

			if ("changesetId".equalsIgnoreCase(key.toString())) {
				lasstChangeSetId = Integer.parseInt(hm.getValue().toString());
				LOGGER.info("last changeset Id   " + lasstChangeSetId);
			}
		}
		getLastCommitFromDB();
		getAllCommitsData();

		return toolList;

	}

	public int getLastCommitFromDB() {
		AnnotationConfigApplicationContext ctx = DataConfig.getContext();
		SCMToolRepository repository = ctx.getBean(SCMToolRepository.class);
		List<SCMTool> list = repository.findByScTypeAndProjectName("TFS", projectName);
		if (!list.isEmpty()) {
			fromId = Integer.parseInt(list.get(list.size() - 1).getRevisioNo());
			toId = fromId + 99;
		} else {
			fromId = 0;
		}
		return fromId;

	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public int[] makeRestCallForChangeType(String url, String userId, String password) {
		int totalAdditions = 0;
		int totalDeletions = 0;
		int totalModification = 0;
		int totalChanges = 0;
		FileDetails fileDetails = null;

		JSONObject jsonObject = makeRestCall(url, userId, password);
		List<Object> objOutput = (ArrayList<Object>) jsonObject.get("value");
		list = new ArrayList<>();
		for (Object objOutputJson : objOutput) {
			Iterator<Entry<Object, Object>> keySetIterator = ((Map<Object, Object>) objOutputJson).entrySet()
					.iterator();
			String editType;
			String filePath = null;
			while (keySetIterator.hasNext()) {
				Entry hm = (Entry) keySetIterator.next();
				if ("changeType".equalsIgnoreCase(hm.getKey().toString())) {
					editType = hm.getValue().toString();
					if (editType.contains("add")) {
						totalAdditions = totalAdditions + 1;

					}
					if (editType.contains("delete")) {
						totalDeletions = totalDeletions + 1;
						String path = getDeletedFilePath(filePath);
						if (!(null == path)) {
							DeletedFileDetails info = new DeletedFileDetails();
							info.setCommitter(committer);
							info.setDeletedDateTime(deletedTimeStamp);
							info.setFileName(path);
							list.add(info);
						}

					}
					if (editType.contains("edit"))
						totalModification = totalModification + 1;
				}
				if ("item".equalsIgnoreCase(hm.getKey().toString())) {
					filePath = hm.getValue().toString();
					filePath = getPath(filePath);
					fileDetails = getCommittedFilePath(filePath);

				}

			}

		}
		totalChanges = (totalAdditions + totalDeletions + totalModification);
		LOGGER.info("total additions per revision  " + totalAdditions);
		LOGGER.info("total deletions per revision " + totalDeletions);
		LOGGER.info("Total changes per revision " + (totalAdditions + totalDeletions + totalModification));
		if (!(null == fileDetails)) {

			if (!(null == fileDetails.getFilename()) && !(totalChanges == 0)) {
				filesDetailsList.add(fileDetails);
			}
		}
		return new int[] { totalAdditions, totalDeletions, totalModification, totalChanges };
	}

	public String getDeletedFilePath(String xPath) {

		String path = null;

		if (xPath.contains("=") && xPath.contains("/") && xPath.contains(".")) {
			String[] xPathArray = xPath.split(Pattern.quote("="));
			String[] yPathArray = xPathArray[1].split(Pattern.quote("/"));
			if (yPathArray[yPathArray.length - 1].contains(".")) {
				String[] zPathArray = yPathArray[yPathArray.length - 1].split(Pattern.quote("."));

				if (!(zPathArray[0].equals(" ")) && !(zPathArray[1].equals(" ")))
					path = yPathArray[yPathArray.length - 1];
			}
		}
		return path;

	}

	public FileDetails getCommittedFilePath(String xPath) {

		FileDetails fileDetails = null;

		if (xPath.contains("=") && xPath.contains("/") && xPath.contains(".")) {
			fileDetails = new FileDetails();
			String[] xPathArray = xPath.split(Pattern.quote("="));
			String[] yPathArray = xPathArray[1].split(Pattern.quote("/"));
			if (yPathArray[yPathArray.length - 1].contains(".")) {
				String[] zPathArray = yPathArray[yPathArray.length - 1].split(Pattern.quote("."));

				if (!(zPathArray[0].equals(" ")) && !(zPathArray[1].equals(" ")))
					fileDetails.setFilename(yPathArray[yPathArray.length - 1]);
			}
		}
		return fileDetails;

	}

	public String getPath(String filePath) {
		if (filePath.contains(",")) {
			String[] filePathArray = filePath.split(Pattern.quote(","));
			return filePathArray[1];

		} else {
			return null;
		}
	}

	public JSONObject makeRestCall(String url, String userId, String password) {

		if (!"".equals(userId) && !"".equals(password)) {

			com.sun.jersey.api.client.Client restClient = com.sun.jersey.api.client.Client.create();
			String authString = userId + ":" + password;
			String authStringEnc = Arrays.toString(new Base64().encode(authString.getBytes()));
			WebResource webResource = restClient.resource(url);
			ClientResponse resp = webResource.accept("application/json")
					.header("Authorization", "Basic " + authStringEnc).get(ClientResponse.class);
			if (resp.getStatus() != 200) {
				LOGGER.error("Unable to connect to the server");
			}
			return resp.getEntity(JSONObject.class);

		}
		return null;

	}

	public void getAllCommitsData() {
		String instanceUrl;
		while (fromId < lasstChangeSetId) {
			if (toId > lasstChangeSetId) {
				instanceUrl = commitUrl + "?fromId=" + fromId + "&toId=" + lasstChangeSetId + "&api-version=1.0";
				LOGGER.info("instanceUrl   " + instanceUrl);
			} else {
				instanceUrl = commitUrl + "?fromId=" + fromId + "&toId=" + toId + "&api-version=1.0";
				LOGGER.info("instanceUrl   " + instanceUrl);
			}

			JSONObject objOutputJson = makeRestCall(instanceUrl, uName, password);
			getIndividualCommitData(objOutputJson);
			fromId = fromId + toId;

			toId = toId + 99;

		}
		LOGGER.info("No commits are to be stored......");
	}

	public void getIndividualCommitData(JSONObject outputJson) {

		List<Object> objOutput = (ArrayList<Object>) outputJson.get("value");

		for (int i = objOutput.size() - 1; i >= 0; i--) {
			Object objOutputJson = objOutput.get(i);
			Iterator<Entry<Object, Object>> keySetIterator = ((Map<Object, Object>) objOutputJson).entrySet()
					.iterator();

			SCMTool commit = new SCMTool();
			commit.setScType("TFS");
			commit.setTimestamp(new Date().getTime());
			commit.setProjectName(projectName);
			while (keySetIterator.hasNext()) {
				Entry hm = (Entry) keySetIterator.next();
				Object key = hm.getKey();
				Object value = hm.getValue();
				if ("comment".equalsIgnoreCase(hm.getKey().toString()) && hm.getValue().toString() != null) {
					commit.setCommitLog(hm.getValue().toString());
				}
				if ("createdDate".equalsIgnoreCase(hm.getKey().toString()) && hm.getValue() != null) {
					long scmDate = ConstantVariable.timestamp(hm.getValue(), projectName);
					deletedTimeStamp = scmDate;
					commit.setCommitTS(scmDate);
				}
				if ("url".equalsIgnoreCase(hm.getKey().toString()) && hm.getValue().toString() != null) {
					commit.setUrl(hm.getValue().toString());
				}
				if ("changesetId".equalsIgnoreCase(key.toString())) {
					filesDetailsList = new ArrayList<>();
					String revision = hm.getValue().toString();

					commit.setRevisionNo(revision);
					String changeSetUrl = commitUrl + revision + "/changes?api-version=1.0";
					LOGGER.info("Changeset Id URL    " + changeSetUrl);
					int[] changes = new int[4];
					changes = makeRestCallForChangeType(changeSetUrl, uName, password);
					int totalAdditions = changes[0];
					int totalDeletions = changes[1];
					int totalModification = changes[2];
					commit.setAddition(totalAdditions);
					commit.setDeletion(totalDeletions);
					commit.setModification(totalModification);
					commit.setNoOfChanges(totalAdditions + totalDeletions + totalModification);
					commit.setFileDetails(filesDetailsList);
					commit.setDeletedFileDetails(list);
				}
				if ("checkedInBy".equalsIgnoreCase(key.toString())) {
					Iterator<Entry<Object, Object>> keySetIteratorNew = ((Map<Object, Object>) value).entrySet()
							.iterator();
					while (keySetIteratorNew.hasNext()) {
						Entry hmNew = (Entry) keySetIteratorNew.next();

						if ("displayName".equalsIgnoreCase(hmNew.getKey().toString()) && hmNew.getValue() != null) {
							committer = hmNew.getValue().toString();
							commit.setCommiter(hmNew.getValue().toString());

						}

					}

				}

			}
			toolList.add(commit);

		}

	}
}
