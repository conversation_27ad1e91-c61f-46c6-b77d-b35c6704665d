package com.bolt.dashboard.util;

import java.nio.charset.StandardCharsets;
import java.util.Map;

import org.apache.commons.codec.binary.Base64;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;

public class AzureDevOpsUtils {

	public long getTimeInMiliseconds(String temp_date) {
		
		String[] splitDate = temp_date.split("T");
		String[] temp = splitDate[1].split("\\.");
		temp = temp[0].split("Z");
		// temp=temp_date.split(".");
		String tempTime = splitDate[0] + " " + temp[0];

		DateTimeFormatter fmt = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
		DateTime createdDate = fmt.parseDateTime(tempTime);
		return createdDate.getMillis();
	}
	
    public ResponseEntity<String> makeRestCall(String url, String userId, String password) {
		// Basic Auth only.
		 if (!"".equals(userId) && !"".equals(password)) {
		 return get().exchange(url, HttpMethod.GET, new
		 HttpEntity<>(createHeaders(userId, password)), String.class);
		
		 } else {
		 return get().exchange(url, HttpMethod.GET, null, String.class);
		 }
		
	}
    
    public ResponseEntity<String> makePostRestCall(String url, String userId, String password,Map requestBody) {
		// Basic Auth only.
		 if (!"".equals(userId) && !"".equals(password)) {

		 return get().exchange(url, HttpMethod.POST, new
		 HttpEntity<>(requestBody,createHeaders(userId, password)), String.class);
		
		 } else {
			 
		 return get().exchange(url, HttpMethod.GET, null, String.class);
		 
		 }
		
	}
    
 

    public JSONObject parseAsObject(ResponseEntity<String> staticsticResponse) {
		
		return (JSONObject) new JSONTokener(staticsticResponse.getBody()).nextValue();
	}
	
	
    private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000000);
		requestFactory.setReadTimeout(20000000);
		return new RestTemplate(requestFactory);
	}
    
	private HttpHeaders createHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}
    
}
