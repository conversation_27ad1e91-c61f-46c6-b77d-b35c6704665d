package com.bolt.dashboard.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.ComponentGroomingTable;
import com.bolt.dashboard.core.model.ComponentSprintWiseStories;
import com.bolt.dashboard.core.model.ComponentStoryAgeing;
import com.bolt.dashboard.core.model.ComponentTaskRisk;
import com.bolt.dashboard.core.model.DefectInsightData;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MetricAgeData;
import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.TransitionModel;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.MetricRepo;
import com.bolt.dashboard.core.repository.ProjectIterationRepo;
import com.bolt.dashboard.jira.ChartCalculations;

public class BacklogCalculation {

	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
	CommonFunctions commonFunc = new CommonFunctions();
	MetricRepo metricRepo = null;
	private static final Logger LOGGER = LogManager.getLogger(DefectCalculations.class);
	ALMConfiguration almConfiguration = null;
	List<MetricsModel> allBugs = null;
	List<IterationOutModel> allIterations = null;
	List<IterationOutModel> allIterationsAndBacklog = null;
	List<String> closeStates = null;
	private ProjectIterationRepo authorRepo;
	
	private void getInititialDetails(String projectName, String almType) {

		ALMConfigRepo almConfigRepo = ctx.getBean(ALMConfigRepo.class);
		almConfiguration = almConfigRepo.findByProjectName(projectName).get(0);

		try {
			MongoTemplate mongo = DataConfig.getInstance().mongoTemplate();

			Query q = new Query();
			q.addCriteria(Criteria.where("pName").is(projectName).and("sName").nin("FUTURE", "Future", "future"));
			q.with(new Sort(Sort.Direction.ASC, "stDate"));
			allIterationsAndBacklog = mongo.find(q, IterationOutModel.class, "Author");
		} catch (Exception e) {
			LOGGER.error("Mongo error");
		}
	}
//	public static void main(String[] args) {
//	new BacklogCalculation().caluclateStoryAgeing("SP 1", "JIRA");
//}
	public List<ComponentStoryAgeing> caluclateStoryAgeing(String projName, String almType) {
		List<ComponentStoryAgeing> response = new ArrayList<ComponentStoryAgeing>();
		getInititialDetails(projName, almType);
		closeStates = Arrays.asList(almConfiguration.getCloseState());
		long currentTime = new DateTime().getMillis();

		ArrayList<String> componentLists = new ArrayList<String>();
		componentLists.add("All");
		componentLists.addAll(new ChartCalculations().getComponents(projName));
		Collections.sort(componentLists);
		boolean flag = (componentLists.size() > 1) ? true : false;
		for (String component : componentLists) {
			ComponentStoryAgeing c = new ComponentStoryAgeing();

			List<MetricAgeData> storyInsightList = new ArrayList<MetricAgeData>();

			for (IterationOutModel iter : allIterationsAndBacklog) {
				if (iter.getMetrics() != null) {
					List<MonogOutMetrics> stories= null;
					if (flag && !component.equals("All"))
					 stories = iter.getMetrics().stream()
							.filter(m -> m.getType().equals(almConfiguration.getStoryName()) 
									&&(m.getComponents() != null ? m.getComponents().get(0).equals(component) : false))
							.collect(Collectors.toList());
					
					else
					stories = iter.getMetrics().stream()
						.filter(m -> m.getType().equals(almConfiguration.getStoryName()))
						.collect(Collectors.toList());

					for (MonogOutMetrics metric : stories) {
						MetricAgeData metricAgeData = new MetricAgeData();
						storyInsightList.add(metricAgeData);
						metricAgeData.setsName(iter.getsName());
						if (metric.getwId().equals("BD-1724")) {
							System.out.println("Stop");
						}
						long age = 0;
						String status = "Open";
						if (closeStates.indexOf(metric.getState()) > -1) {
							long time = metric.getResDate();
							if (time == 0) {
								time = metric.getUpdatedDate();
							}
							age = time - metric.getCreateDate();
							status = "Closed";
						} else {

							age = currentTime - metric.getCreateDate();
						}
						List<TransitionModel> transitions = metric.getTransitions();

						List<Map<String, String>> storyWorkFlowList = new ArrayList<Map<String, String>>();
						metricAgeData.setDefectWorkFlow(storyWorkFlowList);
						if (transitions != null && transitions.size() > 0) {

							for (int index = 0; index <= transitions.size() - 1; index++) {

								TransitionModel trans = transitions.get(index);

								if (index == 0) {

									pushStateFlowObject(trans.getFrmState(), commonFunc
											.convertMilisToDisplayValuesDefect(trans.getPreStateWaitTime(), 24),
											metricAgeData, status);

									if (index == transitions.size()) {
										pushStateFlowObject(trans.getCrState(), "Present State", metricAgeData, status);
									}
									pushStateFlowObject(trans.getCrState(),
											commonFunc.convertMilisToDisplayValuesDefect(trans.getWaitTime(), 24),
											metricAgeData, status);

								} else if (index == transitions.size() - 1) {
									pushStateFlowObject(trans.getCrState(), "Present State", metricAgeData, status);
								} else {

									pushStateFlowObject(trans.getCrState(),
											commonFunc.convertMilisToDisplayValuesDefect(trans.getWaitTime(), 24),
											metricAgeData, status);
								}

							}

						} else {

							pushStateFlowObject(metric.getState(),
									commonFunc.convertMilisToDisplayValuesDefect(age, 24), metricAgeData, status);
						}
						double sp = 0;

						if (metric.getStoryPoints() != null && metric.getStoryPoints().size() > 0) {
							Map<Long, Double> sps = metric.getStoryPoints();
							Set<Long> keysSp = sps.keySet();
							List<Long> list = new ArrayList<Long>(keysSp);
							Collections.sort(list);
							sp = sps.get(list.get(list.size() - 1));

						}
						metricAgeData.setSp(sp);
						metricAgeData.setAgeDisplay(commonFunc.convertMilisToDisplayValuesDefect(age, 24));
						metricAgeData.setAgeInDays((int) commonFunc.toDaysString(age, 24));
						metricAgeData.setAgeInMilis(age);
						metricAgeData.setId(metric.getwId());
						metricAgeData.setStatus(status);
						metricAgeData.setCreateDate(metric.getCreateDate());
						metricAgeData.setPriority(metric.getPriority());
					}
				}

			}
			c.setComponent(component);
			c.setMetricAgeData(storyInsightList);
			response.add(c);
		}
		return response;

	}

	private void pushStateFlowObject(String state, String value, MetricAgeData defectMetric, String status) {
		
		Map<String, String> stateFlowMap = new HashMap<String, String>();
		stateFlowMap.put("name", state);
		stateFlowMap.put("value", value);
		stateFlowMap.put("Status", String.valueOf(false));
		if (status.equals("Closed")) {
			stateFlowMap.put("Status", String.valueOf(true));
		}
		defectMetric.getDefectWorkFlow().add(stateFlowMap);
	}
	
//	public static void main(String[] args) {
//		new BacklogCalculation().calculateGroomingTable("SP 1", "JIRA");
//	}
	
	public List<ComponentGroomingTable> calculateGroomingTable(String projName, String almType) {
		List<ComponentGroomingTable> responseData = new ArrayList<ComponentGroomingTable>();

		metricRepo = ctx.getBean(MetricRepo.class);
		List<MetricsModel> metircs = metricRepo.findByPNameAndPAlmType(projName, almType);

		ALMConfigRepo almConfigRepo = ctx.getBean(ALMConfigRepo.class);
		almConfiguration = almConfigRepo.findByProjectName(projName).get(0);
		List<String> closedStates = Arrays.asList(almConfiguration.getCloseState());

		ArrayList<String> componentLists = new ArrayList<String>();
		componentLists.add("All");
		componentLists.addAll(new ChartCalculations().getComponents(projName));
		Collections.sort(componentLists);
		boolean flag = (componentLists.size() > 1) ? true : false;
		for (String component : componentLists) {
			List<MetricsModel> filterMetrics = null;
			Map<String, Integer> data = new HashMap<String, Integer>();
			if (flag && !component.equals("All"))
				filterMetrics = metircs.stream()
						.filter(m -> (m.getComponents() != null ? m.getComponents().get(0).equals(component) : false))
						.collect(Collectors.toList());
			else
				filterMetrics = metircs;

			Iterator<MetricsModel> itr = filterMetrics.iterator();
			int totalStories = 0, totalStoryPoints = 0;
			int completedStories = 0, completedStoryPoints = 0;
			int pendingEstimatedStories = 0, pendingEstimatedStoryPoints = 0;
			int pendingNonEstimatedStories = 0, pendingNonEStimatedStoryPoints = 0;
			int PendingTotalStories = 0;
			while (itr.hasNext()) {
				MetricsModel metric = itr.next();
				if (metric.getType().equalsIgnoreCase(almConfiguration.getStoryName())) {
					totalStories = totalStories + 1;
					double lastValue = 0;
					for (Map.Entry<Long, Double> entry : metric.getStoryPoints().entrySet()) {
						lastValue = entry.getValue();
					}
					totalStoryPoints = (int) (totalStoryPoints + lastValue);
					if (closedStates.indexOf(metric.getState()) > -1) {
						completedStories = completedStories + 1;
						completedStoryPoints = (int) (completedStoryPoints + lastValue);
					} else if (lastValue != 0) {
						pendingEstimatedStories = pendingEstimatedStories + 1;
						pendingEstimatedStoryPoints = (int) (pendingEstimatedStoryPoints + lastValue);
					} else if (lastValue == 0) {
						pendingNonEstimatedStories = pendingNonEstimatedStories + 1;
						pendingNonEStimatedStoryPoints = (int) (pendingNonEStimatedStoryPoints + lastValue);
					}
				}
			}

			data.put("totalStories", totalStories);
			data.put("totalStoryPoints", totalStoryPoints);
			data.put("completedStories", completedStories);
			data.put("completedStoryPoints", completedStoryPoints);
			data.put("pendingEstimatedStories", pendingEstimatedStories);
			data.put("pendingEstimatedStoryPoints", pendingEstimatedStoryPoints);
			data.put("pendingNonEstimatedStories", pendingNonEstimatedStories);
			data.put("pendingNonEStimatedStoryPoints", pendingNonEStimatedStoryPoints);
			data.put("PendingTotalStories", pendingNonEstimatedStories + pendingEstimatedStories);
			ComponentGroomingTable c = new ComponentGroomingTable();
			c.setComponent(component);
			c.setData(data);
			responseData.add(c);
		}

		return responseData;
	}
	

}
