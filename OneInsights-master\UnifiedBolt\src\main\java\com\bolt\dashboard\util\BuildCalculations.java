package com.bolt.dashboard.util;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.JobDetailsRepo;
import com.mchange.v2.beans.BeansUtils;
import com.bolt.dashboard.core.model.BuildSteps;
import com.bolt.dashboard.core.model.BuildTool;
import com.bolt.dashboard.core.model.BuildToolMetric;
import com.bolt.dashboard.core.model.EffortHistoryModel;
import com.bolt.dashboard.core.model.GitlabValueStream;
import com.bolt.dashboard.core.model.JobDetails;
import com.bolt.dashboard.core.model.JobDetailsMetrics;
import com.bolt.dashboard.core.model.ValueStreamStep;
import com.bolt.dashboard.core.model.ValueStreamStep2;

public class BuildCalculations {
	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
	BuildToolRep buildRepo;
	Set<BuildTool> buildData;
	List<BuildTool> buildDatalist;
	BuildTool lastBuild;
	JobDetails jobDetails;
	JobDetailsRepo jobDetailsRepo;
	MongoTemplate mongo;
	private static final Logger LOGGER = LogManager.getLogger(SprintProgressCalculations.class);
	public BuildCalculations() {
		buildRepo = ctx.getBean(BuildToolRep.class);
		jobDetailsRepo = ctx.getBean(JobDetailsRepo.class);
		if (mongo == null) {
			try {
				mongo = DataConfig.getInstance().mongoTemplate();
			} catch (Exception e) {
				LOGGER.error("Mongo error");
			}
		}
	}
	
//	public static void main(String[] args) {
//		new BuildCalculations().getValueStreamPipelineJobs("Brillio BOLT", "Jenkins");
//	}
	
	public List<ValueStreamStep> getValueStreamSteps(String projectName,String toolName, String repoName ,String branchName){
		
		List<ValueStreamStep> response = new ArrayList<ValueStreamStep>();
		// Check the RepoName are null or not,then update the Query
		if(repoName == null)
		buildData = buildRepo.findByName(projectName);
		else
		buildData = buildRepo.findByNameAndRepoNameAndBranchName(projectName, repoName, branchName);
		buildDatalist = new ArrayList<BuildTool>();
		
		Map<String, List<BuildSteps>> groupedBySteps = buildStepArray();
		buildDatalist.sort(Comparator.comparing(BuildTool::getBuildID));

		if (buildDatalist.size() > 1) {
			lastBuild = buildDatalist.get(buildDatalist.size() - 1);
			int count = 0;
			if (lastBuild.getStepsList() != null) {
				for (BuildSteps step : lastBuild.getStepsList()) {
					ValueStreamStep valueStreamStep = new ValueStreamStep();
					List<BuildSteps> steps = groupedBySteps.get(step.getStepName());
					int successCount = 0;
					int duration = 0;
					int executed = 0;
					int avgSuccessRate = 0, avgDuration = 0;
					boolean failed = false;
					long lastfailTime = 0;
					long mttrDuration = 0l;
					int mttrCount = 0;
					steps.sort(Comparator.comparing(BuildSteps::getStartedTime));
					for (BuildSteps ele : steps) {
						if (ele.getResult() != null && (ele.getResult().equalsIgnoreCase("passed")
								|| ele.getResult().equalsIgnoreCase("pass")
								|| ele.getResult().equalsIgnoreCase("SUCCESS")
								|| ele.getResult().equalsIgnoreCase("SUCCESSFUL"))) {
							successCount++;
							if (failed) {
								mttrDuration = ele.getStartedTime() - lastfailTime;
								mttrCount++;
							}
						} else if (ele.getDuration() != 0) {
							if (!failed) {
								lastfailTime = ele.getStartedTime();
								failed = true;
							}
						}
						if (ele.getDuration() != 0) {
							executed += 1;
						}
						duration += ele.getDuration();

					}

					if (executed != 0) {
						avgSuccessRate = successCount * 100 / executed;
						avgDuration = duration / executed;
					}

					valueStreamStep.setName(step.getStepName());
					valueStreamStep.setAvgDuration(avgDuration);
					valueStreamStep.setAvgSuccessRate(avgSuccessRate);
					valueStreamStep.setLastBuildDate(getFormattedDate(steps.get(steps.size() - 1).getStartedTime()));
					valueStreamStep.setLastBuildState(getState(steps.get(steps.size() - 1)));
					valueStreamStep.setStepNo(++count);
					valueStreamStep.setJobs(step.getStepName());
					if (mttrDuration > 0 && mttrCount > 0) {
						valueStreamStep.setAvgMttr(mttrDuration / mttrCount);
					} else {
						valueStreamStep.setAvgMttr(0);
					}

					response.add(valueStreamStep);
				}
			}
		}
		
		return response;
		
	}

	private Map<String,List<BuildSteps>> buildStepArray(){
		Map<String,List<BuildSteps>> groupedBySteps = new HashMap<String, List<BuildSteps>>();
		for(BuildTool build:buildData) {
			buildDatalist.add(build);
			if(build.getStepsList() != null) {
				for(BuildSteps step:build.getStepsList()) {
					if(groupedBySteps.containsKey(step.getStepName())) {
						if(step.getStartedTime() == 0) {
							step.setStartedTime(build.getTimestamp());
						}
						groupedBySteps.get(step.getStepName()).add(step);
					}else {
						List<BuildSteps> steps = new ArrayList<BuildSteps>();
						steps.add(step);
						groupedBySteps.put(step.getStepName(), steps);
					}
				}
			}
			
		}
		return groupedBySteps;
	}
	
	private String getFormattedDate(long millis){
		DateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(millis);
		return formatter.format(calendar.getTime());
	}
	
	private String getState(BuildSteps step) {
		if(step.getResult() ==null) {
			return "aborted";
		}else if(step.getResult().equalsIgnoreCase("passed") ||step.getResult().equalsIgnoreCase("pass") ||step.getResult().equalsIgnoreCase("SUCCESS")|| step.getResult().equalsIgnoreCase("SUCCESSFUL")){
			return "success";
		}else {
			return "fail";
		}
	}
	
	private String getState(BuildTool lastBuild) {
		BuildToolMetric metric = getMetric(lastBuild.getMetrics(), "result");
		String state = null;
		if(metric !=null) {
			state = metric.getValue().toString();
		}
		if(state ==null) {
			return "aborted";
		}else if(state.equalsIgnoreCase("passed") ||state.equalsIgnoreCase("pass") ||state.equalsIgnoreCase("SUCCESS")|| state.equalsIgnoreCase("SUCCESSFUL")){
			return "success";
		}else {
			return "fail";
		}
		
	}

	public List<ValueStreamStep> getValueStreamPipelineJobs(String projectName,String toolName){
		List<ValueStreamStep> response = new ArrayList<ValueStreamStep>();
		buildData = buildRepo.findByName(projectName);
		jobDetails = jobDetailsRepo.findByProjectName(projectName);
		Map<String,List<String>> groupByValueStream =preparePipeLine();
		if(groupByValueStream!=null) {
		Map<String,List<BuildTool>> jobList = prepareJobList(projectName,groupByValueStream);
		int count=0;
		
		for(Map.Entry<String,List<String>> val:groupByValueStream.entrySet()) {
			ValueStreamStep valueStreamStep = new ValueStreamStep();
			List<BuildTool> jobs = jobList.get(val.getKey());
			if(jobs.size()>0) {
			jobs.sort(Comparator.comparing(BuildTool::getTimestamp));
			int successCount = 0;
			int duration = 0;
			int executed = 0;
			int avgSuccessRate=0,avgDuration=0;
			boolean failed = false;
			long lastfailTime=0;
			long mttrDuration =0l;
			int mttrCount=0;
			for(BuildTool job:jobs) {
				BuildToolMetric status = getMetric(job.getMetrics(), "result");
				String result =null;
				long dur=0;
				if(status!=null) {
					result = (String) status.getValue();
				}
				BuildToolMetric durationMetric = getMetric(job.getMetrics(), "duration"); 
				if(durationMetric != null) {
					dur = Long.parseLong(durationMetric.getValue().toString());
				}
				if(result!=null && (result.equalsIgnoreCase("passed") ||result.equalsIgnoreCase("pass") ||result.equalsIgnoreCase("SUCCESS")|| result.equalsIgnoreCase("SUCCESSFUL"))) {
					successCount++;
					if(failed) {
						mttrDuration = job.getTimestamp() - lastfailTime;
						mttrCount++;
					}
				}else if(dur !=0){
					if(!failed) {
						lastfailTime = job.getTimestamp();
						failed=true;
					}
				}
				if(dur !=0) {
					executed +=1;
				}
				duration+= dur;
			}
			if(executed !=0 ) {
				avgSuccessRate = successCount * 100 / executed;
				avgDuration = duration / executed;
			}
			valueStreamStep.setName(val.getKey());
			valueStreamStep.setAvgDuration(avgDuration);
			valueStreamStep.setAvgSuccessRate(avgSuccessRate);
			valueStreamStep.setLastBuildDate(getFormattedDate(jobs.get(jobs.size()-1).getTimestamp()));
			valueStreamStep.setLastBuildState(getState(jobs.get(jobs.size()-1)));
			valueStreamStep.setStepNo(++count);
			if(mttrDuration> 0 && mttrCount > 0) {
				valueStreamStep.setAvgMttr(mttrDuration/mttrCount);
			}else {
				valueStreamStep.setAvgMttr(0);
			}
			String st= "";
			for(int i=0;i<=val.getValue().size()-2;i++) {
				st =st+val.getValue().get(i)+",";
			}
			st = st+val.getValue().get(val.getValue().size()-1);
			valueStreamStep.setJobs(st);
			response.add(valueStreamStep);
			ArrayList<ValueStreamStep2> arr=new ArrayList<ValueStreamStep2>();
			valueStreamStep.setSubSteps(arr);
			if(val.getValue().size()>1) {
				calculateChildSteps(val,valueStreamStep,jobs);
			}else {
				ValueStreamStep2 child = new ValueStreamStep2();
				BeanUtils.copyProperties(valueStreamStep, child);
				child.setName(val.getValue().get(0));
				child.setJobs(val.getValue().get(0));
				valueStreamStep.getSubSteps().add(child);
				
			}
			}
		}
		}
		return response;
	}
	
	private void calculateChildSteps(Entry<String, List<String>> val, ValueStreamStep valueStreamStep,
			List<BuildTool> jobs) {
		int count=0;
		for(String jobName:val.getValue()) {
			ValueStreamStep2 child=new ValueStreamStep2();
			List<BuildTool> filteredJobs = jobs.stream().filter((ele)->{
				if(ele.getJobName().equalsIgnoreCase(jobName)) {
					return true;
				}else {
					return false;
				}
			}).collect(Collectors.toList());
			
			filteredJobs.sort(Comparator.comparing(BuildTool::getTimestamp));
			int successCount = 0;
			int duration = 0;
			int executed = 0;
			int avgSuccessRate=0,avgDuration=0;
			boolean failed = false;
			long lastfailTime=0;
			long mttrDuration =0l;
			int mttrCount=0;
			for(BuildTool job:filteredJobs) {
				BuildToolMetric status = getMetric(job.getMetrics(), "result");
				String result =null;
				long dur=0;
				if(status!=null) {
					result = (String) status.getValue();
				}
				BuildToolMetric durationMetric = getMetric(job.getMetrics(), "duration"); 
				if(durationMetric != null) {
					dur = Long.parseLong(durationMetric.getValue().toString());
				}
				if(result!=null && (result.equalsIgnoreCase("passed") ||result.equalsIgnoreCase("pass") ||result.equalsIgnoreCase("SUCCESS")|| result.equalsIgnoreCase("SUCCESSFUL"))) {
					successCount++;
					if(failed) {
						mttrDuration = job.getTimestamp() - lastfailTime;
						mttrCount++;
					}
				}else if(dur !=0){
					if(!failed) {
						lastfailTime = job.getTimestamp();
						failed=true;
					}
				}
				if(dur !=0) {
					executed +=1;
				}
				duration+= dur;
			}
			if(executed !=0 ) {
				avgSuccessRate = successCount * 100 / executed;
				avgDuration = duration / executed;
			}
			child.setName(jobName);
			child.setAvgDuration(avgDuration);
			child.setAvgSuccessRate(avgSuccessRate);
			child.setLastBuildDate(getFormattedDate(filteredJobs.get(filteredJobs.size()-1).getTimestamp()));
			child.setLastBuildState(getState(filteredJobs.get(filteredJobs.size()-1)));
			child.setStepNo(++count);
			child.setJobs(jobName);
			child.setAvgMttr(count);
			if(mttrDuration> 0 && mttrCount > 0) {
				child.setAvgMttr(mttrDuration/mttrCount);
			}else {
				child.setAvgMttr(0);
			}
			valueStreamStep.getSubSteps().add(child);
			
		}
		
	}

	private BuildToolMetric getMetric(Set<BuildToolMetric> set,String metricName) {
		for(BuildToolMetric metric:set) {
			if(metric.getName().equalsIgnoreCase(metricName) ) {
				return metric;
			}
		}
		return null;
	}

	private  Map<String,List<String>> preparePipeLine(){
		Map<String,List<String>> groupByValueStream = new HashMap<String, List<String>>();
		if(jobDetails!=null) 
		for(JobDetailsMetrics job:jobDetails.getMetrics()) {
			if(groupByValueStream.containsKey(job.getValueStreamName())) {
				groupByValueStream.get(job.getValueStreamName()).add(job.getJobName());
			}else {
				ArrayList<String> arr = new ArrayList<String>();
				arr.add(job.getJobName());
				groupByValueStream.put(job.getValueStreamName(),arr);
			}
		}
		return groupByValueStream;
	}
	
	private Map<String,List<BuildTool>> prepareJobList(String projectName,Map<String,List<String>> groupByValueStream){
		Map<String,List<BuildTool>> jobList = new HashMap<String, List<BuildTool>>();
		for(Map.Entry<String, List<String>> valueStream:groupByValueStream.entrySet()) {
			Query q=new Query();
			q.addCriteria(Criteria.where("name").is(projectName).and("jobName").in(valueStream.getValue()));
			List<BuildTool> jobs = mongo.find(q, BuildTool.class);
			jobList.put(valueStream.getKey(), jobs);
		}
		return jobList;
	}

	public List<GitlabValueStream> getGitlabValueStreamSteps(String projectName, String toolName) {
		List<GitlabValueStream> gitlabValueStreamArr = new ArrayList<GitlabValueStream>();
		
		String[] branchName = {"DEV", "UAT"};
	    Set<String> repoNameSet = new HashSet<String>();   

		// Step 1. Filter out the Repo from Build
		Iterable<BuildTool> gitlabBuildData = buildRepo.findByNameAndBuildTypeIgnoreCase(projectName, toolName);
		
		gitlabBuildData.forEach (e -> repoNameSet.add(e.getRepoName()) );
		
		// step 2: for Branch and RepoName - with the getValueStreamSteps
		for(String branch: branchName) {
			if(repoNameSet.size()>0)
			repoNameSet.forEach(repoName -> {
				List<ValueStreamStep> valueStream = getValueStreamSteps(projectName, toolName, repoName, branch);
				GitlabValueStream valueObj =	new GitlabValueStream();
				valueObj.setBranchName(branch);
				valueObj.setRepoName(repoName);
				valueObj.setValueStream(valueStream);
				
				gitlabValueStreamArr.add(valueObj);
			});
		}
		
		return gitlabValueStreamArr;
	
	}
}
