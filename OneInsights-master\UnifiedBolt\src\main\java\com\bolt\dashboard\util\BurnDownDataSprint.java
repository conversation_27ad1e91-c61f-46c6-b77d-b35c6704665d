package com.bolt.dashboard.util;

import java.util.List;

public class BurnDownDataSprint {
  
	private String sprintName;
	private List<List<Long>> idealLine;
	private List<List<Long>> timeRemaining;
	private List<List<Long>> prediction;
	private List<List<Long>> timeSpent;
	
	public List<List<Long>> getIdealLine() {
		return idealLine;
	}
	public void setIdealLine(List<List<Long>> idealLine) {
		this.idealLine = idealLine;
	}
	public List<List<Long>> getPrediction() {
		return prediction;
	}
	public void setPrediction(List<List<Long>> prediction) {
		this.prediction = prediction;
	}
	public List<List<Long>> getTimeSpent() {
		return timeSpent;
	}
	public void setTimeSpent(List<List<Long>> timeSpent) {
		this.timeSpent = timeSpent;
	}
	public String getSprintName() {
		return sprintName;
	}
	public void setSprintName(String sprintName) {
		this.sprintName = sprintName;
	}
	public List<List<Long>> getTimeRemaining() {
		return timeRemaining;
	}
	public void setTimeRemaining(List<List<Long>> timeRemaining) {
		this.timeRemaining = timeRemaining;
	}
	
	
	
	
	
	
}
