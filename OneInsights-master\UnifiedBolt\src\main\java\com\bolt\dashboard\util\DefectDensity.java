package com.bolt.dashboard.util;

import java.util.List;

public class DefectDensity {
	List<Double>  defectDenisty;
	List<String>  category;
	List<Double> spCompleted;
	List<Double> defectsAdded;
	public List<Double> getDefectDenisty() {
		return defectDenisty;
	}
	public void setDefectDenisty(List<Double> defectDenisty) {
		this.defectDenisty = defectDenisty;
	}
	public List<String> getCategory() {
		return category;
	}
	public void setCategory(List<String> category) {
		this.category = category;
	}
	public List<Double> getSpCompleted() {
		return spCompleted;
	}
	public void setSpCompleted(List<Double> spCompleted) {
		this.spCompleted = spCompleted;
	}
	public List<Double> getDefectsAdded() {
		return defectsAdded;
	}
	public void setDefectsAdded(List<Double> defectsAdded) {
		this.defectsAdded = defectsAdded;
	}


	
	
	
}
