package com.bolt.dashboard.util;

import java.util.List;

public class DefectParetoModel {
	  List<Integer[]> moduleDefect ;
      List<String[]>  moduleCategory;
      List<Integer> moduleCumulative;
      int sum;
      String type;
	public List<Integer[]> getModuleDefect() {
		return moduleDefect;
	}
	public void setModuleDefect(List<Integer[]> moduleDefect) {
		this.moduleDefect = moduleDefect;
	}
	public List<String[]> getModuleCategory() {
		return moduleCategory;
	}
	public void setModuleCategory(List<String[]> moduleCategory) {
		this.moduleCategory = moduleCategory;
	}
	public List<Integer> getModuleCumulative() {
		return moduleCumulative;
	}
	public void setModuleCumulative(List<Integer> moduleCumulative) {
		this.moduleCumulative = moduleCumulative;
	}
	public int getSum() {
		return sum;
	}
	public void setSum(int sum) {
		this.sum = sum;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
      
	
}
