package com.bolt.dashboard.util;

import java.util.List;

public class DefectProductionSlippage {
	List<Double>  productionSlippage;
	List<Double>  testEffective;
	List<String>  category;
	public List<Double> getProductionSlippage() {
		return productionSlippage;
	}
	public void setProductionSlippage(List<Double> productionSlippage) {
		this.productionSlippage = productionSlippage;
	}
	public List<Double> getTestEffective() {
		return testEffective;
	}
	public void setTestEffective(List<Double> testEffective) {
		this.testEffective = testEffective;
	}
	public List<String> getCategory() {
		return category;
	}
	public void setCategory(List<String> category) {
		this.category = category;
	}

}
