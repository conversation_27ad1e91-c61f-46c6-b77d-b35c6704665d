package com.bolt.dashboard.util;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.joda.time.DateTime;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.EngAreaData;
import com.bolt.dashboard.core.model.EngParamData;
import com.bolt.dashboard.core.model.EngRuleData;
import com.bolt.dashboard.core.model.EngRulesBasedOnMonth;
import com.bolt.dashboard.core.model.EngScorecardSubjectiveSprintData;
import com.bolt.dashboard.core.model.EngSubParamData;
import com.bolt.dashboard.core.model.EngagementConfig;
import com.bolt.dashboard.core.model.EngagementConfigData;
import com.bolt.dashboard.core.model.EngagementConfigMetrics;
import com.bolt.dashboard.core.model.EngagementGuides;
import com.bolt.dashboard.core.model.EngagementRule;
import com.bolt.dashboard.core.model.EngagementScorecardSubjectiveData;
import com.bolt.dashboard.core.model.PortfolioConfig;
import com.bolt.dashboard.core.model.ProjectModel;
import com.bolt.dashboard.core.model.TeamIndexConfiguration;
import com.bolt.dashboard.core.model.TeamIndexQuestion;
import com.bolt.dashboard.core.repository.EngScorecardSubjectiveDataRepo;
import com.bolt.dashboard.core.repository.EngagementConfigRepo;
import com.bolt.dashboard.core.repository.EngagementScoreRepo;
import com.bolt.dashboard.core.repository.PortfolioConfigRepo;
import com.bolt.dashboard.core.repository.ProjectRepo;
import com.bolt.dashboard.core.repository.TeamIndexConfigRepo;

public class EIScoresCalculation {

	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
	private static final Logger LOGGER = LogManager.getLogger(EIScoresCalculation.class);
	List<EngScorecardSubjectiveSprintData> engSubjectiveDataSprint = null;
	EngScorecardSubjectiveDataRepo engSubjectiveRepo = null;
	EngagementScoreRepo engRuleRepo = null;
	EngagementRule engRule = null;
	EngRulesBasedOnMonth engRuleMonthTemp = null;
	EngagementConfig eiConfig = null;
	EngagementConfigMetrics eiConfigTemp = null;
	EngagementIndexCommon engIndexCommon = new EngagementIndexCommon();
	ProjectRepo projectRepo = null;
	PortfolioConfigRepo portfolioConfigRepo = null;
	Map<String,TeamIndexQuestion> teamIndexQuestions =null; 

//	public static void main(String[] args) {
//		new EIScoresCalculation().processEIScores("News Insights");
//	}

	public void processEIScores(String pName) {
		
		boolean dataPresent = true;

		init(pName);
		if (!engSubjectiveDataSprint.isEmpty()) {
			engSubjectiveDataSprint = engSubjectiveDataSprint.stream()
					.sorted(Comparator.comparingLong(EngScorecardSubjectiveSprintData::getStDate))
					.collect(Collectors.toList());
			long firstMontTimeStamp = engSubjectiveDataSprint.get(0).getStDate();
			DateTime firstMonth = new DateTime(firstMontTimeStamp);
			while (dataPresent) {
				DateTime startMonth = new DateTime(firstMonth.getYear(), firstMonth.getMonthOfYear(), 1, 0, 0, 0);
				DateTime endMonth = startMonth.plusDays(firstMonth.dayOfMonth().getMaximumValue() - 1);
				firstMonth = endMonth.plusDays(1);
				List<EngScorecardSubjectiveSprintData> tempEngScoreSubjectiveSprint = engSubjectiveDataSprint.stream()
						.filter(p -> {
							return p.getStDate() <= endMonth.getMillis() && p.getStDate() >= startMonth.getMillis();
						}).collect(Collectors.toList());
				if (tempEngScoreSubjectiveSprint.isEmpty()) {
					dataPresent = false;
				} else {

					eiRuleCalc(tempEngScoreSubjectiveSprint, startMonth, pName);

				}

			}
		}
		engRuleRepo.save(engRule);

	}

	private String getdisplayMonth(DateTime time) {
		String month = "";
		LOGGER.info(time.getMonthOfYear());
		switch (time.getMonthOfYear()) {
		case 1: {
			month = "Jan,";
			break;
		}
		case 2: {
			month = "Feb,";
			break;
		}
		case 3: {
			month = "Mar,";
			break;
		}
		case 4: {
			month = "Apr,";
			break;
		}
		case 5: {
			month = "May,";
			break;
		}
		case 6: {
			month = "Jun,";
			break;
		}
		case 7: {
			month = "July,";
			break;
		}
		case 8: {
			month = "Aug,";
			break;
		}
		case 9: {
			month = "Sep,";
			break;
		}
		case 10: {
			month = "Oct,";
			break;
		}
		case 11: {
			month = "Nov,";
			break;
		}
		default:
			month = "Dec,";

		}
		String year = String.valueOf(time.getYear());
		return month + year.substring(2);

	}

	private void eiRuleCalc(List<EngScorecardSubjectiveSprintData> tempEngScoreSubjectiveSprint, DateTime startMonth,
			String pName) {
		
		// long tempMilis = new
		// DateTime(tempEngScoreSubjectiveSprint.get(0).getStDate()., monthOfYear,
		// dayOfMonth, hourOfDay, minuteOfHour)

		List<EngRulesBasedOnMonth> engRulesMonthsData = engRule.getListOfRulesBasedOnMonths().stream().filter(temp -> {
			return Long.parseLong(temp.getMonthName()) == startMonth.getMillis();
		}).collect(Collectors.toList());

		if (engRulesMonthsData.isEmpty()) {
			engRuleMonthTemp = new EngRulesBasedOnMonth();
			engRuleMonthTemp.setMonthName(String.valueOf(startMonth.getMillis()));
			engRuleMonthTemp.setDisplayMonth(getdisplayMonth(startMonth));
			engRule.getListOfRulesBasedOnMonths().add(engRuleMonthTemp);
		} else {
			engRuleMonthTemp = engRulesMonthsData.get(0);
		}
		engRuleMonthTemp.setAreaData(new ArrayList<EngAreaData>());
		engRuleMonthTemp.setParamData(new ArrayList<EngParamData>());
		engRuleMonthTemp.setSubParamData(new ArrayList<EngSubParamData>());

		int totalCommitedSps = 0;
		int totalCompletedSps = 0;
		long technicalDebt = 0;
		float unitTestCoverage = 0;
		int regressionCoverage = 0;
		int readinessIndex1 = 0;
		int readinessIndex2 = 0;
		int testEffectiveness = 0;
		int capacityUtilization = 0;
		for (EngScorecardSubjectiveSprintData engSubjective : tempEngScoreSubjectiveSprint) {
			if(engSubjective.getCommitedStoryPoints().get("score")!=null) {
			totalCommitedSps = totalCommitedSps + Integer.parseInt(engSubjective.getCommitedStoryPoints().get("score"));
			}
			if(engSubjective.getVelocity().get("score")!=null) {
			totalCompletedSps = totalCompletedSps + Integer.parseInt(engSubjective.getVelocity().get("score"));
			}
			
			if(engSubjective.getTechnicalDebt().get("score")!=null) {
			technicalDebt = Long.parseLong(engSubjective.getTechnicalDebt().get("score"));
			}
			if(engSubjective.getCoverage().get("score")!=null) {
			unitTestCoverage = unitTestCoverage + Float.parseFloat(engSubjective.getCoverage().get("score"));
			}
			if(engSubjective.getTestAutomation().get("score")!=null) {
			regressionCoverage = regressionCoverage + Integer.parseInt(engSubjective.getTestAutomation().get("score"));
			}
			if(engSubjective.getReadinessIndex1().get("score")!=null) {
			readinessIndex1 = readinessIndex1 + Integer.parseInt(engSubjective.getReadinessIndex1().get("score"));
			}
			if(engSubjective.getReadinessIndex2().get("score")!=null) {
			readinessIndex2 = readinessIndex2 + Integer.parseInt(engSubjective.getReadinessIndex2().get("score"));
			}
			int defectsSIT=0;
			int commonDefect=0;
			int uatDefect=0;
			int customerRepDef=0;
			if(engSubjective.getSitDefects().get("score")!=null) {
			 defectsSIT = Integer.parseInt(engSubjective.getSitDefects().get("score"));
			}
			if(engSubjective.getUatDefects().get("score")!=null ) {
				uatDefect=Integer.parseInt(engSubjective.getUatDefects().get("score"));
			}
			if(engSubjective.getCustomerReportedDefects().get("score")!=null ) {
				customerRepDef=Integer.parseInt(engSubjective.getCustomerReportedDefects().get("score"));
			}
			commonDefect = uatDefect+customerRepDef;
			if (commonDefect > 0 && defectsSIT > 0) {
				testEffectiveness = testEffectiveness
						+ (Integer.parseInt(engSubjective.getSitDefects().get("score")) * 100)
								/ commonDefect;
			}
			int plannedCapacity =0;
			int hoursSpent=0;
			if(engSubjective.getCapacity().get("score")!=null) {
		   plannedCapacity = Integer.parseInt(engSubjective.getCapacity().get("score"));
			}
			if(engSubjective.getHoursSpent().get("score")!=null) {
			 hoursSpent = Integer.parseInt(engSubjective.getHoursSpent().get("score"));
			}
			int calcCapacityUtil=0;
			if(plannedCapacity>0) {
		 calcCapacityUtil = ((plannedCapacity - hoursSpent) * 100) / plannedCapacity;
			}
			if (calcCapacityUtil < 0) {
				calcCapacityUtil = calcCapacityUtil * -1;
			}
			capacityUtilization = capacityUtilization + calcCapacityUtil;

		}
		int scopeAdherencePercentage = (totalCompletedSps / totalCommitedSps) * 100;
		List<EngRuleData> rulesData = engRuleMonthTemp.getRuleData();
		// Execution Excelence

		eiConfigTemp = searchEIConfig(eiConfig.getEngMetrics(), "Execution Excellence").get(0);
		// Scope Adherence
		List<EngRuleData> rulesTemp = searchRule(rulesData, "Scope Adherence");
		EngRuleData engRuleData = null;
		if (rulesTemp.isEmpty()) {

			engRuleData = new EngRuleData("Scope Adherence", "Schedule Adherence", "Execution Excellence",
					eiConfigTemp.getWeightage());
			engRuleMonthTemp.getRuleData().add(engRuleData);
		} else {
			engRuleData = rulesTemp.get(0);

		}
		double score = getScoreByEngGuides(eiConfigTemp.getEngData(), "Scope Adherence", scopeAdherencePercentage);
		engRuleData.setScore(score);

		// N+1
		rulesTemp = searchRule(rulesData, "Requirement Readiness for the sprint");

		if (rulesTemp.isEmpty()) {

			engRuleData = new EngRuleData("Requirement Readiness for the sprint", "Requirement Readiness / Stability",
					"Execution Excellence", eiConfigTemp.getWeightage());
			engRuleMonthTemp.getRuleData().add(engRuleData);
		} else {
			engRuleData = rulesTemp.get(0);

		}
		double val1 = (double)readinessIndex1 / tempEngScoreSubjectiveSprint.size();
		score = getScoreByEngGuides(eiConfigTemp.getEngData(), "Requirement Readiness for the sprint",
				val1);

		engRuleData.setScore(score);

		// N+2

		rulesTemp = searchRule(rulesData, "N+2 Readiness for future");

		if (rulesTemp.isEmpty()) {

			engRuleData = new EngRuleData("N+2 Readiness for future", "Requirement Readiness / Stability",
					"Execution Excellence", eiConfigTemp.getWeightage());
			engRuleMonthTemp.getRuleData().add(engRuleData);
		} else {
			engRuleData = rulesTemp.get(0);

		}
		double val = (double)readinessIndex2 / tempEngScoreSubjectiveSprint.size();
		score = getScoreByEngGuides(eiConfigTemp.getEngData(), "N+2 Readiness for future",
				val);

		engRuleData.setScore(score);
		// Test Effectiveness
		rulesTemp = searchRule(rulesData, "Test Effectiveness");

		if (rulesTemp.isEmpty()) {

			engRuleData = new EngRuleData("Test Effectiveness", "Test Effectiveness/Quality", "Execution Excellence",
					eiConfigTemp.getWeightage());
			engRuleMonthTemp.getRuleData().add(engRuleData);
		} else {
			engRuleData = rulesTemp.get(0);

		}
		double val3 = (double)testEffectiveness / tempEngScoreSubjectiveSprint.size();
		score = getScoreByEngGuides(eiConfigTemp.getEngData(), "Test Effectiveness",
				val3 );

		engRuleData.setScore(score);

		// Capacity Utilization
		rulesTemp = searchRule(rulesData, "Capacity Utilization");

		if (rulesTemp.isEmpty()) {

			engRuleData = new EngRuleData("Capacity Utilization", "Productivity", "Execution Excellence",
					eiConfigTemp.getWeightage());
			engRuleMonthTemp.getRuleData().add(engRuleData);
		} else {
			engRuleData = rulesTemp.get(0);

		}
		double val4 = (double)capacityUtilization / tempEngScoreSubjectiveSprint.size();
		score = getScoreByEngGuides(eiConfigTemp.getEngData(), "Capacity Utilization",
				val4);

		engRuleData.setScore(score);
		
		//Velocity
		rulesTemp = searchRule(rulesData, "Velocity");

		if (rulesTemp.isEmpty()) {

			engRuleData = new EngRuleData("Velocity", "Productivity",
					"Execution Excellence", eiConfigTemp.getWeightage());
			engRuleMonthTemp.getRuleData().add(engRuleData);
		} else {
			engRuleData = rulesTemp.get(0);

		}
		score =calcScoreVelocity(totalCompletedSps,totalCommitedSps);

		engRuleData.setScore(score);
		

		// Automation
		eiConfigTemp = searchEIConfig(eiConfig.getEngMetrics(), "Automation").get(0);
		
		//Static Code Analysis
		rulesTemp = searchRule(rulesData, "Code Quality (Static Code Analysis)");

		if (rulesTemp.isEmpty()) {

			engRuleData = new EngRuleData("Code Quality (Static Code Analysis)", "Automation Coverage", "Automation",
					eiConfigTemp.getWeightage());
			engRuleMonthTemp.getRuleData().add(engRuleData);
		} else {
			engRuleData = rulesTemp.get(0);

		}
		score = getscoreForStaticCodeAnalysis(technicalDebt);
		
		engRuleData.setScore(score);
		// Unit Test Automation coverage
		rulesTemp = searchRule(rulesData, "Unit Test Automation Coverage");

		if (rulesTemp.isEmpty()) {

			engRuleData = new EngRuleData("Unit Test Automation Coverage", "Automation Coverage", "Automation",
					eiConfigTemp.getWeightage());
			engRuleMonthTemp.getRuleData().add(engRuleData);
		} else {
			engRuleData = rulesTemp.get(0);

		}

		score = getScoreByEngGuides(eiConfigTemp.getEngData(), "Regression Coverage",
				unitTestCoverage / tempEngScoreSubjectiveSprint.size());
		engRuleData.setScore(score);

		// Regression Coverage
		rulesTemp = searchRule(rulesData, "Regression Coverage");
		if (rulesTemp.isEmpty()) {

			engRuleData = new EngRuleData("Regression Coverage", "Automation Coverage", "Automation",
					eiConfigTemp.getWeightage());
			engRuleMonthTemp.getRuleData().add(engRuleData);
		} else {
			engRuleData = rulesTemp.get(0);

		}
		double val5 = (double)regressionCoverage / tempEngScoreSubjectiveSprint.size();
		score = getScoreByEngGuides(eiConfigTemp.getEngData(), "Regression Coverage",
				val5);
		engRuleData.setScore(score);

		processRemainingData(engRuleMonthTemp, pName);

	}

	private double getscoreForStaticCodeAnalysis(long technicalDebt) {
		if(teamIndexQuestions!=null) {
		 TeamIndexQuestion teamIndexQ=	teamIndexQuestions.get("technicalDebt");
			if(teamIndexQ.getGoal().equals("")) {
				return 1.0;
			}else {
				long goal= Long.parseLong(teamIndexQ.getGoal());
				if(goal>technicalDebt) {
					return 5.0;
				}else if(goal==technicalDebt) {
					return 3.0;
				}else {
					return 1.0;
				}
			}
		}else {
			return 1.0;
		}
		
		
	}

	private double calcScoreVelocity(int totalCompletedSps, int totalCommitedSps) {
		
		if(totalCommitedSps==totalCompletedSps) {
			return 3.0;
		}else if(totalCommitedSps<totalCompletedSps) {
			return 5.0;
		}
		return 1.0;
	}

	private void processRemainingData(EngRulesBasedOnMonth engRuleMonth, String pName) {
		
		Map<String, List<EngRuleData>> paramGroupRuleData = engRuleMonth.getRuleData().stream()
				.collect(Collectors.groupingBy(EngRuleData::getParameter));
		engIndexCommon.processSubParam(paramGroupRuleData, eiConfig.getEngMetrics(), engRuleMonth);
		double engScoreparam = engIndexCommon.processParam(engRuleMonth.getSubParamData(), eiConfig.getEngMetrics(),
				engRuleMonth);
		engRuleMonth.setEngScore(engScoreparam);
		double engScoreArea = engIndexCommon.processArea(paramGroupRuleData, eiConfig.getEngMetrics(), engRuleMonth);

		saveEngScoreAlm(pName, engRuleMonth.getMonthName(), engScoreArea);
		saveEngScorePort(pName, engRuleMonth.getMonthName(), engScoreArea);

	}

	private double getScoreByEngGuides(List<EngagementConfigData> engConfigData, String ruleName, double value) {

		List<EngagementConfigData> configData = engConfigData.stream().filter(temp -> {
			return temp.getRuleName().equals(ruleName);
		}).collect(Collectors.toList());
		EngagementConfigData config = configData.get(0);
		List<EngagementGuides> configEngGuides = config.getEngGuides();
		for (EngagementGuides engGuide : configEngGuides) {
			if (value <= engGuide.getMaxValue() && value >= engGuide.getMinValue()) {
				return engGuide.getScore();
			}
		}

		return 0.0;

	}

	private List<EngagementConfigMetrics> searchEIConfig(List<EngagementConfigMetrics> engConfigMetrics,
			String parameter) {
		return engConfigMetrics.stream().filter(temp -> {
			return temp.getParameter().equals(parameter);
		}).collect(Collectors.toList());

	}

	private List<EngRuleData> searchRule(List<EngRuleData> rulesData, String rule) {
		return rulesData.stream().filter(temp -> {
			return temp.getRuleName().equals(rule);
		}).collect(Collectors.toList());

	}

	private void init(String pName) {
		

		engSubjectiveRepo = ctx.getBean(EngScorecardSubjectiveDataRepo.class);
		EngagementScorecardSubjectiveData subjective = engSubjectiveRepo.findByPName(pName);
		engRuleRepo = ctx.getBean(EngagementScoreRepo.class);

		List<EngagementRule> engRuleData = engRuleRepo.findByProjectName(pName);
		if (subjective != null) {
			engSubjectiveDataSprint = subjective.getEngScorecardSprintData();

		}
		portfolioConfigRepo = ctx.getBean(PortfolioConfigRepo.class);
		PortfolioConfig portfolioConfig = portfolioConfigRepo.findByProjectName(pName).get(0);
		if (engRuleData.isEmpty()) {

			engRule = new EngagementRule();
			engRule.setProjectName(pName);
			engRule.setTowerName(portfolioConfig.getTowerName());
			engRule.setListOfRulesBasedOnMonths(new ArrayList<EngRulesBasedOnMonth>());
		} else {
			engRule = engRuleData.get(0);
		}

		EngagementConfigRepo engConfigRepo = ctx.getBean(EngagementConfigRepo.class);
		List<EngagementConfig> eiConfigdata = engConfigRepo.findByTowerName(portfolioConfig.getTowerName());
		if (!eiConfigdata.isEmpty()) {
			eiConfig = eiConfigdata.get(0);
		}
		projectRepo = ctx.getBean(ProjectRepo.class);
		TeamIndexConfigRepo teamIndexConfigRepo= ctx.getBean(TeamIndexConfigRepo.class);
		List<TeamIndexConfiguration> teamIndexConfigs=teamIndexConfigRepo.findByPName(pName);
		teamIndexConfigs= teamIndexConfigs.stream().sorted(Comparator.comparingLong(TeamIndexConfiguration::getTimestamp)).collect(Collectors.toList());
		if(!teamIndexConfigs.isEmpty()) {
			teamIndexQuestions=teamIndexConfigs.get(teamIndexConfigs.size()-1).getTeamIndexQuestions();
		}

	}

	public void saveEngScorePort(String projectName, String month, double engScore) {
		
		try {
			List<PortfolioConfig> result = portfolioConfigRepo.findByProjectName(projectName);
			PortfolioConfig proj = null;
			if (!result.isEmpty()) {
				proj = result.get(0);
			}
			if(proj!=null) {
				SortedMap<String, Double> engScrMap = proj.getEngScores();
	
				if (engScrMap == null) {
					engScrMap = new TreeMap<>();
				}
				engScrMap.put(month, engScore);
				proj.setEngScores(engScrMap);
				portfolioConfigRepo.save(proj);
			}

		} catch (Exception e) {
			LOGGER.info(e);
		}

	}

	public void saveEngScoreAlm(String projectName, String month, double engScore) {
		
		try {
			List<ProjectModel> proj = projectRepo.findByProjectName(projectName);
			if (!proj.isEmpty()) {
				SortedMap<String, Double> engScrMap = proj.get(0).getEngScores();
				if (engScrMap == null) {
					engScrMap = new TreeMap<String, Double>();
				}
				engScrMap.put(month, engScore);
				proj.get(0).setEngScores(engScrMap);
				projectRepo.save(proj);
			}
			// return "Success";
		} catch (Exception e) {
			LOGGER.info(e);
			// return "Failure";
		}

	}

}
