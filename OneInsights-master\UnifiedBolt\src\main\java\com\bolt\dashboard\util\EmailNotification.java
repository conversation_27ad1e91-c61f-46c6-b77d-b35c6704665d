package com.bolt.dashboard.util;

import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Properties;
import java.util.regex.Pattern;

import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.CollaborativeMailModel;
import com.bolt.dashboard.core.model.MailSetup;
import com.bolt.dashboard.core.repository.MailSetupRepo;


public class EmailNotification {

	private static final Logger LOGGER = LogManager.getLogger(EmailNotification.class);

	@SuppressWarnings("null")
	public void sendMail(CollaborativeMailModel model) {
		String dwnldFolder = System.getProperty("user.dir");
//		FileOutputStream fo = null;
		long lastUpdate = 1;
		String[] smtpFieldsArray = new String[6];
		AnnotationConfigApplicationContext context = new AnnotationConfigApplicationContext(DataConfig.class);
		MailSetupRepo repo = context.getBean(MailSetupRepo.class);
		List<MailSetup> mailSetupModelList = repo.findAll();
		Iterator<MailSetup> it = mailSetupModelList.iterator();
		MailSetup mailSetup;
		while (it.hasNext()) {

		    mailSetup = it.next();
		    smtpFieldsArray[0] = mailSetup.getHost();
		    smtpFieldsArray[1] = mailSetup.getPort();
		    smtpFieldsArray[2] = mailSetup.getUserName();
		    smtpFieldsArray[3] = mailSetup.getPassword();
		    smtpFieldsArray[4] = String.valueOf(mailSetup.isStarttls());
		}
		
		  model.setTimeStamp(new Date().getTime());
		  
		
	
		Properties props = new Properties();
		props.put(ConstantVariable.MAIL_SMTP_AUTH, smtpFieldsArray[4]);
		props.put(ConstantVariable.MAIL_SMTP_ENABLE, smtpFieldsArray[4]);
		props.put(ConstantVariable.MAIL_SMTP_HOST, smtpFieldsArray[0]);
		props.put(ConstantVariable.MAIL_SMTP_PORT, smtpFieldsArray[1]);
		props.put(ConstantVariable.MAIL_SMTP_SSL_TRUST,smtpFieldsArray[0]);
		props.setProperty(ConstantVariable.MAIL_SMTP_SSL_PROTOCOLS, "TLSv1.2");
		LOGGER.info("property file updated.....");
		Session session = Session.getInstance(props, new javax.mail.Authenticator() {
		    protected PasswordAuthentication getPasswordAuthentication() {
			return new PasswordAuthentication(smtpFieldsArray[2], smtpFieldsArray[3]);
		    }
		});
		LOGGER.info("Successfully Authenticated.........");

		try {
			MimeMessage message = new MimeMessage(session);
			message.setFrom(new InternetAddress(smtpFieldsArray[2]));

			// byte[] decodes = Base64.getMimeDecoder().decode(model.getFileName());

			// fo = new FileOutputStream(dwnldFolder + "\\test215.png");
			// fo.write(decodes);

			message.setSubject(model.getSubject());

			MimeMultipart multipart = new MimeMultipart("related");

			BodyPart messageBodyPart = new MimeBodyPart();
			if (model.getToAddress().contains(";")) {
				String[] toAddressCollection = model.getToAddress().split(";");
				StringBuilder toAddress = new StringBuilder();
				for (String to : toAddressCollection) {
					toAddress.append(to + ",");
					message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(toAddress.toString()));
				}
			} else {
				message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(model.getToAddress()));
			}

			String[] toAddress = model.getToAddress().split(Pattern.quote("."));
			String htmlText = "";
			if (model.getFileName().equals("")) {
				htmlText = /* "Hi " + toAddress[0] + ",\n" + */ model.getComment();
			} else {
				htmlText = /* "Hi " + toAddress[0] + ",\n" + */ model.getComment() + "\n" + "<img src=\"cid:image\">";
			}

			messageBodyPart.setContent(htmlText, "text/html");
			multipart.addBodyPart(messageBodyPart);

			// messageBodyPart = new MimeBodyPart();
			// DataSource fds = new FileDataSource(dwnldFolder + "\\test215.png");

			// messageBodyPart.setDataHandler(new DataHandler(fds));
			messageBodyPart.setHeader("Content-ID", "<image>");

			multipart.addBodyPart(messageBodyPart);
			LOGGER.info("All datad addd to Mutltipart....");
			message.setContent(multipart);

			new Thread(new Runnable() {
				public void run() {
					try {
						Transport.send(message);
						LOGGER.info("mail sent successfully");
					} catch (MessagingException e) {
						LOGGER.info(e);
					}
				}
			}).start();

			LOGGER.info("Mail put in queue");
			// saveMailDetails(model);

		} catch (Exception e) {
			LOGGER.error("Error In Email ", e);
			LOGGER.info(e);
		} finally {

//			try {
//				if (fo != null) {
//					fo.close();
//				}
//
//			} catch (IOException e) {
//				LOGGER.info(e);
//			}
		}
		// return new DataResponse<MailStatusModel>(messageSend, lastUpdate);

	}

}
