package com.bolt.dashboard.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.web.multipart.MultipartFile;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.EngAreaData;
import com.bolt.dashboard.core.model.EngParamData;
import com.bolt.dashboard.core.model.EngRuleData;
import com.bolt.dashboard.core.model.EngRulesBasedOnMonth;
import com.bolt.dashboard.core.model.EngSubParamData;
import com.bolt.dashboard.core.model.EngagementConfig;
import com.bolt.dashboard.core.model.EngagementConfigMetrics;
import com.bolt.dashboard.core.model.EngagementRule;
import com.bolt.dashboard.core.model.PortfolioConfig;
import com.bolt.dashboard.core.model.ProjectModel;
import com.bolt.dashboard.core.repository.EngagementConfigRepo;
import com.bolt.dashboard.core.repository.EngagementScoreRepo;
import com.bolt.dashboard.core.repository.PortfolioConfigRepo;
import com.bolt.dashboard.core.repository.ProjectRepo;

public class EngagementExcelUtil {
	private static final Logger LOGGER = LogManager.getLogger(EngagementExcelUtil.class);
	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
	EngagementIndexCommon engIndexCommon =  new EngagementIndexCommon();
	EngagementScoreRepo engRuleRepo=null;
	EngagementRule engRule = null;
	EngRulesBasedOnMonth  engRuleMonth=null; 
	EngagementConfig engConfig= null;
	ProjectRepo projectRepo=null;
	PortfolioConfigRepo portfolioConfigRepo=null;

//	public static void main(String[] args) {
//		//new EngagementExcelUtil().readExcel("Project Eng", "1556649000000","May,19");
//		new EngagementExcelUtil().readExcel("Project Eng", "1554057000000","Apr,19");
//	}

	@SuppressWarnings("resource")
	public boolean readExcel(MultipartFile file2, String projName, String monthName, String displayMonth) {
	
	
		init(projName, monthName,displayMonth);
		List<EngagementConfigMetrics> engConfigMetric= engConfig.getEngMetrics();
		List<EngagementConfigMetrics> engConfigMetricTemp= null;
		
		
     List<EngRuleData> engRulesData=engRuleMonth.getRuleData();
		try {
			
			File excelFile = new File(projName+"_t_"+monthName+".xlsx");
			excelFile.createNewFile();
			FileOutputStream fos = new FileOutputStream(excelFile);
			fos.write(file2.getBytes());
			
//			URL resource = getClass().getClassLoader().getResource(projName+"_t_"+monthName+".xlsx");
//			File excelFile= new File(resource.toURI());
		FileInputStream file = new FileInputStream(excelFile);
//			File excelFile = new File(projName+"_t_"+monthName+".xlsx");
//			//excelFile.createNewFile();
//			FileInputStream file = new FileInputStream(excelFile);
			Workbook workbook = new XSSFWorkbook(file);

			Sheet sheet = workbook.getSheetAt(0);
			//Map<Integer, List<String>> data = new HashMap<>();

			int rowLength = sheet.getLastRowNum();
			for (int index = 1; index <= rowLength; index++) {
				
				// data.put(i, new ArrayList<String>());
				Row row = sheet.getRow(index);
				if(row!=null) {
					
				int cellLength = row.getLastCellNum();
				EngRuleData engRule= new EngRuleData();
				if(cellLength>0) {
					
					engRulesData.add(engRule);
				}
				
				for (int indexCells = 0; indexCells <= cellLength; indexCells++) {
					Cell cell = row.getCell(indexCells);
					
					if(indexCells==0) {
						engRule.setParameter(cell.getStringCellValue().trim().replaceAll("\n", " "));
						engConfigMetricTemp= engConfigMetric.stream().filter(engConf->engConf.getParameter().equals(engRule.getParameter())).collect(Collectors.toList());
						engRule.setWeightage(engConfigMetricTemp.get(0).getWeightage());
					}else if(indexCells==1) {
						engRule.setSubParameter(cell.getStringCellValue().trim());
					}else if(indexCells==2) {
						engRule.setRuleName(cell.getStringCellValue().trim());
					}else if(indexCells==4) {
						engRule.setScore(cell.getNumericCellValue());
					}
					
					
					

				}
			 }

			}	
		Map<String,List<EngRuleData>> paramGroupRuleData= engRulesData.stream().collect(Collectors.groupingBy(EngRuleData::getParameter));
		engIndexCommon.processSubParam(paramGroupRuleData,engConfigMetric,engRuleMonth);
		double engScoreparam=engIndexCommon.processParam(engRuleMonth.getSubParamData(),engConfigMetric,engRuleMonth);
		engRuleMonth.setEngScore(engScoreparam);
		double engScoreArea=engIndexCommon.processArea(paramGroupRuleData,engConfigMetric,engRuleMonth);
		saveEngScoreAlm(projName, engRuleMonth.getMonthName(), engScoreArea);
		saveEngScorePort(projName, engRuleMonth.getMonthName(), engScoreArea);
		saveEngRuleData();
		excelFile.delete();
		return true;
		} catch (Exception e) {

			LOGGER.info(e);
			return false;
		} 
	 
	}

	/*
	 * private boolean createFile(MultipartFile file, String projName, String
	 * monthName) {
	 * 
	 * try { File excelFile = new File(projName+"_t_"+monthName+".xlsx");
	 * excelFile.createNewFile(); FileOutputStream fos = new
	 * FileOutputStream(excelFile); fos.write(file.getBytes()); fos.close(); } catch
	 * (IOException e) {
	 * 
	 * 
	 * LOGGER.error(e); return false; } return true; }
	 */

	private void saveEngRuleData() {

		engRuleRepo.save(engRule);
		EIScoresCalculation eiScoreCalc= new EIScoresCalculation();
		eiScoreCalc.processEIScores(engRule.getProjectName());
	
}



	



	private void init(String projName, String monthName, String displayMonth) {
	
		 engRuleRepo = ctx.getBean(EngagementScoreRepo.class);
		List<EngagementRule> engRules = engRuleRepo.findByProjectName(projName);
		List<EngRulesBasedOnMonth> engRulesMonthData=null;
		if (!engRules.isEmpty()) {
			engRule = engRules.get(0);
		}else {
			engRule =new EngagementRule();
			engRule.setProjectName(projName);
			
		}
		 engRulesMonthData= engRule.getListOfRulesBasedOnMonths();
	List<EngRulesBasedOnMonth> engRulesMonthDatatemp= engRulesMonthData.stream()
				.filter(engRuleMonth-> engRuleMonth.getMonthName().equals(monthName)).collect(Collectors.toList());
		if(engRulesMonthDatatemp.isEmpty()) {
			
			engRuleMonth= new EngRulesBasedOnMonth();
			engRuleMonth.setMonthName(monthName);
			engRuleMonth.setDisplayMonth(displayMonth);
			engRulesMonthData.add(engRuleMonth);
			engRuleMonth.setRuleData(new ArrayList<EngRuleData>());
		}else {
			engRuleMonth=engRulesMonthDatatemp.get(0);
			
		}
		
		engRule.setListOfRulesBasedOnMonths(engRulesMonthData);
		engRuleMonth.setAreaData(new ArrayList<EngAreaData>());
		engRuleMonth.setParamData(new ArrayList<EngParamData>());
		
		engRuleMonth.setSubParamData(new ArrayList<EngSubParamData>());
		
		 portfolioConfigRepo = ctx.getBean(PortfolioConfigRepo.class);
		PortfolioConfig portFolioConfig=portfolioConfigRepo.findByProjectName(projName).get(0);
         EngagementConfigRepo engconfigRepo= ctx.getBean(EngagementConfigRepo.class); 		
		  List<EngagementConfig> engagementConfigs =engconfigRepo.findByTowerName(portFolioConfig.getTowerName());
		  engRule.setTowerName(portFolioConfig.getTowerName());
		  if(!engagementConfigs.isEmpty()) {
			  engConfig=engagementConfigs.get(0);
		  }
		  projectRepo = ctx.getBean(ProjectRepo.class);

	}

	public void saveEngScorePort(String projectName, String month, double engScore) {

		try {
			List<PortfolioConfig> result = portfolioConfigRepo.findByProjectName(projectName);
			PortfolioConfig proj = null;
			if (!result.isEmpty()) {
				proj = result.get(0);

				if (proj != null) {
					SortedMap<String, Double> engScrMap = proj.getEngScores();

					if (engScrMap == null) {
						engScrMap = new TreeMap<>();
					}
					engScrMap.put(month, engScore);
					proj.setEngScores(engScrMap);
					portfolioConfigRepo.save(proj);
				}
			}

		} catch (Exception e) {

			LOGGER.info(e);

		}
		
	}
	
	public void saveEngScoreAlm(String projectName, String month, double engScore) {
	
		try {
			List<ProjectModel> proj=projectRepo.findByProjectName(projectName);
			if(!proj.isEmpty()) {
            SortedMap<String,Double> engScrMap=proj.get(0).getEngScores();
            if(engScrMap==null){
	           engScrMap=new TreeMap<>();
                }
			     engScrMap.put(month, engScore);
			     proj.get(0).setEngScores(engScrMap);
			    projectRepo.save(proj);
			}
		} catch (Exception e) {
			LOGGER.info(e);
		}
		
	}

}
