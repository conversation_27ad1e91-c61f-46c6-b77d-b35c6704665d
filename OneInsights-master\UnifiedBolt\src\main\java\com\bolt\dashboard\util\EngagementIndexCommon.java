package com.bolt.dashboard.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.bolt.dashboard.core.model.EngAreaData;
import com.bolt.dashboard.core.model.EngParamData;
import com.bolt.dashboard.core.model.EngRuleData;
import com.bolt.dashboard.core.model.EngRulesBasedOnMonth;
import com.bolt.dashboard.core.model.EngSubParamData;
import com.bolt.dashboard.core.model.EngagementConfigMetrics;

public class EngagementIndexCommon {
	
	public double processParam(List<EngSubParamData> subParamData, List<EngagementConfigMetrics> engConfigMetricTemp,EngRulesBasedOnMonth engRuleMonth) {
		double engScore=0.00;
		List<EngParamData> engParamDatas= engRuleMonth.getParamData();
		
		
		Map<String,List<EngSubParamData>> paramGroupSubParamData=subParamData.stream().collect(Collectors.groupingBy(EngSubParamData::getParameter));
		for(String param: paramGroupSubParamData.keySet()) {
			EngParamData engParam = new EngParamData();
			engParamDatas.add(engParam);
			double tempSum=0.00;
			List<EngSubParamData> tempSubParam = paramGroupSubParamData.get(param);
			for(EngSubParamData engSubParam:tempSubParam) {
				double score= engSubParam.getScore()!=null?engSubParam.getScore():0.00;
				 tempSum+=score;
				
			}
			 BigDecimal bd=BigDecimal.valueOf(tempSum/tempSubParam.size()).setScale(2,RoundingMode.HALF_DOWN);
			 engParam.setParameter(param);
			 engParam.setScore(bd.doubleValue());
			 if(!engConfigMetricTemp.isEmpty()) {
				 double weightage=Double.parseDouble(tempSubParam.get(0).getWeightage());
				 engParam.setWeightage(tempSubParam.get(0).getWeightage());
				
				 engScore+=BigDecimal.valueOf(bd.doubleValue()*(weightage/100)).setScale(2,RoundingMode.HALF_DOWN).doubleValue();
			 }
			 //weightage needed for engscore 
			// engScore += bd.doubleValue() * ((+parGroup[each][0].weightage) / 100);
			
		}
		return Math.round(engScore * 100.0) / 100.0;
	}
	
	public double processArea(Map<String, List<EngRuleData>> paramGroupRuleData, List<EngagementConfigMetrics> engConfigMetricTemp,EngRulesBasedOnMonth engRuleMonth) {
	
		double engScore=0.00;
		List<EngAreaData> areaData= engRuleMonth.getAreaData();
		for(String param: paramGroupRuleData.keySet()) {
			EngAreaData engArea = new EngAreaData();
			areaData.add(engArea);
			double tempSum=0.00;
			List<EngRuleData> tempSubParamRule = paramGroupRuleData.get(param);
			for(EngRuleData engSubParam:tempSubParamRule) {
				 tempSum+=engSubParam.getScore();
				
			}
			 BigDecimal bd=BigDecimal.valueOf(tempSum/tempSubParamRule.size()).setScale(2,RoundingMode.HALF_DOWN);
			 engArea.setParameter(param);
			 engArea.setScore(bd.doubleValue());
			 
			 //weightage needed for engscore 
			 if(!engConfigMetricTemp.isEmpty()) {
				 double weightage=Double.parseDouble(tempSubParamRule.get(0).getWeightage());
				 engArea.setWeightage(String.valueOf(weightage));
				 engScore+=BigDecimal.valueOf(bd.doubleValue()*(weightage/100.00)).setScale(2,RoundingMode.HALF_DOWN).doubleValue();
			 }
			 
			
			
		}
		return engScore;
		
		
	}
	
	public void processSubParam(Map<String, List<EngRuleData>> paramGroupRuleData, List<EngagementConfigMetrics> engConfigMetric, EngRulesBasedOnMonth engRuleMonth) {

		List<EngSubParamData> subParamData= engRuleMonth.getSubParamData();
		for(String param: paramGroupRuleData.keySet()) {
			List<EngagementConfigMetrics> engConfigMetricTemp = engConfigMetric.stream().filter(eng->eng.getParameter().equals(param)).collect(Collectors.toList());
			
			 List<EngRuleData> engdataParam= paramGroupRuleData.get(param);
			 Map<String,List<EngRuleData>> subParamGroupRuleData =engdataParam.stream().collect(Collectors.groupingBy(EngRuleData::getSubParameter));
			 
			 for(String subParam: subParamGroupRuleData.keySet()) {
				 double tempSum=0.00;
				 EngSubParamData engSubParam= new EngSubParamData();
				 subParamData.add(engSubParam);
				 List<EngRuleData> ruleData=subParamGroupRuleData.get(subParam);
				 for(EngRuleData engRule:ruleData) {
					 double score= engRule.getScore()!=null?engRule.getScore():1.00;
					 tempSum+=score;
					 
				 }
				 engSubParam.setParameter(param);
				 engSubParam.setSubParameter(subParam);
				 BigDecimal bd=BigDecimal.valueOf(tempSum/ruleData.size()).setScale(2,RoundingMode.HALF_DOWN);
				 engSubParam.setScore(bd.doubleValue());
				 if(!engConfigMetricTemp.isEmpty()) {
					 engSubParam.setWeightage(engConfigMetricTemp.get(0).getWeightage());
				 }
				 
				 
			 }
			
		}
	
}

}
