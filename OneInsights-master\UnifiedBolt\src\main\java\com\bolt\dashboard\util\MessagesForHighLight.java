/**
 * 
 */
package com.bolt.dashboard.util;

/**
 * <AUTHOR>
 *
 */
public class MessagesForHighLight {
    
    public static final String SUCCESS_MESSAGE_VOLATILITY = "Volatility is fine.";
    public static final String FAILURE_MESSAGE_VOLATILITY = "Volatility is bad.";
    public static final String SUCCESS_MESSAGE_CYCLETIME_CRTICAL = "Cycle Time for critical is fine."; 
    public static final String FAILURE_MESSAGE_CYCLETIME_CRTICAL = "Cycle Time for Critical is too High.";
    public static final String SUCCESS_MESSAGE_CYCLETIME_HIGH = "Cycle Time for high and highest is fine.";
    public static final String FAILURE_MESSAGE_CYCLETIME_HIGH = "Cycle Time for high and highest is too high.";
    public static final String SUCCESS_MESSAGE_TEAM_VELOCITY = "Team is working fine";
    public static final String FAILURE_MESSAGE_TEAM_VELOCITY = "Need to look for team's impediments";
    public static final String SUCCESS_MESSAGE_ESTIMATION_ACCURACY = "Team's maturity is good.";
    public static final String FAILURE_MESSAGE_ESTIMATION_ACCURACY = "Team need to be groomed for story estimation";
    public static final String SUCCESS_MESSAGE_GROOMED_STORIES = "Team has enough groomed stories for future srpints";
    public static final String FAILURE_MESSAGE_GROOMED_STORIES = "Team has to work on stories for future sprints";
    public static final String SUCCESS_MESSAGE_TEAM_EFFICIENCY = "Team is working fine";
    public static final String FAILURE_MESSAGE_TEAM_EFFICIENCY = "Team has to paln well.";
    public static final String SUCCESS_MESSAGE_RELEASE_DEADLINE = "Team is on time to deliver";
    public static final String FAILURE_MESSAGE_RELEASE_DEADLINE = "Team needs to plan for Critical first.";
    public static final String SUCCESS_MESSAGE_PROJECT_DEADLINE = "Team is on time to deliver";
    public static final String FAILURE_MESSAGE_PROJECT_DEADLINE = "Team needs to plan for Critical first.";
    public static final String SUCCESS_MESSAGE_CODE_TO_DEFECT = "Code sucesss";
    public static final String FAILURE_MESSAGE_CODE_TO_DEFECT = "code failure";

    /**
     * 
     */
    private MessagesForHighLight() {
        // TODO Auto-generated constructor stub
    }

}
