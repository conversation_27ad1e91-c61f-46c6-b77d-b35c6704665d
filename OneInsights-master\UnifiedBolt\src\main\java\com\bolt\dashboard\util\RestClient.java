package com.bolt.dashboard.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestOperations;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

public class RestClient {
	private static final Logger LOGGER = LogManager.getLogger(RestClient.class);

	private RestOperations get() {
		HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
		requestFactory.setConnectTimeout(20000);
		requestFactory.setReadTimeout(20000);
		return new RestTemplate(requestFactory);
	}

	public ResponseEntity<String> makeGetRestCall(String url, String userName, String password) {

		if (!"".equals(userName) && !"".equals(password)) {
			try {

//				return get().exchange(pipelineUrl, HttpMethod.GET, new HttpEntity<>(createHeaders(userName, password)),String.class);

				UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(url);
				UriComponents uriComponents = builder.build();

				URI uri = uriComponents.toUri();
				return get().exchange(uri, HttpMethod.GET, new HttpEntity<>(createBasicAuthHeaders(userName, password)),
						String.class);

			} catch (Exception e) {
				LOGGER.info(String.format("Error in %s", url));
				LOGGER.error(e);
				return null;
			}

		} else {
			try {
				return get().exchange(url, HttpMethod.GET, null, String.class);
			} catch (Exception e) {
				LOGGER.info(String.format("Error in %s", url));
				LOGGER.error(e);
				return null;
			}

		}
	}

	private HttpHeaders createBasicAuthHeaders(final String userId, final String password) {
		String auth = userId + ":" + password;
		byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII));
		String authHeader = "Basic " + new String(encodedAuth);

		HttpHeaders headers = new HttpHeaders();
		headers.set("Authorization", authHeader);
		return headers;
	}

	public void cleanUp() {
		File coverage = new File("test");
		File zip = new File("test.zip");
		if (coverage.exists()) {
			deleteDirectory(coverage);
		}
		if (zip.exists()) {
			zip.delete();
		}
	}

	public boolean downloadXML(String urlReal, String userId, String password) {
		try {

			String authString = userId + ":" + password;
			byte[] authEncBytes = Base64.encodeBase64(authString.getBytes());
			String authStringEnc = new String(authEncBytes);

			URL url = new URL(urlReal);
			HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
			urlConnection.setRequestMethod("GET");

			String authHeader = "Basic " + authStringEnc;
			urlConnection.setRequestProperty("Authorization", authHeader);
			urlConnection.connect();
			InputStream is = urlConnection.getInputStream();

			Files.copy(is, Paths.get("test.zip"));
			Path source = Paths.get("test.zip");
			Path target = Paths.get("test");

			try {

				return unZipIt(source.toString(), target.toString());

			} catch (Exception e) {
			}
			return false;

		} catch (Exception e) {
		}
		return false;
	}

	public static void deleteDirectory(File file) {
		// store all the paths of files and folders present
		// inside directory
		for (File subfile : file.listFiles()) {

			// if it is a subfolder,e.g Rohan and Ritik,
			// recursiley call function to empty subfolder
			if (subfile.isDirectory()) {
				deleteDirectory(subfile);
			} else {
				subfile.delete();
			}

			// delete files and empty subfolders
			subfile.delete();
		}
		file.delete();
	}

	public boolean unZipIt(String zipFile, String outputFolder) {
		byte[] buffer = new byte[1024];
		
		try {
			// create output directory is not exists
			File folder = new File(outputFolder);
			if (!folder.exists()) {
				folder.mkdir();
			}
			// get the zip file content

			try(ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFile));){
						// get the zipped file list entry
						ZipEntry ze = zis.getNextEntry();
						while (ze != null) {
							if (!ze.isDirectory()) {
								String fileName = ze.getName();
								File newFile = new File(outputFolder + File.separator + fileName);
								// create all non exists folders
								// else you will hit FileNotFoundException for compressed folder
								new File(newFile.getParent()).mkdirs();
								try(FileOutputStream fos = new FileOutputStream(newFile);){
								int len;
								while ((len = zis.read(buffer)) > 0) {
									fos.write(buffer, 0, len);
								}
							}catch(Exception e){
								LOGGER.error(e.getMessage());
							}
							ze = zis.getNextEntry();
							} else {
								ze = zis.getNextEntry();
							}
						}
			}catch(Exception e){
				LOGGER.error(e.getMessage());
			}
			return true;
		} catch (Exception ex) {
			LOGGER.error(ex.getMessage());
			return false;
		}  

	
	}

}
