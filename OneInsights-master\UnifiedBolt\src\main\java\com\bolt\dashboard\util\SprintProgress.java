package com.bolt.dashboard.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.bolt.dashboard.core.config.DataConfig;
import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.model.ComponentBurnDown;
import com.bolt.dashboard.core.model.ComponentTaskRisk;
import com.bolt.dashboard.core.model.EffortHistoryBurnDown;
import com.bolt.dashboard.core.model.EffortHistoryModel;
import com.bolt.dashboard.core.model.IterationOutModel;
import com.bolt.dashboard.core.model.MetricStoryPoints;
import com.bolt.dashboard.core.model.MonogOutMetrics;
import com.bolt.dashboard.core.model.TransitionModel;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.ProjectIterationRepo;
import com.bolt.dashboard.jira.ChartCalculations;

public class SprintProgress {

	AnnotationConfigApplicationContext ctx = DataConfig.getContext();
	List<IterationOutModel> authorData = null;
	ALMConfiguration almConfig = null;
	CommonFunctions commonFunc = new CommonFunctions();

	public List<ComponentTaskRisk> getTaskRisk(String projName, String almType, boolean storyPointBased) {
		

		getInitialData(projName, almType);
		List<ComponentTaskRisk> componentWiseData = null;
		if (storyPointBased) {
			componentWiseData = getIssueRiskStoryPoint(projName);
		} else {
			componentWiseData = getIssueRiskEffortBased(projName);
		}
		return componentWiseData;

	}

	private List<ComponentTaskRisk> getIssueRiskEffortBased(String projName) {
		
		List<ComponentTaskRisk> response = new ArrayList<ComponentTaskRisk>();
		List<String> closeStates = Arrays.asList(almConfig.getCloseState());

		List<IterationOutModel> authorData = this.authorData.stream()
				.filter(auth -> auth.getStDate() != null && auth.getStDate() != 0).collect(Collectors.toList());
		ArrayList<String> componentList = new ArrayList<String>();
		componentList.add("All");

		componentList.addAll(new ChartCalculations().getComponents(projName));
		Collections.sort(componentList);

		boolean flag = (componentList.size() > 1) ? true : false;
		for (String component : componentList) {
			ComponentTaskRisk c = new ComponentTaskRisk();
			List<TaskRiskSprint> taskRiskSprintWise = new ArrayList<>();
			for (IterationOutModel iter : authorData) {

				TaskRiskSprint sprintTaskRisk = new TaskRiskSprint();
				List<Map<String, String>> assigneWiseDataSprint = new ArrayList<Map<String, String>>();

				// sprintTaskRisk.setAssigneWiseData(assigneWiseDataSprint);
				long startDate = iter.getStDate();
				long endDate = iter.getCompletedDate();
				if (endDate == 0) {
					endDate = iter.getEndDate();
				}
				sprintTaskRisk.setSprintName(iter.getsName());
				sprintTaskRisk.setStartDate(iter.getStDate());
				sprintTaskRisk.setEndDate(endDate);
				long totalEstimationSprint = 0;
				long effortSpentOnSprint = 0;
				int closedIssues = 0;
				sprintTaskRisk.setEndDate(endDate);
				List<MonogOutMetrics> metrics = null;
				Map<String, List<MonogOutMetrics>> assigneeTaskMap = new HashMap<String, List<MonogOutMetrics>>();
				if (iter.getMetrics() != null) {
					if (flag && !component.equals("All")) {

						metrics = iter.getMetrics().stream()
								.filter(met -> (met.getComponents() != null
										? met.getComponents().get(0).equals(component)
										: false) && met.getAssgnTo() != null)
								.collect(Collectors.toList());
					} else {
						metrics = iter.getMetrics().stream().filter(met -> met.getAssgnTo() != null)
								.collect(Collectors.toList());

					}

					Map<String, List<MonogOutMetrics>> stateGroupedMetrics = metrics.stream()
							.collect(Collectors.groupingBy(w -> w.getAssgnTo()));

					for (Map.Entry<String, List<MonogOutMetrics>> assigneeEntry : stateGroupedMetrics.entrySet()) {
						int closed = 0;
						long effortSpentAssignee = 0;
						long orgEstimateAssignee = 0;
						long actualEstimateAssignee = 0;
						Map<String, String> assigneeData = new HashMap<String, String>();
						assigneeData.put("Assignee", assigneeEntry.getKey());

						List<MonogOutMetrics> assigneeTasks = new ArrayList<MonogOutMetrics>();
						assigneeTaskMap.put(assigneeEntry.getKey(), assigneeTasks);
						for (MonogOutMetrics metric : assigneeEntry.getValue()) {
							if (!metric.getType().equals(almConfig.getStoryName())) {
								MonogOutMetrics metricVal = new MonogOutMetrics();
								setRequiredValues(metric, metricVal);
								assigneeTasks.add(metricVal);
								if (metric.getEffort() != null) {
									effortSpentOnSprint = effortSpentOnSprint + metric.getEffort().longValue();
									effortSpentAssignee = effortSpentAssignee + metric.getEffort().longValue();
								}
								if (metric.getOrgEst() != null) {
									totalEstimationSprint = totalEstimationSprint + metric.getOrgEst().longValue();
									orgEstimateAssignee = orgEstimateAssignee + metric.getOrgEst().longValue();
								}
								if (metric.getActEst() != null) {
									actualEstimateAssignee = actualEstimateAssignee + metric.getActEst().longValue();
								}

							}
							if (closeStates.indexOf(metric.getState()) > -1) {
								closed++;

							}
						}

						String openClosed = "" + closed + "/" + assigneeEntry.getValue().size();
						assigneeData.put("Closed_Open", openClosed);
						assigneeData.put("Orignal Estimate",
								commonFunc.convertSecondsToStringDisplay(orgEstimateAssignee, 8));
						assigneeData.put("Current Estimate",
								commonFunc.convertSecondsToStringDisplay(actualEstimateAssignee, 8));
						assigneeData.put("Actual Effort",
								commonFunc.convertSecondsToStringDisplay(effortSpentAssignee, 8));
						assigneeData.put("Remaining", commonFunc
								.convertSecondsToStringDisplay(actualEstimateAssignee - effortSpentAssignee, 8));
						assigneeData.put("Estimated Efforts Hours", commonFunc.toHoursString(orgEstimateAssignee));
						assigneeData.put("Effort Spent Hours", commonFunc.toHoursString(effortSpentAssignee));
						// status Calculation changed from prev to According task Completed or not will
						// update if required
						assigneeData.put("Status",
								getIssueRiskStatus(assigneeEntry.getValue().size(), closed, startDate, endDate));
						assigneWiseDataSprint.add(assigneeData);
					}
				}
				sprintTaskRisk.setAssigneWiseData(assigneWiseDataSprint);
				sprintTaskRisk.setAssigneeWiseTasks(assigneeTaskMap);
				closedIssues = iter.getMetrics().stream().filter(met -> closeStates.indexOf(met.getState()) > -1)
						.collect(Collectors.toList()).size();
				double percentage = ((double) closedIssues / iter.getMetrics().size()) * 100;

				sprintTaskRisk.setCompleted(commonFunc.convertSecondsToStringDisplay(effortSpentOnSprint, 8));
				sprintTaskRisk.setEstimation(commonFunc.convertSecondsToStringDisplay(totalEstimationSprint, 8));
				sprintTaskRisk.setIssueCompletionPercentage((int) percentage);

				taskRiskSprintWise.add(sprintTaskRisk);
			}
			c.setComponent(component);
			c.setTaskRiskSprintList(taskRiskSprintWise);
			response.add(c);
		}
		return response;
	}

	private void setRequiredValues(MonogOutMetrics metric, MonogOutMetrics metricVal) {
		

		metricVal.setwId(metric.getwId());
		metricVal.setEffort(metric.getEffort());
		metricVal.setEfforts(metric.getEfforts());
		metricVal.setEstChange(metric.getEstChange());
		metricVal.setActEst(metric.getActEst());
		metricVal.setOrgEst(metric.getOrgEst());
		metricVal.setRemTime(metric.getRemTime());
		metricVal.setType(metric.getType());
		metricVal.setState(metric.getState());
		metricVal.setExtEffort(metric.getExtEffort());

	}

	private List<ComponentTaskRisk> getIssueRiskStoryPoint(String projName) {
		List<ComponentTaskRisk> response = new ArrayList<ComponentTaskRisk>();
		List<String> closeStates = Arrays.asList(almConfig.getCloseState());

		List<IterationOutModel> authorData = this.authorData
				.stream().filter(auth -> auth.getStDate() != null && auth.getStDate() != 0
						&& !auth.getState().equals("future") && !auth.getState().equals("FUTURE"))
				.collect(Collectors.toList());
		Collections.sort(authorData, new SprintComparator());
		ArrayList<String> componentList = new ArrayList<String>();
		componentList.add("All");

		componentList.addAll(new ChartCalculations().getComponents(projName));
		Collections.sort(componentList);

		boolean flag = (componentList.size() > 1) ? true : false;
		for (String component : componentList) {
			ComponentTaskRisk c = new ComponentTaskRisk();
			List<TaskRiskSprint> taskRiskSprintWise = new ArrayList<>();

			for (IterationOutModel iter : authorData) {

				TaskRiskSprint sprintTaskRisk = new TaskRiskSprint();
				List<Map<String, String>> assigneWiseDataSprint = new ArrayList<Map<String, String>>();
				// sprintTaskRisk.setAssigneWiseData(assigneWiseDataSprint);
				long startDate = iter.getStDate();
				long endDate = iter.getCompletedDate();
				if (endDate == 0) {
					endDate = iter.getEndDate();
				}
//				Collections.sort(authorData, new SprintComparatort());
				sprintTaskRisk.setSprintName(iter.getsName());
				sprintTaskRisk.setStartDate(iter.getStDate());
				sprintTaskRisk.setEndDate(endDate);
				int totalSpSprint = 0;
				int closedSpSprint = 0;
				int closedIssues = 0;
				sprintTaskRisk.setEndDate(endDate);
				List<MonogOutMetrics> metrics = null;
				Map<String, List<MonogOutMetrics>> stateGroupedMetrics = null;
				if (iter.getMetrics() != null) {
					if (flag && !component.equals("All")) {

						metrics = iter.getMetrics().stream()
								.filter(met -> (met.getComponents() != null
										? met.getComponents().get(0).equals(component)
										: false) && met.getAssgnTo() != null)
								.collect(Collectors.toList());
					} else {
						metrics = iter.getMetrics().stream().filter(met -> met.getAssgnTo() != null)
								.collect(Collectors.toList());

					}
					
					
					//logic for calculating current storypoints
					List<MetricStoryPoints> metricSp = new ArrayList<MetricStoryPoints>() ;
					for(MonogOutMetrics m:metrics) {
						MetricStoryPoints ms = new MetricStoryPoints();
						BeanUtils.copyProperties(m, ms);
						metricSp.add(ms);
					}
					
					
					stateGroupedMetrics = metricSp.stream().collect(Collectors.groupingBy((w) -> {
						w.setTransitions(null);
						((MetricStoryPoints) w).setCurrentstoryPoints(getLatestStoryPoints(w));
						return w.getAssgnTo();
					}));
					sprintTaskRisk.setAssigneeWiseTasks(stateGroupedMetrics);
					for (Map.Entry<String, List<MonogOutMetrics>> assigneeEntry : stateGroupedMetrics.entrySet()) {

						int assigneeClosedSp = 0;
						int totalAssigneeSp = 0;
						int closed = 0;

						Map<String, String> assigneeData = new HashMap<String, String>();
						Map<String, MonogOutMetrics> assigneeStories = new HashMap<String, MonogOutMetrics>();
						assigneeData.put("Assignee", assigneeEntry.getKey());

						for (MonogOutMetrics metric : assigneeEntry.getValue()) {
							Map<Long, Double> storyPoints = metric.getStoryPoints();
							int sp = 0;
							if (storyPoints != null && storyPoints.size() > 0) {
								Map<Long, Double> sortedMap = new TreeMap<Long, Double>(storyPoints);
								Map.Entry<Long, Double> entry = sortedMap.entrySet().iterator().next();
								sp = entry.getValue().intValue();
							}

							if (closeStates.indexOf(metric.getState()) > -1) {
								closedSpSprint = closedSpSprint + sp;
								assigneeClosedSp = assigneeClosedSp + sp;
								closed++;
							}
							totalSpSprint = totalSpSprint + sp;
							totalAssigneeSp = totalAssigneeSp + sp;
						}
						assigneeData.put("ESP", String.valueOf(totalAssigneeSp));
						assigneeData.put("CSP", String.valueOf(assigneeClosedSp));
						assigneeData.put("RSP", String.valueOf(totalAssigneeSp - assigneeClosedSp));
						String openClosed = "" + closed + "/" + assigneeEntry.getValue().size();
						assigneeData.put("Closed_Open", openClosed);
						// status Calculation changed from prev to According task Completed or not will
						// update if required
						assigneeData.put("Status",
								getIssueRiskStatus(assigneeEntry.getValue().size(), closed, startDate, endDate));
						assigneWiseDataSprint.add(assigneeData);
					}
				}
				sprintTaskRisk.setCompleted(String.valueOf(closedSpSprint));
				sprintTaskRisk.setEstimation(String.valueOf(totalSpSprint));
				sprintTaskRisk.setAssigneWiseData(assigneWiseDataSprint);

//				if (component.equals("Team Shield"))
//					System.out.println("");
//				
//				if(iter.getsName().equals("BROA-Scr Sprint 15"))
//					System.out.println("");
				if (metrics != null) {
					closedIssues = metrics.stream().filter(met -> closeStates.indexOf(met.getState()) > -1)
							.collect(Collectors.toList()).size();
					double percentage = ((double) closedIssues / metrics.size()) * 100;

					sprintTaskRisk.setIssueCompletionPercentage((int) percentage);
				} else
					sprintTaskRisk.setIssueCompletionPercentage(0);

				taskRiskSprintWise.add(sprintTaskRisk);
			}
			c.setComponent(component);
			c.setTaskRiskSprintList(taskRiskSprintWise);
			response.add(c);
		}
		return response;
	}

	private double getLatestStoryPoints(MonogOutMetrics w) {
		double storyPoints=0;
		for(Map.Entry<Long, Double> entry:w.getStoryPoints().entrySet()) {
			storyPoints = entry.getValue();
		}
		
		return storyPoints;
	}

	private String getIssueRiskStatus(int total, int closed, long startDate, long endDate) {
		

		String state = "RED";
		int remaining = total - closed;
		long todayDate = new DateTime().getMillis();

		if (remaining == 0) {
			state = "GREEN";
		} else if (!(todayDate > endDate)) {
			int remainingPercentage = (closed / total) * 100;
			if (remainingPercentage > 70) {
				state = "AMBER";
			} else {

				state = "RED";
			}
		}

		return state;
	}

	private void getInitialData(String projName, String almType) {
		
		ProjectIterationRepo authorRepo = ctx.getBean(ProjectIterationRepo.class);

		authorData = authorRepo.findByPNameAndPAlmType(projName, almType);

		ALMConfigRepo almConfigRepo = ctx.getBean(ALMConfigRepo.class);
		almConfig = almConfigRepo.findByProjectName(projName).get(0);

	}
//	public static void main(String[] args) {
//	new SprintProgress().getTaskRisk("BrillioOne", "JIRA", true);
//}

	public List<ComponentBurnDown> getBurndown(String projName, String almType, boolean storyPointBased) {
		
		getInitialData(projName, almType);
		List<ComponentBurnDown> burnDownData = null;

		if (storyPointBased) {
			burnDownData = getBurndownStoryPoint(projName, almType);
		} else {
			burnDownData = getBurndownEffort(projName, almType);
		}

		return burnDownData;
	}

	private List<ComponentBurnDown> getBurndownEffort(String projName, String almType) {
		List<String> closeStates = Arrays.asList(almConfig.getCloseState());
		List<ComponentBurnDown> response = new ArrayList<ComponentBurnDown>();

		List<IterationOutModel> authorData = this.authorData.stream()
				.filter(auth -> auth.getStDate() != null && auth.getStDate() != 0).collect(Collectors.toList());
		ArrayList<String> componentList = new ArrayList<String>();
		componentList.add("All");

		componentList.addAll(new ChartCalculations().getComponents(projName));
		Collections.sort(componentList);

//		boolean flag = (componentList.size() > 1) ? true : false;
		for (String component : componentList) {
			ComponentBurnDown c = new ComponentBurnDown();
			List<BurnDownDataSprint> sprints = new ArrayList<BurnDownDataSprint>();
//			Collections.sort(authorData, new SprintComparator());
			for (IterationOutModel iter : authorData) {
				if (iter.getStDate() != null && iter.getStDate() != 0) {
					long startDate = iter.getStDate();
					long endDate = iter.getEndDate();
					BurnDownDataSprint sp = new BurnDownDataSprint();
					List<List<Long>> chartEffor = new ArrayList<List<Long>>();
					List<List<Long>> chartIdeal = new ArrayList<List<Long>>();
					List<List<Long>> chartRemaining = new ArrayList<List<Long>>();
					List<EffortHistoryModel> mainArray = new ArrayList<EffortHistoryModel>();
					int totalRemaining = 0, totalSpent = 0, remaining = 0;
					int sprintDays = (int) Math.floor((iter.getEndDate() - iter.getStDate()) / 86400000f);
					chartEffor.add(Arrays.asList(iter.getStDate(), 0L));

					for (MonogOutMetrics metric : iter.getMetrics()) {

						if (!metric.getType().equalsIgnoreCase("story") && !metric.getType().equalsIgnoreCase("epic")
								&& metric.getCreateDate() <= startDate) {
							if (isAllocated(metric, startDate, endDate, iter)) {
								if (metric.getOrgEst() != null && metric.getOrgEst() != 0) {
									remaining += metric.getOrgEst();
								}
								long removed = isRemoved(metric, startDate, endDate, iter);
								if (removed == 0) {
									for (EffortHistoryModel effort : metric.getEfforts()) {
										mainArray.add(effort);
									}
								} else {
									for (EffortHistoryModel effort : metric.getEfforts()) {
										if (effort.getLogDate() < removed) {
											mainArray.add(effort);
										}

									}
								}

							}

						} else if (!metric.getType().equalsIgnoreCase("story")
								&& !metric.getType().equalsIgnoreCase("epic") && metric.getCreateDate() > startDate) {
							if (isAllocated(metric, startDate, endDate, iter)) {
								if (metric.getOrgEst() != null && metric.getOrgEst() != 0) {
									EffortHistoryBurnDown m = new EffortHistoryBurnDown();
									m.setRemainingWork(0);
									m.setLogDate(metric.getCreateDate());
									m.setRemainingWorkDec(0);
									m.setRemainingWorkInc(0);
									m.setInitialWork(metric.getOrgEst());
									m.setwId(metric.getwId());
									mainArray.add(m);
								}

								long removed = isRemoved(metric, startDate, endDate, iter);
								if (removed == 0) {
									for (EffortHistoryModel effort : metric.getEfforts()) {
										mainArray.add(effort);
									}
								} else {
									for (EffortHistoryModel effort : metric.getEfforts()) {
										if (effort.getLogDate() < removed) {
											mainArray.add(effort);
										}

									}
								}
							}

						}
					}

					chartIdeal.add(Arrays.asList(startDate, (long) Math.floor(remaining / 3600f)));
					chartRemaining.add(Arrays.asList(startDate, (long) Math.floor(remaining / 3600f)));
					mainArray.sort(Comparator.comparing(EffortHistoryModel::getLogDate));
					totalRemaining = remaining;

					long tempDatePrev = startDate;
					long tempDateNext = startDate + 86400000;
					for (int i = 0; i <= sprintDays; i++) {
						for (EffortHistoryModel o : mainArray) {

							if (o.getLogDate() > tempDatePrev && o.getLogDate() <= tempDateNext
									&& o.getRemainingWorkDec() == 0 && o.getRemainingWorkInc() == 0) {
								totalRemaining += o.getInitialWork();
								chartRemaining
										.add(Arrays.asList(o.getLogDate(), (long) Math.floor(totalRemaining / 3600f)));
							} else if (o.getLogDate() > tempDatePrev && o.getLogDate() <= tempDateNext) {
								totalRemaining += o.getRemainingWorkInc();
								totalRemaining -= o.getRemainingWorkDec();
								chartRemaining
										.add(Arrays.asList(o.getLogDate(), (long) Math.floor(totalRemaining / 3600f)));
							}

						}
						tempDatePrev += 86400000;
						tempDateNext += 86400000;
					}

					Collections.sort(chartRemaining, new LogDateComparator());
					for (EffortHistoryModel o : mainArray) {
						if (o.getTimeSpent() != null && o.getTimeSpent() != 0 && o.getLogDate() > startDate
								&& o.getLogDate() <= endDate) {
							totalSpent += o.getTimeSpent();
							chartEffor.add(Arrays.asList(o.getLogDate(), (long) Math.floor(totalSpent / 3600f)));
						}
					}
					chartIdeal.add(Arrays.asList(endDate, 0l));
					sp.setIdealLine(chartIdeal);
					sp.setTimeRemaining(chartRemaining);
					sp.setTimeSpent(chartEffor);
					sp.setSprintName(iter.getsName());
					sprints.add(sp);
				}

			}

			c.setComponent(component);
			c.setBurnDownDataSprint(sprints);
			response.add(c);
		}
		return response;
	}

	private List<ComponentBurnDown> getBurndownStoryPoint(String projName, String almType) {
		List<ComponentBurnDown> response = new ArrayList<ComponentBurnDown>();

		List<String> closeStates = Arrays.asList(almConfig.getCloseState());

		ArrayList<String> componentList = new ArrayList<String>();
		componentList.add("All");

		componentList.addAll(new ChartCalculations().getComponents(projName));
		Collections.sort(componentList);
		List<IterationOutModel> withOutBacklogAuthor = authorData
				.stream().filter(auth -> auth.getStDate() != null && auth.getStDate() != 0
						&& !auth.getState().equals("future") && !auth.getState().equals("FUTURE"))
				.collect(Collectors.toList());
		boolean flag = (componentList.size() > 1) ? true : false;
		for (String component : componentList) {
			ComponentBurnDown c = new ComponentBurnDown();
			List<BurnDownDataSprint> burnDownDataSprintList = new ArrayList<BurnDownDataSprint>();
			for (IterationOutModel iter : withOutBacklogAuthor) {
				BurnDownDataSprint burnDownSprint = new BurnDownDataSprint();
				burnDownDataSprintList.add(burnDownSprint);
				burnDownSprint.setSprintName(iter.getsName());
				long estimatedSp = 0;

				List<List<Long>> idealLine = new ArrayList<List<Long>>();
				List<List<Long>> remainingSp = new ArrayList<List<Long>>();
				burnDownSprint.setIdealLine(idealLine);
				burnDownSprint.setTimeRemaining(remainingSp);
				List<Map<String, Long>> allData = new ArrayList<Map<String, Long>>();
				long endDate = iter.getCompletedDate();
				long startDate = iter.getStDate();
				if (endDate == 0 || endDate < startDate) {
					endDate = iter.getEndDate();
				}

				List<Long> idealDataStart = new ArrayList<Long>();
				List<Long> idealDataEnd = new ArrayList<Long>();
				idealLine.add(idealDataStart);
				idealLine.add(idealDataEnd);
				idealDataStart.add(startDate);
				idealDataEnd.add(endDate);
				idealDataEnd.add(0L);
				if (iter.getsName().equals("BROA-Scr Sprint 8")) {
					System.out.println("Dfdfd");
				}

				for (IterationOutModel iter2 : authorData) {
					if (iter2.getMetrics() != null) {
						List<MonogOutMetrics> metrics = null;
						if (flag && !component.equals("All")) {
							metrics = iter2.getMetrics().stream()
									.filter(met -> (met.getComponents() != null
											? met.getComponents().get(0).equals(component)
											: false))
									.collect(Collectors.toList());
						} else {
							metrics = iter2.getMetrics();
						}
						for (MonogOutMetrics metric : metrics) {
							boolean doneFlag = false;
							boolean allocFlag = false;
							long allocationTime = 0;
							long removedTime = 0;
							long checkSp = 0;
							long prevSp = 0;
							long tempSp = 0;

//				 if(metric.getwId().equals("NTSBW-202")) {
//					 System.out.println("Check");
//				 }
							if (metric.getStoryPoints() != null) {
								Map<Long, Double> storyPoints = metric.getStoryPoints();
								Map<Long, String> allocations = metric.getAllocatedDate();
								List<Long> storyPointAllocationDates = new ArrayList<Long>(storyPoints.keySet());
								Collections.sort(storyPointAllocationDates);
								// List<Long> storyPointKeys = new ArrayList<>(storyPoints.keySet());

								for (int indexSp = 0; indexSp <= storyPointAllocationDates.size() - 1; indexSp++) {
									if (allocations != null) {
										List<Long> allocKey = new ArrayList<>(allocations.keySet());
										// Get Allocation Time for the issue
										for (int index = 0; index < allocKey.size(); index++) {
											String alloc = allocations.get(allocKey.get(index));
											List<String> sprints = Arrays.asList(alloc.split(","));
											if ((allocationTime == 0
													&& sprints.indexOf(String.valueOf(iter.getsId())) > -1
													&& index == allocKey.size() - 1)
													|| (allocationTime == 0
															&& sprints.indexOf(String.valueOf(iter.getsId())) > -1
															&& allocKey.get(index + 1) > iter.getStDate())) {
												allocationTime = allocKey.get(index);
												// break;
											}
											if ((allocationTime != 0 && allocKey.get(index) < endDate
													&& sprints.indexOf(String.valueOf(iter.getsId())) <= -1
													&& index == allocKey.size() - 1)
													|| (allocationTime != 0 && index != allocKey.size() - 1
															&& sprints.indexOf(String.valueOf(iter.getsId())) <= -1
															&& allocKey.get(index + 1) > endDate)) {
												removedTime = allocKey.get(index);

											}
										}
									}

									if (allocationTime != 0) {
										if (removedTime == 0) {
											// Done state Calculation
											List<TransitionModel> transitions = metric.getTransitions();
											if (transitions != null) {
												for (TransitionModel trans : transitions) {
													if (closeStates.indexOf(trans.getCrState()) > -1 && !doneFlag
															&& trans.getMdfDate() >= startDate
															&& trans.getMdfDate() <= endDate) {
														Map<String, Long> closedVal = new HashMap<String, Long>();
														for (int i = storyPointAllocationDates.size()
																- 1; i >= 0; i--) {
															if (storyPointAllocationDates.get(i) < trans.getMdfDate()) {
																checkSp = storyPoints
																		.get(storyPointAllocationDates.get(i))
																		.longValue();
																break;
															}
														}

														doneFlag = true;
														metric.setDoneDate(trans.getMdfDate());
														if (checkSp != 0) {
															closedVal.put("DecSp", checkSp);
															closedVal.put("Time", trans.getMdfDate());
															// System.out.println("WID "+ metric.getwId() + "DecSp "
															// +checkSp);
															allData.add(closedVal);
														}

													}

												}

											}
										}
										// For New Allocation
										if (allocationTime >= startDate && allocationTime <= endDate && !allocFlag
												&& storyPointAllocationDates.get(indexSp) < metric.getDoneDate()) {
											allocFlag = true;
											prevSp = storyPoints.get(storyPointAllocationDates.get(indexSp))
													.longValue();
											Map<String, Long> newAlloc = new HashMap<String, Long>();
											newAlloc.put("Time", allocationTime);
											newAlloc.put("IncSp", prevSp);
											// System.out.println("WID "+ metric.getwId() +" IncSp " + prevSp);
											allData.add(newAlloc);
											continue;
										}
										// Estimate Changes
										long currentSp = storyPoints.get(storyPointAllocationDates.get(indexSp))
												.longValue();
										if (storyPointAllocationDates.get(indexSp) > allocationTime
												&& storyPointAllocationDates.get(indexSp) >= startDate
												&& storyPointAllocationDates.get(indexSp) <= endDate
												&& currentSp != prevSp
												&& storyPointAllocationDates.get(indexSp) < metric.getDoneDate()) {

											Map<String, Long> estimateChanges = new HashMap<String, Long>();
											estimateChanges.put("Time", storyPointAllocationDates.get(indexSp));

											if (currentSp < prevSp) {
												// System.out.println(
												// "WID " + metric.getwId() + " DecSp " + String.valueOf(prevSp -
												// currentSp));
												estimateChanges.put("DecSp", prevSp - currentSp);
											} else {
//									System.out.println(
//											"WID " + metric.getwId() + "IncSp " + String.valueOf(currentSp - prevSp));
												estimateChanges.put("IncSp", currentSp - prevSp);
											}
											allData.add(estimateChanges);
										}

										prevSp = storyPoints.get(storyPointAllocationDates.get(indexSp)).longValue();
										// System.out.println(metric.getwId());
										if ((storyPointAllocationDates.get(indexSp) < startDate
												&& allocationTime < startDate
												&& indexSp == storyPointAllocationDates.size() - 1)
												|| (indexSp != storyPointAllocationDates.size() - 1
														&& storyPointAllocationDates.get(indexSp) < startDate
														&& storyPointAllocationDates.get(indexSp + 1) > startDate
														&& allocationTime < startDate)) {

											tempSp = storyPoints.get(storyPointAllocationDates.get(indexSp))
													.longValue();
											if (iter.getsName().equals("NP PI-4.4")) {
												System.out.println("WID " + metric.getwId() + " Sp " + tempSp);
											}
										}
									}
									if (removedTime != 0) {
										Map<String, Long> removedChanges = new HashMap<String, Long>();
										removedChanges.put("Time", removedTime);
										removedChanges.put("DecSp", tempSp);
										allData.add(removedChanges);
									}

									estimatedSp = estimatedSp + tempSp;
									tempSp = 0;

								}
							}
						}
					}
				}
				idealDataStart.add(estimatedSp);

				allData.sort(Comparator.nullsLast(
						Comparator.comparing(m -> m.get("Time"), Comparator.nullsLast(Comparator.naturalOrder()))));

				List<Long> dataList = new ArrayList<Long>();
				dataList.add(startDate);
				dataList.add(estimatedSp);
				remainingSp.add(dataList);
				for (Map<String, Long> data : allData) {
					List<Long> dataArr = new ArrayList<Long>();
					if (data.containsKey("IncSp")) {
						dataArr.add(data.get("Time"));
						estimatedSp = estimatedSp + data.get("IncSp");
						dataArr.add(estimatedSp);
						remainingSp.add(dataArr);
					}
					if (data.containsKey("DecSp")) {
						dataArr.add(data.get("Time"));
						estimatedSp = estimatedSp - data.get("DecSp");
						dataArr.add(estimatedSp);
						remainingSp.add(dataArr);
					}

				}
			}
			c.setComponent(component);
			c.setBurnDownDataSprint(burnDownDataSprintList);
			response.add(c);
		}
//		return burnDownData;
		return response;
	}

	class LogDateComparator implements Comparator<List<Long>> {

		@Override
		public int compare(List<Long> o1, List<Long> o2) {

			if (o1.get(0) - o2.get(0) > 0) {
				return 0;
			} else {
				return 1;
			}

		}

	}

	private boolean isAllocated(MonogOutMetrics metric, long startDate, long endDate, IterationOutModel iter) {
		Map<Long, String> allocateMap = metric.getAllocatedDate();
		HashMap<Long, String> sortedAllocatedMap = new HashMap<Long, String>();
		if (allocateMap != null) {
			for (Map.Entry<Long, String> entry : allocateMap.entrySet()) {
				sortedAllocatedMap.put(entry.getKey(), entry.getValue());
			}
			// sorting by using treemap constructor
			TreeMap<Long, String> tm = new TreeMap<Long, String>(sortedAllocatedMap);

			Entry<Long, String> set = null;
			for (Map.Entry<Long, String> entry : tm.entrySet()) {
				if (entry.getKey() < startDate) {
					set = entry;
				}
			}
			if (set != null && set.getValue().contains(iter.getsId() + "")) {
				return true;
			} else {
				for (Map.Entry<Long, String> entry : tm.entrySet()) {
					if (entry.getKey() >= startDate && entry.getKey() <= endDate) {
						if (entry.getValue().contains(iter.getsId() + "")) {
							return true;
						}
					}
				}
				return false;
			}
		} else {
			return true;
		}

	}

	class SprintComparator implements Comparator {

		@Override
		public int compare(Object o1, Object o2) {
			IterationOutModel s1 = (IterationOutModel) o1;
			IterationOutModel s2 = (IterationOutModel) o2;

			if (s1.getStDate() != null && s2.getStDate() != null) {
				if (s1.getStDate().equals(s2.getStDate()))
					return 0;
				else if (s1.getStDate() > s2.getStDate())
					return 1;
				else
					return -1;
			} else if (s1.getStDate() == null) {
				return 1;
			} else {
				return -1;
			}

		}

	}

	private long isRemoved(MonogOutMetrics metric, long startDate, long endDate, IterationOutModel iter) {

		Map<Long, String> allocateMap = metric.getAllocatedDate();
		HashMap<Long, String> sortedAllocatedMap = new HashMap<Long, String>();
		if (allocateMap != null) {
			for (Map.Entry<Long, String> entry : allocateMap.entrySet()) {
				sortedAllocatedMap.put(entry.getKey(), entry.getValue());
			}
			// sorting by using treemap constructor
			TreeMap<Long, String> tm = new TreeMap<Long, String>(sortedAllocatedMap);
			Entry<Long, String> set = null;
			for (Map.Entry<Long, String> entry : tm.entrySet()) {
				if (entry.getKey() > startDate && entry.getKey() <= endDate) {
					set = entry;
				}
			}
			if (set != null && !set.getValue().contains(iter.getsId() + "")) {

				return set.getKey();
			} else {
				return 0;
			}
		} else {
			return 0;
		}

	}


}


