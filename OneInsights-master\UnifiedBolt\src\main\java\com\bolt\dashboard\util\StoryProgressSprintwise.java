package com.bolt.dashboard.util;

import java.util.ArrayList;
import java.util.List;



public class StoryProgressSprintwise {
	private List<StoryProgressModel> storyTasks = new ArrayList<>();
	private String sprintName;

	public StoryProgressSprintwise(String sprintName, List<StoryProgressModel> storyTasks) {
		this.sprintName = sprintName;
		this.storyTasks = storyTasks;
	}

	public String getSprintName() {
		return sprintName;
	}

	public void setSprintName(String sprintName) {
		this.sprintName = sprintName;
	}

	public List<StoryProgressModel> getStoryTasks() {
		return storyTasks;
	}

	public void setStoryTasks(List<StoryProgressModel> storyTasks) {
		this.storyTasks = storyTasks;
	}

}