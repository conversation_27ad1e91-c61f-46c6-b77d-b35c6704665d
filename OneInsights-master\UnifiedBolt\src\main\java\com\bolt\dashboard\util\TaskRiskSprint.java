package com.bolt.dashboard.util;

import java.util.List;
import java.util.Map;

import com.bolt.dashboard.core.model.MetricsModel;
import com.bolt.dashboard.core.model.MonogOutMetrics;

public class TaskRiskSprint {
	
	private String sprintName; 
	private long startDate;
	private long endDate;
	private String estimation;
	private String completed;
	List<Map<String, String>> assigneWiseData;
	Map<String,List<MonogOutMetrics>> assigneeWiseTasks;
	
	
	private int issueCompletionPercentage;
	public Map<String, List<MonogOutMetrics>> getAssigneeWiseTasks() {
		return assigneeWiseTasks;
	}
	public void setAssigneeWiseTasks(Map<String, List<MonogOutMetrics>> assigneeWiseTasks) {
		this.assigneeWiseTasks = assigneeWiseTasks;
	}
	public List<Map<String, String>> getAssigneWiseData() {
		return assigneWiseData;
	}
	public void setAssigneWiseData(List<Map<String, String>> assigneWiseData) {
		this.assigneWiseData = assigneWiseData;
	}
	public String getSprintName() {
		return sprintName;
	}
	public void setSprintName(String sprintName) {
		this.sprintName = sprintName;
	}
	public long getStartDate() {
		return startDate;
	}
	public void setStartDate(long startDate) {
		this.startDate = startDate;
	}
	public long getEndDate() {
		return endDate;
	}
	public void setEndDate(long endDate) {
		this.endDate = endDate;
	}
	
	public int getIssueCompletionPercentage() {
		return issueCompletionPercentage;
	}
	public void setIssueCompletionPercentage(int issueCompletionPercentage) {
		this.issueCompletionPercentage = issueCompletionPercentage;
	}
	public String getEstimation() {
		return estimation;
	}
	public void setEstimation(String estimation) {
		this.estimation = estimation;
	}
	public String getCompleted() {
		return completed;
	}
	public void setCompleted(String completed) {
		this.completed = completed;
	}
	
	

}
