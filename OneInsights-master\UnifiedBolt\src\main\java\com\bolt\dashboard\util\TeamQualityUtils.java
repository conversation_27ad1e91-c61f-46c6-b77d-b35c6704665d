package com.bolt.dashboard.util;

public class TeamQualityUtils {

	public double calclulateAbsDiff(double first, double second, String op) {
		if (op == null) {
			return 0;
		}

		switch (op) {
		case ">":
			if (first > second)
				return 1;
			else
				return (second - first);

		case "<":
			if (first < second)
				return 1;
			else
				return (first - second);

		case "==":
			boolean v = first == second;
			if (v)
				return 1;
			else
				return (Math.abs(first - second));

		case ">=":
			if (first >= second)
				return 1;
			else
				return (second - first);
		case "<=":
			if (first <= second)
				return 1;
			else
				return (first - second);
		default:
			return 0;
		}
	}
	
	public double calclulateDiff(double first, double second, String op) {
		if (op == null) {
			return 0;
		}

		switch (op) {
		case ">=":
			return (first - second);

		case "<=":
			return (second - first);
			
		case ">":
			return (first - second);

		case "<":
			return (second - first);

		case "==":
			return (first - second);

		default:
			return 0;
		}
	}
	
	public double calculatePoints(double doMathResult, double  goalValue) {
		if (doMathResult == 1) {
			return 10;

		} else if (doMathResult >=goalValue) {
			return 0;
		}

		else {
			double diff = (doMathResult * 10 / goalValue);
			
			return Math.floor(10-diff);
		}
	}
	
	public double calculateAbsPoints(double doMathResult, double  goalValue) {
		double absPoints = (doMathResult/goalValue)*10;
		return absPoints;
	}
}



