# ================== data_lineage.py ==================
import os
import json
import javalang
from collections import defaultdict
from neo4j import GraphDatabase

# Neo4j Configuration
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "my-oneinsights"

# ================== Stage 1: Extract Relations ==================
def extract_relations_to_json_javalang(project_path):
    nodes = {}  # metadata for nodes
    relations = []
    collection_entities = {}  # full class name → collection
    class_uses_classes = defaultdict(set)
    existing_relations = set()

    def register_node(raw_node, file_path):
        if not raw_node or raw_node in nodes:
            return
        node_type, full_name = raw_node.split(":", 1)

        # Determine short name
        if node_type == "file":
            short_name = os.path.basename(full_name)
        elif node_type in ("folder", "project", "application", "package"):
            short_name = os.path.basename(full_name.rstrip("/\\"))
        else:
            short_name = full_name.split(".")[-1] if "." in full_name else full_name

        nodes[raw_node] = {
            "id": raw_node,
            "type": node_type,
            "name": short_name,
            "full_name": full_name,
            "file_path": file_path
        }

    def add_relation(src, rel, dst, file_path):
        if not src or not dst:
            return
        key = (src, rel, dst)
        if key in existing_relations:
            return
        existing_relations.add(key)
        register_node(src, file_path)
        register_node(dst, file_path)
        relations.append([src, rel, dst])

    # Project Node
    project_name = os.path.basename(os.path.abspath(project_path))
    project_node = f"project:{project_name}"

    # Walk through folders to identify Java files
    java_files = []
    for root, dirs, files in os.walk(project_path):
        rel_root = os.path.relpath(root, project_path)
        abs_root = os.path.join(project_path, rel_root)
        path_parts = rel_root.split(os.sep)

        # Determine current folder node
        if rel_root == ".":
            current_node = project_node
        elif len(path_parts) == 1:
            current_node = f"application:{abs_root}"
            add_relation(project_node, "contains", current_node, abs_root)
        else:
            current_node = f"package:{abs_root}"
            parent_path = os.path.join(project_path, *path_parts[:-1])
            parent_node = (
                f"application:{parent_path}" if len(path_parts) == 2 else f"package:{parent_path}"
            )
            add_relation(parent_node, "contains", current_node, abs_root)

        # Subfolders
        for d in dirs:
            subfolder_abs = os.path.join(root, d)
            sub_rel = os.path.relpath(subfolder_abs, project_path)
            sub_parts = sub_rel.split(os.sep)
            sub_node = f"application:{subfolder_abs}" if len(sub_parts) == 1 else f"package:{subfolder_abs}"
            add_relation(current_node, "contains", sub_node, abs_root)

        # Java files
        for file in files:
            if file.endswith(".java"):
                java_files.append(os.path.join(root, file))

    # Parse Java files
    parsed_files = {}
    for file_path in java_files:
        with open(file_path, "r", encoding="utf-8") as f:
            try:
                parsed_files[file_path] = javalang.parse.parse(f.read())
            except javalang.parser.JavaSyntaxError:
                continue

    # Extract Class ↔ Collection mapping
    for file_path, tree in parsed_files.items():
        package_name = tree.package.name if tree.package else None

        for type_decl in tree.types:
            if not isinstance(type_decl, javalang.tree.ClassDeclaration):
                continue

            class_name = type_decl.name
            full_class_name = f"{package_name}.{class_name}" if package_name else class_name
            class_node = f"class:{full_class_name}"

            for annotation in type_decl.annotations:
                if annotation.name == "Document":
                    for pair in annotation.element:
                        if pair.name == "collection":
                            collection = pair.value.value
                            collection_entities[full_class_name] = collection
                            rel_path = os.path.relpath(file_path, project_path)
                            add_relation(class_node, "mapped_to_collection", f"collection:{collection}", rel_path)

    # Process Files → Classes, Variables, Methods, Calls
    for file_path, tree in parsed_files.items():
        rel_path = os.path.relpath(file_path, project_path)
        abs_path = os.path.join(project_path, rel_path)

        # Folder mapping
        folder_path = os.path.dirname(abs_path)
        folder_parts = os.path.relpath(folder_path, project_path).split(os.sep)
        folder_node = (
            f"application:{folder_path}" if len(folder_parts) == 1
            else f"package:{folder_path}" if len(folder_parts) >= 2
            else project_node
        )

        file_node = f"file:{abs_path}"
        add_relation(folder_node, "contains", file_node, abs_path)

        # Build import map
        import_map = {}
        package_name = tree.package.name if tree.package else None
        for imp in tree.imports:
            if imp.path and not imp.wildcard and not imp.path.startswith(("java.", "javax.")):
                class_name = imp.path.split('.')[-1]
                import_map[class_name] = imp.path

        # Classes & Interfaces
        for type_decl in tree.types:
            if not isinstance(type_decl, (javalang.tree.ClassDeclaration, javalang.tree.InterfaceDeclaration)):
                continue

            decl_type = "class" if isinstance(type_decl, javalang.tree.ClassDeclaration) else "interface"
            full_decl_name = f"{package_name}.{type_decl.name}" if package_name else type_decl.name
            decl_node = f"{decl_type}:{full_decl_name}"
            add_relation(file_node, "declares", decl_node, rel_path)

            # Inheritance
            if isinstance(type_decl, javalang.tree.ClassDeclaration) and type_decl.implements:
                for impl in type_decl.implements:
                    if impl.name in import_map:
                        add_relation(decl_node, "implements", f"interface:{import_map[impl.name]}", rel_path)

            if type_decl.extends:
                exts = type_decl.extends if isinstance(type_decl.extends, list) else [type_decl.extends]
                for ext in exts:
                    if ext.name in import_map:
                        add_relation(decl_node, "extends", f"{decl_type}:{import_map[ext.name]}", rel_path)

            # Class Variables
            for field in getattr(type_decl, "fields", []):
                for decl in field.declarators:
                    var_node = f"variable:{full_decl_name}.{decl.name}"
                    add_relation(decl_node, "has_variable", var_node, rel_path)

            # Methods
            for method in getattr(type_decl, "methods", []):
                method_node = f"method:{full_decl_name}.{method.name}"
                add_relation(decl_node, "has_method", method_node, rel_path)

    return {
        "nodes": list(nodes.values()),
        "relations": relations
    }

# ================== Stage 2: Load JSON to Neo4j ==================
def load_json_to_neo4j(json_path, uri, user, password, db):
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    nodes = data["nodes"]
    relations = data["relations"]

    driver = GraphDatabase.driver(uri, auth=(user, password))

    def clean_db(tx):
        tx.run("MATCH (n) DETACH DELETE n")

    def import_data(tx):
        for node in nodes:
            label = node["type"].capitalize()
            tx.run(f"""
                MERGE (n:{label} {{id: $id}})
                SET n.name = $name,
                    n.full_name = $full_name
            """, id=node["id"], name=node["name"], full_name=node["full_name"])

        for src_id, rel_type, dst_id in relations:
            tx.run(f"""
                MATCH (src {{id: $src_id}})
                MATCH (dst {{id: $dst_id}})
                MERGE (src)-[:`{rel_type}`]->(dst)
            """, src_id=src_id, dst_id=dst_id)

    with driver.session(database=db) as session:
        session.execute_write(clean_db)
        session.execute_write(import_data)

    driver.close()
    print("Graph imported to Neo4j.")

# ================== Stage 3: Main Execution ==================
if __name__ == "__main__":
    project_path = "OneInsights" #Project Path
    json_path = "graph_06_08_temp.json" #JSON Path


    # Extract and Save JSON
    graph_data = extract_relations_to_json_javalang(project_path)
    with open(json_path, "w") as f:
        json.dump(graph_data, f, indent=2)

    # Load to Neo4j
    load_json_to_neo4j(
        json_path=json_path,
        uri=NEO4J_URI, #Neo4J URI
        user=NEO4J_USER,
        password=NEO4J_PASSWORD, #Neo4J Password
        db=NEO4J_DB #Neo4J Database Name
    )
