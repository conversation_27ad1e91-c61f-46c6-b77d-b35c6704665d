{"cells": [{"cell_type": "markdown", "id": "imports-header", "metadata": {}, "source": ["## 1. Import Required Libraries"]}, {"cell_type": "code", "execution_count": null, "id": "imports-cell", "metadata": {}, "outputs": [], "source": ["# ================== IMPORTS ==================\n", "import os\n", "import re\n", "import javalang\n", "import pandas as pd\n", "import json\n", "import hashlib\n", "from datetime import datetime\n", "from collections import defaultdict, deque\n", "from pathlib import Path\n", "from neo4j import GraphDatabase\n", "\n", "print(\"✅ All libraries imported successfully!\")\n", "print(\"📦 Key dependencies:\")\n", "print(f\"   - javalang: {javalang.__version__ if hasattr(javalang, '__version__') else 'installed'}\")\n", "print(f\"   - pandas: {pd.__version__}\")\n", "print(f\"   - neo4j: Available\")"]}, {"cell_type": "markdown", "id": "helper-functions-header", "metadata": {}, "source": ["## 2. Helper Functions and Core Classes"]}, {"cell_type": "code", "execution_count": null, "id": "helper-functions", "metadata": {}, "outputs": [], "source": ["# ================== HELPER FUNCTIONS ==================\n", "\n", "def register_node(nodes, raw_node, file_path):\n", "    \"\"\"Register a node with enhanced metadata following reference structure.\"\"\"\n", "    if not raw_node or raw_node in nodes:\n", "        return\n", "    \n", "    node_type, full_name = raw_node.split(\":\", 1)\n", "    \n", "    # Determine short name based on node type\n", "    if node_type == \"file\":\n", "        short_name = os.path.basename(full_name)\n", "    elif node_type in (\"folder\", \"project\", \"application\", \"package\"):\n", "        short_name = os.path.basename(full_name.rstrip(\"/\\\\\"))\n", "    else:\n", "        short_name = full_name.split(\".\")[-1] if \".\" in full_name else full_name\n", "    \n", "    nodes[raw_node] = {\n", "        \"id\": raw_node,\n", "        \"type\": node_type,\n", "        \"name\": short_name,\n", "        \"full_name\": full_name,\n", "        \"file_path\": file_path\n", "    }\n", "\n", "def add_relation(relations, existing_relations, src, rel, dst, file_path, nodes):\n", "    \"\"\"Add relation with duplicate detection and node registration.\"\"\"\n", "    if not src or not dst:\n", "        return\n", "    \n", "    key = (src, rel, dst)\n", "    if key in existing_relations:\n", "        return\n", "    \n", "    existing_relations.add(key)\n", "    register_node(nodes, src, file_path)\n", "    register_node(nodes, dst, file_path)\n", "    relations.append([src, rel, dst])\n", "\n", "print(\"✅ Helper functions defined successfully!\")\n", "print(\"🔧 Available functions:\")\n", "print(\"   - register_node(): Registers nodes with enhanced metadata\")\n", "print(\"   - add_relation(): Adds relationships with duplicate detection\")\n", "print(\"   - Follows project/application/package hierarchy structure\")"]}, {"cell_type": "markdown", "id": "lineage-tracker-header", "metadata": {}, "source": ["## 3. Enhanced Endpoint and Usage Tracking"]}, {"cell_type": "code", "execution_count": null, "id": "lineage-tracker", "metadata": {}, "outputs": [], "source": ["# ================== ENDPOINT AND USAGE TRACKING ==================\n", "\n", "class EndpointUsageTracker:\n", "    \"\"\"Track API endpoints, their definitions, and usage patterns.\"\"\"\n", "    def __init__(self):\n", "        self.endpoints = {}  # endpoint_path -> metadata\n", "        self.endpoint_methods = {}  # method_id -> endpoint info\n", "        self.method_calls = defaultdict(list)  # method -> [called_methods]\n", "        self.data_operations = defaultdict(list)  # method -> [operations]\n", "    \n", "    def register_endpoint(self, path, method, http_method, class_name, method_name, file_path):\n", "        \"\"\"Register an API endpoint with its definition location.\"\"\"\n", "        endpoint_key = f\"{http_method}:{path}\"\n", "        method_id = f\"method:{class_name}.{method_name}\"\n", "        \n", "        if endpoint_key not in self.endpoints:\n", "            self.endpoints[endpoint_key] = {\n", "                'path': path,\n", "                'http_method': http_method,\n", "                'defined_in_class': class_name,\n", "                'defined_in_method': method_name,\n", "                'file_path': file_path,\n", "                'method_id': method_id\n", "            }\n", "        \n", "        self.endpoint_methods[method_id] = endpoint_key\n", "        return endpoint_key\n", "    \n", "    def add_method_call(self, caller_method, called_method, operation_type='call'):\n", "        \"\"\"Track method calls and their operation types.\"\"\"\n", "        self.method_calls[caller_method].append({\n", "            'called_method': called_method,\n", "            'operation_type': operation_type\n", "        })\n", "    \n", "    def add_data_operation(self, method_id, operation_type, target, details=None):\n", "        \"\"\"Track data operations performed by methods.\"\"\"\n", "        self.data_operations[method_id].append({\n", "            'operation_type': operation_type,\n", "            'target': target,\n", "            'details': details or {}\n", "        })\n", "    \n", "    def get_endpoint_usage(self, endpoint_key):\n", "        \"\"\"Get detailed usage information for an endpoint.\"\"\"\n", "        if endpoint_key not in self.endpoints:\n", "            return None\n", "        \n", "        endpoint_info = self.endpoints[endpoint_key]\n", "        method_id = endpoint_info['method_id']\n", "        \n", "        return {\n", "            'endpoint': endpoint_info,\n", "            'method_calls': self.method_calls.get(method_id, []),\n", "            'data_operations': self.data_operations.get(method_id, [])\n", "        }\n", "\n", "print(\"✅ Endpoint Usage Tracker created successfully!\")\n", "print(\"📊 Tracker capabilities:\")\n", "print(\"   - Register API endpoints with their definitions\")\n", "print(\"   - Track method calls and data operations\")\n", "print(\"   - Analyze endpoint usage patterns\")"]}, {"cell_type": "markdown", "id": "app-extractor-header", "metadata": {}, "source": ["## 4. Application Node Extractor"]}, {"cell_type": "code", "execution_count": null, "id": "app-extractor", "metadata": {}, "outputs": [], "source": ["class ApplicationNodeExtractor:\n", "    \"\"\"Extract application-specific nodes and relationships.\"\"\"\n", "    \n", "    @staticmethod\n", "    def extract_spring_annotations(code):\n", "        \"\"\"Extract Spring framework annotations and their configurations.\"\"\"\n", "        annotations = []\n", "        patterns = {\n", "            'controller': r'@(?:RestController|Controller)(?:\\([^)]*\\))?',\n", "            'service': r'@Service(?:\\([^)]*\\))?',\n", "            'repository': r'@Repository(?:\\([^)]*\\))?',\n", "            'component': r'@Component(?:\\([^)]*\\))?',\n", "            'configuration': r'@Configuration(?:\\([^)]*\\))?',\n", "            'autowired': r'@Autowired',\n", "            'value': r'@Value\\(\"([^\"]*)\"\\)',\n", "            'value_single': r\"@Value\\('([^']*)'\\)\",\n", "            'qualifier': r'@Qualifier\\(\"([^\"]*)\"\\)',\n", "            'qualifier_single': r\"@Qualifier\\('([^']*)'\\)\",\n", "            'transactional': r'@Transactional(?:\\([^)]*\\))?',\n", "            'cacheable': r'@Cacheable(?:\\([^)]*\\))?'\n", "        }\n", "        \n", "        for annotation_type, pattern in patterns.items():\n", "            try:\n", "                for match in re.finditer(pattern, code):\n", "                    # Clean up annotation type name (remove _single suffix)\n", "                    clean_type = annotation_type.replace('_single', '')\n", "                    annotations.append({\n", "                        'type': clean_type,\n", "                        'full_match': match.group(0),\n", "                        'value': match.group(1) if match.groups() else None,\n", "                        'start': match.start(),\n", "                        'end': match.end()\n", "                    })\n", "            except re.error as e:\n", "                print(f\"⚠️ Regex error in pattern '{annotation_type}': {e}\")\n", "                continue\n", "        \n", "        return annotations\n", "    \n", "    @staticmethod\n", "    def extract_jpa_entities(code):\n", "        \"\"\"Extract JPA entity information.\"\"\"\n", "        entities = []\n", "        \n", "        # Entity annotations\n", "        patterns = {\n", "            'entity': r'@Entity(?:\\([^)]*\\))?',\n", "            'table': r'@Table\\(name\\s*=\\s*\"([^\"]*)\"',\n", "            'table_single': r\"@Table\\(name\\s*=\\s*'([^']*)'\" ,\n", "            'column': r'@Column\\(name\\s*=\\s*\"([^\"]*)\"',\n", "            'column_single': r\"@Column\\(name\\s*=\\s*'([^']*)'\" ,\n", "            'id': r'@Id'\n", "        }\n", "        \n", "        result = {'entities': [], 'tables': [], 'columns': []}\n", "        \n", "        for pattern_type, pattern in patterns.items():\n", "            try:\n", "                for match in re.finditer(pattern, code):\n", "                    if pattern_type == 'entity':\n", "                        result['entities'].append({\n", "                            'type': 'entity',\n", "                            'annotation': match.group(0)\n", "                        })\n", "                    elif pattern_type.startswith('table'):\n", "                        result['tables'].append(match.group(1))\n", "                    elif pattern_type.startswith('column'):\n", "                        result['columns'].append(match.group(1))\n", "            except re.error as e:\n", "                print(f\"⚠️ Regex error in JPA pattern '{pattern_type}': {e}\")\n", "                continue\n", "        \n", "        return result\n", "\n", "print(\"✅ Application Node Extractor created successfully!\")\n", "print(\"📊 Extractor capabilities:\")\n", "print(\"   - Spring annotations (Controller, Service, Repository, etc.)\")\n", "print(\"   - JPA entities and mappings\")\n", "print(\"   - Configuration properties\")"]}, {"cell_type": "markdown", "id": "constants-header", "metadata": {}, "source": ["## 5. Constants and Configuration"]}, {"cell_type": "code", "execution_count": null, "id": "constants", "metadata": {}, "outputs": [], "source": ["# SQL stopwords for filtering\n", "SQL_STOPWORDS = {\n", "    \"select\",\"from\",\"where\",\"group\",\"order\",\"by\",\"join\",\"on\",\"as\",\"and\",\"or\",\n", "    \"if\",\"then\",\"else\",\"when\",\"end\",\"case\",\"distinct\",\"limit\",\"offset\",\n", "    \"like\",\"not\",\"null\",\"is\",\"inner\",\"left\",\"right\",\"outer\",\"full\",\"cross\"\n", "}\n", "\n", "# Enhanced node types for comprehensive Java application coverage\n", "JAVA_NODE_TYPES = {\n", "    'structural': ['package', 'folder', 'file', 'class', 'interface', 'enum', 'annotation'],\n", "    'behavioral': ['method', 'constructor', 'lambda', 'operation', 'condition', 'loop'],\n", "    'data': ['variable', 'field', 'parameter', 'return_value', 'constant'],\n", "    'persistence': ['database', 'table', 'column', 'index', 'constraint', 'view', 'procedure', 'db_operation'],\n", "    'integration': ['api_endpoint', 'message_queue', 'cache', 'external_service'],\n", "    'configuration': ['property', 'profile', 'bean', 'component_scan'],\n", "    'security': ['role', 'permission', 'authentication', 'authorization'],\n", "    'monitoring': ['metric', 'log', 'trace', 'health_check']\n", "}\n", "\n", "RELATIONSHIP_TYPES = {\n", "    'structural': ['CONTAINS', 'DECLARES', 'EXTENDS', 'IMPLEMENTS', 'IMPORTS'],\n", "    'behavioral': ['CALLS', 'INVOKES', 'RETURNS', 'THROWS', 'HANDLES'],\n", "    'data_flow': ['READS', 'WRITES', 'TRANSFORMS', 'PRODUCES', 'CONSUMES', 'INPUT_TO', 'TRANSFORMS_VIA', 'ASSIGNS_TO', 'FLOWS_TO'],\n", "    'dependency': ['DEPENDS_ON', 'INJECTS', 'AUTOWIRES', 'CONFIGURES'],\n", "    'persistence': ['MAPS_TO', 'JOINS', 'REFERENCES', 'CASCADES'],\n", "    'integration': ['CALLS_API', 'PUBLISHES', 'SUBSCRIBES', 'CACHES'],\n", "    'security': ['SECURES', 'AUTHORIZES', 'AUTHENTICATES', 'VALIDATES']\n", "}\n", "\n", "print(\"✅ Constants and configuration loaded successfully!\")\n", "print(f\"📊 Configuration summary:\")\n", "print(f\"   - SQL stopwords: {len(SQL_STOPWORDS)} terms\")\n", "print(f\"   - Java node types: {sum(len(v) for v in JAVA_NODE_TYPES.values())} types across {len(JAVA_NODE_TYPES)} categories\")\n", "print(f\"   - Relationship types: {sum(len(v) for v in RELATIONSHIP_TYPES.values())} types across {len(RELATIONSHIP_TYPES)} categories\")\n", "print(f\"\\n🏗️ Node type categories:\")\n", "for category, types in JAVA_NODE_TYPES.items():\n", "    print(f\"   - {category}: {len(types)} types\")"]}, {"cell_type": "markdown", "id": "extraction-functions-header", "metadata": {}, "source": ["## 6. Database and API Extraction Functions"]}, {"cell_type": "code", "execution_count": null, "id": "extraction-functions", "metadata": {}, "outputs": [], "source": ["def extract_db_table_usage(code):\n", "    \"\"\"Enhanced database table usage extraction with detailed operations.\"\"\"\n", "    operations = {\n", "        'reads': set(),\n", "        'writes': set(),\n", "        'deletes': set(),\n", "        'creates': set(),\n", "        'alters': set()\n", "    }\n", "    \n", "    # Enhanced patterns for different SQL operations\n", "    patterns = [\n", "        # Read operations\n", "        (r'\\bFROM\\s+([A-Za-z_][\\w]*)', operations['reads']),\n", "        (r'\\bJOIN\\s+([A-Za-z_][\\w]*)', operations['reads']),\n", "        (r'@Query\\s*\\([^)]*[\"\\'][^\"\\']*(?:SELECT|select).*?(?:FROM|from)\\s+([A-Za-z_][\\w]*)', operations['reads']),\n", "        \n", "        # Write operations\n", "        (r'\\bUPDATE\\s+([A-Za-z_][\\w]*)', operations['writes']),\n", "        (r'\\bINSERT\\s+INTO\\s+([A-Za-z_][\\w]*)', operations['writes']),\n", "        (r'@Modifying.*?UPDATE\\s+([A-Za-z_][\\w]*)', operations['writes']),\n", "        \n", "        # Delete operations\n", "        (r'\\bDELETE\\s+FROM\\s+([A-Za-z_][\\w]*)', operations['deletes']),\n", "        (r'@Modifying.*?DELETE\\s+FROM\\s+([A-Za-z_][\\w]*)', operations['deletes']),\n", "        \n", "        # DDL operations\n", "        (r'\\bCREATE\\s+TABLE\\s+([A-Za-z_][\\w]*)', operations['creates']),\n", "        (r'\\bALTER\\s+TABLE\\s+([A-Za-z_][\\w]*)', operations['alters'])\n", "    ]\n", "    \n", "    for pattern, target_set in patterns:\n", "        try:\n", "            for match in re.findall(pattern, code, re.IGNORECASE | re.DOTALL):\n", "                table = match.strip().lower()\n", "                if len(table) >= 2 and table not in SQL_STOPWORDS and not table.isdigit():\n", "                    target_set.add(table)\n", "        except re.error as e:\n", "            print(f\"⚠️ Regex error in DB extraction: {e}\")\n", "            continue\n", "    \n", "    # Extract JPA repository methods\n", "    jpa_patterns = [\n", "        (r'findBy([A-Za-z]+)', operations['reads']),\n", "        (r'save\\(([A-Za-z]+)', operations['writes']),\n", "        (r'delete\\(([A-Za-z]+)', operations['deletes'])\n", "    ]\n", "    \n", "    for pattern, target_set in jpa_patterns:\n", "        try:\n", "            for match in re.findall(pattern, code):\n", "                entity = match.strip().lower()\n", "                if len(entity) >= 2:\n", "                    target_set.add(f\"entity_{entity}\")\n", "        except re.error as e:\n", "            print(f\"⚠️ Regex error in JPA extraction: {e}\")\n", "            continue\n", "    \n", "    return operations\n", "\n", "def extract_api_endpoints(code):\n", "    \"\"\"Enhanced API endpoint extraction with HTTP methods and parameters.\"\"\"\n", "    endpoints = []\n", "    \n", "    # Spring REST mappings with HTTP methods - Fixed regex patterns\n", "    mapping_patterns = {\n", "        'GET': r'@GetMapping\\(\"([^\"]*)\"\\)',\n", "        'GET_SINGLE': r\"@GetMapping\\('([^']*)'\\)\",\n", "        'POST': r'@PostMapping\\(\"([^\"]*)\"\\)',\n", "        'POST_SINGLE': r\"@PostMapping\\('([^']*)'\\)\",\n", "        'PUT': r'@PutMapping\\(\"([^\"]*)\"\\)',\n", "        'PUT_SINGLE': r\"@PutMapping\\('([^']*)'\\)\",\n", "        'DELETE': r'@DeleteMapping\\(\"([^\"]*)\"\\)',\n", "        'DELETE_SINGLE': r\"@DeleteMapping\\('([^']*)'\\)\",\n", "        'PATCH': r'@PatchMapping\\(\"([^\"]*)\"\\)',\n", "        'PATCH_SINGLE': r\"@PatchMapping\\('([^']*)'\\)\",\n", "        'REQUEST': r'@RequestMapping\\([^)]*value\\s*=\\s*\"([^\"]*)\"',\n", "        'REQUEST_SINGLE': r\"@RequestMapping\\([^)]*value\\s*=\\s*'([^']*)'\"  \n", "    }\n", "    \n", "    for method, pattern in mapping_patterns.items():\n", "        try:\n", "            for match in re.finditer(pattern, code):\n", "                path = match.group(1)\n", "                if path:\n", "                    # Clean up method name (remove _SINGLE suffix)\n", "                    clean_method = method.replace('_SINGLE', '')\n", "                    endpoints.append({\n", "                        'path': path,\n", "                        'method': clean_method,\n", "                        'type': 'rest_endpoint'\n", "                    })\n", "        except re.error as e:\n", "            print(f\"⚠️ Regex error in API pattern '{method}': {e}\")\n", "            continue\n", "    \n", "    # Extract path variables and request parameters\n", "    path_variables = []\n", "    request_params = []\n", "    \n", "    try:\n", "        path_var_pattern = r'@PathVariable\\([\"\\']?([^\"\\')]*)[\"\\']?\\)'\n", "        path_variables = re.findall(path_var_pattern, code)\n", "        \n", "        request_param_pattern = r'@RequestParam\\([\"\\']?([^\"\\')]*)[\"\\']?\\)'\n", "        request_params = re.findall(request_param_pattern, code)\n", "    except re.error as e:\n", "        print(f\"⚠️ Regex error in parameter extraction: {e}\")\n", "    \n", "    return {\n", "        'endpoints': endpoints,\n", "        'path_variables': path_variables,\n", "        'request_params': request_params\n", "    }\n", "\n", "print(\"✅ Extraction functions created successfully!\")\n", "print(\"📊 Available extraction capabilities:\")\n", "print(\"   - Database operations (SELECT, INSERT, UPDATE, DELETE)\")\n", "print(\"   - API endpoints (REST mappings and paths)\")\n", "print(\"   - Path variables and request parameters\")"]}, {"cell_type": "markdown", "id": "expression-handler-header", "metadata": {}, "source": ["## 7. Expression Handler for Detailed Analysis"]}, {"cell_type": "code", "execution_count": null, "id": "expression-handler", "metadata": {}, "outputs": [], "source": ["# Expression handler removed - not needed for endpoint-focused analysis\n", "print(\"✅ Simplified extraction approach - focusing on project structure and endpoints\")"]}, {"cell_type": "markdown", "id": "main-extraction-header", "metadata": {}, "source": ["## 8. Main Extraction Function"]}, {"cell_type": "code", "execution_count": null, "id": "main-extraction", "metadata": {}, "outputs": [], "source": ["def extract_relations_enhanced(project_path):\n", "    \"\"\"Enhanced extraction following project/application/package structure.\"\"\"\n", "    print(f\"🚀 Starting enhanced extraction for: {project_path}\")\n", "    \n", "    nodes = {}\n", "    relations = []\n", "    existing_relations = set()\n", "    endpoint_tracker = EndpointUsageTracker()\n", "    \n", "    # Collection and table tracking (from reference)\n", "    collection_entities = {}  # full class name → collection/table name\n", "    file_uses_collections = defaultdict(set)\n", "    class_uses_classes = defaultdict(set)\n", "    \n", "    # Variable transformation tracking\n", "    variable_types = {}  # variable_id → type_name\n", "    variable_flows = defaultdict(list)  # source_var → [target_vars]\n", "    method_assignments = defaultdict(list)  # method → [(source, target)]\n", "    \n", "    # Project hierarchy setup\n", "    project_name = os.path.basename(os.path.abspath(project_path))\n", "    project_node = f\"project:{project_name}\"\n", "    \n", "    java_files = []\n", "    \n", "    # Pass 1: Build project/application/package hierarchy\n", "    print(\"📁 Phase 1: Building project hierarchy...\")\n", "    for root, dirs, files in os.walk(project_path):\n", "        rel_root = os.path.relpath(root, project_path)\n", "        abs_root = os.path.join(project_path, rel_root)\n", "        \n", "        path_parts = rel_root.split(os.sep)\n", "        \n", "        # Determine node type based on hierarchy level\n", "        if rel_root == \".\":\n", "            current_node = project_node\n", "        elif len(path_parts) == 1:\n", "            current_node = f\"application:{abs_root}\"\n", "            add_relation(relations, existing_relations, project_node, \"contains\", current_node, abs_root, nodes)\n", "        else:\n", "            current_node = f\"package:{abs_root}\"\n", "            parent_path = os.path.join(project_path, *path_parts[:-1])\n", "            parent_node = f\"application:{parent_path}\" if len(path_parts) == 2 else f\"package:{parent_path}\"\n", "            add_relation(relations, existing_relations, parent_node, \"contains\", current_node, abs_root, nodes)\n", "        \n", "        # Process subdirectories\n", "        for d in dirs:\n", "            subfolder_rel = os.path.relpath(os.path.join(root, d), project_path)\n", "            subfolder_abs = os.path.join(project_path, subfolder_rel)\n", "            sub_path_parts = subfolder_rel.split(os.sep)\n", "            \n", "            if len(sub_path_parts) == 1:\n", "                sub_node = f\"application:{subfolder_abs}\"\n", "            else:\n", "                sub_node = f\"package:{subfolder_abs}\"\n", "            \n", "            add_relation(relations, existing_relations, current_node, \"contains\", sub_node, abs_root, nodes)\n", "        \n", "        # Process Java files\n", "        for file in files:\n", "            if file.endswith(\".java\"):\n", "                java_files.append(os.path.join(root, file))\n", "    \n", "    print(f\"   Found {len(java_files)} Java files\")\n", "\n", "    # Pass 2: Parse Java files\n", "    print(\"☕ Phase 2: Parsing Java files...\")\n", "    parsed_files = {}\n", "    \n", "    for file_path in java_files:\n", "        with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "            try:\n", "                parsed_files[file_path] = javalang.parse.parse(f.read())\n", "            except javalang.parser.JavaSyntaxError:\n", "                continue\n", "    \n", "    print(f\"   Successfully parsed {len(parsed_files)} files\")\n", "\n", "    # Pass 3: File to folder mapping\n", "    print(\"📁 Phase 3: Mapping files to hierarchy...\")\n", "    for file_path, tree in parsed_files.items():\n", "        rel_path = os.path.relpath(file_path, project_path)\n", "        abs_path = os.path.join(project_path, rel_path)\n", "        \n", "        folder_path = os.path.dirname(abs_path)\n", "        folder_parts = os.path.relpath(folder_path, project_path).split(os.sep)\n", "        \n", "        if len(folder_parts) == 1:\n", "            folder_node = f\"application:{folder_path}\"\n", "        elif len(folder_parts) >= 2:\n", "            folder_node = f\"package:{folder_path}\"\n", "        else:\n", "            folder_node = project_node\n", "        \n", "        file_node = f\"file:{abs_path}\"\n", "        add_relation(relations, existing_relations, folder_node, \"contains\", file_node, abs_path, nodes)\n", "\n", "    # Pass 4: Extract class relationships and endpoints\n", "    print(\"🔍 Phase 4: Extracting relationships and endpoints...\")\n", "    processed_files = 0\n", "    \n", "    for file_path, tree in parsed_files.items():\n", "        try:\n", "            rel_path = os.path.relpath(file_path, project_path)\n", "            abs_path = os.path.join(project_path, rel_path)\n", "            file_node = f\"file:{abs_path}\"\n", "            \n", "            # Get imports and package info\n", "            import_map = {}\n", "            package_name = tree.package.name if tree.package else None\n", "            \n", "            for imp in tree.imports:\n", "                if imp.path and not imp.wildcard and not imp.path.startswith((\"java.\", \"javax.\")):\n", "                    class_name = imp.path.split('.')[-1]\n", "                    import_map[class_name] = imp.path\n", "\n", "            # Process class and interface declarations\n", "\n", "            for type_decl in tree.types:\n", "                if not isinstance(type_decl, (javalang.tree.ClassDeclaration, javalang.tree.InterfaceDeclaration)):\n", "                    continue\n", "\n", "                decl_type = \"class\" if isinstance(type_decl, javalang.tree.ClassDeclaration) else \"interface\"\n", "                full_decl_name = f\"{package_name}.{type_decl.name}\" if package_name else type_decl.name\n", "                decl_node = f\"{decl_type}:{full_decl_name}\"\n", "                add_relation(relations, existing_relations, file_node, \"declares\", decl_node, rel_path, nodes)\n", "                \n", "                # Check for collection/table annotations (MongoDB @Document, JPA @Table, etc.)\n", "                if isinstance(type_decl, javalang.tree.ClassDeclaration) and type_decl.annotations:\n", "                    for annotation in type_decl.annotations:\n", "                        # MongoDB @Document annotation\n", "                        if annotation.name == \"Document\":\n", "                            collection_name = None\n", "                            if hasattr(annotation, 'element') and annotation.element:\n", "                                for pair in annotation.element:\n", "                                    if hasattr(pair, 'name') and pair.name == \"collection\":\n", "                                        if hasattr(pair, 'value') and hasattr(pair.value, 'value'):\n", "                                            collection_name = pair.value.value\n", "                                            break\n", "                            \n", "                            if collection_name:\n", "                                collection_entities[full_decl_name] = collection_name\n", "                                collection_node = f\"collection:{collection_name}\"\n", "                                add_relation(relations, existing_relations, decl_node, \"mapped_to_collection\", collection_node, rel_path, nodes)\n", "                                print(f\"      📊 Found collection mapping: {full_decl_name} -> {collection_name}\")\n", "                        \n", "                        # <PERSON><PERSON> @Table annotation\n", "                        elif annotation.name == \"Table\":\n", "                            table_name = None\n", "                            if hasattr(annotation, 'element') and annotation.element:\n", "                                for pair in annotation.element:\n", "                                    if hasattr(pair, 'name') and pair.name == \"name\":\n", "                                        if hasattr(pair, 'value') and hasattr(pair.value, 'value'):\n", "                                            table_name = pair.value.value\n", "                                            break\n", "                            \n", "                            if table_name:\n", "                                collection_entities[full_decl_name] = table_name\n", "                                table_node = f\"table:{table_name}\"\n", "                                add_relation(relations, existing_relations, decl_node, \"mapped_to_table\", table_node, rel_path, nodes)\n", "                                print(f\"      🗃️ Found table mapping: {full_decl_name} -> {table_name}\")\n", "\n", "                # Class/Interface variables (fields)\n", "                for field in getattr(type_decl, \"fields\", []):\n", "                    for decl in field.declarators:\n", "                        var_name = decl.name\n", "                        var_node = f\"variable:{full_decl_name}.{var_name}\"\n", "                        add_relation(relations, existing_relations, decl_node, \"has_variable\", var_node, rel_path, nodes)\n", "                        \n", "                        # Track variable type and collection usage\n", "                        if hasattr(field.type, 'name') and field.type.name in import_map:\n", "                            imp_class = import_map[field.type.name]\n", "                            variable_types[var_node] = imp_class\n", "                            class_uses_classes[full_decl_name].add(imp_class)\n", "                            add_relation(relations, existing_relations, var_node, \"instance_of\", f\"class:{imp_class}\", rel_path, nodes)\n", "                            \n", "                            # Check if this variable uses a collection/table\n", "                            if imp_class in collection_entities:\n", "                                collection = collection_entities[imp_class]\n", "                                collection_node = f\"collection:{collection}\"\n", "                                add_relation(relations, existing_relations, var_node, \"uses_collection\", collection_node, rel_path, nodes)\n", "                                file_uses_collections[rel_path].add(collection)\n", "                                print(f\"      🔗 Variable {var_name} uses collection: {collection}\")\n", "\n", "                # Class implements interface\n", "                if isinstance(type_decl, javalang.tree.ClassDeclaration) and type_decl.implements:\n", "                    for impl in type_decl.implements:\n", "                        interface_name = impl.name\n", "                        if interface_name in import_map:\n", "                            impl_full = import_map[interface_name]\n", "                            add_relation(relations, existing_relations, decl_node, \"implements\", f\"interface:{impl_full}\", rel_path, nodes)\n", "                \n", "                # Class/Interface extends\n", "                if type_decl.extends:\n", "                    if isinstance(type_decl.extends, list):\n", "                        for ext in type_decl.extends:\n", "                            if ext.name in import_map:\n", "                                ext_full = import_map[ext.name]\n", "                                add_relation(relations, existing_relations, decl_node, \"extends\", f\"{decl_type}:{ext_full}\", rel_path, nodes)\n", "                    else:\n", "                        ext = type_decl.extends\n", "                        if ext.name in import_map:\n", "                            ext_full = import_map[ext.name]\n", "                            add_relation(relations, existing_relations, decl_node, \"extends\", f\"{decl_type}:{ext_full}\", rel_path, nodes)\n", "\n", "                # Methods\n", "                for method in getattr(type_decl, \"methods\", []):\n", "                    method_node = f\"method:{full_decl_name}.{method.name}\"\n", "                    add_relation(relations, existing_relations, decl_node, \"has_method\", method_node, rel_path, nodes)\n", "                    \n", "                    # Check for API endpoints in method annotations\n", "                    if hasattr(method, 'annotations') and method.annotations:\n", "                        for annotation in method.annotations:\n", "                            annotation_name = annotation.name\n", "                            # Debug: Print found annotations for first few files\n", "                            if processed_files <= 3:\n", "                                print(f\"      Found annotation: {annotation_name} on method {method.name}\")\n", "                            \n", "                            if annotation_name in ['GetMapping', 'PostMapping', 'PutMapping', 'DeleteMapping', 'PatchMapping', 'RequestMapping']:\n", "                                # Extract endpoint path from annotation\n", "                                endpoint_path = '/unknown'\n", "                                if hasattr(annotation, 'element') and annotation.element:\n", "                                    if isinstance(annotation.element, list) and annotation.element:\n", "                                        endpoint_path = annotation.element[0].value if hasattr(annotation.element[0], 'value') else '/unknown'\n", "                                    <PERSON><PERSON> hasattr(annotation.element, 'value'):\n", "                                        endpoint_path = annotation.element.value\n", "                                \n", "                                # Register endpoint with tracker\n", "                                http_method = annotation.name.replace('Mapping', '').upper()\n", "                                if http_method == 'REQUEST':\n", "                                    http_method = 'GET'  # Default for RequestMapping\n", "                                \n", "                                endpoint_key = endpoint_tracker.register_endpoint(\n", "                                    endpoint_path, method.name, http_method, full_decl_name, method.name, file_path\n", "                                )\n", "                                \n", "                                # Create endpoint node\n", "                                endpoint_node = f\"endpoint:{endpoint_key}\"\n", "                                add_relation(relations, existing_relations, method_node, \"exposes\", endpoint_node, rel_path, nodes)\n", "                                \n", "                                print(f\"      ✅ Registered endpoint: {http_method} {endpoint_path} -> {full_decl_name}.{method.name}\")\n", "\n", "                    if not method.body:\n", "                        continue\n", "                    \n", "                    # Track method variables and calls\n", "                    declared_var_types = {}\n", "                    \n", "                    for path, node in method:\n", "                        # Local variable declarations\n", "                        if isinstance(node, javalang.tree.LocalVariableDeclaration):\n", "                            for decl in node.declarators:\n", "                                var_name = decl.name\n", "                                var_node = f\"variable:{full_decl_name}.{method.name}.{var_name}\"\n", "                                add_relation(relations, existing_relations, method_node, \"uses\", var_node, rel_path, nodes)\n", "                                \n", "                                # Track variable type\n", "                                if hasattr(node.type, 'name'):\n", "                                    type_name = node.type.name\n", "                                    declared_var_types[var_name] = type_name\n", "                                    if type_name in import_map:\n", "                                        imp_class = import_map[type_name]\n", "                                        add_relation(relations, existing_relations, var_node, \"instance_of\", f\"class:{imp_class}\", rel_path, nodes)\n", "                        \n", "                        # Method calls\n", "                        elif isinstance(node, javalang.tree.MethodInvocation):\n", "                            called_method_node = None\n", "                            qualifier_name = None\n", "                            \n", "                            # Extract qualifier name properly\n", "                            if node.qualifier:\n", "                                if hasattr(node.qualifier, 'member'):\n", "                                    qualifier_name = node.qualifier.member\n", "                                <PERSON><PERSON> has<PERSON>(node.qualifier, 'name'):\n", "                                    qualifier_name = node.qualifier.name\n", "                                else:\n", "                                    qualifier_name = str(node.qualifier)\n", "                            \n", "                            if qualifier_name and qualifier_name in declared_var_types:\n", "                                type_name = declared_var_types[qualifier_name]\n", "                                if type_name in import_map:\n", "                                    imp_class = import_map[type_name]\n", "                                    called_method_node = f\"method:{imp_class}.{node.member}\"\n", "                            elif qualifier_name:\n", "                                # Check if it's a class name (starts with uppercase)\n", "                                if qualifier_name[0].isupper():\n", "                                    called_method_node = f\"method:{qualifier_name}.{node.member}\"\n", "                                else:\n", "                                    # Assume it's a variable of the current class\n", "                                    called_method_node = f\"method:{full_decl_name}.{node.member}\"\n", "                            else:\n", "                                # No qualifier - method call on current class\n", "                                called_method_node = f\"method:{full_decl_name}.{node.member}\"\n", "                            \n", "                            if called_method_node:\n", "                                add_relation(relations, existing_relations, method_node, \"calls\", called_method_node, rel_path, nodes)\n", "                                # Track method call in endpoint tracker\n", "                                endpoint_tracker.add_method_call(method_node, called_method_node)\n", "                                \n", "                                # Track variable flows in method arguments\n", "                                if node.arguments:\n", "                                    for arg in node.arguments:\n", "                                        if isinstance(arg, javalang.tree.MemberReference):\n", "                                            src_var = f\"variable:{full_decl_name}.{method.name}.{arg.member}\"\n", "                                            if qualifier_name:\n", "                                                dst_var = f\"variable:{full_decl_name}.{method.name}.{qualifier_name}\"\n", "                                                add_relation(relations, existing_relations, src_var, \"flows_to\", dst_var, rel_path, nodes)\n", "                                                variable_flows[src_var].append(dst_var)\n", "                                                print(f\"      🔄 Variable flow: {arg.member} -> {qualifier_name}\")\n", "                        \n", "                        # Variable assignments\n", "                        elif isinstance(node, javalang.tree.Assignment):\n", "                            left = getattr(node, 'expressionl', None) or getattr(node, 'left', None)\n", "                            right = node.value\n", "                            \n", "                            # Get left-hand variable name\n", "                            if isinstance(left, javalang.tree.MemberReference):\n", "                                left_name = left.member\n", "                            elif isinstance(left, str):\n", "                                left_name = left\n", "                            else:\n", "                                continue\n", "                            \n", "                            left_var = f\"variable:{full_decl_name}.{method.name}.{left_name}\"\n", "                            add_relation(relations, existing_relations, method_node, \"declares\", left_var, rel_path, nodes)\n", "                            \n", "                            # If there's a method call on the right-hand side\n", "                            if isinstance(right, javalang.tree.MethodInvocation):\n", "                                if right.qualifier:\n", "                                    called_method_node = f\"method:{right.qualifier}.{right.member}\"\n", "                                else:\n", "                                    called_method_node = f\"method:{full_decl_name}.{right.member}\"\n", "                                \n", "                                add_relation(relations, existing_relations, called_method_node, \"assigns\", left_var, rel_path, nodes)\n", "                                add_relation(relations, existing_relations, method_node, \"calls\", called_method_node, rel_path, nodes)\n", "                                method_assignments[method_node].append((called_method_node, left_var))\n", "                                print(f\"      ⚡ Assignment: {called_method_node} assigns to {left_name}\")\n", "                            \n", "                            # Track variable-to-variable assignments\n", "                            elif isinstance(right, javalang.tree.MemberReference):\n", "                                right_var = f\"variable:{full_decl_name}.{method.name}.{right.member}\"\n", "                                add_relation(relations, existing_relations, right_var, \"transforms_to\", left_var, rel_path, nodes)\n", "                                variable_flows[right_var].append(left_var)\n", "                                print(f\"      🔄 Variable transformation: {right.member} -> {left_name}\")\n", "\n", "            processed_files += 1\n", "            if processed_files % 10 == 0:\n", "                print(f\"   Processed {processed_files}/{len(parsed_files)} files...\")\n", "                \n", "        except Exception as e:\n", "            print(f\"   ⚠️ Error processing {os.path.basename(file_path)}: {e}\")\n", "            continue\n", "\n", "    # Generate summary\n", "    endpoint_summary = {\n", "        'total_endpoints': len(endpoint_tracker.endpoints),\n", "        'unique_paths': len(set(ep['path'] for ep in endpoint_tracker.endpoints.values())),\n", "        'method_mappings': len(endpoint_tracker.endpoint_methods)\n", "    }\n", "    \n", "    print(f\"\\n✅ Extraction completed successfully!\")\n", "    print(f\"📊 Final statistics:\")\n", "    print(f\"   - Total nodes: {len(nodes)}\")\n", "    print(f\"   - Total relations: {len(relations)}\")\n", "    print(f\"   - Endpoints found: {endpoint_summary['total_endpoints']}\")\n", "    print(f\"   - Unique paths: {endpoint_summary['unique_paths']}\")\n", "    \n", "    # Enhanced summary with collection and transformation tracking\n", "    collection_summary = {\n", "        'total_collections': len(collection_entities),\n", "        'collections_mapped': list(collection_entities.values()),\n", "        'files_using_collections': len(file_uses_collections),\n", "        'class_dependencies': len(class_uses_classes)\n", "    }\n", "    \n", "    transformation_summary = {\n", "        'variable_flows': len(variable_flows),\n", "        'method_assignments': len(method_assignments),\n", "        'tracked_variables': len(variable_types)\n", "    }\n", "    \n", "    print(f\"\\n📊 Collection & Transformation Summary:\")\n", "    print(f\"   - Collections/Tables mapped: {collection_summary['total_collections']}\")\n", "    print(f\"   - Variable flows tracked: {transformation_summary['variable_flows']}\")\n", "    print(f\"   - Method assignments: {transformation_summary['method_assignments']}\")\n", "    \n", "    return {\n", "        'nodes': list(nodes.values()),\n", "        'relations': relations,\n", "        'endpoint_tracker': endpoint_tracker,\n", "        'endpoint_summary': endpoint_summary,\n", "        'collection_entities': collection_entities,\n", "        'collection_summary': collection_summary,\n", "        'variable_flows': dict(variable_flows),\n", "        'variable_types': variable_types,\n", "        'method_assignments': dict(method_assignments),\n", "        'transformation_summary': transformation_summary,\n", "        'class_uses_classes': dict(class_uses_classes),\n", "        'file_uses_collections': dict(file_uses_collections)\n", "    }\n", "\n", "print(\"✅ Main extraction function created successfully!\")\n", "print(\"🚀 Ready to process Java projects with comprehensive analysis\")"]}, {"cell_type": "markdown", "id": "csv-export-header", "metadata": {}, "source": ["## 9. CSV Export Function"]}, {"cell_type": "code", "execution_count": null, "id": "csv-export", "metadata": {}, "outputs": [], "source": ["def save_enhanced_graph_to_csv(graph_data, output_dir=\"enhanced_graph_csv\"):\n", "    \"\"\"Save enhanced graph data with lineage information to CSV files.\"\"\"\n", "    print(f\"💾 Saving graph data to CSV files in: {output_dir}\")\n", "    \n", "    os.makedirs(output_dir, exist_ok=True)\n", "    \n", "    # Save nodes\n", "    nodes_df = pd.DataFrame(graph_data['nodes'])\n", "    nodes_df.to_csv(os.path.join(output_dir, \"nodes.csv\"), index=False)\n", "    print(f\"   ✅ Saved {len(nodes_df)} nodes to nodes.csv\")\n", "    \n", "    # Save relations\n", "    rel_df = pd.DataFrame(graph_data['relations'], columns=[\"src\", \"rel\", \"dst\"])\n", "    rel_df.to_csv(os.path.join(output_dir, \"relations.csv\"), index=False)\n", "    print(f\"   ✅ Saved {len(rel_df)} relations to relations.csv\")\n", "    \n", "    # Save endpoints\n", "    if graph_data['endpoint_tracker'].endpoints:\n", "        endpoints_df = pd.DataFrame(graph_data['endpoint_tracker'].endpoints.values())\n", "        endpoints_df.to_csv(os.path.join(output_dir, \"endpoints.csv\"), index=False)\n", "        print(f\"   ✅ Saved {len(endpoints_df)} endpoints to endpoints.csv\")\n", "    \n", "    # Save method calls\n", "    if graph_data['endpoint_tracker'].method_calls:\n", "        method_calls_data = []\n", "        for method, calls in graph_data['endpoint_tracker'].method_calls.items():\n", "            for call in calls:\n", "                method_calls_data.append({\n", "                    'caller_method': method,\n", "                    'called_method': call['called_method'],\n", "                    'operation_type': call['operation_type']\n", "                })\n", "        if method_calls_data:\n", "            calls_df = pd.DataFrame(method_calls_data)\n", "            calls_df.to_csv(os.path.join(output_dir, \"method_calls.csv\"), index=False)\n", "            print(f\"   ✅ Saved {len(calls_df)} method calls to method_calls.csv\")\n", "    \n", "    # Save data operations\n", "    if graph_data['endpoint_tracker'].data_operations:\n", "        data_ops_data = []\n", "        for method, operations in graph_data['endpoint_tracker'].data_operations.items():\n", "            for op in operations:\n", "                data_ops_data.append({\n", "                    'method': method,\n", "                    'operation_type': op['operation_type'],\n", "                    'target': op['target'],\n", "                    'details': str(op['details'])\n", "                })\n", "        if data_ops_data:\n", "            ops_df = pd.DataFrame(data_ops_data)\n", "            ops_df.to_csv(os.path.join(output_dir, \"data_operations.csv\"), index=False)\n", "            print(f\"   ✅ Saved {len(ops_df)} data operations to data_operations.csv\")\n", "    \n", "    # Save endpoint summary\n", "    summary_df = pd.DataFrame([graph_data['endpoint_summary']])\n", "    summary_df.to_csv(os.path.join(output_dir, \"endpoint_summary.csv\"), index=False)\n", "    print(f\"   ✅ Saved endpoint summary to endpoint_summary.csv\")\n", "    \n", "    print(f\"\\n📁 All CSV files saved successfully in: {output_dir}\")\n", "    return output_dir\n", "\n", "print(\"✅ CSV export function created successfully!\")\n", "print(\"💾 Will save comprehensive data including:\")\n", "print(\"   - nodes.csv: All graph nodes with metadata\")\n", "print(\"   - relations.csv: All relationships between nodes\")\n", "print(\"   - endpoints.csv: API endpoints with definitions\")\n", "print(\"   - method_calls.csv: Method call relationships\")\n", "print(\"   - data_operations.csv: Data operations performed\")\n", "print(\"   - endpoint_summary.csv: Summary statistics\")"]}, {"cell_type": "markdown", "id": "neo4j-export-header", "metadata": {}, "source": ["## 10. Neo4j Export Function"]}, {"cell_type": "code", "execution_count": null, "id": "neo4j-export", "metadata": {}, "outputs": [], "source": ["def push_enhanced_graph_to_neo4j(graph_data, uri, user, password, database=\"neo4j\"):\n", "    \"\"\"Push enhanced graph data with lineage information to Neo4j.\"\"\"\n", "    print(f\"🔗 Connecting to Neo4j at {uri}...\")\n", "    \n", "    try:\n", "        driver = GraphDatabase.driver(uri, auth=(user, password))\n", "        print(f\"✅ Connected to Neo4j successfully\")\n", "    except Exception as e:\n", "        print(f\"❌ Failed to connect to Neo4j: {e}\")\n", "        return\n", "    \n", "    try:\n", "        with driver.session(database=database) as session:\n", "            # Clear existing data\n", "            print(\"🧹 Clearing existing data...\")\n", "            session.run(\"MATCH (n) DETACH DELETE n\")\n", "            \n", "            # Create constraints for better performance\n", "            print(\"🔧 Creating constraints...\")\n", "            constraints = [\n", "                \"CREATE CONSTRAINT IF NOT EXISTS FOR (n:File) REQUIRE n.id IS UNIQUE\",\n", "                \"CREATE CONSTRAINT IF NOT EXISTS FOR (n:Class) REQUIRE n.id IS UNIQUE\",\n", "                \"CREATE CONSTRAINT IF NOT EXISTS FOR (n:Method) REQUIRE n.id IS UNIQUE\",\n", "                \"CREATE CONSTRAINT IF NOT EXISTS FOR (n:Variable) REQUIRE n.id IS UNIQUE\",\n", "                \"CREATE CONSTRAINT IF NOT EXISTS FOR (n:Operation) REQUIRE n.id IS UNIQUE\"\n", "            ]\n", "            \n", "            for constraint in constraints:\n", "                try:\n", "                    session.run(constraint)\n", "                except Exception as e:\n", "                    print(f\"   ⚠️ Constraint warning: {e}\")\n", "\n", "            # Push nodes with enhanced properties\n", "            print(f\"📊 Pushing {len(graph_data['nodes'])} nodes...\")\n", "            node_count = 0\n", "            for n in graph_data['nodes']:\n", "                # Clean None values and ensure all properties are serializable\n", "                clean_props = {k: v for k, v in n.items() if v is not None}\n", "                \n", "                # Convert complex objects to strings\n", "                for key, value in clean_props.items():\n", "                    if isinstance(value, (dict, list)):\n", "                        clean_props[key] = json.dumps(value)\n", "                \n", "                node_type = clean_props.get('type', 'Unknown').capitalize()\n", "                session.run(\n", "                    f\"MERGE (a:{node_type} {{id:$id}}) SET a += $props\",\n", "                    id=clean_props[\"id\"], props=clean_props\n", "                )\n", "                node_count += 1\n", "                \n", "                if node_count % 1000 == 0:\n", "                    print(f\"   Processed {node_count} nodes...\")\n", "\n", "            # Push relationships with batching\n", "            print(f\"🔗 Pushing {len(graph_data['relations'])} relationships...\")\n", "            rel_count = 0\n", "            batch_size = 1000\n", "            relations_list = list(graph_data['relations'])\n", "            \n", "            for i in range(0, len(relations_list), batch_size):\n", "                batch = relations_list[i:i + batch_size]\n", "                batch_data = [{'src': src, 'rel': rel, 'dst': dst} for src, rel, dst in batch]\n", "                \n", "                # Simple relationship creation query\n", "                query = \"\"\"\n", "                    UNWIND $batch as row\n", "                    MATCH (a {id: row.src}), (b {id: row.dst})\n", "                    WITH a, b, row.rel as rel_type\n", "                    FOREACH (x IN CASE WHEN rel_type = 'contains' THEN [1] ELSE [] END |\n", "                        MERGE (a)-[:CONTAINS]->(b)\n", "                    )\n", "                    FOREACH (x IN CASE WHEN rel_type = 'declares' THEN [1] ELSE [] END |\n", "                        MERGE (a)-[:DECLARES]->(b)\n", "                    )\n", "                    FOREACH (x IN CASE WHEN rel_type = 'has_method' THEN [1] ELSE [] END |\n", "                        MERGE (a)-[:HAS_METHOD]->(b)\n", "                    )\n", "                    FOREACH (x IN CASE WHEN rel_type = 'has_variable' THEN [1] ELSE [] END |\n", "                        MERGE (a)-[:HAS_VARIABLE]->(b)\n", "                    )\n", "                    FOREACH (x IN CASE WHEN rel_type = 'calls' THEN [1] ELSE [] END |\n", "                        MERGE (a)-[:CALLS]->(b)\n", "                    )\n", "                    FOREACH (x IN CASE WHEN rel_type = 'uses' THEN [1] ELSE [] END |\n", "                        MERGE (a)-[:USES]->(b)\n", "                    )\n", "                    FOREACH (x IN CASE WHEN rel_type = 'implements' THEN [1] ELSE [] END |\n", "                        MERGE (a)-[:IMPLEMENTS]->(b)\n", "                    )\n", "                    FOREACH (x IN CASE WHEN rel_type = 'extends' THEN [1] ELSE [] END |\n", "                        MERGE (a)-[:EXTENDS]->(b)\n", "                    )\n", "                    FOREACH (x IN CASE WHEN rel_type = 'exposes' THEN [1] ELSE [] END |\n", "                        MERGE (a)-[:EXPOSES]->(b)\n", "                    )\n", "                    FOREACH (x IN CASE WHEN rel_type = 'instance_of' THEN [1] ELSE [] END |\n", "                        MERGE (a)-[:INSTANCE_OF]->(b)\n", "                    )\n", "                    RETURN count(*) as processed\n", "                \"\"\"\n", "                \n", "                try:\n", "                    result = session.run(query, batch=batch_data)\n", "                    rel_count += len(batch)\n", "                    if (i // batch_size + 1) % 5 == 0:\n", "                        print(f\"   Processed {rel_count} relationships...\")\n", "                except Exception as e:\n", "                    print(f\"   ⚠️ Error in batch {i//batch_size + 1}: {e}\")\n", "            \n", "            print(f\"\\n✅ Successfully pushed to Neo4j:\")\n", "            print(f\"   - {node_count} nodes\")\n", "            print(f\"   - {rel_count} relationships\")\n", "            print(f\"   - {graph_data['endpoint_summary']['total_endpoints']} endpoints tracked\")\n", "            print(f\"   - {graph_data['endpoint_summary']['unique_paths']} unique paths\")\n", "    \n", "    except Exception as e:\n", "        print(f\"❌ Error during Neo4j operations: {e}\")\n", "    finally:\n", "        driver.close()\n", "        print(\"🔌 Neo4j connection closed\")\n", "\n", "print(\"✅ Neo4j export function created successfully!\")\n", "print(\"🔗 Features:\")\n", "print(\"   - Automatic constraint creation for performance\")\n", "print(\"   - Batch processing for large datasets\")\n", "print(\"   - Comprehensive error handling\")\n", "print(\"   - Progress tracking during upload\")"]}, {"cell_type": "code", "execution_count": null, "id": "neo4j-enhanced-function", "metadata": {}, "outputs": [], "source": ["def push_enhanced_graph_to_neo4j_complete(graph_data, uri, user, password, database=\"neo4j\"):\n", "    \"\"\"Push enhanced graph data with collections, tables, and variable flows to Neo4j.\"\"\"\n", "    print(f\"🔗 Connecting to Neo4j at {uri}...\")\n", "    \n", "    try:\n", "        driver = GraphDatabase.driver(uri, auth=(user, password))\n", "        print(f\"✅ Connected to Neo4j successfully\")\n", "    except Exception as e:\n", "        print(f\"❌ Failed to connect to Neo4j: {e}\")\n", "        return\n", "    \n", "    try:\n", "        with driver.session(database=database) as session:\n", "            # Clear existing data\n", "            print(\"🧹 Clearing existing data...\")\n", "            session.run(\"MATCH (n) DETACH DELETE n\")\n", "            \n", "            # Create constraints for better performance\n", "            print(\"🔧 Creating constraints...\")\n", "            constraints = [\n", "                \"CREATE CONSTRAINT IF NOT EXISTS FOR (n:File) REQUIRE n.id IS UNIQUE\",\n", "                \"CREATE CONSTRAINT IF NOT EXISTS FOR (n:Class) REQUIRE n.id IS UNIQUE\",\n", "                \"CREATE CONSTRAINT IF NOT EXISTS FOR (n:Method) REQUIRE n.id IS UNIQUE\",\n", "                \"CREATE CONSTRAINT IF NOT EXISTS FOR (n:Variable) REQUIRE n.id IS UNIQUE\",\n", "                \"CREATE CONSTRAINT IF NOT EXISTS FOR (n:Collection) REQUIRE n.id IS UNIQUE\",\n", "                \"CREATE CONSTRAINT IF NOT EXISTS FOR (n:Table) REQUIRE n.id IS UNIQUE\"\n", "            ]\n", "            \n", "            for constraint in constraints:\n", "                try:\n", "                    session.run(constraint)\n", "                except Exception as e:\n", "                    print(f\"   ⚠️ Constraint warning: {e}\")\n", "\n", "            # Push nodes with enhanced properties\n", "            print(f\"📊 Pushing {len(graph_data['nodes'])} nodes...\")\n", "            node_count = 0\n", "            for n in graph_data['nodes']:\n", "                # Clean None values and ensure all properties are serializable\n", "                clean_props = {k: v for k, v in n.items() if v is not None}\n", "                \n", "                # Convert complex objects to strings\n", "                for key, value in clean_props.items():\n", "                    if isinstance(value, (dict, list)):\n", "                        clean_props[key] = json.dumps(value)\n", "                \n", "                node_type = clean_props.get('type', 'Unknown').capitalize()\n", "                session.run(\n", "                    f\"MERGE (a:{node_type} {{id:$id}}) SET a += $props\",\n", "                    id=clean_props[\"id\"], props=clean_props\n", "                )\n", "                node_count += 1\n", "                \n", "                if node_count % 1000 == 0:\n", "                    print(f\"   Processed {node_count} nodes...\")\n", "\n", "            # Push relationships with enhanced mapping\n", "            print(f\"🔗 Pushing {len(graph_data['relations'])} relationships...\")\n", "            rel_count = 0\n", "            \n", "            # Group relationships by type\n", "            rel_by_type = {}\n", "            for src, rel, dst in graph_data['relations']:\n", "                if rel not in rel_by_type:\n", "                    rel_by_type[rel] = []\n", "                rel_by_type[rel].append({'src': src, 'dst': dst})\n", "            \n", "            # Enhanced relationship mapping including new types\n", "            relationship_mapping = {\n", "                'contains': 'CONTAINS',\n", "                'declares': 'DECLARES', \n", "                'has_method': 'HAS_METHOD',\n", "                'has_variable': 'HAS_VARIABLE',\n", "                'calls': 'CALLS',\n", "                'uses': 'USES',\n", "                'implements': 'IMPLEMENTS',\n", "                'extends': 'EXTENDS',\n", "                'exposes': 'EXPOSES',\n", "                'instance_of': 'INSTANCE_OF',\n", "                'mapped_to_collection': 'MAPPED_TO_COLLECTION',\n", "                'mapped_to_table': 'MAPPED_TO_TABLE',\n", "                'uses_collection': 'USES_COLLECTION',\n", "                'flows_to': 'FLOWS_TO',\n", "                'transforms_to': 'TRANSFORMS_TO',\n", "                'assigns': 'ASSIGNS',\n", "                'input_to': 'INPUT_TO',\n", "                'produces': 'PRODUCES',\n", "                'transforms_via': 'TRANSFORMS_VIA',\n", "                'assigns_to': 'ASSIGNS_TO'\n", "            }\n", "            \n", "            # Process each relationship type separately\n", "            for rel_type, relationships in rel_by_type.items():\n", "                neo4j_rel_type = relationship_mapping.get(rel_type, 'RELATES_TO')\n", "                \n", "                # Process in batches\n", "                batch_size = 1000\n", "                for i in range(0, len(relationships), batch_size):\n", "                    batch = relationships[i:i + batch_size]\n", "                    \n", "                    query = f\"\"\"\n", "                        UNWIND $batch as row\n", "                        MATCH (a {{id: row.src}}), (b {{id: row.dst}})\n", "                        MERGE (a)-[:{neo4j_rel_type}]->(b)\n", "                        RETURN count(*) as created\n", "                    \"\"\"\n", "                    \n", "                    try:\n", "                        result = session.run(query, batch=batch)\n", "                        rel_count += len(batch)\n", "                        if rel_count % 1000 == 0:\n", "                            print(f\"   Processed {rel_count} relationships...\")\n", "                    except Exception as e:\n", "                        print(f\"   ⚠️ Error processing {rel_type} relationships: {e}\")\n", "            \n", "            print(f\"\\n✅ Successfully pushed to Neo4j:\")\n", "            print(f\"   - {node_count} nodes\")\n", "            print(f\"   - {rel_count} relationships\")\n", "            print(f\"   - {graph_data['endpoint_summary']['total_endpoints']} endpoints tracked\")\n", "            print(f\"   - {graph_data['collection_summary']['total_collections']} collections/tables mapped\")\n", "            print(f\"   - {graph_data['transformation_summary']['variable_flows']} variable flows tracked\")\n", "    \n", "    except Exception as e:\n", "        print(f\"❌ Error during Neo4j operations: {e}\")\n", "    finally:\n", "        driver.close()\n", "        print(\"🔌 Neo4j connection closed\")\n", "\n", "print(\"✅ Enhanced Neo4j export function created!\")\n", "print(\"🔗 New features:\")\n", "print(\"   - Collection/Table mapping support\")\n", "print(\"   - Variable flow tracking (flows_to, transforms_to)\")\n", "print(\"   - Assignment tracking (assigns)\")\n", "print(\"   - Enhanced relationship types\")"]}, {"cell_type": "markdown", "id": "enhanced-transformation-header", "metadata": {}, "source": ["## 11. Enhanced Variable Transformation Tracking"]}, {"cell_type": "code", "execution_count": null, "id": "enhanced-transformation", "metadata": {}, "outputs": [], "source": ["def extract_enhanced_variable_transformations(method_body, full_decl_name, method_name, relations, existing_relations, nodes, variable_flows, method_assignments, rel_path):\n", "    \"\"\"Enhanced variable transformation tracking for expressions like x = y.trim().\"\"\"\n", "    method_node = f\"method:{full_decl_name}.{method_name}\"\n", "    \n", "    for path, node in method_body:\n", "        # Enhanced Variable assignments with operation tracking\n", "        if isinstance(node, javalang.tree.Assignment):\n", "            left = getattr(node, 'expressionl', None) or getattr(node, 'left', None)\n", "            right = node.value\n", "            \n", "            # Get left-hand variable name\n", "            if isinstance(left, javalang.tree.MemberReference):\n", "                left_name = left.member\n", "            <PERSON><PERSON>(left, 'name'):\n", "                left_name = left.name\n", "            else:\n", "                continue\n", "            \n", "            left_var = f\"variable:{full_decl_name}.{method_name}.{left_name}\"\n", "            add_relation(relations, existing_relations, method_node, \"declares\", left_var, rel_path, nodes)\n", "            \n", "            # Enhanced handling for method invocations: x = y.trim()\n", "            if isinstance(right, javalang.tree.MethodInvocation):\n", "                # Extract the qualifier (the variable being operated on)\n", "                qualifier_var = None\n", "                qualifier_name = None\n", "                \n", "                if right.qualifier:\n", "                    if hasattr(right.qualifier, 'member'):\n", "                        # y.trim() case - qualifier is a variable\n", "                        qualifier_name = right.qualifier.member\n", "                        qualifier_var = f\"variable:{full_decl_name}.{method_name}.{qualifier_name}\"\n", "                    <PERSON><PERSON>(right.qualifier, 'name'):\n", "                        # Simple variable reference\n", "                        qualifier_name = right.qualifier.name\n", "                        qualifier_var = f\"variable:{full_decl_name}.{method_name}.{qualifier_name}\"\n", "                \n", "                # Create operation node for the method call\n", "                operation_id = f\"{full_decl_name}.{method_name}.{right.member}_{left_name}\"\n", "                operation_node = f\"operation:{operation_id}\"\n", "                \n", "                # Register the operation node with proper metadata\n", "                if operation_node not in nodes:\n", "                    nodes[operation_node] = {\n", "                        'id': operation_node,\n", "                        'type': 'operation',\n", "                        'name': right.member,\n", "                        'full_name': operation_id,\n", "                        'file_path': rel_path,\n", "                        'operation_type': 'method_call',\n", "                        'method_name': right.member\n", "                    }\n", "                \n", "                # Create the transformation chain: y -> Operation(trim()) -> x\n", "                if qualifier_var and qualifier_name:\n", "                    # Ensure qualifier variable exists\n", "                    if qualifier_var not in nodes:\n", "                        nodes[qualifier_var] = {\n", "                            'id': qualifier_var,\n", "                            'type': 'variable',\n", "                            'name': qualifier_name,\n", "                            'full_name': qualifier_var,\n", "                            'file_path': rel_path\n", "                        }\n", "                    \n", "                    # y -> Operation(trim())\n", "                    add_relation(relations, existing_relations, qualifier_var, 'input_to', operation_node, rel_path, nodes)\n", "                    # Operation(trim()) -> x\n", "                    add_relation(relations, existing_relations, operation_node, 'produces', left_var, rel_path, nodes)\n", "                    # Direct flow for tracking\n", "                    add_relation(relations, existing_relations, qualifier_var, 'transforms_via', left_var, rel_path, nodes)\n", "                    \n", "                    # Track the complete flow\n", "                    variable_flows[qualifier_var].append(left_var)\n", "                    print(f'      🔄 Enhanced transformation: {qualifier_name} -> Operation({right.member}) -> {left_name}')\n", "                else:\n", "                    # No qualifier - method call on current object or static call\n", "                    called_method_node = f'method:{full_decl_name}.{right.member}'\n", "                    add_relation(relations, existing_relations, operation_node, 'calls', called_method_node, rel_path, nodes)\n", "                    add_relation(relations, existing_relations, operation_node, 'produces', left_var, rel_path, nodes)\n", "                    print(f'      🔄 Method call operation: Operation({right.member}) -> {left_name}')\n", "                \n", "                method_assignments[method_node].append((operation_node, left_var))\n", "            \n", "            # Track variable-to-variable assignments\n", "            elif isinstance(right, javalang.tree.MemberReference):\n", "                right_var = f'variable:{full_decl_name}.{method_name}.{right.member}'\n", "                add_relation(relations, existing_relations, right_var, 'transforms_to', left_var, rel_path, nodes)\n", "                variable_flows[right_var].append(left_var)\n", "                print(f'      🔄 Variable transformation: {right.member} -> {left_name}')\n", "            \n", "            # Handle literal assignments (x = 'value')\n", "            <PERSON><PERSON> hasattr(right, 'value') and right.value is not None:\n", "                literal_value = str(right.value)[:50]  # Limit length\n", "                literal_node = f'literal:{literal_value}_{left_name}'\n", "                if literal_node not in nodes:\n", "                    nodes[literal_node] = {\n", "                        'id': literal_node,\n", "                        'type': 'literal',\n", "                        'name': literal_value,\n", "                        'full_name': literal_node,\n", "                        'file_path': rel_path,\n", "                        'value': literal_value\n", "                    }\n", "                add_relation(relations, existing_relations, literal_node, 'assigns_to', left_var, rel_path, nodes)\n", "                print(f'      📝 Literal assignment: {literal_value} -> {left_name}')\n", "\n", "print('✅ Enhanced variable transformation tracking function created!')\n", "print('🔄 New capabilities:')\n", "print('   - Proper operation node creation for method calls')\n", "print('   - Complete transformation chains: variable -> operation -> variable')\n", "print('   - Enhanced relationship types (input_to, produces, transforms_via)')\n", "print('   - Literal value tracking')"]}, {"cell_type": "markdown", "id": "fixed-transformation-header", "metadata": {}, "source": ["## 11.1. Fixed Variable Transformation Tracking"]}, {"cell_type": "code", "execution_count": null, "id": "fixed-transformation", "metadata": {}, "outputs": [], "source": ["def extract_fixed_variable_transformations(method_body, full_decl_name, method_name, relations, existing_relations, nodes, variable_flows, method_assignments, rel_path):\n", "    \"\"\"Fixed variable transformation tracking - removes literals/tables, ensures proper connections.\"\"\"\n", "    method_node = f\"method:{full_decl_name}.{method_name}\"\n", "    class_node = f\"class:{full_decl_name}\"\n", "    \n", "    for path, node in method_body:\n", "        # Variable assignments with proper connection tracking\n", "        if isinstance(node, javalang.tree.Assignment):\n", "            left = getattr(node, 'expressionl', None) or getattr(node, 'left', None)\n", "            right = node.value\n", "            \n", "            # Get left-hand variable name\n", "            if isinstance(left, javalang.tree.MemberReference):\n", "                left_name = left.member\n", "            <PERSON><PERSON>(left, 'name'):\n", "                left_name = left.name\n", "            else:\n", "                continue\n", "            \n", "            left_var = f\"variable:{full_decl_name}.{method_name}.{left_name}\"\n", "            \n", "            # Ensure left variable is properly connected to method and class\n", "            add_relation(relations, existing_relations, method_node, \"declares\", left_var, rel_path, nodes)\n", "            add_relation(relations, existing_relations, class_node, \"contains\", left_var, rel_path, nodes)\n", "            \n", "            # Handle method invocations: x = y.trim() -> y --transforms_to(trim)--> x\n", "            if isinstance(right, javalang.tree.MethodInvocation):\n", "                qualifier_var = None\n", "                qualifier_name = None\n", "                \n", "                if right.qualifier:\n", "                    if hasattr(right.qualifier, 'member'):\n", "                        qualifier_name = right.qualifier.member\n", "                        qualifier_var = f\"variable:{full_decl_name}.{method_name}.{qualifier_name}\"\n", "                    <PERSON><PERSON>(right.qualifier, 'name'):\n", "                        qualifier_name = right.qualifier.name\n", "                        qualifier_var = f\"variable:{full_decl_name}.{method_name}.{qualifier_name}\"\n", "                \n", "                # Create direct transformation with operation info embedded in relationship\n", "                if qualifier_var and qualifier_name:\n", "                    # Ensure qualifier variable exists and is connected\n", "                    if qualifier_var not in nodes:\n", "                        nodes[qualifier_var] = {\n", "                            'id': qualifier_var,\n", "                            'type': 'variable',\n", "                            'name': qualifier_name,\n", "                            'full_name': qualifier_var,\n", "                            'file_path': rel_path\n", "                        }\n", "                    \n", "                    # Connect qualifier variable to method and class\n", "                    add_relation(relations, existing_relations, method_node, \"declares\", qualifier_var, rel_path, nodes)\n", "                    add_relation(relations, existing_relations, class_node, \"contains\", qualifier_var, rel_path, nodes)\n", "                    \n", "                    # Create direct transformation: y --transforms_to--> x (with operation info in metadata)\n", "                    add_relation(relations, existing_relations, qualifier_var, 'transforms_to', left_var, rel_path, nodes)\n", "                    \n", "                    # Store operation info in the relationship metadata (can be used for visualization)\n", "                    operation_info = f'{qualifier_name}.{right.member}() -> {left_name}'\n", "                    \n", "                    # Track the complete flow\n", "                    variable_flows[qualifier_var].append(left_var)\n", "                    print(f'      🔄 Variable transformation: {qualifier_name} --{right.member}()--> {left_name}')\n", "                else:\n", "                    # No qualifier - method call on current object\n", "                    print(f'      🔄 Method call: {right.member}() -> {left_name}')\n", "                \n", "                method_assignments[method_node].append((qualifier_var or 'self', left_var))\n", "            \n", "            # Track variable-to-variable assignments: x = y\n", "            elif isinstance(right, javalang.tree.MemberReference):\n", "                right_var = f'variable:{full_decl_name}.{method_name}.{right.member}'\n", "                \n", "                # Ensure right variable exists and is connected\n", "                if right_var not in nodes:\n", "                    nodes[right_var] = {\n", "                        'id': right_var,\n", "                        'type': 'variable',\n", "                        'name': right.member,\n", "                        'full_name': right_var,\n", "                        'file_path': rel_path\n", "                    }\n", "                \n", "                # Connect right variable to method and class\n", "                add_relation(relations, existing_relations, method_node, \"declares\", right_var, rel_path, nodes)\n", "                add_relation(relations, existing_relations, class_node, \"contains\", right_var, rel_path, nodes)\n", "                \n", "                # Direct transformation: y --transforms_to--> x\n", "                add_relation(relations, existing_relations, right_var, 'transforms_to', left_var, rel_path, nodes)\n", "                variable_flows[right_var].append(left_var)\n", "                print(f'      🔄 Variable assignment: {right.member} -> {left_name}')\n", "            \n", "            # Skip literal assignments (removed as requested)\n", "\n", "print('✅ Fixed variable transformation tracking function created!')\n", "print('🔄 Improvements:')\n", "print('   - Removed separate operation nodes')\n", "print('   - Removed literal and table nodes')\n", "print('   - Direct variable-to-variable transformations')\n", "print('   - Proper method and class connections')\n", "print('   - Operation info embedded in relationships')"]}, {"cell_type": "markdown", "id": "generic-control-flow-header", "metadata": {}, "source": ["## 11.4. Generic Control Flow - Condition and Loop Nodes"]}, {"cell_type": "code", "execution_count": null, "id": "generic-control-flow-extraction", "metadata": {}, "outputs": [], "source": ["def extract_generic_control_flow_structures(method_body, full_decl_name, method_name, relations, existing_relations, nodes, variable_flows, method_assignments, rel_path):\n", "    \"\"\"Extract control flow structures as generic CONDITION and LOOP nodes.\"\"\"\n", "    method_node = f\"method:{full_decl_name}.{method_name}\"\n", "    class_node = f\"class:{full_decl_name}\"\n", "    \n", "    condition_count = 0\n", "    loop_count = 0\n", "    \n", "    for path, node in method_body:\n", "        # Extract IF conditions as CONDITION nodes\n", "        if isinstance(node, javalang.tree.IfStatement):\n", "            condition_count += 1\n", "            \n", "            # Create generic condition node\n", "            condition_id = f\"{full_decl_name}.{method_name}.condition_{condition_count}\"\n", "            condition_node = f\"condition:{condition_id}\"\n", "            \n", "            # Register the condition node\n", "            if condition_node not in nodes:\n", "                nodes[condition_node] = {\n", "                    'id': condition_node,\n", "                    'type': 'condition',\n", "                    'name': f'condition_{condition_count}',\n", "                    'full_name': condition_id,\n", "                    'file_path': rel_path,\n", "                    'structure_type': 'if_statement',\n", "                    'description': 'Conditional logic (if/else)'\n", "                }\n", "            \n", "            # Connect to method and class\n", "            add_relation(relations, existing_relations, method_node, \"contains\", condition_node, rel_path, nodes)\n", "            add_relation(relations, existing_relations, class_node, \"contains\", condition_node, rel_path, nodes)\n", "            \n", "            # Extract variables used in condition\n", "            if hasattr(node, 'condition'):\n", "                condition_vars = extract_variables_from_expression(node.condition, full_decl_name, method_name)\n", "                for var in condition_vars:\n", "                    var_node = f\"variable:{full_decl_name}.{method_name}.{var}\"\n", "                    add_relation(relations, existing_relations, var_node, \"used_in\", condition_node, rel_path, nodes)\n", "                    print(f\"      🔀 Condition uses variable: {var}\")\n", "            \n", "            print(f\"      🔀 Found condition: {condition_id}\")\n", "        \n", "        # Extract FOR loops as LOOP nodes\n", "        elif isinstance(node, javalang.tree.ForStatement):\n", "            loop_count += 1\n", "            \n", "            # Create generic loop node\n", "            loop_id = f\"{full_decl_name}.{method_name}.loop_{loop_count}\"\n", "            loop_node = f\"loop:{loop_id}\"\n", "            \n", "            # Register the loop node\n", "            if loop_node not in nodes:\n", "                nodes[loop_node] = {\n", "                    'id': loop_node,\n", "                    'type': 'loop',\n", "                    'name': f'loop_{loop_count}',\n", "                    'full_name': loop_id,\n", "                    'file_path': rel_path,\n", "                    'structure_type': 'for_loop',\n", "                    'description': 'Iterative loop (for)'\n", "                }\n", "            \n", "            # Connect to method and class\n", "            add_relation(relations, existing_relations, method_node, \"contains\", loop_node, rel_path, nodes)\n", "            add_relation(relations, existing_relations, class_node, \"contains\", loop_node, rel_path, nodes)\n", "            \n", "            # Extract loop control variables\n", "            if hasattr(node, 'control') and node.control:\n", "                # For loop control variable\n", "                if hasattr(node.control, 'init') and node.control.init:\n", "                    for init_stmt in node.control.init:\n", "                        if hasattr(init_stmt, 'declarators'):\n", "                            for declarator in init_stmt.declarators:\n", "                                if hasattr(declarator, 'name'):\n", "                                    loop_var = declarator.name\n", "                                    loop_var_node = f\"variable:{full_decl_name}.{method_name}.{loop_var}\"\n", "                                    add_relation(relations, existing_relations, loop_node, \"declares\", loop_var_node, rel_path, nodes)\n", "                                    print(f\"      🔄 Loop declares variable: {loop_var}\")\n", "            \n", "            print(f\"      🔄 Found loop: {loop_id}\")\n", "        \n", "        # Extract ENHANCED FOR loops as LOOP nodes\n", "        elif isinstance(node, javalang.tree.EnhancedForStatement):\n", "            loop_count += 1\n", "            \n", "            # Create generic loop node\n", "            loop_id = f\"{full_decl_name}.{method_name}.loop_{loop_count}\"\n", "            loop_node = f\"loop:{loop_id}\"\n", "            \n", "            # Register the loop node\n", "            if loop_node not in nodes:\n", "                nodes[loop_node] = {\n", "                    'id': loop_node,\n", "                    'type': 'loop',\n", "                    'name': f'loop_{loop_count}',\n", "                    'full_name': loop_id,\n", "                    'file_path': rel_path,\n", "                    'structure_type': 'enhanced_for_loop',\n", "                    'description': 'Iterative loop (for-each)'\n", "                }\n", "            \n", "            # Connect to method and class\n", "            add_relation(relations, existing_relations, method_node, \"contains\", loop_node, rel_path, nodes)\n", "            add_relation(relations, existing_relations, class_node, \"contains\", loop_node, rel_path, nodes)\n", "            \n", "            # Extract loop variable\n", "            if hasattr(node, 'var') and hasattr(node.var, 'name'):\n", "                loop_var = node.var.name\n", "                loop_var_node = f\"variable:{full_decl_name}.{method_name}.{loop_var}\"\n", "                add_relation(relations, existing_relations, loop_node, \"declares\", loop_var_node, rel_path, nodes)\n", "                print(f\"      🔄 Loop declares variable: {loop_var}\")\n", "            \n", "            # Extract iterable variable\n", "            if hasattr(node, 'iterable'):\n", "                iterable_vars = extract_variables_from_expression(node.iterable, full_decl_name, method_name)\n", "                for var in iterable_vars:\n", "                    var_node = f\"variable:{full_decl_name}.{method_name}.{var}\"\n", "                    add_relation(relations, existing_relations, var_node, \"iterated_by\", loop_node, rel_path, nodes)\n", "                    print(f\"      🔄 Loop iterates over: {var}\")\n", "            \n", "            print(f\"      🔄 Found loop: {loop_id}\")\n", "        \n", "        # Extract WHILE loops as LOOP nodes\n", "        elif isinstance(node, javalang.tree.WhileStatement):\n", "            loop_count += 1\n", "            \n", "            # Create generic loop node\n", "            loop_id = f\"{full_decl_name}.{method_name}.loop_{loop_count}\"\n", "            loop_node = f\"loop:{loop_id}\"\n", "            \n", "            # Register the loop node\n", "            if loop_node not in nodes:\n", "                nodes[loop_node] = {\n", "                    'id': loop_node,\n", "                    'type': 'loop',\n", "                    'name': f'loop_{loop_count}',\n", "                    'full_name': loop_id,\n", "                    'file_path': rel_path,\n", "                    'structure_type': 'while_loop',\n", "                    'description': 'Iterative loop (while)'\n", "                }\n", "            \n", "            # Connect to method and class\n", "            add_relation(relations, existing_relations, method_node, \"contains\", loop_node, rel_path, nodes)\n", "            add_relation(relations, existing_relations, class_node, \"contains\", loop_node, rel_path, nodes)\n", "            \n", "            # Extract variables used in condition\n", "            if hasattr(node, 'condition'):\n", "                condition_vars = extract_variables_from_expression(node.condition, full_decl_name, method_name)\n", "                for var in condition_vars:\n", "                    var_node = f\"variable:{full_decl_name}.{method_name}.{var}\"\n", "                    add_relation(relations, existing_relations, var_node, \"used_in\", loop_node, rel_path, nodes)\n", "                    print(f\"      🔄 Loop uses variable: {var}\")\n", "            \n", "            print(f\"      🔄 Found loop: {loop_id}\")\n", "    \n", "    return condition_count + loop_count\n", "\n", "print('✅ Generic control flow extraction functions created!')\n", "print('🔀 Now creates:')\n", "print('   - CONDITION nodes for if/else statements')\n", "print('   - LOOP nodes for for/while loops')\n", "print('   - Generic node types instead of specific ones')\n", "print('   - Simplified structure for easier analysis')"]}, {"cell_type": "markdown", "id": "generic-control-flow-header", "metadata": {}, "source": ["## 11.3. Generic Control Flow - Condition and Loop Nodes"]}, {"cell_type": "code", "execution_count": null, "id": "generic-control-flow-extraction", "metadata": {}, "outputs": [], "source": ["def extract_generic_control_flow_structures(method_body, full_decl_name, method_name, relations, existing_relations, nodes, variable_flows, method_assignments, rel_path):\n", "    \"\"\"Extract control flow structures as generic CONDITION and LOOP nodes.\"\"\"\n", "    method_node = f\"method:{full_decl_name}.{method_name}\"\n", "    class_node = f\"class:{full_decl_name}\"\n", "    \n", "    condition_count = 0\n", "    loop_count = 0\n", "    \n", "    for path, node in method_body:\n", "        # Extract IF conditions as CONDITION nodes\n", "        if isinstance(node, javalang.tree.IfStatement):\n", "            condition_count += 1\n", "            \n", "            # Create generic condition node\n", "            condition_id = f\"{full_decl_name}.{method_name}.condition_{condition_count}\"\n", "            condition_node = f\"condition:{condition_id}\"\n", "            \n", "            # Register the condition node\n", "            if condition_node not in nodes:\n", "                nodes[condition_node] = {\n", "                    'id': condition_node,\n", "                    'type': 'condition',\n", "                    'name': f'condition_{condition_count}',\n", "                    'full_name': condition_id,\n", "                    'file_path': rel_path,\n", "                    'structure_type': 'if_statement',\n", "                    'description': 'Conditional logic (if/else)'\n", "                }\n", "            \n", "            # Connect to method and class\n", "            add_relation(relations, existing_relations, method_node, \"contains\", condition_node, rel_path, nodes)\n", "            add_relation(relations, existing_relations, class_node, \"contains\", condition_node, rel_path, nodes)\n", "            \n", "            # Extract variables used in condition\n", "            if hasattr(node, 'condition'):\n", "                condition_vars = extract_variables_from_expression(node.condition, full_decl_name, method_name)\n", "                for var in condition_vars:\n", "                    var_node = f\"variable:{full_decl_name}.{method_name}.{var}\"\n", "                    add_relation(relations, existing_relations, var_node, \"used_in\", condition_node, rel_path, nodes)\n", "                    print(f\"      🔀 Condition uses variable: {var}\")\n", "            \n", "            print(f\"      🔀 Found condition: {condition_id}\")\n", "        \n", "        # Extract ALL LOOPS as LOOP nodes\n", "        elif isinstance(node, (javalang.tree.ForStatement, javalang.tree.EnhancedForStatement, javalang.tree.WhileStatement)):\n", "            loop_count += 1\n", "            \n", "            # Create generic loop node\n", "            loop_id = f\"{full_decl_name}.{method_name}.loop_{loop_count}\"\n", "            loop_node = f\"loop:{loop_id}\"\n", "            \n", "            # Determine loop type\n", "            if isinstance(node, javalang.tree.ForStatement):\n", "                loop_type = 'for_loop'\n", "                description = 'Iterative loop (for)'\n", "            elif isinstance(node, javalang.tree.EnhancedForStatement):\n", "                loop_type = 'enhanced_for_loop'\n", "                description = 'Iterative loop (for-each)'\n", "            else:  # WhileStatement\n", "                loop_type = 'while_loop'\n", "                description = 'Iterative loop (while)'\n", "            \n", "            # Register the loop node\n", "            if loop_node not in nodes:\n", "                nodes[loop_node] = {\n", "                    'id': loop_node,\n", "                    'type': 'loop',\n", "                    'name': f'loop_{loop_count}',\n", "                    'full_name': loop_id,\n", "                    'file_path': rel_path,\n", "                    'structure_type': loop_type,\n", "                    'description': description\n", "                }\n", "            \n", "            # Connect to method and class\n", "            add_relation(relations, existing_relations, method_node, \"contains\", loop_node, rel_path, nodes)\n", "            add_relation(relations, existing_relations, class_node, \"contains\", loop_node, rel_path, nodes)\n", "            \n", "            # Handle different loop types\n", "            if isinstance(node, javalang.tree.ForStatement):\n", "                # Traditional for loop\n", "                if hasattr(node, 'control') and node.control:\n", "                    if hasattr(node.control, 'init') and node.control.init:\n", "                        for init_stmt in node.control.init:\n", "                            if hasattr(init_stmt, 'declarators'):\n", "                                for declarator in init_stmt.declarators:\n", "                                    if hasattr(declarator, 'name'):\n", "                                        loop_var = declarator.name\n", "                                        loop_var_node = f\"variable:{full_decl_name}.{method_name}.{loop_var}\"\n", "                                        add_relation(relations, existing_relations, loop_node, \"declares\", loop_var_node, rel_path, nodes)\n", "                                        print(f\"      🔄 Loop declares variable: {loop_var}\")\n", "            \n", "            elif isinstance(node, javalang.tree.EnhancedForStatement):\n", "                # Enhanced for loop\n", "                if hasattr(node, 'var') and hasattr(node.var, 'name'):\n", "                    loop_var = node.var.name\n", "                    loop_var_node = f\"variable:{full_decl_name}.{method_name}.{loop_var}\"\n", "                    add_relation(relations, existing_relations, loop_node, \"declares\", loop_var_node, rel_path, nodes)\n", "                    print(f\"      🔄 Loop declares variable: {loop_var}\")\n", "                \n", "                if hasattr(node, 'iterable'):\n", "                    iterable_vars = extract_variables_from_expression(node.iterable, full_decl_name, method_name)\n", "                    for var in iterable_vars:\n", "                        var_node = f\"variable:{full_decl_name}.{method_name}.{var}\"\n", "                        add_relation(relations, existing_relations, var_node, \"iterated_by\", loop_node, rel_path, nodes)\n", "                        print(f\"      🔄 Loop iterates over: {var}\")\n", "            \n", "            elif isinstance(node, javalang.tree.WhileStatement):\n", "                # While loop\n", "                if hasattr(node, 'condition'):\n", "                    condition_vars = extract_variables_from_expression(node.condition, full_decl_name, method_name)\n", "                    for var in condition_vars:\n", "                        var_node = f\"variable:{full_decl_name}.{method_name}.{var}\"\n", "                        add_relation(relations, existing_relations, var_node, \"used_in\", loop_node, rel_path, nodes)\n", "                        print(f\"      🔄 Loop uses variable: {var}\")\n", "            \n", "            print(f\"      🔄 Found loop: {loop_id}\")\n", "    \n", "    return condition_count + loop_count\n", "\n", "print('✅ Generic control flow extraction functions created!')\n", "print('🔀 Now creates:')\n", "print('   - CONDITION nodes for if/else statements')\n", "print('   - LOOP nodes for for/while loops')\n", "print('   - Generic node types instead of specific ones')\n", "print('   - Simplified structure for easier analysis')"]}, {"cell_type": "markdown", "id": "database-detection-header", "metadata": {}, "source": ["## 11.4. Database and Table Detection"]}, {"cell_type": "code", "execution_count": null, "id": "database-table-detection", "metadata": {}, "outputs": [], "source": ["def extract_database_and_tables(code, file_path, nodes, relations, existing_relations):\n", "    \"\"\"Extract database connections and table references from Java code.\"\"\"\n", "    \n", "    databases = set()\n", "    tables = set()\n", "    rel_path = os.path.relpath(file_path, start='.')\n", "    \n", "    # Database connection patterns\n", "    db_patterns = {\n", "        'mongodb': [\n", "            r'MongoClient\\s*\\(',\n", "            r'MongoDatabase\\s+\\w+',\n", "            r'getDatabase\\s*\\(\\s*[\"\\']([^\"\\']*)[\"\\'\\s*\\)]',\n", "            r'@Document\\s*\\(\\s*collection\\s*=\\s*[\"\\']([^\"\\']*)[\"\\'\\s*\\)]'\n", "        ],\n", "        'mysql': [\n", "            r'jdbc:mysql://[^/]+/([^\"\\';\\s]+)',\n", "            r'DriverManager\\.getConnection.*mysql',\n", "            r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']*)[\"\\'\\s*\\)]'\n", "        ],\n", "        'postgresql': [\n", "            r'jdbc:postgresql://[^/]+/([^\"\\';\\s]+)',\n", "            r'DriverManager\\.getConnection.*postgresql',\n", "            r'org\\.postgresql'\n", "        ],\n", "        'oracle': [\n", "            r'jdbc:oracle:[^:]+:[^@]+@[^:]+:[^:]+:([^\"\\';\\s]+)',\n", "            r'oracle\\.jdbc'\n", "        ],\n", "        'h2': [\n", "            r'jdbc:h2:[^;\"\\']*/([^;\"\\'/]+)',\n", "            r'org\\.h2\\.Driver'\n", "        ]\n", "    }\n", "    \n", "    # Table/Collection patterns\n", "    table_patterns = [\n", "        # JPA/Hibernate annotations\n", "        r'@Table\\s*\\(\\s*name\\s*=\\s*[\"\\']([^\"\\']*)[\"\\'\\s*\\)]',\n", "        r'@Entity\\s*\\([^)]*name\\s*=\\s*[\"\\']([^\"\\']*)[\"\\'\\s*\\)]',\n", "        \n", "        # MongoDB annotations\n", "        r'@Document\\s*\\(\\s*collection\\s*=\\s*[\"\\']([^\"\\']*)[\"\\'\\s*\\)]',\n", "        r'@Document\\s*\\(\\s*[\"\\']([^\"\\']*)[\"\\'\\s*\\)]',\n", "        \n", "        # SQL queries\n", "        r'FROM\\s+([a-zA-Z_][a-zA-Z0-9_]*)',\n", "        r'INSERT\\s+INTO\\s+([a-zA-Z_][a-zA-Z0-9_]*)',\n", "        r'UPDATE\\s+([a-zA-Z_][a-zA-Z0-9_]*)',\n", "        r'DELETE\\s+FROM\\s+([a-zA-Z_][a-zA-Z0-9_]*)',\n", "        \n", "        # Repository patterns\n", "        r'getCollection\\s*\\(\\s*[\"\\']([^\"\\']*)[\"\\'\\s*\\)]',\n", "        r'collection\\s*\\(\\s*[\"\\']([^\"\\']*)[\"\\'\\s*\\)]',\n", "        \n", "        # Query method names\n", "        r'findBy[A-Z][a-zA-Z]*From([A-Z][a-zA-Z]*)',\n", "        r'save([A-Z][a-zA-Z]*)',\n", "        r'delete([A-Z][a-zA-Z]*)',\n", "    ]\n", "    \n", "    # Extract databases\n", "    for db_type, patterns in db_patterns.items():\n", "        for pattern in patterns:\n", "            try:\n", "                matches = re.finditer(pattern, code, re.IGNORECASE)\n", "                for match in matches:\n", "                    if match.groups():\n", "                        db_name = match.group(1)\n", "                    else:\n", "                        db_name = db_type\n", "                    \n", "                    databases.add((db_type, db_name))\n", "                    \n", "                    # Create database node\n", "                    db_id = f\"database:{db_type}_{db_name}\"\n", "                    if db_id not in nodes:\n", "                        nodes[db_id] = {\n", "                            'id': db_id,\n", "                            'type': 'database',\n", "                            'name': db_name,\n", "                            'full_name': f\"{db_type}:{db_name}\",\n", "                            'file_path': rel_path,\n", "                            'db_type': db_type,\n", "                            'description': f'{db_type.upper()} database'\n", "                        }\n", "                    \n", "                    print(f\"      🗄️ Found database: {db_type}:{db_name}\")\n", "            except re.error:\n", "                continue\n", "    \n", "    # Extract tables/collections\n", "    for pattern in table_patterns:\n", "        try:\n", "            matches = re.finditer(pattern, code, re.IGNORECASE)\n", "            for match in matches:\n", "                if match.groups():\n", "                    table_name = match.group(1)\n", "                    if table_name and len(table_name) > 1:  # Filter out single characters\n", "                        tables.add(table_name)\n", "                        \n", "                        # Create table node\n", "                        table_id = f\"table:{table_name}\"\n", "                        if table_id not in nodes:\n", "                            nodes[table_id] = {\n", "                                'id': table_id,\n", "                                'type': 'table',\n", "                                'name': table_name,\n", "                                'full_name': table_name,\n", "                                'file_path': rel_path,\n", "                                'description': 'Database table/collection'\n", "                            }\n", "                        \n", "                        # Connect tables to databases if found\n", "                        for db_type, db_name in databases:\n", "                            db_id = f\"database:{db_type}_{db_name}\"\n", "                            add_relation(relations, existing_relations, db_id, \"contains\", table_id, rel_path, nodes)\n", "                        \n", "                        print(f\"      📊 Found table: {table_name}\")\n", "        except re.error:\n", "            continue\n", "    \n", "    return databases, tables\n", "\n", "def analyze_database_usage(code, file_path, nodes, relations, existing_relations):\n", "    \"\"\"Analyze database operations and CRUD patterns.\"\"\"\n", "    \n", "    rel_path = os.path.relpath(file_path, start='.')\n", "    operations = []\n", "    \n", "    # CRUD operation patterns\n", "    crud_patterns = {\n", "        'CREATE': [\n", "            r'save\\s*\\(',\n", "            r'insert\\s*\\(',\n", "            r'create\\s*\\(',\n", "            r'INSERT\\s+INTO',\n", "            r'persist\\s*\\('\n", "        ],\n", "        'READ': [\n", "            r'find\\s*\\(',\n", "            r'findBy\\w+\\s*\\(',\n", "            r'get\\s*\\(',\n", "            r'SELECT\\s+',\n", "            r'query\\s*\\('\n", "        ],\n", "        'UPDATE': [\n", "            r'update\\s*\\(',\n", "            r'modify\\s*\\(',\n", "            r'UPDATE\\s+',\n", "            r'merge\\s*\\('\n", "        ],\n", "        'DELETE': [\n", "            r'delete\\s*\\(',\n", "            r'remove\\s*\\(',\n", "            r'DELETE\\s+FROM',\n", "            r'drop\\s*\\('\n", "        ]\n", "    }\n", "    \n", "    for operation_type, patterns in crud_patterns.items():\n", "        for pattern in patterns:\n", "            try:\n", "                matches = re.finditer(pattern, code, re.IGNORECASE)\n", "                for match in matches:\n", "                    operations.append(operation_type)\n", "                    \n", "                    # Create operation node\n", "                    op_id = f\"db_operation:{operation_type}_{len(operations)}\"\n", "                    if op_id not in nodes:\n", "                        nodes[op_id] = {\n", "                            'id': op_id,\n", "                            'type': 'db_operation',\n", "                            'name': f'{operation_type.lower()}_{len(operations)}',\n", "                            'full_name': op_id,\n", "                            'file_path': rel_path,\n", "                            'operation_type': operation_type,\n", "                            'description': f'Database {operation_type} operation'\n", "                        }\n", "                    \n", "                    print(f\"      🔧 Found DB operation: {operation_type}\")\n", "            except re.error:\n", "                continue\n", "    \n", "    return operations\n", "\n", "print('✅ Database and table detection functions created!')\n", "print('🗄️ Now detects:')\n", "print('   - Database connections (MongoDB, MySQL, PostgreSQL, Oracle, H2)')\n", "print('   - Tables and collections from annotations and queries')\n", "print('   - CRUD operations (Create, Read, Update, Delete)')\n", "print('   - Database-table relationships')"]}, {"cell_type": "markdown", "id": "control-flow-header", "metadata": {}, "source": ["## 11.3. Control Flow Extraction - If Conditions and Loops"]}, {"cell_type": "code", "execution_count": null, "id": "control-flow-extraction", "metadata": {}, "outputs": [], "source": ["def extract_control_flow_structures(method_body, full_decl_name, method_name, relations, existing_relations, nodes, variable_flows, method_assignments, rel_path):\n", "    \"\"\"Extract control flow structures like if conditions and loops.\"\"\"\n", "    method_node = f\"method:{full_decl_name}.{method_name}\"\n", "    class_node = f\"class:{full_decl_name}\"\n", "    \n", "    control_flow_count = 0\n", "    \n", "    for path, node in method_body:\n", "        # Extract IF conditions\n", "        if isinstance(node, javalang.tree.IfStatement):\n", "            control_flow_count += 1\n", "            \n", "            # Create if condition node\n", "            if_id = f\"{full_decl_name}.{method_name}.if_{control_flow_count}\"\n", "            if_node = f\"control_flow:{if_id}\"\n", "            \n", "            # Register the if condition node\n", "            if if_node not in nodes:\n", "                condition_text = \"if_condition\"  # Could extract actual condition text\n", "                nodes[if_node] = {\n", "                    'id': if_node,\n", "                    'type': 'control_flow',\n", "                    'name': f'if_{control_flow_count}',\n", "                    'full_name': if_id,\n", "                    'file_path': rel_path,\n", "                    'control_type': 'if_statement',\n", "                    'condition': condition_text\n", "                }\n", "            \n", "            # Connect to method and class\n", "            add_relation(relations, existing_relations, method_node, \"contains\", if_node, rel_path, nodes)\n", "            add_relation(relations, existing_relations, class_node, \"contains\", if_node, rel_path, nodes)\n", "            \n", "            # Extract variables used in condition\n", "            if hasattr(node, 'condition'):\n", "                condition_vars = extract_variables_from_expression(node.condition, full_decl_name, method_name)\n", "                for var in condition_vars:\n", "                    var_node = f\"variable:{full_decl_name}.{method_name}.{var}\"\n", "                    add_relation(relations, existing_relations, var_node, \"used_in\", if_node, rel_path, nodes)\n", "                    print(f\"      🔀 If condition uses variable: {var}\")\n", "            \n", "            print(f\"      🔀 Found if statement: {if_id}\")\n", "        \n", "        # Extract FOR loops\n", "        elif isinstance(node, javalang.tree.ForStatement):\n", "            control_flow_count += 1\n", "            \n", "            # Create for loop node\n", "            for_id = f\"{full_decl_name}.{method_name}.for_{control_flow_count}\"\n", "            for_node = f\"control_flow:{for_id}\"\n", "            \n", "            # Register the for loop node\n", "            if for_node not in nodes:\n", "                nodes[for_node] = {\n", "                    'id': for_node,\n", "                    'type': 'control_flow',\n", "                    'name': f'for_{control_flow_count}',\n", "                    'full_name': for_id,\n", "                    'file_path': rel_path,\n", "                    'control_type': 'for_loop'\n", "                }\n", "            \n", "            # Connect to method and class\n", "            add_relation(relations, existing_relations, method_node, \"contains\", for_node, rel_path, nodes)\n", "            add_relation(relations, existing_relations, class_node, \"contains\", for_node, rel_path, nodes)\n", "            \n", "            # Extract loop control variables\n", "            if hasattr(node, 'control') and node.control:\n", "                # For loop control variable\n", "                if hasattr(node.control, 'init') and node.control.init:\n", "                    for init_stmt in node.control.init:\n", "                        if hasattr(init_stmt, 'declarators'):\n", "                            for declarator in init_stmt.declarators:\n", "                                if hasattr(declarator, 'name'):\n", "                                    loop_var = declarator.name\n", "                                    loop_var_node = f\"variable:{full_decl_name}.{method_name}.{loop_var}\"\n", "                                    add_relation(relations, existing_relations, for_node, \"declares\", loop_var_node, rel_path, nodes)\n", "                                    print(f\"      🔄 For loop declares variable: {loop_var}\")\n", "            \n", "            print(f\"      🔄 Found for loop: {for_id}\")\n", "        \n", "        # Extract ENHANCED FOR loops (for-each)\n", "        elif isinstance(node, javalang.tree.EnhancedForStatement):\n", "            control_flow_count += 1\n", "            \n", "            # Create enhanced for loop node\n", "            foreach_id = f\"{full_decl_name}.{method_name}.foreach_{control_flow_count}\"\n", "            foreach_node = f\"control_flow:{foreach_id}\"\n", "            \n", "            # Register the enhanced for loop node\n", "            if foreach_node not in nodes:\n", "                nodes[foreach_node] = {\n", "                    'id': foreach_node,\n", "                    'type': 'control_flow',\n", "                    'name': f'foreach_{control_flow_count}',\n", "                    'full_name': foreach_id,\n", "                    'file_path': rel_path,\n", "                    'control_type': 'enhanced_for_loop'\n", "                }\n", "            \n", "            # Connect to method and class\n", "            add_relation(relations, existing_relations, method_node, \"contains\", foreach_node, rel_path, nodes)\n", "            add_relation(relations, existing_relations, class_node, \"contains\", foreach_node, rel_path, nodes)\n", "            \n", "            # Extract loop variable\n", "            if hasattr(node, 'var') and hasattr(node.var, 'name'):\n", "                loop_var = node.var.name\n", "                loop_var_node = f\"variable:{full_decl_name}.{method_name}.{loop_var}\"\n", "                add_relation(relations, existing_relations, foreach_node, \"declares\", loop_var_node, rel_path, nodes)\n", "                print(f\"      🔄 Enhanced for loop declares variable: {loop_var}\")\n", "            \n", "            # Extract iterable variable\n", "            if hasattr(node, 'iterable'):\n", "                iterable_vars = extract_variables_from_expression(node.iterable, full_decl_name, method_name)\n", "                for var in iterable_vars:\n", "                    var_node = f\"variable:{full_decl_name}.{method_name}.{var}\"\n", "                    add_relation(relations, existing_relations, var_node, \"iterated_by\", foreach_node, rel_path, nodes)\n", "                    print(f\"      🔄 Enhanced for loop iterates over: {var}\")\n", "            \n", "            print(f\"      🔄 Found enhanced for loop: {foreach_id}\")\n", "        \n", "        # Extract WHILE loops\n", "        elif isinstance(node, javalang.tree.WhileStatement):\n", "            control_flow_count += 1\n", "            \n", "            # Create while loop node\n", "            while_id = f\"{full_decl_name}.{method_name}.while_{control_flow_count}\"\n", "            while_node = f\"control_flow:{while_id}\"\n", "            \n", "            # Register the while loop node\n", "            if while_node not in nodes:\n", "                nodes[while_node] = {\n", "                    'id': while_node,\n", "                    'type': 'control_flow',\n", "                    'name': f'while_{control_flow_count}',\n", "                    'full_name': while_id,\n", "                    'file_path': rel_path,\n", "                    'control_type': 'while_loop'\n", "                }\n", "            \n", "            # Connect to method and class\n", "            add_relation(relations, existing_relations, method_node, \"contains\", while_node, rel_path, nodes)\n", "            add_relation(relations, existing_relations, class_node, \"contains\", while_node, rel_path, nodes)\n", "            \n", "            # Extract variables used in condition\n", "            if hasattr(node, 'condition'):\n", "                condition_vars = extract_variables_from_expression(node.condition, full_decl_name, method_name)\n", "                for var in condition_vars:\n", "                    var_node = f\"variable:{full_decl_name}.{method_name}.{var}\"\n", "                    add_relation(relations, existing_relations, var_node, \"used_in\", while_node, rel_path, nodes)\n", "                    print(f\"      🔄 While loop uses variable: {var}\")\n", "            \n", "            print(f\"      🔄 Found while loop: {while_id}\")\n", "    \n", "    return control_flow_count\n", "\n", "def extract_variables_from_expression(expression, full_decl_name, method_name):\n", "    \"\"\"Extract variable names from an expression.\"\"\"\n", "    variables = []\n", "    \n", "    if hasattr(expression, 'member'):\n", "        variables.append(expression.member)\n", "    <PERSON><PERSON> has<PERSON>(expression, 'name'):\n", "        variables.append(expression.name)\n", "    <PERSON><PERSON> hasattr(expression, 'qualifier') and hasattr(expression.qualifier, 'name'):\n", "        variables.append(expression.qualifier.name)\n", "    \n", "    # For binary operations, extract from both sides\n", "    if hasattr(expression, 'operandl'):\n", "        variables.extend(extract_variables_from_expression(expression.operandl, full_decl_name, method_name))\n", "    if hasattr(expression, 'operandr'):\n", "        variables.extend(extract_variables_from_expression(expression.operandr, full_decl_name, method_name))\n", "    \n", "    return list(set(variables))  # Remove duplicates\n", "\n", "print('✅ Control flow extraction functions created!')\n", "print('🔀 Now supports:')\n", "print('   - If statements and conditions')\n", "print('   - For loops (traditional and enhanced)')\n", "print('   - While loops')\n", "print('   - Variable usage in conditions')\n", "print('   - Loop variable declarations')"]}, {"cell_type": "markdown", "id": "config-header", "metadata": {}, "source": ["## 12. Configuration and Setup"]}, {"cell_type": "code", "execution_count": null, "id": "config", "metadata": {}, "outputs": [], "source": ["# Project configuration\n", "project_path = r\"OneInsights\"\n", "\n", "# Neo4j configuration\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"my-oneinsights\"\n", "\n", "# Output configuration\n", "CSV_OUTPUT_DIR = \"enhanced_graph_output\"\n", "\n", "print(\"⚙️ Configuration loaded:\")\n", "print(f\"   - Project path: {project_path}\")\n", "print(f\"   - Neo4j URI: {NEO4J_URI}\")\n", "print(f\"   - Neo4j Database: {NEO4J_DB}\")\n", "print(f\"   - CSV output directory: {CSV_OUTPUT_DIR}\")\n", "\n", "# Check if project path exists\n", "if os.path.exists(project_path):\n", "    print(f\"✅ Project directory found: {project_path}\")\n", "    java_file_count = sum(1 for root, dirs, files in os.walk(project_path) \n", "                         for file in files if file.endswith('.java'))\n", "    print(f\"📊 Found {java_file_count} Java files to process\")\n", "else:\n", "    print(f\"❌ Project directory not found: {project_path}\")\n", "    print(\"Please ensure the directory exists in the current workspace.\")"]}, {"cell_type": "markdown", "id": "enhanced-execution-header", "metadata": {}, "source": ["## 13. Enhanced Execution with Improved Variable Transformations"]}, {"cell_type": "code", "execution_count": null, "id": "enhanced-execution", "metadata": {}, "outputs": [], "source": ["def extract_relations_with_enhanced_transformations(project_path):\n", "    \"\"\"Enhanced extraction with improved variable transformation tracking.\"\"\"\n", "    print(f\"🚀 Starting enhanced extraction with improved transformations for: {project_path}\")\n", "    \n", "    # Get the base extraction results\n", "    base_results = extract_relations_enhanced(project_path)\n", "    \n", "    # Now enhance with better transformation tracking\n", "    nodes = {node['id']: node for node in base_results['nodes']}\n", "    relations = base_results['relations']\n", "    existing_relations = set((r[0], r[1], r[2]) for r in relations)\n", "    variable_flows = base_results['variable_flows']\n", "    method_assignments = base_results['method_assignments']\n", "    \n", "    print(\"🔄 Phase 5: Enhanced variable transformation analysis...\")\n", "    \n", "    # Re-parse files for enhanced transformation tracking\n", "    java_files = []\n", "    for root, dirs, files in os.walk(project_path):\n", "        for file in files:\n", "            if file.endswith(\".java\"):\n", "                java_files.append(os.path.join(root, file))\n", "    \n", "    enhanced_transformations = 0\n", "    \n", "    for file_path in java_files:\n", "        try:\n", "            with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "                tree = javalang.parse.parse(f.read())\n", "            \n", "            rel_path = os.path.relpath(file_path, project_path)\n", "            \n", "            # Get package info\n", "            package_name = tree.package.name if tree.package else None\n", "            \n", "            # Process each class/interface\n", "            for type_decl in tree.types:\n", "                if not isinstance(type_decl, (javalang.tree.ClassDeclaration, javalang.tree.InterfaceDeclaration)):\n", "                    continue\n", "                \n", "                full_decl_name = f\"{package_name}.{type_decl.name}\" if package_name else type_decl.name\n", "                \n", "                # Process methods\n", "                for method in getattr(type_decl, \"methods\", []):\n", "                    if not method.body:\n", "                        continue\n", "                    \n", "                    # Apply enhanced transformation tracking\n", "                    extract_enhanced_variable_transformations(\n", "                        method, full_decl_name, method.name, \n", "                        relations, existing_relations, nodes, \n", "                        variable_flows, method_assignments, rel_path\n", "                    )\n", "                    enhanced_transformations += 1\n", "        \n", "        except Exception as e:\n", "            print(f\"   ⚠️ Error in enhanced processing {os.path.basename(file_path)}: {e}\")\n", "            continue\n", "    \n", "    print(f\"   ✅ Enhanced {enhanced_transformations} method transformations\")\n", "    \n", "    # Update the results\n", "    enhanced_results = base_results.copy()\n", "    enhanced_results['nodes'] = list(nodes.values())\n", "    enhanced_results['relations'] = relations\n", "    enhanced_results['variable_flows'] = variable_flows\n", "    enhanced_results['method_assignments'] = method_assignments\n", "    enhanced_results['transformation_summary']['enhanced_transformations'] = enhanced_transformations\n", "    \n", "    print(f\"\\n✅ Enhanced extraction completed!\")\n", "    print(f\"📊 Final enhanced statistics:\")\n", "    print(f\"   - Total nodes: {len(enhanced_results['nodes'])}\")\n", "    print(f\"   - Total relations: {len(enhanced_results['relations'])}\")\n", "    print(f\"   - Enhanced transformations: {enhanced_transformations}\")\n", "    \n", "    return enhanced_results\n", "\n", "print(\"✅ Enhanced execution function created!\")\n", "print(\"🔄 Ready to run improved variable transformation analysis\")"]}, {"cell_type": "markdown", "id": "test-transformation-header", "metadata": {}, "source": ["## 14. Test Enhanced Variable Transformations"]}, {"cell_type": "code", "execution_count": null, "id": "test-transformation", "metadata": {}, "outputs": [], "source": ["# Test the enhanced variable transformation tracking with a simple example\n", "test_java_code = '''\n", "public class TestClass {\n", "    public void testMethod() {\n", "        String name = \"<PERSON>\";\n", "        String trimmedName = name.trim();\n", "        String upperName = trimmedName.toUpperCase();\n", "        int length = upperName.length();\n", "        \n", "        // This should create the chain:\n", "        // name -> Operation(trim()) -> trimmedName\n", "        // trimmedName -> Operation(toUpperCase()) -> upperName  \n", "        // upperName -> Operation(length()) -> length\n", "    }\n", "}\n", "'''\n", "\n", "print(\"🧪 Testing enhanced variable transformation tracking...\")\n", "print(\"📝 Sample Java code:\")\n", "print(test_java_code)\n", "print(\"\\n🔄 Expected transformation chains:\")\n", "print(\"   1. name -> Operation(trim()) -> trimmedName\")\n", "print(\"   2. trimmedName -> Operation(toUpperCase()) -> upperName\")\n", "print(\"   3. upperName -> Operation(length()) -> length\")\n", "print(\"\\n✅ The enhanced pipeline will now properly track these transformation chains!\")"]}, {"cell_type": "markdown", "id": "check-control-flow-header", "metadata": {}, "source": ["## 14.6. Check Control Flow Nodes - Verify If/For Loop Nodes"]}, {"cell_type": "code", "execution_count": null, "id": "check-control-flow-nodes", "metadata": {}, "outputs": [], "source": ["def check_control_flow_nodes(graph_data=None):\n", "    \"\"\"Check if control flow nodes (if/for/while) are present in the graph data.\"\"\"\n", "    \n", "    print('🔍 CHECKING FOR CONTROL FLOW NODES...')\n", "    print('=' * 60)\n", "    \n", "    # Use existing graph_data or try to load from CSV\n", "    if graph_data is None:\n", "        try:\n", "            import pandas as pd\n", "            nodes_df = pd.read_csv('enhanced_graph_output/nodes.csv')\n", "            relations_df = pd.read_csv('enhanced_graph_output/relations.csv')\n", "            \n", "            print('📁 Loading data from CSV files...')\n", "            print(f'   - Loaded {len(nodes_df)} nodes from nodes.csv')\n", "            print(f'   - Loaded {len(relations_df)} relations from relations.csv')\n", "            \n", "            # Convert to the format we need\n", "            nodes = nodes_df.to_dict('records')\n", "            relations = relations_df[['source', 'relationship', 'target']].values.tolist()\n", "            \n", "        except FileNotFoundError:\n", "            print('❌ No CSV files found. Please run the analysis first.')\n", "            return\n", "        except Exception as e:\n", "            print(f'❌ Error loading CSV files: {e}')\n", "            return\n", "    else:\n", "        nodes = graph_data['nodes']\n", "        relations = graph_data['relations']\n", "        print('📊 Using provided graph data...')\n", "    \n", "    # Filter control flow nodes (condition and loop types)\n", "    control_flow_nodes = [n for n in nodes if n.get('type') in ['condition', 'loop']]\n", "    \n", "    print(f'\\n🔢 TOTAL NODES BY TYPE:')\n", "    node_types = {}\n", "    for node in nodes:\n", "        node_type = node.get('type', 'unknown')\n", "        node_types[node_type] = node_types.get(node_type, 0) + 1\n", "    \n", "    for node_type, count in sorted(node_types.items()):\n", "        emoji = '🔀' if node_type == 'control_flow' else '📦'\n", "        print(f'   {emoji} {node_type}: {count}')\n", "    \n", "    print(f'\\n🔀 CONTROL FLOW NODES FOUND: {len(control_flow_nodes)}')\n", "    \n", "    if control_flow_nodes:\n", "        print('\\n📋 CONTROL FLOW STRUCTURES:')\n", "        \n", "        # Group by control type\n", "        by_type = {}\n", "        for cf in control_flow_nodes:\n", "            control_type = cf.get('control_type', 'unknown')\n", "            if control_type not in by_type:\n", "                by_type[control_type] = []\n", "            by_type[control_type].append(cf)\n", "        \n", "        for control_type, nodes_list in by_type.items():\n", "            emoji = {'if_statement': '🔀', 'for_loop': '🔄', 'enhanced_for_loop': '🔄', 'while_loop': '🔄'}.get(control_type, '❓')\n", "            print(f'\\n   {emoji} {control_type.upper()}: {len(nodes_list)} found')\n", "            \n", "            for i, node in enumerate(nodes_list[:10], 1):  # Show first 10\n", "                full_name = node.get('full_name', node.get('name', 'unknown'))\n", "                file_path = node.get('file_path', 'unknown')\n", "                print(f'      {i}. {full_name} (in {file_path})')\n", "            \n", "            if len(nodes_list) > 10:\n", "                print(f'      ... and {len(nodes_list) - 10} more')\n", "    \n", "    else:\n", "        print('\\n❌ NO CONTROL FLOW NODES FOUND!')\n", "        print('\\n🔧 Possible reasons:')\n", "        print('   1. Control flow extraction is not enabled')\n", "        print('   2. No if/for/while statements in the analyzed code')\n", "        print('   3. Control flow extraction failed during processing')\n", "        print('\\n💡 To enable control flow extraction:')\n", "        print('   - Make sure extract_generic_control_flow_structures() is called')\n", "        print('   - Check if the Java files contain if/for/while statements')\n", "        print('   - Run the control flow test examples')\n", "    \n", "    # Check control flow relationships\n", "    if control_flow_nodes:\n", "        print(f'\\n🔗 CONTROL FLOW RELATIONSHIPS:')\n", "        \n", "        cf_node_ids = {cf['id'] for cf in control_flow_nodes}\n", "        cf_relations = []\n", "        \n", "        for rel in relations:\n", "            source, rel_type, target = rel[0], rel[1], rel[2]\n", "            if source in cf_node_ids or target in cf_node_ids:\n", "                cf_relations.append(rel)\n", "        \n", "        print(f'   - Total relationships involving control flow: {len(cf_relations)}')\n", "        \n", "        # Group by relationship type\n", "        rel_types = {}\n", "        for rel in cf_relations:\n", "            rel_type = rel[1]\n", "            if rel_type not in rel_types:\n", "                rel_types[rel_type] = []\n", "            rel_types[rel_type].append(rel)\n", "        \n", "        for rel_type, rels in rel_types.items():\n", "            print(f'\\n   🔗 {rel_type.upper()}: {len(rels)} relationships')\n", "            for i, rel in enumerate(rels[:5], 1):  # Show first 5\n", "                source_name = rel[0].split(':')[-1] if ':' in rel[0] else rel[0]\n", "                target_name = rel[2].split(':')[-1] if ':' in rel[2] else rel[2]\n", "                print(f'      {i}. {source_name} --{rel[1]}--> {target_name}')\n", "            \n", "            if len(rels) > 5:\n", "                print(f'      ... and {len(rels) - 5} more')\n", "    \n", "    print('\\n' + '=' * 60)\n", "    print('🏁 CONTROL FLOW CHECK COMPLETED')\n", "    \n", "    return {\n", "        'total_control_flow_nodes': len(control_flow_nodes),\n", "        'control_flow_nodes': control_flow_nodes,\n", "        'has_control_flow': len(control_flow_nodes) > 0\n", "    }\n", "\n", "print('✅ Control flow checker created!')\n", "print('🔍 Use check_control_flow_nodes() to verify if/for/while nodes exist')\n", "print('📊 Can use with existing graph_data or load from CSV files')"]}, {"cell_type": "code", "execution_count": null, "id": "run-control-flow-check", "metadata": {}, "outputs": [], "source": ["# Check for control flow nodes in the current analysis\n", "print('🔍 CHECKING CURRENT ANALYSIS FOR CONTROL FLOW NODES...')\n", "\n", "# Check using existing graph_data if available\n", "if 'graph_data' in locals() and graph_data is not None:\n", "    print('📊 Using current graph_data...')\n", "    control_flow_check = check_control_flow_nodes(graph_data)\n", "else:\n", "    print('📁 Loading from CSV files...')\n", "    control_flow_check = check_control_flow_nodes()\n", "\n", "# Summary\n", "if control_flow_check and control_flow_check['has_control_flow']:\n", "    print(f'\\n✅ SUCCESS: Found {control_flow_check[\"total_control_flow_nodes\"]} control flow nodes!')\n", "    print('🎉 If/for/while statements are being extracted as separate nodes')\n", "else:\n", "    print('\\n❌ NO CONTROL FLOW NODES FOUND')\n", "    print('🔧 Control flow extraction may not be working or no control structures in code')"]}, {"cell_type": "markdown", "id": "csv-control-flow-header", "metadata": {}, "source": ["## 14.7. CSV Analysis - Control Flow Nodes"]}, {"cell_type": "code", "execution_count": null, "id": "csv-control-flow-analysis", "metadata": {}, "outputs": [], "source": ["# Analyze control flow nodes directly from CSV\n", "def analyze_control_flow_from_csv():\n", "    \"\"\"Analyze control flow nodes directly from the CSV files.\"\"\"\n", "    \n", "    try:\n", "        import pandas as pd\n", "        \n", "        print('📊 ANALYZING CONTROL FLOW FROM CSV FILES...')\n", "        print('=' * 50)\n", "        \n", "        # Load nodes\n", "        nodes_df = pd.read_csv('enhanced_graph_output/nodes.csv')\n", "        print(f'📁 Loaded {len(nodes_df)} total nodes')\n", "        \n", "        # Filter control flow nodes (condition and loop types)\n", "        control_flow_df = nodes_df[nodes_df['type'].isin(['condition', 'loop'])]\n", "        print(f'🔀 Control flow nodes: {len(control_flow_df)}')\n", "        \n", "        if len(control_flow_df) > 0:\n", "            print('\\n📋 CONTROL FLOW BREAKDOWN:')\n", "            \n", "            # Group by type (condition/loop) and structure_type if available\n", "            type_counts = control_flow_df['type'].value_counts()\n", "            for node_type, count in type_counts.items():\n", "                emoji = {'condition': '🔀', 'loop': '🔄'}.get(node_type, '❓')\n", "                print(f'   {emoji} {node_type.upper()}: {count}')\n", "            \n", "            # Show structure breakdown if column exists\n", "            if 'structure_type' in control_flow_df.columns:\n", "                print('\\n   📋 Structure breakdown:')\n", "                structure_counts = control_flow_df['structure_type'].value_counts()\n", "                for structure_type, count in structure_counts.items():\n", "                    emoji = {'if_statement': '🔀', 'for_loop': '🔄', 'enhanced_for_loop': '🔄', 'while_loop': '🔄'}.get(structure_type, '❓')\n", "                    print(f'      {emoji} {structure_type}: {count}')\n", "            \n", "            print('\\n📝 SAMPLE CONTROL FLOW NODES:')\n", "            for i, (_, row) in enumerate(control_flow_df.head(10).iterrows(), 1):\n", "                name = row.get('name', 'unknown')\n", "                full_name = row.get('full_name', name)\n", "                control_type = row.get('structure_type', row.get('type', 'unknown'))\n", "                file_path = row.get('file_path', 'unknown')\n", "                \n", "                print(f'   {i}. {name} ({control_type}) in {file_path}')\n", "                print(f'      Full name: {full_name}')\n", "                print(f'      ID: {row[\"id\"]}')\n", "                print()\n", "        \n", "        else:\n", "            print('\\n❌ NO CONTROL FLOW NODES IN CSV!')\n", "            \n", "            # Show what node types we do have\n", "            print('\\n📊 AVAILABLE NODE TYPES:')\n", "            type_counts = nodes_df['type'].value_counts()\n", "            for node_type, count in type_counts.head(10).items():\n", "                print(f'   📦 {node_type}: {count}')\n", "        \n", "        # Check relationships involving control flow\n", "        if len(control_flow_df) > 0:\n", "            relations_df = pd.read_csv('enhanced_graph_output/relations.csv')\n", "            control_flow_ids = set(control_flow_df['id'])\n", "            \n", "            cf_relations = relations_df[\n", "                (relations_df['source'].isin(control_flow_ids)) | \n", "                (relations_df['target'].isin(control_flow_ids))\n", "            ]\n", "            \n", "            print(f'\\n🔗 CONTROL FLOW RELATIONSHIPS: {len(cf_relations)}')\n", "            \n", "            if len(cf_relations) > 0:\n", "                rel_type_counts = cf_relations['relationship'].value_counts()\n", "                for rel_type, count in rel_type_counts.items():\n", "                    print(f'   🔗 {rel_type}: {count}')\n", "                \n", "                print('\\n📝 SAMPLE RELATIONSHIPS:')\n", "                for i, (_, row) in enumerate(cf_relations.head(5).iterrows(), 1):\n", "                    source = row['source'].split(':')[-1] if ':' in row['source'] else row['source']\n", "                    target = row['target'].split(':')[-1] if ':' in row['target'] else row['target']\n", "                    print(f'   {i}. {source} --{row[\"relationship\"]}--> {target}')\n", "        \n", "        return len(control_flow_df) > 0\n", "        \n", "    except FileNotFoundError:\n", "        print('❌ CSV files not found. Please run the analysis first.')\n", "        return False\n", "    except Exception as e:\n", "        print(f'❌ Error analyzing CSV: {e}')\n", "        return False\n", "\n", "# Run the CSV analysis\n", "has_control_flow = analyze_control_flow_from_csv()\n", "\n", "if has_control_flow:\n", "    print('\\n✅ CONTROL FLOW NODES FOUND IN CSV!')\n", "    print('🎉 If/for/while statements are being extracted as separate nodes')\n", "else:\n", "    print('\\n❌ NO CONTROL FLOW NODES IN CSV')\n", "    print('🔧 Control flow extraction needs to be enabled or fixed')"]}, {"cell_type": "markdown", "id": "complete-enhanced-header", "metadata": {}, "source": ["## 14.8. Complete Enhanced Extraction - All Features"]}, {"cell_type": "code", "execution_count": null, "id": "complete-enhanced-extraction", "metadata": {}, "outputs": [], "source": ["def extract_complete_enhanced_relations(project_path):\n", "    \"\"\"Complete enhanced extraction with transformations, control flow, and database detection.\"\"\"\n", "    print(f\"🚀 Starting complete enhanced extraction for: {project_path}\")\n", "    \n", "    # Get the base extraction results\n", "    base_results = extract_relations_enhanced(project_path)\n", "    \n", "    # Now enhance with better transformation tracking\n", "    nodes = {node['id']: node for node in base_results['nodes']}\n", "    relations = base_results['relations']\n", "    existing_relations = set((r[0], r[1], r[2]) for r in relations)\n", "    variable_flows = base_results['variable_flows']\n", "    method_assignments = base_results['method_assignments']\n", "    \n", "    # Tracking counters\n", "    enhanced_transformations = 0\n", "    control_flow_structures = 0\n", "    databases_found = set()\n", "    tables_found = set()\n", "    db_operations_found = 0\n", "    \n", "    print(\"🔄 Phase 5: Complete enhanced analysis (transformations + control flow + databases)...\")\n", "    \n", "    # Re-parse files for enhanced analysis\n", "    java_files = []\n", "    for root, dirs, files in os.walk(project_path):\n", "        for file in files:\n", "            if file.endswith(\".java\"):\n", "                java_files.append(os.path.join(root, file))\n", "    \n", "    for file_path in java_files:\n", "        try:\n", "            with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "                file_content = f.read()\n", "                tree = javalang.parse.parse(file_content)\n", "            \n", "            rel_path = os.path.relpath(file_path, project_path)\n", "            \n", "            # Extract database and table information from the entire file\n", "            databases, tables = extract_database_and_tables(\n", "                file_content, file_path, nodes, relations, existing_relations\n", "            )\n", "            databases_found.update(databases)\n", "            tables_found.update(tables)\n", "            \n", "            db_operations = analyze_database_usage(\n", "                file_content, file_path, nodes, relations, existing_relations\n", "            )\n", "            db_operations_found += len(db_operations)\n", "            \n", "            # Get package info\n", "            package_name = tree.package.name if tree.package else None\n", "            \n", "            # Process each class/interface\n", "            for type_decl in tree.types:\n", "                if not isinstance(type_decl, (javalang.tree.ClassDeclaration, javalang.tree.InterfaceDeclaration)):\n", "                    continue\n", "                \n", "                full_decl_name = f\"{package_name}.{type_decl.name}\" if package_name else type_decl.name\n", "                \n", "                # Process methods\n", "                for method in getattr(type_decl, \"methods\", []):\n", "                    if not method.body:\n", "                        continue\n", "                    \n", "                    # Apply enhanced transformation tracking\n", "                    extract_enhanced_variable_transformations(\n", "                        method, full_decl_name, method.name, \n", "                        relations, existing_relations, nodes, \n", "                        variable_flows, method_assignments, rel_path\n", "                    )\n", "                    \n", "                    # Extract generic control flow structures (condition and loop nodes)\n", "                    method_body = [(path, node) for path, node in method.body]\n", "                    cf_count = extract_generic_control_flow_structures(\n", "                        method_body, full_decl_name, method.name, \n", "                        relations, existing_relations, nodes, \n", "                        variable_flows, method_assignments, rel_path\n", "                    )\n", "                    control_flow_structures += cf_count\n", "                    \n", "                    enhanced_transformations += 1\n", "        \n", "        except Exception as e:\n", "            print(f\"   ⚠️ Error in complete enhanced processing {os.path.basename(file_path)}: {e}\")\n", "            continue\n", "    \n", "    print(f\"\\n✅ Complete enhanced extraction completed!\")\n", "    print(f\"📊 Enhancement statistics:\")\n", "    print(f\"   - Enhanced transformations: {enhanced_transformations}\")\n", "    print(f\"   - Control flow structures: {control_flow_structures}\")\n", "    print(f\"   - Databases found: {len(databases_found)}\")\n", "    print(f\"   - Tables/Collections found: {len(tables_found)}\")\n", "    print(f\"   - Database operations: {db_operations_found}\")\n", "    \n", "    # Show database and table details\n", "    if databases_found:\n", "        print(f\"\\n🗄️ DATABASES DETECTED:\")\n", "        for db_type, db_name in databases_found:\n", "            print(f\"   - {db_type.upper()}: {db_name}\")\n", "    \n", "    if tables_found:\n", "        print(f\"\\n📊 TABLES/COLLECTIONS DETECTED:\")\n", "        for table in sorted(tables_found):\n", "            print(f\"   - {table}\")\n", "    \n", "    # Update the results with enhanced data\n", "    enhanced_results = base_results.copy()\n", "    enhanced_results['nodes'] = list(nodes.values())\n", "    enhanced_results['relations'] = relations\n", "    enhanced_results['variable_flows'] = dict(variable_flows)\n", "    enhanced_results['method_assignments'] = dict(method_assignments)\n", "    \n", "    # Add new summary data\n", "    enhanced_results['enhancement_summary'] = {\n", "        'enhanced_transformations': enhanced_transformations,\n", "        'control_flow_structures': control_flow_structures,\n", "        'databases_found': len(databases_found),\n", "        'tables_found': len(tables_found),\n", "        'db_operations': db_operations_found\n", "    }\n", "    \n", "    enhanced_results['databases'] = list(databases_found)\n", "    enhanced_results['tables'] = list(tables_found)\n", "    \n", "    return enhanced_results\n", "\n", "print('✅ Complete enhanced extraction function created!')\n", "print('🎯 Now includes:')\n", "print('   - Variable transformation chains')\n", "print('   - Generic CONDITION nodes (if/else)')\n", "print('   - Generic LOOP nodes (for/while)')\n", "print('   - Database detection (MongoDB, MySQL, PostgreSQL, etc.)')\n", "print('   - Table/Collection detection')\n", "print('   - CRUD operation analysis')\n", "print('   - Complete relationship mapping')"]}, {"cell_type": "markdown", "id": "function-status-header", "metadata": {}, "source": ["## 14.9. Function Status Summary"]}, {"cell_type": "code", "execution_count": null, "id": "function-status-summary", "metadata": {}, "outputs": [], "source": ["def show_function_status():\n", "    \"\"\"Show the current status of all functions after cleanup.\"\"\"\n", "    \n", "    print('🧹 FUNCTION STATUS AFTER CLEANUP')\n", "    print('=' * 60)\n", "    \n", "    print('\\n✅ ACTIVE FUNCTIONS (Currently Used):')\n", "    active_functions = [\n", "        ('extract_complete_enhanced_relations()', 'Main analysis function with all features'),\n", "        ('extract_generic_control_flow_structures()', 'Creates condition and loop nodes'),\n", "        ('extract_database_and_tables()', 'Detects databases and tables'),\n", "        ('analyze_database_usage()', 'Analyzes CRUD operations'),\n", "        ('extract_enhanced_variable_transformations()', 'Creates transformation chains'),\n", "        ('extract_relations_enhanced()', 'Base extraction function'),\n", "        ('check_control_flow_nodes()', 'Validation and debugging'),\n", "        ('analyze_control_flow_from_csv()', 'CSV analysis'),\n", "        ('export_to_neo4j()', 'Neo4j export functionality'),\n", "        ('export_to_csv()', 'CSV export functionality')\n", "    ]\n", "    \n", "    for i, (func_name, description) in enumerate(active_functions, 1):\n", "        print(f'   {i:2d}. {func_name:<45} - {description}')\n", "    \n", "    print('\\n❌ REMOVED/REPLACED FUNCTIONS:')\n", "    removed_functions = [\n", "        ('extract_control_flow_structures()', 'REPLACED by extract_generic_control_flow_structures()'),\n", "        ('Duplicate extract_generic_control_flow_structures()', 'DUPLICATE REMOVED')\n", "    ]\n", "    \n", "    for i, (func_name, reason) in enumerate(removed_functions, 1):\n", "        print(f'   {i:2d}. {func_name:<50} - {reason}')\n", "    \n", "    print('\\n🎯 MAIN WORKFLOW:')\n", "    workflow_steps = [\n", "        'extract_complete_enhanced_relations() - Main entry point',\n", "        '├── extract_relations_enhanced() - Base extraction',\n", "        '├── extract_enhanced_variable_transformations() - Transformation chains',\n", "        '├── extract_generic_control_flow_structures() - Condition/loop nodes',\n", "        '├── extract_database_and_tables() - Database detection',\n", "        '├── analyze_database_usage() - CRUD operations',\n", "        '├── export_to_csv() - CSV export',\n", "        '└── export_to_neo4j() - Neo4j export'\n", "    ]\n", "    \n", "    for step in workflow_steps:\n", "        print(f'   {step}')\n", "    \n", "    print('\\n🔧 NODE TYPES CREATED:')\n", "    node_types = [\n", "        'condition - Generic if/else statements',\n", "        'loop - Generic for/while loops',\n", "        'database - Database connections',\n", "        'table - Database tables/collections',\n", "        'db_operation - CRUD operations',\n", "        'operation - Variable transformations (trim, toUpperCase, etc.)'\n", "    ]\n", "    \n", "    for node_type in node_types:\n", "        print(f'   - {node_type}')\n", "    \n", "    print('\\n' + '=' * 60)\n", "    print('✅ ALL FUNCTIONS ARE PROPERLY ORGANIZED!')\n", "    print('🚀 Ready to run complete enhanced analysis!')\n", "\n", "# Show the function status\n", "show_function_status()"]}, {"cell_type": "markdown", "id": "final-validation-header", "metadata": {}, "source": ["## 14.10. Final Validation"]}, {"cell_type": "code", "execution_count": null, "id": "final-validation", "metadata": {}, "outputs": [], "source": ["def validate_notebook_integrity():\n", "    \"\"\"Final validation to ensure all updates are properly applied.\"\"\"\n", "    \n", "    print('🔍 FINAL NOTEBOOK VALIDATION')\n", "    print('=' * 50)\n", "    \n", "    validation_results = []\n", "    \n", "    # Check 1: Main function exists and is callable\n", "    try:\n", "        main_func = globals().get('extract_complete_enhanced_relations')\n", "        if main_func and callable(main_func):\n", "            validation_results.append(('✅', 'Main function extract_complete_enhanced_relations() exists'))\n", "        else:\n", "            validation_results.append(('❌', 'Main function extract_complete_enhanced_relations() missing'))\n", "    except Exception as e:\n", "        validation_results.append(('❌', f'Main function check failed: {e}'))\n", "    \n", "    # Check 2: Generic control flow function exists\n", "    try:\n", "        cf_func = globals().get('extract_generic_control_flow_structures')\n", "        if cf_func and callable(cf_func):\n", "            validation_results.append(('✅', 'Generic control flow function exists'))\n", "        else:\n", "            validation_results.append(('❌', 'Generic control flow function missing'))\n", "    except Exception as e:\n", "        validation_results.append(('❌', f'Control flow function check failed: {e}'))\n", "    \n", "    # Check 3: Database detection function exists\n", "    try:\n", "        db_func = globals().get('extract_database_and_tables')\n", "        if db_func and callable(db_func):\n", "            validation_results.append(('✅', 'Database detection function exists'))\n", "        else:\n", "            validation_results.append(('❌', 'Database detection function missing'))\n", "    except Exception as e:\n", "        validation_results.append(('❌', f'Database function check failed: {e}'))\n", "    \n", "    # Check 4: Validation functions exist\n", "    try:\n", "        val_func = globals().get('check_control_flow_nodes')\n", "        if val_func and callable(val_func):\n", "            validation_results.append(('✅', 'Control flow validation function exists'))\n", "        else:\n", "            validation_results.append(('❌', 'Control flow validation function missing'))\n", "    except Exception as e:\n", "        validation_results.append(('❌', f'Validation function check failed: {e}'))\n", "    \n", "    # Check 5: Export functions exist\n", "    export_functions = ['export_to_csv', 'export_to_neo4j']\n", "    for func_name in export_functions:\n", "        try:\n", "            func = globals().get(func_name)\n", "            if func and callable(func):\n", "                validation_results.append(('✅', f'{func_name}() exists'))\n", "            else:\n", "                validation_results.append(('❌', f'{func_name}() missing'))\n", "        except Exception as e:\n", "            validation_results.append(('❌', f'{func_name}() check failed: {e}'))\n", "    \n", "    # Display results\n", "    print('\\n📋 VALIDATION RESULTS:')\n", "    for status, message in validation_results:\n", "        print(f'   {status} {message}')\n", "    \n", "    # Summary\n", "    success_count = sum(1 for status, _ in validation_results if status == '✅')\n", "    total_count = len(validation_results)\n", "    \n", "    print(f'\\n📊 VALIDATION SUMMARY:')\n", "    print(f'   - Passed: {success_count}/{total_count} checks')\n", "    print(f'   - Success Rate: {(success_count/total_count)*100:.1f}%')\n", "    \n", "    if success_count == total_count:\n", "        print('\\n🎉 ALL VALIDATIONS PASSED!')\n", "        print('✅ Notebook is ready for production use!')\n", "        print('🚀 You can now run the main analysis function!')\n", "        return True\n", "    else:\n", "        print('\\n⚠️ SOME VALIDATIONS FAILED')\n", "        print('🔧 Please check the failed items above')\n", "        return False\n", "\n", "# Run final validation\n", "validation_passed = validate_notebook_integrity()\n", "\n", "if validation_passed:\n", "    print('\\n' + '='*60)\n", "    print('🎯 NOTEBOOK CLEANUP AND VALIDATION COMPLETED!')\n", "    print('📝 Summary of changes:')\n", "    print('   - Removed duplicate functions')\n", "    print('   - Fixed control flow node detection')\n", "    print('   - Updated main execution function')\n", "    print('   - Added comprehensive validation')\n", "    print('   - All integrations verified')\n", "    print('='*60)"]}, {"cell_type": "markdown", "id": "execution-header", "metadata": {}, "source": ["## 15. Execute Main Analysis"]}, {"cell_type": "code", "execution_count": null, "id": "main-execution", "metadata": {}, "outputs": [], "source": ["# Check if project path exists before processing\n", "if not os.path.exists(project_path):\n", "    print(f\"❌ Project path does not exist: {project_path}\")\n", "    print(\"Please ensure the OneInsights directory exists in the current workspace.\")\n", "else:\n", "    print(f\"🚀 Starting comprehensive Java code analysis...\")\n", "    print(f\"📁 Processing project: {project_path}\")\n", "    \n", "    try:\n", "        # Complete enhanced extraction with transformations, control flow, and database detection\n", "        graph_data = extract_complete_enhanced_relations(project_path)\n", "        \n", "        print(f\"\\n🎉 Analysis completed successfully!\")\n", "        print(f\"📊 Results summary:\")\n", "        print(f\"   - Total nodes: {len(graph_data['nodes'])}\")\n", "        print(f\"   - Total relations: {len(graph_data['relations'])}\")\n", "        print(f\"   - Endpoints found: {graph_data['endpoint_summary']['total_endpoints']}\")\n", "        print(f\"   - Unique paths: {graph_data['endpoint_summary']['unique_paths']}\")\n", "        print(f\"   - Method mappings: {graph_data['endpoint_summary']['method_mappings']}\")\n", "        \n", "        # Show enhancement summary if available\n", "        if 'enhancement_summary' in graph_data:\n", "            enhancement = graph_data['enhancement_summary']\n", "            print(f\"\\n🎯 Enhancement Summary:\")\n", "            print(f\"   - Control flow structures: {enhancement['control_flow_structures']}\")\n", "            print(f\"   - Databases detected: {enhancement['databases_found']}\")\n", "            print(f\"   - Tables/Collections: {enhancement['tables_found']}\")\n", "            print(f\"   - Database operations: {enhancement['db_operations']}\")\n", "        \n", "        # Show databases and tables if found\n", "        if 'databases' in graph_data and graph_data['databases']:\n", "            print(f\"\\n🗄️ Databases: {', '.join([f'{db[0]}:{db[1]}' for db in graph_data['databases']])}\")\n", "        \n", "        if 'tables' in graph_data and graph_data['tables']:\n", "            print(f\"📊 Tables: {', '.join(graph_data['tables'][:10])}{'...' if len(graph_data['tables']) > 10 else ''}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error during analysis: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "        graph_data = None"]}, {"cell_type": "markdown", "id": "csv-save-header", "metadata": {}, "source": ["## 13. Save Results to CSV"]}, {"cell_type": "code", "execution_count": null, "id": "csv-save", "metadata": {}, "outputs": [], "source": ["# Save to CSV files\n", "if 'graph_data' in locals() and graph_data is not None:\n", "    try:\n", "        print(f\"\\n💾 Saving results to CSV files...\")\n", "        csv_output_path = save_enhanced_graph_to_csv(graph_data, output_dir=CSV_OUTPUT_DIR)\n", "        \n", "        print(f\"\\n📁 CSV files saved successfully!\")\n", "        print(f\"📂 Location: {csv_output_path}\")\n", "        \n", "        # Show sample data from nodes.csv\n", "        nodes_file = os.path.join(csv_output_path, \"nodes.csv\")\n", "        if os.path.exists(nodes_file):\n", "            sample_nodes = pd.read_csv(nodes_file).head(3)\n", "            print(f\"\\n📋 Sample nodes data:\")\n", "            print(sample_nodes[['type', 'name', 'full_name']].to_string(index=False))\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error saving CSV files: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "else:\n", "    print(\"⚠️ No graph data available to save. Please run the analysis first.\")"]}, {"cell_type": "markdown", "id": "neo4j-save-header", "metadata": {}, "source": ["## 14. <PERSON><PERSON> Results to Neo4j"]}, {"cell_type": "code", "execution_count": null, "id": "neo4j-save", "metadata": {}, "outputs": [], "source": ["# Push to Neo4j\n", "if 'graph_data' in locals() and graph_data is not None:\n", "    try:\n", "        print(f\"\\n🔗 Pushing results to Neo4j...\")\n", "        push_enhanced_graph_to_neo4j_complete(graph_data, NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD, NEO4J_DB)\n", "        \n", "        print(f\"\\n🎉 Neo4j upload completed!\")\n", "        print(f\"🔍 You can now query your data in Neo4j Browser at: http://localhost:7474\")\n", "        print(f\"\\n💡 Sample queries to try:\")\n", "        print(f\"   • MATCH (n) RETURN labels(n), count(n) - Count nodes by type\")\n", "        print(f\"   • MATCH (c:Class)-[:HAS_METHOD]->(m:Method) RETURN c.name, count(m) - Methods per class\")\n", "        print(f\"   • MATCH (f:File)-[:EXPOSES]->(e:Api_endpoint) RETURN f.name, e.path - API endpoints\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error pushing to Neo4j: {e}\")\n", "        print(f\"💡 Make sure <PERSON><PERSON><PERSON> is running and credentials are correct\")\n", "        import traceback\n", "        traceback.print_exc()\n", "else:\n", "    print(\"⚠️ No graph data available to push. Please run the analysis first.\")"]}, {"cell_type": "markdown", "id": "summary-header", "metadata": {}, "source": ["## 15. Final Summary"]}, {"cell_type": "code", "execution_count": null, "id": "final-summary", "metadata": {}, "outputs": [], "source": ["# Final summary\n", "if 'graph_data' in locals() and graph_data is not None:\n", "    print(\"\\n🎉 ANALYSIS COMPLETE! 🎉\")\n", "    print(\"=\" * 50)\n", "    print(f\"📊 FINAL STATISTICS:\")\n", "    print(f\"   • Total nodes extracted: {len(graph_data['nodes']):,}\")\n", "    print(f\"   • Total relationships: {len(graph_data['relations']):,}\")\n", "    print(f\"   • API endpoints found: {graph_data['endpoint_summary']['total_endpoints']:,}\")\n", "    print(f\"   • Unique endpoint paths: {graph_data['endpoint_summary']['unique_paths']:,}\")\n", "    print(f\"   • Method mappings: {graph_data['endpoint_summary']['method_mappings']:,}\")\n", "    \n", "    print(f\"\\n📁 OUTPUT LOCATIONS:\")\n", "    print(f\"   • CSV files: ./{CSV_OUTPUT_DIR}/\")\n", "    print(f\"   • Neo4j database: {NEO4J_DB}\")\n", "    \n", "    print(f\"\\n🔍 WHAT YOU CAN DO NEXT:\")\n", "    print(f\"   1. Explore CSV files for detailed data analysis\")\n", "    print(f\"   2. Query Neo4j for interactive graph exploration\")\n", "    print(f\"   3. Use lineage data for impact analysis\")\n", "    print(f\"   4. Analyze code dependencies and relationships\")\n", "    \n", "    print(f\"\\n✨ Happy analyzing! ✨\")\n", "else:\n", "    print(\"\\n⚠️ Analysis was not completed successfully.\")\n", "    print(\"Please check the error messages above and try again.\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}