{"cells": [{"cell_type": "markdown", "id": "header", "metadata": {}, "source": ["# Java Code Analysis Pipeline v2.0\n", "## Clean, Streamlined Data Lineage and Graph Analysis\n", "\n", "**Key Improvements:**\n", "- Removed duplicate functions\n", "- Single Neo4j push at the end\n", "- Consolidated similar functions\n", "- Clean execution flow\n", "- Enhanced data lineage with transformation chains\n", "- Field-level data lineage tracking\n", "- Cross-method variable flow analysis\n", "- Package-level dependency mapping\n", "- Exception flow lineage tracking\n", "- Annotation-driven behavior analysis"]}, {"cell_type": "markdown", "id": "config-header", "metadata": {}, "source": ["## 1. Configuration and Setup"]}, {"cell_type": "code", "execution_count": 13, "id": "config-cell", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Configuration loaded successfully!\n", "Libraries: javalang, pandas 2.3.1, neo4j\n", "Project: OneInsights\n", "CSV Output: enhanced_graph_csv_v2\n", "Neo4j: bolt://localhost:7687\n", "Enhanced configuration:\n", "   - SQL stopwords: 67 terms\n", "   - Java node types: 42 types across 8 categories\n", "   - Relationship types: 35 types across 7 categories\n"]}], "source": ["# ================== CONFIGURATION ==================\n", "import os\n", "import re\n", "import javalang\n", "import pandas as pd\n", "import json\n", "import hashlib\n", "from datetime import datetime\n", "from collections import defaultdict, deque\n", "from pathlib import Path\n", "from neo4j import GraphDatabase\n", "\n", "# Project Configuration\n", "PROJECT_PATH = \"OneInsights\"  # Update this to your Java project path\n", "CSV_OUTPUT_DIR = \"enhanced_graph_csv_v2\"\n", "\n", "# Neo4j Configuration\n", "NEO4J_URI = \"bolt://localhost:7687\"\n", "NEO4J_USER = \"neo4j\"\n", "NEO4J_PASSWORD = \"Test@7889\"\n", "NEO4J_DB = \"my-oneinsights\"\n", "\n", "# SQL stopwords for filtering\n", "SQL_STOPWORDS = {\n", "    \"select\",\"from\",\"where\",\"and\",\"or\",\"not\",\"in\",\"on\",\"as\",\"by\",\"with\",\"into\",\"values\",\n", "    \"insert\",\"update\",\"delete\",\"create\",\"drop\",\"alter\",\"table\",\"index\",\"view\",\"database\",\n", "    \"schema\",\"column\",\"primary\",\"foreign\",\"key\",\"constraint\",\"unique\",\"null\",\"default\",\n", "    \"varchar\",\"char\",\"int\",\"integer\",\"bigint\",\"smallint\",\"decimal\",\"numeric\",\"float\",\n", "    \"double\",\"boolean\",\"date\",\"time\",\"timestamp\",\"text\",\"blob\",\"clob\",\"auto_increment\",\n", "    \"if\",\"then\",\"else\",\"when\",\"end\",\"case\",\"distinct\",\"limit\",\"offset\",\n", "    \"like\",\"not\",\"null\",\"is\",\"inner\",\"left\",\"right\",\"outer\",\"full\",\"cross\"\n", "}\n", "\n", "# Enhanced node types for comprehensive Java application coverage\n", "JAVA_NODE_TYPES = {\n", "    'structural': ['package', 'folder', 'file', 'class', 'interface', 'enum', 'annotation'],\n", "    'behavioral': ['method', 'constructor', 'lambda', 'operation', 'condition', 'loop'],\n", "    'data': ['variable', 'field', 'parameter', 'return_value', 'constant'],\n", "    'persistence': ['database', 'table', 'column', 'index', 'constraint', 'view', 'procedure', 'db_operation'],\n", "    'integration': ['api_endpoint', 'message_queue', 'cache', 'external_service'],\n", "    'configuration': ['property', 'profile', 'bean', 'component_scan'],\n", "    'security': ['role', 'permission', 'authentication', 'authorization'],\n", "    'monitoring': ['metric', 'log', 'trace', 'health_check']\n", "}\n", "\n", "RELATIONSHIP_TYPES = {\n", "    'structural': ['CONTAINS', 'DECLARES', 'EXTENDS', 'IMPLEMENTS', 'IMPORTS'],\n", "    'behavioral': ['CALLS', 'INVOKES', 'RETURNS', 'THROWS', 'HANDLES'],\n", "    'data_flow': ['READS', 'WRITES', 'TRANSFORMS', 'PRODUCES', 'CONSUMES', 'INPUT_TO', 'TRANSFORMS_VIA', 'ASSIGNS_TO', 'FLOWS_TO'],\n", "    'dependency': ['DEPENDS_ON', 'INJECTS', 'AUTOWIRES', 'CONFIGURES'],\n", "    'persistence': ['MAPS_TO', 'JOINS', 'REFERENCES', 'CASCADES'],\n", "    'integration': ['CALLS_API', 'PUBLISHES', 'SUBSCRIBES', 'CACHES'],\n", "    'security': ['SECURES', 'AUTHORIZES', 'AUTHENTICATES', 'VALIDATES']\n", "}\n", "\n", "print(\"Configuration loaded successfully!\")\n", "print(f\"Libraries: javalang, pandas {pd.__version__}, neo4j\")\n", "print(f\"Project: {PROJECT_PATH}\")\n", "print(f\"CSV Output: {CSV_OUTPUT_DIR}\")\n", "print(f\"Neo4j: {NEO4J_URI}\")\n", "print(f\"Enhanced configuration:\")\n", "print(f\"   - SQL stopwords: {len(SQL_STOPWORDS)} terms\")\n", "print(f\"   - Java node types: {sum(len(v) for v in JAVA_NODE_TYPES.values())} types across {len(JAVA_NODE_TYPES)} categories\")\n", "print(f\"   - Relationship types: {sum(len(v) for v in RELATIONSHIP_TYPES.values())} types across {len(RELATIONSHIP_TYPES)} categories\")"]}, {"cell_type": "markdown", "id": "core-classes-header", "metadata": {}, "source": ["## 2. Core Classes and Helper Functions"]}, {"cell_type": "code", "execution_count": 14, "id": "core-classes", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Core helper functions loaded\n"]}], "source": ["# ================== CORE HELPER FUNCTIONS ==================\n", "\n", "def register_node(nodes, raw_node, file_path):\n", "    \"\"\"Register a node with enhanced metadata.\"\"\"\n", "    if not raw_node or raw_node in nodes:\n", "        return\n", "    \n", "    node_type, full_name = raw_node.split(\":\", 1)\n", "    short_name = full_name.split(\".\")[-1] if \".\" in full_name else full_name\n", "    \n", "    nodes[raw_node] = {\n", "        \"id\": raw_node,\n", "        \"type\": node_type,\n", "        \"name\": short_name,\n", "        \"full_name\": full_name,\n", "        \"file_path\": file_path\n", "    }\n", "\n", "def add_relation(relations, existing_relations, src, rel, dst, file_path, nodes):\n", "    \"\"\"Add relation with duplicate detection and node registration.\"\"\"\n", "    if not src or not dst:\n", "        return\n", "    \n", "    key = (src, rel, dst)\n", "    if key in existing_relations:\n", "        return\n", "    \n", "    # Register nodes\n", "    register_node(nodes, src, file_path)\n", "    register_node(nodes, dst, file_path)\n", "    \n", "    relations.append({\n", "        \"source\": src,\n", "        \"relation\": rel,\n", "        \"target\": dst,\n", "        \"file_path\": file_path\n", "    })\n", "    existing_relations.add(key)\n", "\n", "print(\"Core helper functions loaded\")"]}, {"cell_type": "markdown", "id": "endpoint-tracker-header", "metadata": {}, "source": ["## 3. Endpoint and Usage Tracking"]}, {"cell_type": "code", "execution_count": 15, "id": "endpoint-tracker", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Endpoint tracking system loaded\n"]}], "source": ["# ================== ENDPOINT TRACKING ==================\n", "\n", "class EndpointUsageTracker:\n", "    \"\"\"Track API endpoints, their definitions, and usage patterns.\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.endpoints = {}  # endpoint_path -> metadata\n", "        self.endpoint_methods = {}  # method_id -> endpoint info\n", "        self.method_calls = defaultdict(list)  # method -> [called_methods]\n", "        self.data_operations = defaultdict(list)  # method -> [operations]\n", "    \n", "    def register_endpoint(self, path, method, http_method, class_name, method_name, file_path):\n", "        \"\"\"Register an API endpoint with its definition location.\"\"\"\n", "        endpoint_key = f\"{http_method}:{path}\"\n", "        method_id = f\"method:{class_name}.{method_name}\"\n", "        \n", "        if endpoint_key not in self.endpoints:\n", "            self.endpoints[endpoint_key] = {\n", "                'path': path,\n", "                'http_method': http_method,\n", "                'class_name': class_name,\n", "                'method_name': method_name,\n", "                'file_path': file_path,\n", "                'method_id': method_id\n", "            }\n", "        \n", "        self.endpoint_methods[method_id] = endpoint_key\n", "        return endpoint_key\n", "    \n", "    def add_method_call(self, caller_method, called_method, operation_type='call'):\n", "        \"\"\"Track method calls and their operation types.\"\"\"\n", "        self.method_calls[caller_method].append({\n", "            'called_method': called_method,\n", "            'operation_type': operation_type\n", "        })\n", "    \n", "    def add_data_operation(self, method_id, operation_type, target, details=None):\n", "        \"\"\"Track data operations performed by methods.\"\"\"\n", "        self.data_operations[method_id].append({\n", "            'operation_type': operation_type,\n", "            'target': target,\n", "            'details': details or {}\n", "        })\n", "    \n", "    def get_endpoint_usage(self, endpoint_key):\n", "        \"\"\"Get detailed usage information for an endpoint.\"\"\"\n", "        if endpoint_key not in self.endpoints:\n", "            return None\n", "        \n", "        endpoint_info = self.endpoints[endpoint_key]\n", "        method_id = endpoint_info['method_id']\n", "        \n", "        return {\n", "            'endpoint': endpoint_info,\n", "            'method_calls': self.method_calls.get(method_id, []),\n", "            'data_operations': self.data_operations.get(method_id, [])\n", "        }\n", "\n", "print(\"Endpoint tracking system loaded\")"]}, {"cell_type": "markdown", "id": "extraction-functions-header", "metadata": {}, "source": ["## 4. Extraction Functions"]}, {"cell_type": "code", "execution_count": 16, "id": "extraction-functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Basic extraction functions loaded\n"]}], "source": ["# ================== EXTRACTION FUNCTIONS ==================\n", "\n", "def extract_spring_annotations(code):\n", "    \"\"\"Extract Spring framework annotations and their configurations.\"\"\"\n", "    annotations = []\n", "    patterns = {\n", "        'controller': r'@(?:RestController|Controller)(?:\\([^)]*\\))?',\n", "        'service': r'@Service(?:\\([^)]*\\))?',\n", "        'repository': r'@Repository(?:\\([^)]*\\))?',\n", "        'component': r'@Component(?:\\([^)]*\\))?',\n", "        'entity': r'@Entity(?:\\([^)]*\\))?',\n", "        'table': r'@Table\\([^)]*\\)',\n", "        'mapping': r'@(?:RequestMapping|GetMapping|PostMapping|PutMapping|DeleteMapping|PatchMapping)\\([^)]*\\)'\n", "    }\n", "    \n", "    for annotation_type, pattern in patterns.items():\n", "        try:\n", "            matches = re.finditer(pattern, code, re.IGNORECASE | re.MULTILINE)\n", "            for match in matches:\n", "                annotations.append({\n", "                    'type': annotation_type,\n", "                    'text': match.group(0),\n", "                    'start': match.start(),\n", "                    'end': match.end()\n", "                })\n", "        except re.error:\n", "            continue\n", "    \n", "    return annotations\n", "\n", "def extract_api_endpoints(code):\n", "    \"\"\"Enhanced API endpoint extraction with HTTP methods and parameters.\"\"\"\n", "    endpoints = []\n", "    \n", "    # Spring REST mappings with HTTP methods\n", "    mapping_patterns = {\n", "        'GET': r'@GetMapping\\s*\\(\\s*(?:value\\s*=\\s*)?[\"\\']([^\"\\']*)[\"\\'\\s]*\\)',\n", "        'POST': r'@PostMapping\\s*\\(\\s*(?:value\\s*=\\s*)?[\"\\']([^\"\\']*)[\"\\'\\s]*\\)',\n", "        'PUT': r'@PutMapping\\s*\\(\\s*(?:value\\s*=\\s*)?[\"\\']([^\"\\']*)[\"\\'\\s]*\\)',\n", "        'DELETE': r'@DeleteMapping\\s*\\(\\s*(?:value\\s*=\\s*)?[\"\\']([^\"\\']*)[\"\\'\\s]*\\)',\n", "        'PATCH': r'@PatchMapping\\s*\\(\\s*(?:value\\s*=\\s*)?[\"\\']([^\"\\']*)[\"\\'\\s]*\\)',\n", "        'REQUEST': r'@RequestMapping\\s*\\([^)]*value\\s*=\\s*[\"\\']([^\"\\']*)[\"\\'\\s]*[^)]*\\)'\n", "    }\n", "    \n", "    for http_method, pattern in mapping_patterns.items():\n", "        try:\n", "            matches = re.finditer(pattern, code, re.IGNORECASE | re.MULTILINE)\n", "            for match in matches:\n", "                path = match.group(1) if len(match.groups()) > 0 else \"/\"\n", "                endpoints.append({\n", "                    'http_method': http_method,\n", "                    'path': path,\n", "                    'annotation': match.group(0),\n", "                    'start': match.start(),\n", "                    'end': match.end()\n", "                })\n", "        except re.error:\n", "            continue\n", "    \n", "    return endpoints\n", "\n", "def extract_database_operations(code):\n", "    \"\"\"Extract database operations and table usage.\"\"\"\n", "    operations = {\n", "        'reads': set(),\n", "        'writes': set(),\n", "        'deletes': set(),\n", "        'tables': set()\n", "    }\n", "    \n", "    # Database operation patterns\n", "    patterns = {\n", "        'select': r'(?i)(?:SELECT|select)\\s+.*?\\s+(?:FROM|from)\\s+([a-zA-Z_][a-zA-Z0-9_]*)',\n", "        'insert': r'(?i)(?:INSERT|insert)\\s+(?:INTO|into)\\s+([a-zA-Z_][a-zA-Z0-9_]*)',\n", "        'update': r'(?i)(?:UPDATE|update)\\s+([a-zA-Z_][a-zA-Z0-9_]*)',\n", "        'delete': r'(?i)(?:DELETE|delete)\\s+(?:FROM|from)\\s+([a-zA-Z_][a-zA-Z0-9_]*)',\n", "        'jpa_find': r'find(?:By|All)([A-Z][a-zA-Z0-9]*)',\n", "        'jpa_save': r'save\\s*\\(',\n", "        'jpa_delete': r'delete(?:By|All)?([A-Z][a-zA-Z0-9]*)?'\n", "    }\n", "    \n", "    for operation_type, pattern in patterns.items():\n", "        try:\n", "            matches = re.finditer(pattern, code)\n", "            for match in matches:\n", "                if operation_type in ['select', 'jpa_find']:\n", "                    if len(match.groups()) > 0 and match.group(1):\n", "                        table_name = match.group(1)\n", "                        operations['reads'].add(table_name)\n", "                        operations['tables'].add(table_name)\n", "                elif operation_type in ['insert', 'update', 'jpa_save']:\n", "                    if len(match.groups()) > 0 and match.group(1):\n", "                        table_name = match.group(1)\n", "                        operations['writes'].add(table_name)\n", "                        operations['tables'].add(table_name)\n", "                elif operation_type in ['delete', 'jpa_delete']:\n", "                    if len(match.groups()) > 0 and match.group(1):\n", "                        table_name = match.group(1)\n", "                        operations['deletes'].add(table_name)\n", "                        operations['tables'].add(table_name)\n", "        except re.error:\n", "            continue\n", "    \n", "    return operations\n", "\n", "print(\"Basic extraction functions loaded\")"]}, {"cell_type": "markdown", "id": "advanced-extraction-header", "metadata": {}, "source": ["## 5. Advanced Extraction Functions"]}, {"cell_type": "code", "execution_count": 17, "id": "advanced-extraction", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Advanced extraction functions loaded\n"]}], "source": ["# ================== ADVANCED EXTRACTION FUNCTIONS ==================\n", "\n", "def extract_field_level_lineage(method_body, full_decl_name, method_name, relations, existing_relations, nodes, variable_flows, method_assignments, rel_path):\n", "    \"\"\"Extract field-level data lineage tracking individual object fields.\"\"\"\n", "    method_node = f\"method:{full_decl_name}.{method_name}\"\n", "    \n", "    for path, node in method_body:\n", "        # Track field access patterns: object.field\n", "        if hasattr(node, 'type') and node.type == 'Assignment':\n", "            if hasattr(node, 'expressionl') and hasattr(node, 'value'):\n", "                # Left side - target field\n", "                if hasattr(node.expressionl, 'qualifier') and hasattr(node.expressionl, 'member'):\n", "                    target_object = str(node.expressionl.qualifier)\n", "                    target_field = node.expressionl.member\n", "                    target_field_node = f\"field:{full_decl_name}.{method_name}.{target_object}.{target_field}\"\n", "                    \n", "                    # Register field node\n", "                    register_node(nodes, target_field_node, rel_path)\n", "                    nodes[target_field_node]['field_name'] = target_field\n", "                    nodes[target_field_node]['object_name'] = target_object\n", "                    \n", "                    # Right side - source field or transformation\n", "                    if hasattr(node.value, 'qualifier') and hasattr(node.value, 'member'):\n", "                        source_object = str(node.value.qualifier)\n", "                        source_field = node.value.member\n", "                        source_field_node = f\"field:{full_decl_name}.{method_name}.{source_object}.{source_field}\"\n", "                        \n", "                        # Register source field\n", "                        register_node(nodes, source_field_node, rel_path)\n", "                        nodes[source_field_node]['field_name'] = source_field\n", "                        nodes[source_field_node]['object_name'] = source_object\n", "                        \n", "                        # Create field-to-field relationship\n", "                        add_relation(relations, existing_relations, source_field_node, \"FLOWS_TO\", target_field_node, rel_path, nodes)\n", "                        \n", "                        # Track field transformation\n", "                        variable_flows.append({\n", "                            'method': method_node,\n", "                            'source_field': f\"{source_object}.{source_field}\",\n", "                            'target_field': f\"{target_object}.{target_field}\",\n", "                            'type': 'field_assignment'\n", "                        })\n", "\n", "def extract_package_dependencies(tree, file_path, nodes, relations, existing_relations):\n", "    \"\"\"Extract package-level dependencies from import statements.\"\"\"\n", "    rel_path = os.path.relpath(file_path, start='.')\n", "    \n", "    # Get current package\n", "    current_package = tree.package.name if tree.package else 'default'\n", "    current_package_node = f\"package:{current_package}\"\n", "    \n", "    # Register current package\n", "    register_node(nodes, current_package_node, rel_path)\n", "    nodes[current_package_node]['package_name'] = current_package\n", "    \n", "    # Process imports\n", "    if tree.imports:\n", "        for import_decl in tree.imports:\n", "            import_path = import_decl.path\n", "            \n", "            # Extract package from import\n", "            if '.' in import_path:\n", "                imported_package = '.'.join(import_path.split('.')[:-1])  # Remove class name\n", "                imported_class = import_path.split('.')[-1]\n", "            else:\n", "                imported_package = 'java.lang'  # Default package\n", "                imported_class = import_path\n", "            \n", "            # Skip java.lang and java.util (built-in packages)\n", "            if not imported_package.startswith(('java.lang', 'java.util', 'java.io')):\n", "                imported_package_node = f\"package:{imported_package}\"\n", "                imported_class_node = f\"class:{import_path}\"\n", "                \n", "                # Register imported package and class\n", "                register_node(nodes, imported_package_node, rel_path)\n", "                register_node(nodes, imported_class_node, rel_path)\n", "                \n", "                nodes[imported_package_node]['package_name'] = imported_package\n", "                nodes[imported_class_node]['class_name'] = imported_class\n", "                nodes[imported_class_node]['import_path'] = import_path\n", "                \n", "                # Create dependency relationships\n", "                add_relation(relations, existing_relations, current_package_node, \"DEPENDS_ON\", imported_package_node, rel_path, nodes)\n", "                add_relation(relations, existing_relations, imported_package_node, \"CONTAINS\", imported_class_node, rel_path, nodes)\n", "                \n", "                # Track static imports\n", "                if import_decl.static:\n", "                    add_relation(relations, existing_relations, current_package_node, \"STATIC_IMPORTS\", imported_class_node, rel_path, nodes)\n", "    \n", "    return current_package_node\n", "\n", "def extract_exception_flow_lineage(tree, full_decl_name, relations, existing_relations, nodes, variable_flows, rel_path):\n", "    \"\"\"Extract exception throwing and handling chains.\"\"\"\n", "    \n", "    exception_flows = []\n", "    \n", "    # Process methods for exception declarations and handling\n", "    for path, method in tree.filter(javalang.tree.MethodDeclaration):\n", "        method_node = f\"method:{full_decl_name}.{method.name}\"\n", "        \n", "        # Extract throws declarations\n", "        if method.throws:\n", "            for exception_type in method.throws:\n", "                exception_node = f\"exception:{exception_type.name}\"\n", "                register_node(nodes, exception_node, rel_path)\n", "                nodes[exception_node]['exception_type'] = exception_type.name\n", "                nodes[exception_node]['declared_by'] = method_node\n", "                \n", "                # Method throws exception\n", "                add_relation(relations, existing_relations, method_node, \"THROWS\", exception_node, rel_path, nodes)\n", "    \n", "    return exception_flows\n", "\n", "print(\"Advanced extraction functions loaded\")"]}, {"cell_type": "markdown", "id": "transformation-header", "metadata": {}, "source": ["## 6. Variable Transformation and Control Flow"]}, {"cell_type": "code", "execution_count": 18, "id": "transformation-functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Variable transformation and control flow functions loaded\n"]}], "source": ["# ================== VARIABLE TRANSFORMATION AND CONTROL FLOW ==================\n", "\n", "def extract_variable_transformations(method_body, full_decl_name, method_name, relations, existing_relations, nodes, variable_flows, method_assignments, rel_path):\n", "    \"\"\"Extract variable transformation chains with operations as intermediate nodes.\"\"\"\n", "    method_node = f\"method:{full_decl_name}.{method_name}\"\n", "    \n", "    for path, node in method_body:\n", "        # Variable assignments with operation tracking\n", "        if hasattr(node, 'type') and node.type == 'LocalVariableDeclaration':\n", "            for declarator in node.declarators:\n", "                if hasattr(declarator, 'initializer') and declarator.initializer:\n", "                    var_name = declarator.name\n", "                    var_node = f\"variable:{full_decl_name}.{method_name}.{var_name}\"\n", "                    \n", "                    # Process method chain operations (e.g., x = y.trim().toUpperCase())\n", "                    if hasattr(declarator.initializer, 'member'):\n", "                        current_expr = declarator.initializer\n", "                        operations = []\n", "                        \n", "                        # Extract the chain\n", "                        while hasattr(current_expr, 'member'):\n", "                            operations.append(current_expr.member)\n", "                            if hasattr(current_expr, 'qualifier'):\n", "                                current_expr = current_expr.qualifier\n", "                            else:\n", "                                break\n", "                        \n", "                        # Get the source variable\n", "                        if hasattr(current_expr, 'member'):\n", "                            source_var = current_expr.member\n", "                        <PERSON><PERSON> has<PERSON>(current_expr, 'name'):\n", "                            source_var = current_expr.name\n", "                        else:\n", "                            source_var = str(current_expr)\n", "                        \n", "                        # Create transformation chain: source -> Operation -> intermediate -> Operation -> target\n", "                        if operations and source_var:\n", "                            source_var_node = f\"variable:{full_decl_name}.{method_name}.{source_var}\"\n", "                            current_var_node = source_var_node\n", "                            \n", "                            # Reverse operations to get correct order\n", "                            operations.reverse()\n", "                            \n", "                            for i, operation in enumerate(operations):\n", "                                # Create operation node\n", "                                operation_node = f\"operation:{full_decl_name}.{method_name}.{operation}_{i}\"\n", "                                register_node(nodes, operation_node, rel_path)\n", "                                \n", "                                # Connect current variable to operation\n", "                                add_relation(relations, existing_relations, current_var_node, \"TRANSFORMS_TO\", operation_node, rel_path, nodes)\n", "                                \n", "                                # Create intermediate variable or final variable\n", "                                if i < len(operations) - 1:\n", "                                    intermediate_var = f\"variable:{full_decl_name}.{method_name}.{source_var}_{operation}_{i}\"\n", "                                    add_relation(relations, existing_relations, operation_node, \"PRODUCES\", intermediate_var, rel_path, nodes)\n", "                                    current_var_node = intermediate_var\n", "                                else:\n", "                                    # Final transformation to target variable\n", "                                    add_relation(relations, existing_relations, operation_node, \"PRODUCES\", var_node, rel_path, nodes)\n", "                            \n", "                            # Track the complete flow\n", "                            variable_flows.append({\n", "                                'method': method_node,\n", "                                'source': source_var,\n", "                                'target': var_name,\n", "                                'operations': operations,\n", "                                'type': 'method_chain'\n", "                            })\n", "\n", "def extract_control_flow_structures(method_body, full_decl_name, method_name, relations, existing_relations, nodes, variable_flows, method_assignments, rel_path):\n", "    \"\"\"Extract control flow structures as generic CONDITION and LOOP nodes.\"\"\"\n", "    method_node = f\"method:{full_decl_name}.{method_name}\"\n", "    \n", "    condition_count = 0\n", "    loop_count = 0\n", "    \n", "    for path, node in method_body:\n", "        # If statements\n", "        if hasattr(node, 'type') and node.type == 'IfStatement':\n", "            condition_count += 1\n", "            condition_id = f\"condition:{full_decl_name}.{method_name}.if_{condition_count}\"\n", "            \n", "            # Register condition node\n", "            register_node(nodes, condition_id, rel_path)\n", "            nodes[condition_id]['condition_type'] = 'if'\n", "            nodes[condition_id]['condition_text'] = str(node.condition) if hasattr(node, 'condition') else 'unknown'\n", "            \n", "            # Connect method to condition\n", "            add_relation(relations, existing_relations, method_node, \"HAS_CONDITION\", condition_id, rel_path, nodes)\n", "        \n", "        # For loops\n", "        elif hasattr(node, 'type') and node.type == 'ForStatement':\n", "            loop_count += 1\n", "            loop_id = f\"loop:{full_decl_name}.{method_name}.for_{loop_count}\"\n", "            \n", "            # Register loop node\n", "            register_node(nodes, loop_id, rel_path)\n", "            nodes[loop_id]['loop_type'] = 'for'\n", "            nodes[loop_id]['condition_text'] = str(node.condition) if hasattr(node, 'condition') else 'unknown'\n", "            \n", "            # Connect method to loop\n", "            add_relation(relations, existing_relations, method_node, \"HAS_LOOP\", loop_id, rel_path, nodes)\n", "        \n", "        # While loops\n", "        elif hasattr(node, 'type') and node.type == 'WhileStatement':\n", "            loop_count += 1\n", "            loop_id = f\"loop:{full_decl_name}.{method_name}.while_{loop_count}\"\n", "            \n", "            # Register loop node\n", "            register_node(nodes, loop_id, rel_path)\n", "            nodes[loop_id]['loop_type'] = 'while'\n", "            nodes[loop_id]['condition_text'] = str(node.condition) if hasattr(node, 'condition') else 'unknown'\n", "            \n", "            # Connect method to loop\n", "            add_relation(relations, existing_relations, method_node, \"HAS_LOOP\", loop_id, rel_path, nodes)\n", "    \n", "    return condition_count + loop_count\n", "\n", "print(\"Variable transformation and control flow functions loaded\")"]}, {"cell_type": "markdown", "id": "main-extraction-header", "metadata": {}, "source": ["## 7. Main Extraction Function"]}, {"cell_type": "code", "execution_count": 19, "id": "main-extraction", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Main extraction function loaded\n"]}], "source": ["# ================== MAIN EXTRACTION FUNCTION ==================\n", "\n", "def extract_java_graph_data(project_path):\n", "    \"\"\"Main function to extract comprehensive Java application graph data.\"\"\"\n", "    \n", "    print(f\"Starting Java code analysis for: {project_path}\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Initialize data structures\n", "    nodes = {}\n", "    relations = []\n", "    existing_relations = set()\n", "    variable_flows = []\n", "    method_assignments = defaultdict(list)\n", "    endpoint_tracker = EndpointUsageTracker()\n", "    databases = set()\n", "    tables = set()\n", "    \n", "    # Statistics\n", "    stats = {\n", "        'files_processed': 0,\n", "        'classes_found': 0,\n", "        'methods_found': 0,\n", "        'endpoints_found': 0,\n", "        'control_flow_structures': 0,\n", "        'variable_transformations': 0,\n", "        'database_operations': 0,\n", "        'field_level_lineages': 0,\n", "        'cross_method_flows': 0,\n", "        'package_dependencies': 0,\n", "        'exception_flows': 0,\n", "        'annotation_behaviors': 0\n", "    }\n", "    \n", "    # Process Java files\n", "    java_files = list(Path(project_path).rglob(\"*.java\"))\n", "    print(f\"Found {len(java_files)} Java files to process\")\n", "    \n", "    for file_path in java_files:\n", "        try:\n", "            rel_path = os.path.relpath(file_path, start='.')\n", "            print(f\"Processing: {rel_path}\")\n", "            \n", "            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:\n", "                code = f.read()\n", "            \n", "            # Parse Java code\n", "            try:\n", "                tree = javalang.parse.parse(code)\n", "            except Exception as parse_error:\n", "                print(f\"   Parse error: {parse_error}\")\n", "                continue\n", "            \n", "            stats['files_processed'] += 1\n", "            \n", "            # Create file node\n", "            file_node = f\"file:{rel_path}\"\n", "            register_node(nodes, file_node, rel_path)\n", "            \n", "            # Extract annotations and endpoints\n", "            annotations = extract_spring_annotations(code)\n", "            endpoints = extract_api_endpoints(code)\n", "            stats['endpoints_found'] += len(endpoints)\n", "            \n", "            # Extract package dependencies\n", "            current_package = extract_package_dependencies(\n", "                tree, file_path, nodes, relations, existing_relations\n", "            )\n", "            \n", "            # Extract exception flow lineage\n", "            exception_flows = extract_exception_flow_lineage(\n", "                tree, \"unknown\", relations, existing_relations, nodes, variable_flows, rel_path\n", "            )\n", "            \n", "            # Process classes\n", "            for path, class_decl in tree.filter(javalang.tree.ClassDeclaration):\n", "                stats['classes_found'] += 1\n", "                full_class_name = f\"{tree.package.name}.{class_decl.name}\" if tree.package else class_decl.name\n", "                class_node = f\"class:{full_class_name}\"\n", "                \n", "                # Register class and connect to file\n", "                register_node(nodes, class_node, rel_path)\n", "                add_relation(relations, existing_relations, file_node, \"CONTAINS\", class_node, rel_path, nodes)\n", "                \n", "                # Process methods\n", "                for method_path, method in class_decl.filter(javalang.tree.MethodDeclaration):\n", "                    stats['methods_found'] += 1\n", "                    method_node = f\"method:{full_class_name}.{method.name}\"\n", "                    \n", "                    # Register method and connect to class\n", "                    register_node(nodes, method_node, rel_path)\n", "                    add_relation(relations, existing_relations, class_node, \"HAS_METHOD\", method_node, rel_path, nodes)\n", "                    \n", "                    # Check if method is an API endpoint\n", "                    for endpoint in endpoints:\n", "                        endpoint_tracker.register_endpoint(\n", "                            endpoint['path'], method, endpoint['http_method'],\n", "                            full_class_name, method.name, rel_path\n", "                        )\n", "                    \n", "                    # Extract variable transformations\n", "                    if hasattr(method, 'body') and method.body:\n", "                        method_body = [(path, node) for path, node in method.body]\n", "                        \n", "                        # Variable transformations\n", "                        extract_variable_transformations(\n", "                            method_body, full_class_name, method.name,\n", "                            relations, existing_relations, nodes,\n", "                            variable_flows, method_assignments, rel_path\n", "                        )\n", "                        \n", "                        # Field-level data lineage\n", "                        extract_field_level_lineage(\n", "                            method_body, full_class_name, method.name,\n", "                            relations, existing_relations, nodes,\n", "                            variable_flows, method_assignments, rel_path\n", "                        )\n", "                        \n", "                        # Control flow structures\n", "                        cf_count = extract_control_flow_structures(\n", "                            method_body, full_class_name, method.name,\n", "                            relations, existing_relations, nodes,\n", "                            variable_flows, method_assignments, rel_path\n", "                        )\n", "                        stats['control_flow_structures'] += cf_count\n", "                        \n", "        except Exception as e:\n", "            print(f\"   Error processing {rel_path}: {e}\")\n", "            continue\n", "    \n", "    # Compile results\n", "    graph_data = {\n", "        'nodes': nodes,\n", "        'relations': relations,\n", "        'variable_flows': variable_flows,\n", "        'endpoint_tracker': endpoint_tracker,\n", "        'databases': list(databases),\n", "        'tables': list(tables),\n", "        'statistics': stats,\n", "        'endpoint_summary': {\n", "            'total_endpoints': len(endpoint_tracker.endpoints),\n", "            'unique_paths': len(set(ep['path'] for ep in endpoint_tracker.endpoints.values())),\n", "            'method_mappings': len(endpoint_tracker.endpoint_methods)\n", "        },\n", "        'transformation_summary': {\n", "            'variable_flows': len(variable_flows),\n", "            'control_flow_structures': stats['control_flow_structures']\n", "        }\n", "    }\n", "    \n", "    print(f\"\\nAnalysis completed!\")\n", "    print(f\"Statistics:\")\n", "    for key, value in stats.items():\n", "        print(f\"   - {key.replace('_', ' ').title()}: {value:,}\")\n", "    \n", "    return graph_data\n", "\n", "print(\"Main extraction function loaded\")"]}, {"cell_type": "markdown", "id": "export-header", "metadata": {}, "source": ["## 8. Export Functions"]}, {"cell_type": "code", "execution_count": 20, "id": "export-functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Export functions loaded\n"]}], "source": ["# ================== EXPORT FUNCTIONS ==================\n", "\n", "def export_to_csv(graph_data, output_dir):\n", "    \"\"\"Export graph data to CSV files.\"\"\"\n", "    \n", "    # Create output directory\n", "    os.makedirs(output_dir, exist_ok=True)\n", "    print(f\"Exporting to CSV files in: {output_dir}\")\n", "    \n", "    # Export nodes\n", "    nodes_df = pd.DataFrame.from_dict(graph_data['nodes'], orient='index')\n", "    nodes_df.to_csv(f\"{output_dir}/nodes.csv\", index=True)\n", "    print(f\"   - Exported {len(nodes_df)} nodes\")\n", "    \n", "    # Export relations\n", "    relations_df = pd.DataFrame(graph_data['relations'])\n", "    relations_df.to_csv(f\"{output_dir}/relations.csv\", index=False)\n", "    print(f\"   - Exported {len(relations_df)} relations\")\n", "    \n", "    # Export variable flows\n", "    if graph_data['variable_flows']:\n", "        flows_df = pd.DataFrame(graph_data['variable_flows'])\n", "        flows_df.to_csv(f\"{output_dir}/variable_flows.csv\", index=False)\n", "        print(f\"   - Exported {len(flows_df)} variable flows\")\n", "    \n", "    # Export endpoints\n", "    if graph_data['endpoint_tracker'].endpoints:\n", "        endpoints_df = pd.DataFrame.from_dict(graph_data['endpoint_tracker'].endpoints, orient='index')\n", "        endpoints_df.to_csv(f\"{output_dir}/endpoints.csv\", index=True)\n", "        print(f\"   - Exported {len(endpoints_df)} endpoints\")\n", "    \n", "    # Export summary statistics\n", "    stats_df = pd.DataFrame.from_dict(graph_data['statistics'], orient='index', columns=['count'])\n", "    stats_df.to_csv(f\"{output_dir}/statistics.csv\", index=True)\n", "    print(f\"   - Exported statistics summary\")\n", "    \n", "    print(f\"CSV export completed!\")\n", "\n", "def push_to_neo4j(graph_data, uri, user, password, database=\"neo4j\"):\n", "    \"\"\"Push graph data to Neo4j database.\"\"\"\n", "    \n", "    print(f\"Connecting to Neo4j at {uri}...\")\n", "    \n", "    try:\n", "        driver = GraphDatabase.driver(uri, auth=(user, password))\n", "        \n", "        with driver.session(database=database) as session:\n", "            # Clear existing data\n", "            print(\"Clearing existing data...\")\n", "            session.run(\"MATCH (n) DETACH DELETE n\")\n", "            \n", "            # Create nodes\n", "            print(f\"Creating {len(graph_data['nodes'])} nodes...\")\n", "            for node_id, node_data in graph_data['nodes'].items():\n", "                node_type = node_data.get('type', 'Unknown')\n", "                properties = {k: v for k, v in node_data.items() if v is not None}\n", "                \n", "                query = f\"CREATE (n:{node_type} $props)\"\n", "                session.run(query, props=properties)\n", "            \n", "            # Create relationships\n", "            print(f\"Creating {len(graph_data['relations'])} relationships...\")\n", "            for relation in graph_data['relations']:\n", "                query = \"\"\"\n", "                MATCH (a {id: $source}), (b {id: $target})\n", "                CREATE (a)-[r:`{relation_type}`]->(b)\n", "                SET r.file_path = $file_path\n", "                \"\"\".format(relation_type=relation['relation'])\n", "                \n", "                session.run(query, \n", "                           source=relation['source'],\n", "                           target=relation['target'],\n", "                           file_path=relation.get('file_path', ''))\n", "            \n", "            print(\"Neo4j push completed successfully!\")\n", "            \n", "    except Exception as e:\n", "        print(f\"Error pushing to Neo4j: {e}\")\n", "    finally:\n", "        if 'driver' in locals():\n", "            driver.close()\n", "\n", "print(\"Export functions loaded\")"]}, {"cell_type": "markdown", "id": "execution-header", "metadata": {}, "source": ["## 9. Execute Analysis"]}, {"cell_type": "code", "execution_count": 21, "id": "execute-analysis", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting Java code analysis for: OneInsights\n", "============================================================\n", "Found 75 Java files to process\n", "Processing: OneInsights\\ServicesBolt\\Application.java\n", "   Error processing OneInsights\\ServicesBolt\\Application.java: 'str' object has no attribute 'name'\n", "Processing: OneInsights\\ServicesBolt\\TriggerCollector.java\n", "   Error processing OneInsights\\ServicesBolt\\TriggerCollector.java: 'str' object has no attribute 'name'\n", "Processing: OneInsights\\ServicesBolt\\api\\ALMConfigController.java\n", "   Error processing OneInsights\\ServicesBolt\\api\\ALMConfigController.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\ServicesBolt\\api\\AlmController.java\n", "   Error processing OneInsights\\ServicesBolt\\api\\AlmController.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\ServicesBolt\\request\\ALMConfigReq.java\n", "   Error processing OneInsights\\ServicesBolt\\request\\ALMConfigReq.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\ServicesBolt\\request\\ALMToolReq.java\n", "   Error processing OneInsights\\ServicesBolt\\request\\ALMToolReq.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\ServicesBolt\\response\\Authentication.java\n", "Processing: OneInsights\\ServicesBolt\\response\\DataResponse.java\n", "Processing: OneInsights\\ServicesBolt\\service\\ALMConfigService.java\n", "Processing: OneInsights\\ServicesBolt\\service\\ALMConfigServiceImplementation.java\n", "   Error processing OneInsights\\ServicesBolt\\service\\ALMConfigServiceImplementation.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\ServicesBolt\\service\\AlmService.java\n", "Processing: OneInsights\\ServicesBolt\\service\\ALMServiceImplementation.java\n", "   Error processing OneInsights\\ServicesBolt\\service\\ALMServiceImplementation.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\ServicesBolt\\service\\ConfigurationSettingService.java\n", "Processing: OneInsights\\ServicesBolt\\service\\ConfigurationSettingServiceImplementation.java\n", "   Error processing OneInsights\\ServicesBolt\\service\\ConfigurationSettingServiceImplementation.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\ServicesBolt\\util\\DateUtil.java\n", "   Error processing OneInsights\\ServicesBolt\\util\\DateUtil.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\Application.java\n", "Processing: OneInsights\\UnifiedBolt\\core\\ConstantVariable.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\ConstantVariable.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\ProjectCollector.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\ProjectCollector.java: 'str' object has no attribute 'name'\n", "Processing: OneInsights\\UnifiedBolt\\core\\config\\Configuration.java\n", "Processing: OneInsights\\UnifiedBolt\\core\\config\\DataConfig.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\config\\DataConfig.java: 'str' object has no attribute 'name'\n", "Processing: OneInsights\\UnifiedBolt\\core\\config\\MongoAggregate.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\config\\MongoAggregate.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\model\\ALMConfiguration.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\model\\ALMConfiguration.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\model\\BaseModel.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\model\\BaseModel.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\model\\ChangeHistoryModel.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\model\\ChangeHistoryModel.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\model\\ComponentVelocityList.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\model\\ComponentVelocityList.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\model\\ConfigurationSetting.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\model\\ConfigurationSetting.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\model\\ConfigurationToolInfoMetric.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\model\\ConfigurationToolInfoMetric.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\model\\CustomFields.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\model\\CustomFields.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\model\\IterationModel.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\model\\IterationModel.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\model\\IterationOutModel.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\model\\IterationOutModel.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\model\\MetricsModel.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\model\\MetricsModel.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\model\\MonogOutMetrics.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\model\\MonogOutMetrics.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\model\\ScoreCardSprintData.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\model\\ScoreCardSprintData.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\model\\TransitionModel.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\model\\TransitionModel.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\model\\VelocityList.java\n", "   Error processing OneInsights\\UnifiedBolt\\core\\model\\VelocityList.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\core\\repository\\ALMConfigRepo.java\n", "Processing: OneInsights\\UnifiedBolt\\core\\repository\\ChangeHisortyRepo.java\n", "Processing: OneInsights\\UnifiedBolt\\core\\repository\\ConfigurationSettingRep.java\n", "Processing: OneInsights\\UnifiedBolt\\core\\repository\\IterationRepo.java\n", "Processing: OneInsights\\UnifiedBolt\\core\\repository\\MetricRepo.java\n", "Processing: OneInsights\\UnifiedBolt\\core\\repository\\TransitionRepo.java\n", "Processing: OneInsights\\UnifiedBolt\\jira\\ALMClientImplementation.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\ALMClientImplementation.java: 'str' object has no attribute 'name'\n", "Processing: OneInsights\\UnifiedBolt\\jira\\ChartCalculations.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\ChartCalculations.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\jira\\customFieldNames.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\customFieldNames.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\jira\\DeleteJiraIssues.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\DeleteJiraIssues.java: 'str' object has no attribute 'name'\n", "Processing: OneInsights\\UnifiedBolt\\jira\\EffortAndChangeItemInfo.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\EffortAndChangeItemInfo.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\jira\\IssueHierarchy.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\IssueHierarchy.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\jira\\IterationInfo.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\IterationInfo.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\jira\\JIRAApplication.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\JIRAApplication.java: 'str' object has no attribute 'name'\n", "Processing: OneInsights\\UnifiedBolt\\jira\\JiraAuthentication.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\JiraAuthentication.java: 'str' object has no attribute 'name'\n", "Processing: OneInsights\\UnifiedBolt\\jira\\JIRAClient.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\JIRAClient.java: 'str' object has no attribute 'name'\n", "Processing: OneInsights\\UnifiedBolt\\jira\\JiraExceptions.java\n", "Processing: OneInsights\\UnifiedBolt\\jira\\MetricsInfo.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\MetricsInfo.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\jira\\RallyAuthentication.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\RallyAuthentication.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\jira\\ReleaseInfo.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\ReleaseInfo.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\jira\\SprintWiseCalculation.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\SprintWiseCalculation.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\jira\\TransitionInfo.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\TransitionInfo.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\jira\\TransitionMetrices.java\n", "   Error processing OneInsights\\UnifiedBolt\\jira\\TransitionMetrices.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\util\\BacklogCalculation.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\BacklogCalculation.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\util\\BuildCalculations.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\BuildCalculations.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\util\\CommonFunctions.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\CommonFunctions.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\util\\Constant.java\n", "Processing: OneInsights\\UnifiedBolt\\util\\CryptoUtils.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\CryptoUtils.java: 'str' object has no attribute 'name'\n", "Processing: OneInsights\\UnifiedBolt\\util\\DateUtil.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\DateUtil.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\util\\DefectCalculations.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\DefectCalculations.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\util\\EncryptionDecryptionAES.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\EncryptionDecryptionAES.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\util\\EncryptorAesGcmPassword.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\EncryptorAesGcmPassword.java: 'str' object has no attribute 'name'\n", "Processing: OneInsights\\UnifiedBolt\\util\\RestClient.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\RestClient.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\util\\SprintProgress.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\SprintProgress.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\util\\SprintProgressCalculations.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\SprintProgressCalculations.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\util\\StoryProgressModel.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\StoryProgressModel.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\util\\StoryProgressSprintwise.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\StoryProgressSprintwise.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\util\\TaskRiskSprint.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\TaskRiskSprint.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\util\\TeamQualityUtils.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\TeamQualityUtils.java: too many values to unpack (expected 2)\n", "Processing: OneInsights\\UnifiedBolt\\util\\VelocityCalculations.java\n", "   Error processing OneInsights\\UnifiedBolt\\util\\VelocityCalculations.java: too many values to unpack (expected 2)\n", "\n", "Analysis completed!\n", "Statistics:\n", "   - Files Processed: 75\n", "   - Classes Found: 54\n", "   - Methods Found: 110\n", "   - Endpoints Found: 50\n", "   - Control Flow Structures: 0\n", "   - Variable Transformations: 0\n", "   - Database Operations: 0\n", "   - Field Level Lineages: 0\n", "   - Cross Method Flows: 0\n", "   - Package Dependencies: 0\n", "   - Exception Flows: 0\n", "   - Annotation Behaviors: 0\n", "Exporting to CSV files in: enhanced_graph_csv_v2\n", "   - Exported 583 nodes\n", "   - Exported 623 relations\n", "   - Exported 50 endpoints\n", "   - Exported statistics summary\n", "CSV export completed!\n", "\n", "Analysis pipeline completed successfully!\n"]}], "source": ["# ================== EXECUTE ANALYSIS ==================\n", "\n", "# Run the analysis\n", "graph_data = extract_java_graph_data(PROJECT_PATH)\n", "\n", "# Export to CSV\n", "export_to_csv(graph_data, CSV_OUTPUT_DIR)\n", "\n", "print(\"\\nAnalysis pipeline completed successfully!\")"]}, {"cell_type": "markdown", "id": "results-header", "metadata": {}, "source": ["## 10. Results and Summary"]}, {"cell_type": "code", "execution_count": 22, "id": "results-summary", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "JAVA CODE ANALYSIS COMPLETE!\n", "============================================================\n", "FINAL STATISTICS:\n", "   • Files processed: 75\n", "   • Classes found: 54\n", "   • Methods found: 110\n", "   • Total nodes: 583\n", "   • Total relationships: 623\n", "   • API endpoints: 50\n", "   • Variable transformations: 0\n", "   • Control flow structures: 0\n", "   • Database tables: 0\n", "   • Field-level lineages: 0\n", "   • Cross-method flows: 0\n", "   • Package dependencies: 0\n", "   • Exception flows: 0\n", "   • Annotation behaviors: 0\n", "\n", "OUTPUT LOCATIONS:\n", "   • CSV files: ./enhanced_graph_csv_v2/\n", "   • Neo4j database: my-oneinsights (when pushed)\n", "\n", "KEY IMPROVEMENTS IN V2:\n", "   • Removed duplicate functions\n", "   • Single Neo4j push at the end\n", "   • Clean execution flow\n", "   • Enhanced data lineage with transformation chains\n", "   • Consolidated similar functions\n", "   • Better error handling and logging\n", "   • Field-level data lineage tracking\n", "   • Cross-method variable flow analysis\n", "   • Package-level dependency mapping\n", "   • Exception flow lineage tracking\n", "   • Annotation-driven behavior analysis\n", "   • Complete inheritance and interface mapping\n", "   • Enhanced database and JPA analysis\n", "\n", "WHAT YOU CAN DO NEXT:\n", "   1. Explore CSV files for detailed data analysis\n", "   2. <PERSON><PERSON> to Neo4j for interactive graph exploration\n", "   3. Use variable flows for data lineage analysis\n", "   4. Analyze endpoint usage patterns\n", "   5. Study control flow and transformation chains\n", "\n", "To push to Neo4j, run:\n", "   push_to_neo4j(graph_data, NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD, NEO4J_DB)\n"]}], "source": ["# ================== RESULTS SUMMARY ==================\n", "\n", "if 'graph_data' in locals() and graph_data is not None:\n", "    print(\"\\nJAVA CODE ANALYSIS COMPLETE!\")\n", "    print(\"=\" * 60)\n", "    print(f\"FINAL STATISTICS:\")\n", "    print(f\"   • Files processed: {graph_data['statistics']['files_processed']:,}\")\n", "    print(f\"   • Classes found: {graph_data['statistics']['classes_found']:,}\")\n", "    print(f\"   • Methods found: {graph_data['statistics']['methods_found']:,}\")\n", "    print(f\"   • Total nodes: {len(graph_data['nodes']):,}\")\n", "    print(f\"   • Total relationships: {len(graph_data['relations']):,}\")\n", "    print(f\"   • API endpoints: {graph_data['endpoint_summary']['total_endpoints']:,}\")\n", "    print(f\"   • Variable transformations: {len(graph_data['variable_flows']):,}\")\n", "    print(f\"   • Control flow structures: {graph_data['transformation_summary']['control_flow_structures']:,}\")\n", "    print(f\"   • Database tables: {len(graph_data['tables']):,}\")\n", "    print(f\"   • Field-level lineages: {graph_data['statistics']['field_level_lineages']:,}\")\n", "    print(f\"   • Cross-method flows: {graph_data['statistics']['cross_method_flows']:,}\")\n", "    print(f\"   • Package dependencies: {graph_data['statistics']['package_dependencies']:,}\")\n", "    print(f\"   • Exception flows: {graph_data['statistics']['exception_flows']:,}\")\n", "    print(f\"   • Annotation behaviors: {graph_data['statistics']['annotation_behaviors']:,}\")\n", "    \n", "    print(f\"\\nOUTPUT LOCATIONS:\")\n", "    print(f\"   • CSV files: ./{CSV_OUTPUT_DIR}/\")\n", "    print(f\"   • Neo4j database: {NEO4J_DB} (when pushed)\")\n", "    \n", "    print(f\"\\nKEY IMPROVEMENTS IN V2:\")\n", "    print(f\"   • Removed duplicate functions\")\n", "    print(f\"   • Single Neo4j push at the end\")\n", "    print(f\"   • Clean execution flow\")\n", "    print(f\"   • Enhanced data lineage with transformation chains\")\n", "    print(f\"   • Consolidated similar functions\")\n", "    print(f\"   • Better error handling and logging\")\n", "    print(f\"   • Field-level data lineage tracking\")\n", "    print(f\"   • Cross-method variable flow analysis\")\n", "    print(f\"   • Package-level dependency mapping\")\n", "    print(f\"   • Exception flow lineage tracking\")\n", "    print(f\"   • Annotation-driven behavior analysis\")\n", "    print(f\"   • Complete inheritance and interface mapping\")\n", "    print(f\"   • Enhanced database and JPA analysis\")\n", "    \n", "    print(f\"\\nWHAT YOU CAN DO NEXT:\")\n", "    print(f\"   1. Explore CSV files for detailed data analysis\")\n", "    print(f\"   2. Push to <PERSON>4j for interactive graph exploration\")\n", "    print(f\"   3. Use variable flows for data lineage analysis\")\n", "    print(f\"   4. Analyze endpoint usage patterns\")\n", "    print(f\"   5. Study control flow and transformation chains\")\n", "    \n", "    print(f\"\\nTo push to Neo4j, run:\")\n", "    print(f\"   push_to_neo4j(graph_data, NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD, NEO4J_DB)\")\n", "    \n", "else:\n", "    print(\"No graph data available. Please run the analysis first.\")"]}, {"cell_type": "markdown", "id": "optional-neo4j-header", "metadata": {}, "source": ["## 11. Optional: Push to <PERSON>4j"]}, {"cell_type": "code", "execution_count": null, "id": "8faeb32a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 23, "id": "optional-neo4j", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connecting to Neo4j at bolt://localhost:7687...\n", "Clearing existing data...\n", "Creating 583 nodes...\n", "Creating 623 relationships...\n", "Error pushing to Neo4j: 'id'\n", "Neo4j push section ready (uncomment to execute)\n"]}], "source": ["# ================== OPTIONAL: PUSH TO NEO4J ==================\n", "# Uncomment the line below to push data to Neo4j\n", "push_to_neo4j(graph_data, NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD, NEO4J_DB)\n", "\n", "print(\"Neo4j push section ready (uncomment to execute)\")"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}