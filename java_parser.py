# v7

import os
import javalang
from collections import defaultdict
import json
import logging

def extract_relations_to_json_javalang(project_path):
    logging.info(f"Scanning Java files in: {project_path}")
    nodes = {}  # metadata
    relations = []
    collection_entities = {}  # full class name → collection
    file_uses_collections = defaultdict(set)
    class_uses_classes = defaultdict(set)

    existing_relations = set()

    def register_node(raw_node, file_path):
        if not raw_node or raw_node in nodes:
            return

        node_type, full_name = raw_node.split(":", 1)

        # Determine short name
        if node_type == "file":
            short_name = os.path.basename(full_name)
        elif node_type in ("folder", "project"):
            short_name = os.path.basename(full_name.rstrip("/\\"))
        else:
            short_name = full_name.split(".")[-1] if "." in full_name else full_name

        nodes[raw_node] = {
            "id": raw_node,
            "type": node_type,
            "name": short_name,
            "full_name": full_name,
            "file_path": file_path
        }

    def add_relation(src, rel, dst, file_path):
        if not src or not dst:
            return

        key = (src, rel, dst)
        if key in existing_relations:
            return

        existing_relations.add(key)
        register_node(src, file_path)
        register_node(dst, file_path)
        relations.append([src, rel, dst])


    project_name = os.path.basename(os.path.abspath(project_path))
    project_node = f"project:{project_name}"
    # add_relation("root", "contains", project_node, project_path)  # Optional top-level root relation

    java_files = []
    for root, dirs, files in os.walk(project_path):
        rel_root = os.path.relpath(root, project_path)
        abs_root = os.path.join(project_path, rel_root)

        path_parts = rel_root.split(os.sep)

        # Determine the node type
        if rel_root == ".":
            current_node = project_node
        elif len(path_parts) == 1:
            current_node = f"application:{abs_root}"
            add_relation(project_node, "contains", current_node, abs_root)
        else:
            current_node = f"package:{abs_root}"
            parent_path = os.path.join(project_path, *path_parts[:-1])
            parent_node = (
                f"application:{parent_path}" if len(path_parts) == 2 else f"package:{parent_path}"
            )
            add_relation(parent_node, "contains", current_node, abs_root)

        for d in dirs:
            subfolder_rel = os.path.relpath(os.path.join(root, d), project_path)
            subfolder_abs = os.path.join(project_path, subfolder_rel)
            sub_path_parts = subfolder_rel.split(os.sep)

            if len(sub_path_parts) == 1:
                sub_node = f"application:{subfolder_abs}"
            else:
                sub_node = f"package:{subfolder_abs}"

            add_relation(current_node, "contains", sub_node, abs_root)

        for file in files:
            if file.endswith(".java"):
                java_files.append(os.path.join(root, file))

    # Parse Java files
    parsed_files = {}
    for file_path in java_files:
        with open(file_path, "r", encoding="utf-8") as f:
            try:
                parsed_files[file_path] = javalang.parse.parse(f.read())
            except javalang.parser.JavaSyntaxError:
                continue

    # Class ↔ MongoDB collection mapping
    for file_path, tree in parsed_files.items():
        import_map = {}
        package_name = tree.package.name if tree.package else None

        for imp in tree.imports:
            if imp.path and not imp.wildcard and not imp.path.startswith(("java.", "javax.")):
                class_name = imp.path.split('.')[-1]
                import_map[class_name] = imp.path

        for type_decl in tree.types:
            if not isinstance(type_decl, javalang.tree.ClassDeclaration):
                continue

            class_name = type_decl.name
            full_class_name = f"{package_name}.{class_name}" if package_name else class_name
            class_node = f"class:{full_class_name}"

            for annotation in type_decl.annotations:
                if annotation.name == "Document":
                    for pair in annotation.element:
                        if pair.name == "collection":
                            collection = pair.value.value
                            collection_entities[full_class_name] = collection
                            rel_path = os.path.relpath(file_path, project_path)
                            add_relation(class_node, "mapped_to_collection", f"collection:{collection}", rel_path)

    # File ↔ Folder mapping (updated to reflect project/app/package structure)
    for file_path, tree in parsed_files.items():
        rel_path = os.path.relpath(file_path, project_path)
        abs_path = os.path.join(project_path, rel_path)

        folder_path = os.path.dirname(abs_path)
        folder_parts = os.path.relpath(folder_path, project_path).split(os.sep)

        if len(folder_parts) == 1:
            folder_node = f"application:{folder_path}"
        elif len(folder_parts) >= 2:
            folder_node = f"package:{folder_path}"
        else:
            folder_node = project_node

        file_node = f"file:{abs_path}"
        add_relation(folder_node, "contains", file_node, abs_path)


        # get all the imports and store in a dictionary
        import_map = {}
        package_name = tree.package.name if tree.package else None

        for imp in tree.imports:
            if imp.path and not imp.wildcard and not imp.path.startswith(("java.", "javax.")): # skip wildcars *, and java imports
                class_name = imp.path.split('.')[-1]
                import_map[class_name] = imp.path

        for type_decl in tree.types: # high level decalrations like class and interface
            if not isinstance(type_decl, (javalang.tree.ClassDeclaration, javalang.tree.InterfaceDeclaration)):
                continue

            decl_type = "class" if isinstance(type_decl, javalang.tree.ClassDeclaration) else "interface"
            full_decl_name = f"{package_name}.{type_decl.name}" if package_name else type_decl.name
            decl_node = f"{decl_type}:{full_decl_name}"
            add_relation(file_node, "declares", decl_node, rel_path) # file delcares class/ interface

            # (class → interface)
            if isinstance(type_decl, javalang.tree.ClassDeclaration) and type_decl.implements:
                for impl in type_decl.implements:
                    interface_name = impl.name
                    if interface_name in import_map:
                        impl_full = import_map[interface_name]
                        add_relation(decl_node, "implements", f"interface:{impl_full}", rel_path) # class implements interface

            # class Extends or interface extends
            if type_decl.extends:
                if isinstance(type_decl.extends, list):
                    for ext in type_decl.extends:
                        if ext.name in import_map:
                            ext_full = import_map[ext.name]
                            add_relation(decl_node, "extends", f"{decl_type}:{ext_full}", rel_path)
                else:
                    ext = type_decl.extends
                    if ext.name in import_map:
                        ext_full = import_map[ext.name]
                        add_relation(decl_node, "extends", f"{decl_type}:{ext_full}", rel_path)


            # direct class /interface variables
            for field in getattr(type_decl, "fields", []):
                for decl in field.declarators:
                    var_name = decl.name
                    var_node = f"variable:{full_decl_name}.{var_name}"
                    add_relation(decl_node, "has_variable", var_node, rel_path)

                    if hasattr(field.type, 'name') and field.type.name in import_map:
                        imp_class = import_map[field.type.name]
                        # add_relation(decl_node, "uses_class", f"class:{imp_class}", rel_path)
                        class_uses_classes[full_decl_name].add(imp_class)

                        if imp_class in collection_entities:
                            collection = collection_entities[imp_class]
                            add_relation(var_node, "uses_collection", f"collection:{collection}", rel_path) # direct varaible uses collection

            for method in getattr(type_decl, "methods", []):
                method_node = f"method:{full_decl_name}.{method.name}"
                add_relation(decl_node, "has_method", method_node, rel_path)

                if not method.body:
                    continue

                declared_var_types = {}


                # methods
                for path, node in method:
                    if isinstance(node, javalang.tree.LocalVariableDeclaration):
                        for decl in node.declarators:
                            var_name = decl.name
                            var_node = f"variable:{full_decl_name}.{method.name}.{var_name}"
                            add_relation(method_node, "uses", var_node, rel_path) # method uses variable

                            # get the variable type
                            if hasattr(node.type, 'name'):
                                type_name = node.type.name
                                declared_var_types[var_name] = type_name
                                # if the type is from imports
                                if type_name in import_map:
                                    imp_class = import_map[type_name]
                                    add_relation(var_node, "instance_of", f"class:{imp_class}", rel_path)
                                    class_uses_classes[full_decl_name].add(imp_class)

                                # get method arguments
                                if hasattr(node.type, 'arguments') and node.type.arguments:
                                    for arg in node.type.arguments:
                                        if hasattr(arg, 'type') and hasattr(arg.type, 'name'):
                                            generic_type = arg.type.name
                                            if generic_type in import_map:
                                                imp_class = import_map[generic_type]
                                                add_relation(var_node, "instance_of", f"class:{imp_class}", rel_path)

                    # method calls
                    elif isinstance(node, javalang.tree.MethodInvocation):
                        skip_standard = False
                        called_method_node = None

                        # arguments of called medthod
                        if node.qualifier and node.arguments:
                            qualifier = node.qualifier
                            for arg in node.arguments:
                                if isinstance(arg, javalang.tree.MemberReference):
                                    src = f"variable:{full_decl_name}.{method.name}.{arg.member}"
                                    dst = f"variable:{full_decl_name}.{method.name}.{qualifier}"
                                    add_relation(src, "flows_to", dst, rel_path)

                        # if method is from known import
                        if node.qualifier and node.qualifier in declared_var_types:
                            type_name = declared_var_types[node.qualifier]
                            if type_name in import_map:
                                imp_class = import_map[type_name]
                                called_method_node = f"method:{imp_class}.{node.member}"
                            else:
                                skip_standard = True

                        elif node.qualifier:
                            if node.qualifier[0].islower(): # if it starts with lowercase likely a variable
                                skip_standard = True
                            else:
                                called_method_node = f"method:{node.qualifier}.{node.member}" # else class
                        else:
                            called_method_node = f"method:{full_decl_name}.{node.member}"

                        if not skip_standard and called_method_node:
                            add_relation(method_node, "calls", called_method_node, rel_path) # local method calls

                        # agruments passed to method
                        for arg in node.arguments:
                            if isinstance(arg, javalang.tree.MemberReference):
                                var_node = f"variable:{full_decl_name}.{method.name}.{arg.member}"
                            elif isinstance(arg, str):
                                var_node = f"literal:{arg}"
                            else:
                                continue
                            add_relation(called_method_node, "uses", var_node, rel_path)

                        # map variable to its type
                        if node.qualifier:
                            qualifier_var = f"variable:{full_decl_name}.{method.name}.{node.qualifier}"
                            if node.qualifier in declared_var_types:
                                type_name = declared_var_types[node.qualifier]
                                if type_name in import_map:
                                    imp_class = import_map[type_name]
                                    add_relation(qualifier_var, "instance_of", f"class:{imp_class}", rel_path)
                                    class_uses_classes[full_decl_name].add(imp_class)

                    # variable assignments
                    elif isinstance(node, javalang.tree.Assignment):
                        left = getattr(node, 'expressionl', None) or getattr(node, 'left', None)
                        right = node.value

                        # get left hand variable name
                        if isinstance(left, javalang.tree.MemberReference):
                            left_name = left.member
                        elif isinstance(left, str):
                            left_name = left
                        else:
                            continue

                        left_var = f"variable:{full_decl_name}.{method.name}.{left_name}"
                        add_relation(method_node, "declares", left_var, rel_path)

                        # if there is method call in right hand side
                        if isinstance(right, javalang.tree.MethodInvocation):
                            if right.qualifier:
                                called_method_node = f"method:{right.qualifier}.{right.member}" # userservice.getname
                            else:
                                called_method_node = f"method:{full_decl_name}.{right.member}" # getname6
                            add_relation(called_method_node, "assigns", left_var, rel_path)
                            add_relation(method_node, "calls", called_method_node, rel_path)
    logging.info(f"Extracted {len(nodes)} nodes and {len(relations)} relations")
    return {
        "nodes": list(nodes.values()),
        "relations": relations
    }
